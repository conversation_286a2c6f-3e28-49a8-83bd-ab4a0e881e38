import type { ResponseCookies } from '../../server/web/spec-extension/cookies';
import { RedirectStatusCode } from './redirect-status-code';
declare const REDIRECT_ERROR_CODE = "NEXT_REDIRECT";
export declare enum RedirectType {
    push = "push",
    replace = "replace"
}
export type RedirectError<U extends string> = Error & {
    digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${U};${RedirectStatusCode};`;
    mutableCookies: ResponseCookies;
};
export declare function getRedirectError(url: string, type: RedirectType, statusCode?: RedirectStatusCode): RedirectError<typeof url>;
/**
 * When used in a streaming context, this will insert a meta tag to
 * redirect the user to the target page. When used in a custom app route, it
 * will serve a 307/303 to the caller.
 *
 * @param url the url to redirect to
 */
export declare function redirect(url: string, type?: RedirectType): never;
/**
 * When used in a streaming context, this will insert a meta tag to
 * redirect the user to the target page. When used in a custom app route, it
 * will serve a 308/303 to the caller.
 *
 * @param url the url to redirect to
 */
export declare function permanentRedirect(url: string, type?: RedirectType): never;
/**
 * Checks an error to determine if it's an error generated by the
 * `redirect(url)` helper.
 *
 * @param error the error that may reference a redirect error
 * @returns true if the error is a redirect error
 */
export declare function isRedirectError<U extends string>(error: any): error is RedirectError<U>;
/**
 * Returns the encoded URL from the error if it's a RedirectError, null
 * otherwise. Note that this does not validate the URL returned.
 *
 * @param error the error that may be a redirect error
 * @return the url if the error was a redirect error
 */
export declare function getURLFromRedirectError<U extends string>(error: RedirectError<U>): U;
export declare function getRedirectTypeFromError<U extends string>(error: RedirectError<U>): RedirectType;
export declare function getRedirectStatusCodeFromError<U extends string>(error: RedirectError<U>): number;
export {};
