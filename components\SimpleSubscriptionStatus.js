// Simple SubscriptionStatus Component - Minimal version to avoid errors
function SimpleSubscriptionStatus({ authContext, subscription, onUpgrade, onManage }) {
    // Early return if no auth context
    if (!authContext) {
        return (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="text-center text-gray-500">
                    <i className="fas fa-user-slash mr-2"></i>
                    Authentication required
                </div>
            </div>
        );
    }

    // If no subscription data, show default message
    if (!subscription) {
        return (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <i className="fas fa-info-circle text-yellow-500 mr-3"></i>
                        <div>
                            <h3 className="text-lg font-medium text-yellow-800">No Active Subscription</h3>
                            <p className="text-sm text-yellow-700">Start your free trial to access all features.</p>
                        </div>
                    </div>
                    {onUpgrade && (
                        <button
                            onClick={onUpgrade}
                            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                        >
                            Start Free Trial
                        </button>
                    )}
                </div>
            </div>
        );
    }

    // Safe property access with defaults
    const planName = (subscription && subscription.plan_name) || 'Current Plan';
    const status = (subscription && subscription.status) || 'unknown';
    const isTrialSubscription = status === 'trial' || (subscription && subscription.is_trial);
    
    // Safe date formatting
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch (e) {
            return 'Invalid Date';
        }
    };

    // Calculate days remaining safely
    const getDaysRemaining = (endDate) => {
        if (!endDate) return 0;
        try {
            const end = new Date(endDate);
            const now = new Date();
            const diffTime = end - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return Math.max(0, diffDays);
        } catch (e) {
            return 0;
        }
    };

    const daysRemaining = isTrialSubscription ? getDaysRemaining(subscription.trial_end_date) : null;

    return (
        <div className={`rounded-lg shadow-sm border p-6 ${
            isTrialSubscription ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
        }`}>
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-4 ${
                        isTrialSubscription ? 'bg-blue-500' : 'bg-green-500'
                    }`}>
                        <i className={`fas ${isTrialSubscription ? 'fa-clock' : 'fa-check'} text-white`}></i>
                    </div>
                    <div>
                        <h3 className={`text-lg font-semibold ${
                            isTrialSubscription ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                            {planName}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm">
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                isTrialSubscription 
                                    ? 'bg-blue-100 text-blue-800' 
                                    : status === 'active'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                            }`}>
                                {isTrialSubscription ? 'Free Trial' : status}
                            </span>
                            {subscription && subscription.billing_cycle && (
                                <span className="text-gray-600">
                                    ${subscription.amount || 0}/{subscription.billing_cycle}
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center space-x-3">
                    {isTrialSubscription && daysRemaining !== null && (
                        <div className="text-right">
                            <div className="text-sm font-medium text-blue-900">
                                {daysRemaining > 0 ? `${daysRemaining} days left` : 'Trial Expired'}
                            </div>
                            <div className="text-xs text-blue-700">
                                Expires: {formatDate(subscription.trial_end_date)}
                            </div>
                        </div>
                    )}
                    
                    <div className="flex space-x-2">
                        {isTrialSubscription && onUpgrade && (
                            <button
                                onClick={onUpgrade}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                Upgrade Now
                            </button>
                        )}
                        {onManage && (
                            <button
                                onClick={onManage}
                                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                                Manage
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.SimpleSubscriptionStatus = SimpleSubscriptionStatus;