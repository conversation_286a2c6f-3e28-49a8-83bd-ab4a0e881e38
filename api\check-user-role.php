<?php
// Include database configuration
require_once 'db-config.php';

// List all users
echo "All users in the database:\n";
$sql = "SELECT object_id, name, email, role FROM users";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    while ($user = $result->fetch_assoc()) {
        echo "User: " . $user['name'] . " (" . $user['email'] . ")\n";
        echo "Role: " . $user['role'] . "\n";
        echo "ID: " . $user['object_id'] . "\n";
        echo "-------------------\n";
    }
} else {
    echo "No users found in the database\n";
}

// Close the connection
$conn->close();
?>