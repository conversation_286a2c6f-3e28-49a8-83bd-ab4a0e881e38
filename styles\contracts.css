.contract-form {
    max-width: 800px;
    margin: 0 auto;
}

.contract-preview {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.contract-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.contract-section {
    margin-bottom: 2rem;
}

.contract-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.contract-parties {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.party-details {
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.contract-terms {
    margin-bottom: 2rem;
}

.contract-signature {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.signature-box {
    padding: 1rem;
    border: 1px dashed #d1d5db;
    border-radius: 0.5rem;
    text-align: center;
    min-height: 100px;
}

.signature-placeholder {
    color: #6b7280;
    font-style: italic;
}

.contract-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.contract-status.draft {
    background-color: #f3f4f6;
    color: #4b5563;
}

.contract-status.pending {
    background-color: #dbeafe;
    color: #1e40af;
}

.contract-status.signed {
    background-color: #d1fae5;
    color: #065f46;
}

.contract-status.expired {
    background-color: #fee2e2;
    color: #991b1b;
}

@media (max-width: 768px) {
    .contract-parties,
    .contract-signature {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
