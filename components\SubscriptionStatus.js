// SubscriptionStatus Component
function SubscriptionStatus({ authContext, subscription, onUpgrade, onManage }) {
    const [currentSubscription, setCurrentSubscription] = React.useState(null);
    const [trialStatus, setTrialStatus] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    // Early return if no auth context to prevent errors
    if (!authContext) {
        return (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="text-center text-gray-500">
                    <i className="fas fa-user-slash mr-2"></i>
                    Authentication required
                </div>
            </div>
        );
    }

    React.useEffect(() => {
        if (authContext && authContext.isAuthenticated && authContext.token) {
            fetchSubscriptionData();
        }
    }, [authContext]);

    const fetchSubscriptionData = async () => {
        try {
            setLoading(true);
            setError(null);
            
            // Fetch current subscription
            const subResponse = await fetch(window.getApiUrl('/subscription-management/current'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (subResponse.ok) {
                const subData = await subResponse.json();
                if (subData.success) {
                    setCurrentSubscription(subData.data);
                }
            }

            // Fetch trial status
            const trialResponse = await fetch(window.getApiUrl('/subscription-management/trial-status'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (trialResponse.ok) {
                const trialData = await trialResponse.json();
                if (trialData.success) {
                    setTrialStatus(trialData.data);
                }
            }

        } catch (error) {
            console.error('Error fetching subscription data:', error);
            setError('Failed to load subscription information');
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    };

    const getDaysRemaining = (endDate) => {
        if (!endDate) return 0;
        const end = new Date(endDate);
        const now = new Date();
        const diffTime = end - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.max(0, diffDays);
    };

    if (loading) {
        return (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                    <i className="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                    <span className="text-red-700">{error}</span>
                </div>
            </div>
        );
    }

    // Use the passed subscription prop or the fetched currentSubscription
    const subscriptionData = subscription || currentSubscription;

    // Debug logging
    React.useEffect(() => {
        console.log('SubscriptionStatus: Received subscription data:', subscriptionData);
        console.log('SubscriptionStatus: Received subscription prop:', subscription);
        console.log('SubscriptionStatus: Current subscription state:', currentSubscription);
    }, [subscriptionData, subscription, currentSubscription]);

    // Parse features and limits safely if they are JSON strings
    const parseJsonSafely = (data) => {
        if (!data) return null;
        if (typeof data === 'object') return data;
        if (typeof data === 'string') {
            try {
                return JSON.parse(data);
            } catch (e) {
                console.warn('Failed to parse JSON data:', data);
                return null;
            }
        }
        return null;
    };

    // Safely parse features and limits
    if (subscriptionData && subscriptionData.features) {
        const originalFeatures = subscriptionData.features;
        subscriptionData.features = parseJsonSafely(subscriptionData.features);
        console.log('SubscriptionStatus: Parsed features from', originalFeatures, 'to', subscriptionData.features);
    }
    if (subscriptionData && subscriptionData.limits) {
        const originalLimits = subscriptionData.limits;
        subscriptionData.limits = parseJsonSafely(subscriptionData.limits);
        console.log('SubscriptionStatus: Parsed limits from', originalLimits, 'to', subscriptionData.limits);
    }

    if (!subscriptionData) {
        return (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <i className="fas fa-info-circle text-yellow-500 mr-3"></i>
                        <div>
                            <h3 className="text-lg font-medium text-yellow-800">No Active Subscription</h3>
                            <p className="text-sm text-yellow-700">Start your free trial to access all features.</p>
                        </div>
                    </div>
                    {onUpgrade && (
                        <button
                            onClick={onUpgrade}
                            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                        >
                            Start Free Trial
                        </button>
                    )}
                </div>
            </div>
        );
    }

    const isTrialSubscription = subscriptionData && (subscriptionData.status === 'trial' || subscriptionData.is_trial);
    const daysRemaining = isTrialSubscription && subscriptionData ? getDaysRemaining(subscriptionData.trial_end_date) : null;

    return (
        <div className={`rounded-lg shadow-sm border p-6 ${
            isTrialSubscription ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
        }`}>
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-4 ${
                        isTrialSubscription ? 'bg-blue-500' : 'bg-green-500'
                    }`}>
                        <i className={`fas ${isTrialSubscription ? 'fa-clock' : 'fa-check'} text-white`}></i>
                    </div>
                    <div>
                        <h3 className={`text-lg font-semibold ${
                            isTrialSubscription ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                            {(subscriptionData && subscriptionData.plan_name) || 'Current Plan'}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm">
                            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                isTrialSubscription 
                                    ? 'bg-blue-100 text-blue-800' 
                                    : (subscriptionData && subscriptionData.status === 'active')
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                            }`}>
                                {isTrialSubscription ? 'Free Trial' : (subscriptionData && subscriptionData.status) || 'Unknown'}
                            </span>
                            {subscriptionData && subscriptionData.billing_cycle && (
                                <span className="text-gray-600">
                                    ${(subscriptionData && subscriptionData.amount) || 0}/{subscriptionData.billing_cycle}
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center space-x-3">
                    {isTrialSubscription && (
                        <div className="text-right">
                            <div className="text-sm font-medium text-blue-900">
                                {daysRemaining > 0 ? `${daysRemaining} days left` : 'Trial Expired'}
                            </div>
                            <div className="text-xs text-blue-700">
                                Expires: {formatDate(subscriptionData && subscriptionData.trial_end_date)}
                            </div>
                        </div>
                    )}
                    
                    <div className="flex space-x-2">
                        {isTrialSubscription && onUpgrade && (
                            <button
                                onClick={onUpgrade}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                Upgrade Now
                            </button>
                        )}
                        {onManage && (
                            <button
                                onClick={onManage}
                                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                                Manage
                            </button>
                        )}
                    </div>
                </div>
            </div>

            {/* Features or limits display */}
            {subscriptionData && subscriptionData.features && Array.isArray(subscriptionData.features) && subscriptionData.features.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {subscriptionData.features
                            .filter(feature => feature && typeof feature === 'object' && feature.name)
                            .slice(0, 4)
                            .map((feature, index) => (
                                <div key={index} className="flex items-center text-sm">
                                    <i className={`fas fa-${feature.enabled ? 'check' : 'times'} mr-2 ${
                                        feature.enabled ? 'text-green-500' : 'text-red-500'
                                    }`}></i>
                                    <span className={feature.enabled ? 'text-gray-700' : 'text-gray-400'}>
                                        {feature.name}
                                    </span>
                                </div>
                            ))
                        }
                    </div>
                </div>
            )}

            {/* Usage limits display */}
            {subscriptionData && subscriptionData.limits && typeof subscriptionData.limits === 'object' && Object.keys(subscriptionData.limits).length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Object.entries(subscriptionData.limits)
                            .filter(([key, limit]) => key && limit !== null && limit !== undefined)
                            .slice(0, 4)
                            .map(([key, limit], index) => (
                                <div key={index} className="text-sm">
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 capitalize">{key.replace('_', ' ')}</span>
                                        <span className="font-medium">
                                            {limit === -1 ? 'Unlimited' : limit}
                                        </span>
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                </div>
            )}
        </div>
    );
}

// Make component globally available
window.SubscriptionStatus = SubscriptionStatus;