<?php
/**
 * Recent Activity API
 * Provides recent activity feed for dashboard
 */

require_once 'db-config.php';

header('Content-Type: application/json');

// For now, use a default company ID - in production, get from authenticated user
$companyId = 'super_admin_001';

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        $activities = [];
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        
        // Get recent activities from different tables
        $queries = [
            [
                'table' => 'customers',
                'query' => "SELECT 'customer' as type, 'Customer Added' as title, CONCAT('New customer: ', name) as description, created_at FROM customers WHERE company_id = ? ORDER BY created_at DESC LIMIT ?",
                'icon' => 'fa-user-plus'
            ],
            [
                'table' => 'invoices', 
                'query' => "SELECT 'invoice' as type, 'Invoice Created' as title, CONCAT('Invoice #', invoice_number, ' for ₹', FORMAT(total_amount, 0)) as description, created_at FROM invoices WHERE company_id = ? ORDE<PERSON> BY created_at DESC LIMIT ?",
                'icon' => 'fa-file-invoice'
            ],
            [
                'table' => 'quotations',
                'query' => "SELECT 'quotation' as type, 'Quotation Created' as title, CONCAT('Quotation #', quotation_number, ' for ₹', FORMAT(total_amount, 0)) as description, created_at FROM quotations WHERE company_id = ? ORDER BY created_at DESC LIMIT ?", 
                'icon' => 'fa-quote-right'
            ],
            [
                'table' => 'leads',
                'query' => "SELECT 'lead' as type, 'Lead Added' as title, CONCAT('New lead: ', name) as description, created_at FROM leads WHERE company_id = ? ORDER BY created_at DESC LIMIT ?",
                'icon' => 'fa-chart-line'
            ]
        ];
        
        foreach ($queries as $queryData) {
            try {
                $stmt = $conn->prepare($queryData['query']);
                $stmt->bind_param("si", $companyId, $limit);
                $stmt->execute();
                $result = $stmt->get_result();
                
                while ($row = $result->fetch_assoc()) {
                    $row['icon'] = $queryData['icon'];
                    $activities[] = $row;
                }
            } catch (Exception $e) {
                // Skip this query if table doesn't exist
                error_log("Recent activity query error for {$queryData['table']}: " . $e->getMessage());
            }
        }
        
        // Sort all activities by created_at
        usort($activities, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        // Limit to requested number
        $activities = array_slice($activities, 0, $limit);
        
        echo json_encode([
            'success' => true,
            'data' => $activities
        ]);
        
    } catch (Exception $e) {
        error_log("Recent Activity Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Failed to get recent activity',
            'data' => []
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

$conn->close();
?>