// Pricing Toggle Component for Monthly/Yearly Billing
function PricingToggle({ billingCycle, onCycleChange, plans = [] }) {
    // Calculate average savings for yearly plans
    const calculateAverageSavings = () => {
        if (!plans || plans.length === 0) return 0;
        
        const totalSavings = plans.reduce((sum, plan) => {
            if (plan.price_monthly && plan.price_yearly) {
                const monthlyCost = plan.price_monthly * 12;
                const yearlyCost = plan.price_yearly;
                const savings = monthlyCost - yearlyCost;
                return sum + (savings > 0 ? savings : 0);
            }
            return sum;
        }, 0);
        
        return Math.round(totalSavings / plans.length);
    };

    const averageSavings = calculateAverageSavings();

    return (
        <div className="flex justify-center mb-8">
            <div className="bg-gray-100 p-1 rounded-lg">
                <div className="relative flex">
                    {/* Monthly Option */}
                    <button
                        onClick={() => onCycleChange('monthly')}
                        className={`relative px-6 py-3 text-sm font-medium rounded-md transition-all duration-200 ${
                            billingCycle === 'monthly'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                        }`}
                    >
                        Monthly
                    </button>

                    {/* Yearly Option */}
                    <button
                        onClick={() => onCycleChange('yearly')}
                        className={`relative px-6 py-3 text-sm font-medium rounded-md transition-all duration-200 ${
                            billingCycle === 'yearly'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                        }`}
                    >
                        <span>Yearly</span>
                        {averageSavings > 0 && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Save ₹{averageSavings}
                            </span>
                        )}
                    </button>
                </div>
            </div>
        </div>
    );
}

// Enhanced Pricing Toggle with Animation
function EnhancedPricingToggle({ billingCycle, onCycleChange, plans = [] }) {
    const calculateMaxSavings = () => {
        if (!plans || plans.length === 0) return 0;
        
        return plans.reduce((maxSavings, plan) => {
            if (plan.price_monthly && plan.price_yearly) {
                const monthlyCost = plan.price_monthly * 12;
                const yearlyCost = plan.price_yearly;
                const savings = monthlyCost - yearlyCost;
                return Math.max(maxSavings, savings > 0 ? savings : 0);
            }
            return maxSavings;
        }, 0);
    };

    const maxSavings = calculateMaxSavings();

    return (
        <div className="flex flex-col items-center mb-8">
            {/* Savings Banner */}
            {billingCycle === 'yearly' && maxSavings > 0 && (
                <div className="mb-4 px-4 py-2 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-green-800 text-sm font-medium text-center">
                        🎉 Save up to ₹{maxSavings} per year with yearly billing!
                    </p>
                </div>
            )}

            {/* Toggle Switch */}
            <div className="relative bg-gray-100 p-1 rounded-xl shadow-inner">
                <div className="relative flex">
                    {/* Background Slider */}
                    <div
                        className={`absolute top-1 bottom-1 bg-white rounded-lg shadow-md transition-transform duration-300 ease-in-out ${
                            billingCycle === 'yearly' ? 'translate-x-full' : 'translate-x-0'
                        }`}
                        style={{ width: '50%' }}
                    />

                    {/* Monthly Button */}
                    <button
                        onClick={() => onCycleChange('monthly')}
                        className={`relative z-10 px-8 py-3 text-sm font-semibold rounded-lg transition-colors duration-200 ${
                            billingCycle === 'monthly'
                                ? 'text-gray-900'
                                : 'text-gray-600 hover:text-gray-800'
                        }`}
                    >
                        Monthly
                    </button>

                    {/* Yearly Button */}
                    <button
                        onClick={() => onCycleChange('yearly')}
                        className={`relative z-10 px-8 py-3 text-sm font-semibold rounded-lg transition-colors duration-200 flex items-center ${
                            billingCycle === 'yearly'
                                ? 'text-gray-900'
                                : 'text-gray-600 hover:text-gray-800'
                        }`}
                    >
                        <span>Yearly</span>
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Save 20%
                        </span>
                    </button>
                </div>
            </div>

            {/* Additional Info */}
            <p className="mt-2 text-xs text-gray-500 text-center">
                {billingCycle === 'monthly' 
                    ? 'Billed monthly • Cancel anytime' 
                    : 'Billed annually • 2 months free'
                }
            </p>
        </div>
    );
}

// Simple Toggle for Basic Use Cases
function SimplePricingToggle({ billingCycle, onCycleChange }) {
    return (
        <div className="flex justify-center mb-6">
            <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                    onClick={() => onCycleChange('monthly')}
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                        billingCycle === 'monthly'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-800'
                    }`}
                >
                    Monthly
                </button>
                <button
                    onClick={() => onCycleChange('yearly')}
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                        billingCycle === 'yearly'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-800'
                    }`}
                >
                    Yearly
                </button>
            </div>
        </div>
    );
}

// Make components globally available
window.PricingToggle = PricingToggle;
window.EnhancedPricingToggle = EnhancedPricingToggle;
window.SimplePricingToggle = SimplePricingToggle;
