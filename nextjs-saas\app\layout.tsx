import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Business SaaS - Complete Business Management Solution',
    template: '%s | Business SaaS'
  },
  description: 'Complete SaaS solution for business management including CRM, invoicing, quotations, and more.',
  keywords: ['SaaS', 'Business Management', 'CRM', 'Invoicing', 'Quotations'],
  authors: [{ name: 'Business SaaS Team' }],
  creator: 'Business SaaS',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL,
    title: 'Business SaaS - Complete Business Management Solution',
    description: 'Complete SaaS solution for business management including CRM, invoicing, quotations, and more.',
    siteName: 'Business SaaS',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Business SaaS - Complete Business Management Solution',
    description: 'Complete SaaS solution for business management including CRM, invoicing, quotations, and more.',
    creator: '@businesssaas',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
