// Plan Management Tests
document.addEventListener('DOMContentLoaded', function() {
    if (typeof TestRunner !== 'undefined') {
        const testRunner = window.testRunner || new TestRunner();
        window.testRunner = testRunner;

        const planTests = [
            {
                name: 'Pricing plans API should be accessible',
                test: async function() {
                    const response = await testRunner.makeApiCall('/pricing-plans.php');
                    testRunner.expect(response.ok).toBeTruthy();
                }
            },
            {
                name: 'Pricing plans should return valid data structure',
                test: async function() {
                    const response = await testRunner.makeApiCall('/pricing-plans.php');
                    testRunner.expect(response.ok).toBeTruthy();
                    testRunner.expect(response.data.success).toBeTruthy();
                    testRunner.expect(response.data.data).toBeInstanceOf(Array);
                }
            },
            {
                name: 'Super admin plan management should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/super-admin/pricing-plans');
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Plan creation should require super admin access',
                test: async function() {
                    const response = await testRunner.makeApiCall('/super-admin/pricing-plans', {
                        method: 'POST',
                        body: JSON.stringify({
                            name: 'Test Plan',
                            price_monthly: 100,
                            price_yearly: 1000
                        })
                    });
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Enhanced pricing plans component should exist',
                test: async function() {
                    testRunner.expect(typeof window.EnhancedPricingPlans).toBe('function');
                }
            },
            {
                name: 'Pricing calculator component should exist',
                test: async function() {
                    testRunner.expect(typeof window.PricingCalculator).toBe('function');
                }
            },
            {
                name: 'Pricing toggle component should exist',
                test: async function() {
                    testRunner.expect(typeof window.PricingToggle).toBe('function');
                }
            },
            {
                name: 'Plan modal component should exist',
                test: async function() {
                    testRunner.expect(typeof window.PlanModal).toBe('function');
                }
            },
            {
                name: 'Pricing calculations should be accurate',
                test: async function() {
                    const monthlyPrice = 500;
                    const yearlyPrice = 5000;
                    const monthlyCostPerYear = monthlyPrice * 12;
                    const savings = monthlyCostPerYear - yearlyPrice;
                    const savingsPercentage = Math.round((savings / monthlyCostPerYear) * 100);
                    
                    testRunner.expect(monthlyCostPerYear).toBe(6000);
                    testRunner.expect(savings).toBe(1000);
                    testRunner.expect(savingsPercentage).toBe(17);
                }
            },
            {
                name: 'Plan features should be properly formatted',
                test: async function() {
                    const mockPlan = {
                        features: [
                            'Customer Management',
                            'Invoice Generation',
                            'Quotation Management'
                        ]
                    };
                    
                    testRunner.expect(mockPlan.features).toBeInstanceOf(Array);
                    testRunner.expect(mockPlan.features.length).toBeGreaterThan(0);
                    mockPlan.features.forEach(feature => {
                        testRunner.expect(typeof feature).toBe('string');
                        testRunner.expect(feature.length).toBeGreaterThan(0);
                    });
                }
            },
            {
                name: 'Plan limits should be properly structured',
                test: async function() {
                    const mockLimits = {
                        max_customers: 100,
                        max_invoices: 500,
                        max_users: 10,
                        max_storage_mb: 1000
                    };
                    
                    Object.values(mockLimits).forEach(limit => {
                        testRunner.expect(typeof limit).toBe('number');
                        testRunner.expect(limit).toBeGreaterThan(0);
                    });
                }
            },
            {
                name: 'Business type filtering should work correctly',
                test: async function() {
                    const mockPlan = {
                        business_types: ['retail', 'jewellery', 'education']
                    };
                    
                    const userBusinessType = 'retail';
                    const isApplicable = mockPlan.business_types.includes(userBusinessType) || 
                                        mockPlan.business_types.length === 0;
                    
                    testRunner.expect(isApplicable).toBeTruthy();
                }
            },
            {
                name: 'Plan visibility should be respected',
                test: async function() {
                    const mockPlans = [
                        { id: 'basic', is_visible: true },
                        { id: 'hidden', is_visible: false },
                        { id: 'premium', is_visible: true }
                    ];
                    
                    const visiblePlans = mockPlans.filter(plan => plan.is_visible);
                    testRunner.expect(visiblePlans.length).toBe(2);
                    testRunner.expect(visiblePlans.map(p => p.id)).toEqual(['basic', 'premium']);
                }
            },
            {
                name: 'Plan sorting should work correctly',
                test: async function() {
                    const mockPlans = [
                        { id: 'premium', sort_order: 2 },
                        { id: 'basic', sort_order: 1 },
                        { id: 'enterprise', sort_order: 3 }
                    ];
                    
                    const sortedPlans = mockPlans.sort((a, b) => a.sort_order - b.sort_order);
                    const expectedOrder = ['basic', 'premium', 'enterprise'];
                    
                    testRunner.expect(sortedPlans.map(p => p.id)).toEqual(expectedOrder);
                }
            },
            {
                name: 'Trial availability should be configurable',
                test: async function() {
                    const mockPlan = {
                        is_trial_available: true,
                        trial_days: 14
                    };
                    
                    testRunner.expect(mockPlan.is_trial_available).toBeTruthy();
                    testRunner.expect(mockPlan.trial_days).toBeGreaterThan(0);
                }
            }
        ];

        testRunner.addTestSuite('Plan Management', planTests);
    }
});
