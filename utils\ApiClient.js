/**
 * API Client
 * A utility for making API requests with consistent error handling and caching
 */

window.ApiClient = (function() {
    // Default options
    const DEFAULT_OPTIONS = {
        useCache: true,
        cacheTTL: 300, // 5 minutes in seconds
        retries: 1,
        retryDelay: 1000, // 1 second
        timeout: 30000, // 30 seconds
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    /**
     * Make an API request
     * @param {string} url - API endpoint URL
     * @param {Object} options - Request options
     * @param {string} [options.method='GET'] - HTTP method
     * @param {Object} [options.body] - Request body (for POST, PUT, PATCH)
     * @param {Object} [options.headers] - Request headers
     * @param {boolean} [options.useCache=true] - Whether to use cache for GET requests
     * @param {number} [options.cacheTTL=300] - Cache TTL in seconds
     * @param {number} [options.retries=1] - Number of retries on failure
     * @param {number} [options.retryDelay=1000] - Delay between retries in milliseconds
     * @param {number} [options.timeout=30000] - Request timeout in milliseconds
     * @returns {Promise<Object>} - API response
     */
    async function request(url, options = {}) {
        // Merge default options with provided options
        const opts = { ...DEFAULT_OPTIONS, ...options };
        
        // Get authentication token if available
        const authToken = localStorage.getItem('authToken');
        if (authToken && !opts.headers.Authorization) {
            opts.headers.Authorization = `Bearer ${authToken}`;
        }
        
        // For GET requests, check cache if enabled
        const isGet = !opts.method || opts.method.toUpperCase() === 'GET';
        if (isGet && opts.useCache && window.dataCache) {
            const cacheKey = `api_${url}`;
            const cachedData = window.dataCache.get(cacheKey);
            if (cachedData) {
                console.log(`Using cached data for ${url}`);
                return cachedData;
            }
        }
        
        // Helper function to make the actual fetch request
        const fetchWithTimeout = async (url, options, timeout) => {
            const controller = new AbortController();
            const { signal } = controller;
            
            const timeoutId = setTimeout(() => controller.abort(), timeout);
            
            try {
                const response = await fetch(url, { ...options, signal });
                clearTimeout(timeoutId);
                return response;
            } catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        };
        
        // Retry logic
        let lastError;
        for (let attempt = 0; attempt <= opts.retries; attempt++) {
            try {
                // If not the first attempt, wait before retrying
                if (attempt > 0) {
                    await new Promise(resolve => setTimeout(resolve, opts.retryDelay));
                    console.log(`Retrying request to ${url} (attempt ${attempt}/${opts.retries})`);
                }
                
                // Make the request
                const fetchOptions = {
                    method: opts.method || 'GET',
                    headers: opts.headers,
                    credentials: 'same-origin'
                };
                
                // Add body for non-GET requests
                if (!isGet && opts.body) {
                    fetchOptions.body = typeof opts.body === 'string' 
                        ? opts.body 
                        : JSON.stringify(opts.body);
                }
                
                // Make the request with timeout
                const response = await fetchWithTimeout(url, fetchOptions, opts.timeout);
                
                // Handle HTTP errors
                if (!response.ok) {
                    // Try to parse error response as JSON
                    const errorData = await response.json().catch(() => ({}));
                    
                    // Create a more detailed error object
                    const error = new Error(errorData.message || `HTTP error ${response.status}: ${response.statusText}`);
                    error.status = response.status;
                    error.statusText = response.statusText;
                    error.data = errorData;
                    
                    // If we have a notification manager, show the error
                    if (window.NotificationManager && response.status !== 401) { // Don't show auth errors
                        window.NotificationManager.error(`API Error: ${error.message}`);
                    }
                    
                    throw error;
                }
                
                // Parse response
                const data = await response.json();
                
                // Cache successful GET responses
                if (isGet && opts.useCache && window.dataCache) {
                    const cacheKey = `api_${url}`;
                    window.dataCache.set(cacheKey, data, opts.cacheTTL);
                }
                
                return data;
            } catch (error) {
                lastError = error;
                
                // Don't retry if it's an abort error (timeout)
                if (error.name === 'AbortError') {
                    throw new Error(`Request timeout after ${opts.timeout}ms`);
                }
                
                // Don't retry on the last attempt
                if (attempt === opts.retries) {
                    throw error;
                }
            }
        }
        
        // This should never be reached, but just in case
        throw lastError || new Error('Request failed');
    }
    
    /**
     * Make a GET request
     * @param {string} url - API endpoint URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>} - API response
     */
    function get(url, options = {}) {
        return request(url, { ...options, method: 'GET' });
    }
    
    /**
     * Make a POST request
     * @param {string} url - API endpoint URL
     * @param {Object} body - Request body
     * @param {Object} options - Request options
     * @returns {Promise<Object>} - API response
     */
    function post(url, body, options = {}) {
        return request(url, { ...options, method: 'POST', body, useCache: false });
    }
    
    /**
     * Make a PUT request
     * @param {string} url - API endpoint URL
     * @param {Object} body - Request body
     * @param {Object} options - Request options
     * @returns {Promise<Object>} - API response
     */
    function put(url, body, options = {}) {
        return request(url, { ...options, method: 'PUT', body, useCache: false });
    }
    
    /**
     * Make a PATCH request
     * @param {string} url - API endpoint URL
     * @param {Object} body - Request body
     * @param {Object} options - Request options
     * @returns {Promise<Object>} - API response
     */
    function patch(url, body, options = {}) {
        return request(url, { ...options, method: 'PATCH', body, useCache: false });
    }
    
    /**
     * Make a DELETE request
     * @param {string} url - API endpoint URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>} - API response
     */
    function del(url, options = {}) {
        return request(url, { ...options, method: 'DELETE', useCache: false });
    }
    
    /**
     * Clear API cache for a specific URL or all URLs
     * @param {string} [url] - Specific URL to clear from cache (optional)
     */
    function clearCache(url) {
        if (!window.dataCache) return;
        
        if (url) {
            const cacheKey = `api_${url}`;
            window.dataCache.remove(cacheKey);
        } else {
            // Clear all API cache entries
            const cache = window.dataCache.cache;
            Object.keys(cache).forEach(key => {
                if (key.startsWith('api_')) {
                    window.dataCache.remove(key);
                }
            });
        }
    }
    
    // Public API
    return {
        request,
        get,
        post,
        put,
        patch,
        delete: del, // 'delete' is a reserved word
        clearCache
    };
})();