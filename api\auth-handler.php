<?php
// Clean auth handler that ensures proper JSON output
error_reporting(0); // Suppress PHP warnings/notices
ini_set('display_errors', 0);

// Start output buffering to catch any unwanted output
ob_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/middleware/security.php';
require_once __DIR__ . '/db-config.php';

// Clear any output that might have been generated
ob_clean();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'login':
            handleLogin($input);
            break;
            
        case 'logout':
            handleLogout($input);
            break;
            
        case 'verify':
            handleVerifyToken($input);
            break;
            
        case 'refresh':
            handleRefreshToken($input);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Auth handler error: " . $e->getMessage());
}

function handleLogin($input) {
    // Validate required fields
    if (empty($input['email']) || empty($input['password'])) {
        throw new Exception('Email and password are required');
    }
    
    $email = SecurityMiddleware::sanitizeInput($input['email']);
    $password = $input['password'];
    $remember_me = $input['remember_me'] ?? false;
    
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed');
    }
    
    // Get user with company information
    $stmt = $conn->prepare("
        SELECT u.*, c.name as company_name
        FROM users u
        LEFT JOIN companies c ON u.company_id = c.id
        WHERE u.email = ? AND u.status = 'active'
    ");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('Invalid email or password');
    }

    $user = $result->fetch_assoc();

    // Verify password
    if (!password_verify($password, $user['password_hash'])) {
        throw new Exception('Invalid email or password');
    }
    
    // Generate JWT token
    $token_payload = [
        'user_id' => $user['id'],
        'email' => $user['email'],
        'company_id' => $user['company_id'],
        'role' => $user['role'],
        'exp' => time() + ($remember_me ? 30 * 24 * 3600 : 24 * 3600) // 30 days or 1 day
    ];
    
    $token = SecurityMiddleware::generateJWT($token_payload);
    
    // Update last login
    $update_stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $update_stmt->bind_param("i", $user['id']);
    $update_stmt->execute();
    
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'token' => $token,
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'company_id' => $user['company_id'],
            'company_name' => $user['company_name'],
            'role' => $user['role'],
            'email_verified' => (bool)$user['email_verified']
        ]
    ]);
}

function handleLogout($input) {
    // For JWT tokens, logout is handled client-side by removing the token
    // We could implement a token blacklist here if needed
    
    echo json_encode([
        'success' => true,
        'message' => 'Logout successful'
    ]);
}

function handleVerifyToken($input) {
    $auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    
    if (!$auth_header || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        throw new Exception('No token provided');
    }
    
    $token = $matches[1];
    
    try {
        $payload = SecurityMiddleware::verifyJWT($token);
        
        // Connect to database to get fresh user data
        $db = Config::getDatabase();
        $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
        
        if ($conn->connect_error) {
            throw new Exception('Database connection failed');
        }
        
        $stmt = $conn->prepare("
            SELECT u.*, c.name as company_name, c.subscription_plan 
            FROM users u 
            LEFT JOIN companies c ON u.company_id = c.id 
            WHERE u.id = ? AND u.active = 1
        ");
        $stmt->bind_param("i", $payload['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception('User not found or inactive');
        }
        
        $user = $result->fetch_assoc();
        $conn->close();
        
        echo json_encode([
            'success' => true,
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'company_id' => $user['company_id'],
                'company_name' => $user['company_name'],
                'role' => $user['role'],
                'subscription_plan' => $user['subscription_plan'],
                'email_verified' => (bool)$user['email_verified']
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Invalid or expired token');
    }
}

function handleRefreshToken($input) {
    $auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    
    if (!$auth_header || !preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        throw new Exception('No token provided');
    }
    
    $token = $matches[1];
    
    try {
        $payload = SecurityMiddleware::verifyJWT($token);

        // Generate new token with extended expiry
        $new_payload = [
            'user_id' => $payload['user_id'],
            'email' => $payload['email'],
            'company_id' => $payload['company_id'],
            'role' => $payload['role'],
            'exp' => time() + 24 * 3600 // 1 day
        ];

        $new_token = SecurityMiddleware::generateJWT($new_payload);
        
        echo json_encode([
            'success' => true,
            'token' => $new_token
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Invalid or expired token');
    }
}

// End output buffering and send clean response
ob_end_flush();
?>
