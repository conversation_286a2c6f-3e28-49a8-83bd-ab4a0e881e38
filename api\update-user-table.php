<?php
// Include database configuration
require_once 'db-config.php';

// Check if the role column needs to be updated
$sql = "SHOW COLUMNS FROM users LIKE 'role'";
$result = $conn->query($sql);
$column = $result->fetch_assoc();

echo "Current role column definition: " . $column['Type'] . "\n";

// Check if super_admin is already in the enum
if (strpos($column['Type'], 'super_admin') === false) {
    // Update the role column to include super_admin
    $sql = "ALTER TABLE users MODIFY COLUMN role ENUM('super_admin','admin','user','manager','viewer') DEFAULT 'user'";
    
    if ($conn->query($sql) === TRUE) {
        echo "Role column updated successfully to include super_admin\n";
    } else {
        echo "Error updating role column: " . $conn->error . "\n";
    }
} else {
    echo "Role column already includes super_admin\n";
}

// Close the connection
$conn->close();
?>