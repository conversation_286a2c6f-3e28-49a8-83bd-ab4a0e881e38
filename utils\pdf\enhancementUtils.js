function addWatermark(element, text, options = {}) {
    try {
        const defaultOptions = {
            color: '#1e3a8a',
            opacity: 0.05,
            fontSize: '5rem',
            fontWeight: 900,
            rotation: -45,
            zIndex: 10
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        const watermark = document.createElement('div');
        watermark.className = 'watermark';
        watermark.textContent = text;
        watermark.style.position = 'absolute';
        watermark.style.top = '50%';
        watermark.style.left = '50%';
        watermark.style.transform = `translate(-50%, -50%) rotate(${mergedOptions.rotation}deg)`;
        watermark.style.fontSize = mergedOptions.fontSize;
        watermark.style.opacity = mergedOptions.opacity;
        watermark.style.fontWeight = mergedOptions.fontWeight;
        watermark.style.color = mergedOptions.color;
        watermark.style.whiteSpace = 'nowrap';
        watermark.style.pointerEvents = 'none';
        watermark.style.textTransform = 'uppercase';
        watermark.style.zIndex = mergedOptions.zIndex;
        
        if (getComputedStyle(element).position === 'static') {
            element.style.position = 'relative';
        }
        
        element.appendChild(watermark);
        return watermark;
    } catch (error) {
        console.error('Error adding watermark:', error);
        return null;
    }
}

function addPageNumbers(pdfDoc, startPage = 1, options = {}) {
    try {
        const defaultOptions = {
            fontSize: 10,
            color: '#666666',
            format: 'Page {page} of {total}',
            position: 'bottom-center',
            marginX: 10,
            marginY: 10
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        const pageCount = pdfDoc.internal.getNumberOfPages();
        
        for (let i = startPage; i <= pageCount; i++) {
            pdfDoc.setPage(i);
            
            const pageText = mergedOptions.format
                .replace('{page}', i)
                .replace('{total}', pageCount);
            
            const pageWidth = pdfDoc.internal.pageSize.getWidth();
            const pageHeight = pdfDoc.internal.pageSize.getHeight();
            
            pdfDoc.setFontSize(mergedOptions.fontSize);
            pdfDoc.setTextColor(mergedOptions.color);
            
            let x = mergedOptions.marginX;
            let y = pageHeight - mergedOptions.marginY;
            
            if (mergedOptions.position === 'bottom-center') {
                const textWidth = pdfDoc.getStringUnitWidth(pageText) * mergedOptions.fontSize / pdfDoc.internal.scaleFactor;
                x = (pageWidth - textWidth) / 2;
            } else if (mergedOptions.position === 'bottom-right') {
                const textWidth = pdfDoc.getStringUnitWidth(pageText) * mergedOptions.fontSize / pdfDoc.internal.scaleFactor;
                x = pageWidth - textWidth - mergedOptions.marginX;
            }
            
            pdfDoc.text(pageText, x, y);
        }
        
        return true;
    } catch (error) {
        console.error('Error adding page numbers:', error);
        return false;
    }
}

function generateQRCode(data, size = 100) {
    try {
        if (typeof QRCode === 'undefined') {
            console.error('QRCode library not loaded');
            return null;
        }
        
        const container = document.createElement('div');
        
        new QRCode(container, {
            text: data,
            width: size,
            height: size,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });
        
        const qrImage = container.querySelector('img');
        if (!qrImage) {
            console.error('Failed to generate QR code');
            return null;
        }
        
        return qrImage.src;
    } catch (error) {
        console.error('Error generating QR code:', error);
        return null;
    }
}
