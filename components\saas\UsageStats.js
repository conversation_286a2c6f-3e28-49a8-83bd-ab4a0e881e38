function UsageStats({ plan, currentUsage }) {
    try {
        const getUsagePercentage = (used, limit) => {
            if (limit === Infinity) return (used / 1000) * 100; // Show relative usage for unlimited plans
            return (used / limit) * 100;
        };

        const formatLimit = (limit) => {
            if (limit === Infinity) return 'Unlimited';
            return limit.toLocaleString();
        };

        return (
            <div data-name="usage-stats" className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Usage Statistics</h2>

                <div className="space-y-6">
                    <div>
                        <div className="flex justify-between mb-2">
                            <span className="text-sm font-medium text-gray-500">Leads</span>
                            <span className="text-sm font-medium text-gray-900">
                                {currentUsage.leads.toLocaleString()} / {formatLimit(plan.limits.leads)}
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${Math.min(getUsagePercentage(currentUsage.leads, plan.limits.leads), 100)}%` }}
                            ></div>
                        </div>
                    </div>

                    <div>
                        <div className="flex justify-between mb-2">
                            <span className="text-sm font-medium text-gray-500">Team Members</span>
                            <span className="text-sm font-medium text-gray-900">
                                {currentUsage.teamMembers} / {formatLimit(plan.limits.teamMembers)}
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${Math.min(getUsagePercentage(currentUsage.teamMembers, plan.limits.teamMembers), 100)}%` }}
                            ></div>
                        </div>
                    </div>

                    <div>
                        <div className="flex justify-between mb-2">
                            <span className="text-sm font-medium text-gray-500">Storage</span>
                            <span className="text-sm font-medium text-gray-900">
                                {(currentUsage.storage / 1024).toFixed(2)} GB / {formatLimit(plan.limits.storage / 1024)} GB
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-purple-600 h-2 rounded-full"
                                style={{ width: `${Math.min(getUsagePercentage(currentUsage.storage, plan.limits.storage), 100)}%` }}
                            ></div>
                        </div>
                    </div>

                    {currentUsage.apiCalls !== undefined && (
                        <div>
                            <div className="flex justify-between mb-2">
                                <span className="text-sm font-medium text-gray-500">API Calls</span>
                                <span className="text-sm font-medium text-gray-900">
                                    {currentUsage.apiCalls.toLocaleString()} / {formatLimit(plan.limits.apiCalls)}
                                </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-yellow-600 h-2 rounded-full"
                                    style={{ width: `${Math.min(getUsagePercentage(currentUsage.apiCalls, plan.limits.apiCalls), 100)}%` }}
                                ></div>
                            </div>
                        </div>
                    )}
                </div>

                {Object.entries(currentUsage).some(([key, value]) => {
                    const limit = plan.limits[key];
                    return limit !== Infinity && value >= limit * 0.9;
                }) && (
                    <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div className="flex">
                            <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-yellow-800">
                                    Approaching Usage Limits
                                </h3>
                                <div className="mt-2 text-sm text-yellow-700">
                                    <p>
                                        You're approaching the limits of your current plan. Consider upgrading to avoid any service interruptions.
                                    </p>
                                </div>
                                <div className="mt-4">
                                    <div className="-mx-2 -my-1.5 flex">
                                        <button
                                            type="button"
                                            onClick={() => window.location.href = '/subscriptions'}
                                            className="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                                        >
                                            View Plans
                                            <i className="fas fa-arrow-right ml-2"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('UsageStats component error:', error);
        reportError(error);
        return null;
    }
}
