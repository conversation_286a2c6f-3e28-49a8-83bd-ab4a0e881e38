function PaymentSuccess() {
    const [paymentStatus, setPaymentStatus] = React.useState('verifying');
    const [paymentDetails, setPaymentDetails] = React.useState(null);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
        verifyPayment();
    }, []);

    const verifyPayment = async () => {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const transactionId = urlParams.get('transaction_id');
            const type = urlParams.get('type');
            const id = urlParams.get('id');

            if (!transactionId) {
                setError('No transaction ID found');
                setPaymentStatus('error');
                return;
            }

            // Verify payment with PhonePe
            const response = await fetch(`/api/payment/payment-api.php/verify?transaction_id=${transactionId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            const result = await response.json();

            if (result.success && result.data && result.data.state === 'COMPLETED') {
                setPaymentStatus('success');
                setPaymentDetails({
                    transactionId,
                    amount: result.data.amount / 100, // Convert from paise
                    type,
                    id
                });

                // Redirect to appropriate page after 3 seconds
                setTimeout(() => {
                    if (type === 'subscription') {
                        window.location.href = '/subscriptions';
                    } else if (type === 'invoice') {
                        window.location.href = '/invoices';
                    } else {
                        window.location.href = '/dashboard';
                    }
                }, 3000);
            } else {
                setPaymentStatus('failed');
                setError(result.message || 'Payment verification failed');
            }
        } catch (error) {
            console.error('Error verifying payment:', error);
            setPaymentStatus('error');
            setError('Failed to verify payment status');
        }
    };

    const renderContent = () => {
        switch (paymentStatus) {
            case 'verifying':
                return (
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">Verifying Payment</h2>
                        <p className="text-gray-600">Please wait while we confirm your payment...</p>
                    </div>
                );

            case 'success':
                return (
                    <div className="text-center">
                        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                            <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h2>
                        <p className="text-gray-600 mb-4">Your payment has been processed successfully.</p>
                        
                        {paymentDetails && (
                            <div className="bg-gray-50 rounded-lg p-4 mb-4">
                                <div className="text-sm text-gray-600">
                                    <p><strong>Transaction ID:</strong> {paymentDetails.transactionId}</p>
                                    <p><strong>Amount:</strong> ₹{paymentDetails.amount}</p>
                                    <p><strong>Type:</strong> {paymentDetails.type}</p>
                                </div>
                            </div>
                        )}
                        
                        <p className="text-sm text-gray-500">Redirecting you back to the application...</p>
                    </div>
                );

            case 'failed':
                return (
                    <div className="text-center">
                        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                            <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Failed</h2>
                        <p className="text-gray-600 mb-4">Unfortunately, your payment could not be processed.</p>
                        <p className="text-sm text-gray-500 mb-4">{error}</p>
                        
                        <div className="space-x-4">
                            <button
                                onClick={() => window.location.href = '/subscriptions'}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                            >
                                Try Again
                            </button>
                            <button
                                onClick={() => window.location.href = '/dashboard'}
                                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                            >
                                Go to Dashboard
                            </button>
                        </div>
                    </div>
                );

            case 'error':
                return (
                    <div className="text-center">
                        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                            <svg className="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">Verification Error</h2>
                        <p className="text-gray-600 mb-4">We encountered an error while verifying your payment.</p>
                        <p className="text-sm text-gray-500 mb-4">{error}</p>
                        
                        <div className="space-x-4">
                            <button
                                onClick={verifyPayment}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                            >
                                Retry Verification
                            </button>
                            <button
                                onClick={() => window.location.href = '/dashboard'}
                                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                            >
                                Go to Dashboard
                            </button>
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    {renderContent()}
                </div>
            </div>
        </div>
    );
}

// Export for use in other files
window.PaymentSuccess = PaymentSuccess;
