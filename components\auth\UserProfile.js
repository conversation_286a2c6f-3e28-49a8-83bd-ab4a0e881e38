function UserProfile() {
    try {
        const { user, updateUserProfile } = useAuth();
        const [editing, setEditing] = React.useState(false);
        const [formData, setFormData] = React.useState({
            name: user && user.name ? user.name : '',
            email: user && user.email ? user.email : '',
            phone: user && user.phone ? user.phone : '',
            title: user && user.title ? user.title : '',
            department: user && user.department ? user.department : ''
        });
        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.name) {
                newErrors.name = 'Name is required';
            }
            if (!formData.email) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(formData.email)) {
                newErrors.email = 'Invalid email format';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                await updateUserProfile(formData);
                setEditing(false);
            } catch (error) {
                setErrors({ submit: error.message });
            } finally {
                setLoading(false);
            }
        };

        const handleCancel = () => {
            setFormData({
                name: user && user.name ? user.name : '',
                email: user && user.email ? user.email : '',
                phone: user && user.phone ? user.phone : '',
                title: user && user.title ? user.title : '',
                department: user && user.department ? user.department : ''
            });
            setErrors({});
            setEditing(false);
        };

        if (!editing) {
            return (
                <div data-name="user-profile" className="bg-white shadow rounded-lg p-6">
                    <div className="flex justify-between items-start mb-6">
                        <div>
                            <h2 className="text-2xl font-bold">Profile Information</h2>
                            <p className="text-gray-500">Update your personal details</p>
                        </div>
                        <Button
                            variant="secondary"
                            icon="fas fa-edit"
                            onClick={() => setEditing(true)}
                        >
                            Edit Profile
                        </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-500">Name</label>
                            <p className="mt-1 text-lg">{user && user.name ? user.name : ''}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-500">Email</label>
                            <p className="mt-1 text-lg">{user && user.email ? user.email : ''}</p>
                        </div>
                        {user && user.phone && (
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Phone</label>
                                <p className="mt-1 text-lg">{user.phone}</p>
                            </div>
                        )}
                        {user && user.title && (
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Title</label>
                                <p className="mt-1 text-lg">{user.title}</p>
                            </div>
                        )}
                        {user && user.department && (
                            <div>
                                <label className="block text-sm font-medium text-gray-500">Department</label>
                                <p className="mt-1 text-lg">{user.department}</p>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        return (
            <div data-name="user-profile-edit" className="bg-white shadow rounded-lg p-6">
                <div className="mb-6">
                    <h2 className="text-2xl font-bold">Edit Profile</h2>
                    <p className="text-gray-500">Update your personal information</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                            label="Name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            error={errors.name}
                            required
                        />
                        <Input
                            label="Email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            error={errors.email}
                            required
                        />
                        <Input
                            label="Phone"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            error={errors.phone}
                        />
                        <Input
                            label="Title"
                            name="title"
                            value={formData.title}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="Department"
                            name="department"
                            value={formData.department}
                            onChange={handleInputChange}
                        />
                    </div>

                    {errors.submit && (
                        <div className="text-red-600 text-sm">
                            {errors.submit}
                        </div>
                    )}

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="secondary"
                            onClick={handleCancel}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            loading={loading}
                            disabled={loading}
                        >
                            Save Changes
                        </Button>
                    </div>
                </form>
            </div>
        );
    } catch (error) {
        console.error('UserProfile component error:', error);
        reportError(error);
        return null;
    }
}
