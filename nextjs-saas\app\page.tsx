import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Building2, 
  Users, 
  FileText, 
  CreditCard, 
  BarChart3, 
  Shield,
  Zap,
  Globe,
  CheckCircle,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

const features = [
  {
    icon: Users,
    title: 'Customer Management',
    description: 'Complete CRM with lead tracking, customer profiles, and activity management.'
  },
  {
    icon: FileText,
    title: 'Invoicing & Quotations',
    description: 'Professional invoices and quotations with automated calculations and templates.'
  },
  {
    icon: Building2,
    title: 'Multi-Tenant Architecture',
    description: 'Secure data isolation with company-based access control and team management.'
  },
  {
    icon: CreditCard,
    title: 'Subscription Management',
    description: 'Flexible pricing plans with trial management and automated billing.'
  },
  {
    icon: BarChart3,
    title: 'Analytics & Reports',
    description: 'Real-time dashboards with business insights and performance metrics.'
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'Role-based access control with audit logs and data encryption.'
  }
]

const businessTypes = [
  { name: 'Retail Business', color: 'bg-blue-100 text-blue-800' },
  { name: 'Healthcare Services', color: 'bg-red-100 text-red-800' },
  { name: 'Consulting Services', color: 'bg-indigo-100 text-indigo-800' },
  { name: 'Manufacturing', color: 'bg-orange-100 text-orange-800' },
  { name: 'Education Services', color: 'bg-green-100 text-green-800' },
  { name: 'Jewellery Business', color: 'bg-purple-100 text-purple-800' }
]

const benefits = [
  'Complete business management in one platform',
  'Industry-specific templates and workflows',
  'Real-time collaboration and team management',
  'Automated invoicing and payment tracking',
  'Advanced analytics and reporting',
  'Mobile-responsive design',
  '24/7 customer support',
  'Regular updates and new features'
]

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Building2 className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">Business SaaS</span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/auth/signin">
              <Button variant="ghost">Sign In</Button>
            </Link>
            <Link href="/auth/signup">
              <Button>Get Started</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Complete Business Management
              <span className="text-blue-600"> Made Simple</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Streamline your business operations with our comprehensive SaaS platform. 
              From CRM to invoicing, manage everything in one place with industry-specific templates.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href="/auth/signup">
                <Button size="lg" className="text-lg px-8 py-3">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="text-lg px-8 py-3">
                Watch Demo
              </Button>
            </div>
            
            {/* Business Types */}
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {businessTypes.map((type) => (
                <Badge key={type.name} className={type.color}>
                  {type.name}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Run Your Business
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our platform provides all the tools you need to manage customers, 
              create invoices, track leads, and grow your business.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => (
              <Card key={feature.title} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                Why Choose Our Platform?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Built specifically for modern businesses, our platform combines 
                powerful features with ease of use to help you succeed.
              </p>
              <div className="grid gap-4">
                {benefits.map((benefit) => (
                  <div key={benefit} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <Zap className="h-8 w-8 text-yellow-500 mb-3" />
                  <h3 className="font-semibold mb-2">Lightning Fast</h3>
                  <p className="text-sm text-gray-600">Optimized for speed and performance</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <Shield className="h-8 w-8 text-green-500 mb-3" />
                  <h3 className="font-semibold mb-2">Secure & Reliable</h3>
                  <p className="text-sm text-gray-600">Enterprise-grade security</p>
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <Globe className="h-8 w-8 text-blue-500 mb-3" />
                  <h3 className="font-semibold mb-2">Global Ready</h3>
                  <p className="text-sm text-gray-600">Multi-currency and localization</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <Users className="h-8 w-8 text-purple-500 mb-3" />
                  <h3 className="font-semibold mb-2">Team Collaboration</h3>
                  <p className="text-sm text-gray-600">Work together seamlessly</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-blue-600">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses already using our platform to streamline 
            their operations and accelerate growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/signup">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                Start Your Free Trial
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 py-3 text-white border-white hover:bg-white hover:text-blue-600">
              Contact Sales
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Building2 className="h-6 w-6" />
                <span className="text-lg font-bold">Business SaaS</span>
              </div>
              <p className="text-gray-400">
                Complete business management solution for modern companies.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white">Features</Link></li>
                <li><Link href="#" className="hover:text-white">Pricing</Link></li>
                <li><Link href="#" className="hover:text-white">Security</Link></li>
                <li><Link href="#" className="hover:text-white">Integrations</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white">About</Link></li>
                <li><Link href="#" className="hover:text-white">Blog</Link></li>
                <li><Link href="#" className="hover:text-white">Careers</Link></li>
                <li><Link href="#" className="hover:text-white">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#" className="hover:text-white">Help Center</Link></li>
                <li><Link href="#" className="hover:text-white">Documentation</Link></li>
                <li><Link href="#" className="hover:text-white">API Reference</Link></li>
                <li><Link href="#" className="hover:text-white">Status</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Business SaaS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
