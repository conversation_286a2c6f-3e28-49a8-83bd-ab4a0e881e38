.lead-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.lead-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.lead-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.lead-details-header {
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

.lead-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.lead-activity-timeline {
    border-left: 2px solid #e5e7eb;
    margin-left: 1rem;
    padding-left: 2rem;
}

.lead-activity-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.lead-activity-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: #3b82f6;
    border: 2px solid white;
}

.lead-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.lead-status.new {
    background-color: #f3f4f6;
    color: #4b5563;
}

.lead-status.contacted {
    background-color: #dbeafe;
    color: #1e40af;
}

.lead-status.qualified {
    background-color: #a7f3d0;
    color: #047857;
}

.lead-status.proposal {
    background-color: #e0e7ff;
    color: #4338ca;
}

.lead-status.negotiation {
    background-color: #fef3c7;
    color: #92400e;
}

.lead-status.won {
    background-color: #d1fae5;
    color: #065f46;
}

.lead-status.lost {
    background-color: #fee2e2;
    color: #991b1b;
}

.lead-priority {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.lead-priority.high {
    background-color: #fee2e2;
    color: #991b1b;
}

.lead-priority.medium {
    background-color: #fef3c7;
    color: #92400e;
}

.lead-priority.low {
    background-color: #d1fae5;
    color: #065f46;
}

.lead-source {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: #f3f4f6;
    color: #4b5563;
}

.lead-source i {
    margin-right: 0.5rem;
}

.tag-input {
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.5rem;
    min-height: 2.5rem;
}

.tag-input:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
}

.tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: #dbeafe;
    color: #1e40af;
    margin: 0.25rem;
}

.tag-remove {
    margin-left: 0.5rem;
    cursor: pointer;
    opacity: 0.5;
}

.tag-remove:hover {
    opacity: 1;
}

.activity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.activity-icon.status {
    background-color: #dbeafe;
    color: #1e40af;
}

.activity-icon.note {
    background-color: #d1fae5;
    color: #065f46;
}

.activity-icon.email {
    background-color: #e0e7ff;
    color: #4338ca;
}

@media (max-width: 768px) {
    .lead-info-grid {
        grid-template-columns: 1fr;
    }

    .lead-activity-timeline {
        margin-left: 0.5rem;
        padding-left: 1.5rem;
    }
}
