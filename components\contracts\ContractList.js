function ContractList({ onContractClick }) {
    try {
        const [contracts, setContracts] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [selectedType, setSelectedType] = React.useState('');
        const [customersMap, setCustomersMap] = React.useState({});

        React.useEffect(() => {
            fetchContracts();
            fetchCustomers();
        }, []);

        const fetchContracts = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/contract'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setContracts(data.items || []);
                } else {
                    throw new Error('Failed to fetch contracts');
                }
            } catch (error) {
                console.error('Error fetching contracts:', error);
                setContracts([]);
            } finally {
                setLoading(false);
            }
        };

        const fetchCustomers = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/customer'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const customerMap = {};
                    (data.items || []).forEach(customer => {
                        customerMap[customer.objectId] = customer.objectData;
                    });
                    setCustomersMap(customerMap);
                } else {
                    throw new Error('Failed to fetch customers');
                }
            } catch (error) {
                console.error('Error fetching customers:', error);
                setCustomersMap({});
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters && filters.status ? filters.status : '');
            setSelectedType(filters && filters.type ? filters.type : '');
        };

        const filteredContracts = React.useMemo(() => {
            return contracts.filter(contract => {
                const customerName = customersMap[contract.objectData.customer] && customersMap[contract.objectData.customer].name ? customersMap[contract.objectData.customer].name : '';
                
                const matchesSearch = !searchQuery || 
                    contract.objectData.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (contract.objectData.contractNumber && contract.objectData.contractNumber.toLowerCase().includes(searchQuery.toLowerCase()));
                
                const matchesStatus = !selectedStatus || contract.objectData.status === selectedStatus;
                const matchesType = !selectedType || contract.objectData.type === selectedType;

                return matchesSearch && matchesStatus && matchesType;
            });
        }, [contracts, searchQuery, selectedStatus, selectedType, customersMap]);

        const contractFilters = [
            { id: 'status', label: 'Status', type: 'select', options: [
                { label: 'Draft', value: 'draft' },
                { label: 'Pending', value: 'pending' },
                { label: 'Signed', value: 'signed' },
                { label: 'Expired', value: 'expired' }
            ]},
            { id: 'type', label: 'Type', type: 'select', options: [
                { label: 'Service Contract', value: 'service' },
                { label: 'Product Contract', value: 'product' },
                { label: 'Licensing Agreement', value: 'licensing' },
                { label: 'Partnership Agreement', value: 'partnership' }
            ]}
        ];

        const columns = [
            { 
                key: 'contractNumber', 
                label: 'Contract #',
                render: (row) => row.objectData.contractNumber || `#${row.objectId.substring(0, 8)}`
            },
            { key: 'title', label: 'Contract Title' },
            { 
                key: 'customer', 
                label: 'Customer',
                render: (row) => customersMap[row.objectData.customer] && customersMap[row.objectData.customer].name ? customersMap[row.objectData.customer].name : 'Unknown Customer'
            },
            { 
                key: 'value', 
                label: 'Value',
                render: (row) => formatCurrency(row.objectData.value)
            },
            {
                key: 'startDate',
                label: 'Start Date',
                render: (row) => formatDate(row.objectData.startDate)
            },
            {
                key: 'endDate',
                label: 'End Date',
                render: (row) => formatDate(row.objectData.endDate)
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`contract-status ${row.objectData.status}`}>
                        {row.objectData.status ? row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1) : 'Unknown'}
                    </span>
                )
            }
        ];

        return (
            <div data-name="contract-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search contracts..."
                        filters={contractFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredContracts}
                    loading={loading}
                    onRowClick={onContractClick}
                    emptyMessage="No contracts found"
                />
            </div>
        );
    } catch (error) {
        console.error('ContractList component error:', error);
        reportError(error);
        return null;
    }
}
