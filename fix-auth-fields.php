<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    $results = [];
    
    // Check if auth_token field exists
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'auth_token'");
    if ($result->num_rows == 0) {
        // Add auth_token field
        $conn->query("ALTER TABLE users ADD COLUMN auth_token VARCHAR(255) NULL");
        $results[] = 'Added auth_token field';
    } else {
        $results[] = 'auth_token field already exists';
    }
    
    // Check if token_expires field exists
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'token_expires'");
    if ($result->num_rows == 0) {
        // Add token_expires field
        $conn->query("ALTER TABLE users ADD COLUMN token_expires DATETIME NULL");
        $results[] = 'Added token_expires field';
    } else {
        $results[] = 'token_expires field already exists';
    }
    
    // Check current table structure
    $result = $conn->query("DESCRIBE users");
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Database fields checked and updated',
        'actions' => $results,
        'current_columns' => $columns
    ]);
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
