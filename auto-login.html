<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }
        .loading { color: #666; }
        .success { color: #2e7d32; }
        .error { color: #c62828; }
    </style>
</head>
<body>
    <h1>Auto Login</h1>
    <div id="status" class="loading">Logging in...</div>

    <script src="config.js"></script>
    <script>
        async function autoLogin() {
            const statusDiv = document.getElementById('status');
            
            try {
                statusDiv.textContent = 'Attempting login...';
                
                const response = await fetch(window.getApiUrl('/auth'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    // Store authentication data
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    statusDiv.textContent = 'Login successful! Redirecting...';
                    statusDiv.className = 'success';
                    
                    // Redirect to policy pages after a short delay
                    setTimeout(() => {
                        window.location.href = '/biz/admin-super-admin-business-types';
                    }, 2000);
                } else {
                    statusDiv.textContent = 'Login failed: ' + (data.message || data.error);
                    statusDiv.className = 'error';
                }
                
            } catch (error) {
                statusDiv.textContent = 'Login error: ' + error.message;
                statusDiv.className = 'error';
            }
        }

        // Check if already logged in
        const token = localStorage.getItem('authToken');
        const user = localStorage.getItem('user');
        
        if (token && user) {
            document.getElementById('status').textContent = 'Already logged in! Redirecting...';
            document.getElementById('status').className = 'success';
            setTimeout(() => {
                window.location.href = '/biz/admin-super-admin-business-types';
            }, 1000);
        } else {
            // Auto login
            autoLogin();
        }
    </script>
</body>
</html>
