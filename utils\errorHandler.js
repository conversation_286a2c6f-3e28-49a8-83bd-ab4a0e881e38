/**
 * Enhanced Error Handling Utility
 * Provides comprehensive error handling, logging, and user feedback
 */

class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxErrors = 100;
        this.setupGlobalHandlers();
    }

    /**
     * Setup global error handlers
     */
    setupGlobalHandlers() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'Unhandled Promise Rejection');
            event.preventDefault();
        });

        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError(event.error, 'JavaScript Error', {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // Handle React errors (if available)
        if (window.React && window.React.version) {
            this.setupReactErrorBoundary();
        }
    }

    /**
     * Handle different types of errors
     */
    handleError(error, type = 'General Error', context = {}) {
        const errorInfo = this.processError(error, type, context);
        
        // Log error
        this.logError(errorInfo);
        
        // Add to queue
        this.addToQueue(errorInfo);
        
        // Show user notification if appropriate
        if (errorInfo.severity === 'high' || errorInfo.showToUser) {
            this.showUserNotification(errorInfo);
        }
        
        return errorInfo;
    }

    /**
     * Process error into standardized format
     */
    processError(error, type, context) {
        const timestamp = new Date().toISOString();
        const errorInfo = {
            id: this.generateErrorId(),
            timestamp,
            type,
            context,
            severity: 'medium',
            showToUser: false
        };

        if (error instanceof Error) {
            errorInfo.name = error.name;
            errorInfo.message = error.message;
            errorInfo.stack = error.stack;
        } else if (typeof error === 'string') {
            errorInfo.message = error;
        } else if (typeof error === 'object' && error !== null) {
            errorInfo.message = error.message || 'Unknown error';
            errorInfo.data = error;
        } else {
            errorInfo.message = String(error);
        }

        // Determine severity
        errorInfo.severity = this.determineSeverity(errorInfo);
        
        // Determine if should show to user
        errorInfo.showToUser = this.shouldShowToUser(errorInfo);

        return errorInfo;
    }

    /**
     * Determine error severity
     */
    determineSeverity(errorInfo) {
        const { type, name, message } = errorInfo;
        
        // High severity errors
        if (type.includes('Security') || 
            name === 'SecurityError' ||
            message.includes('authentication') ||
            message.includes('authorization') ||
            message.includes('CORS')) {
            return 'high';
        }
        
        // Network errors
        if (name === 'NetworkError' || 
            message.includes('fetch') ||
            message.includes('network') ||
            type.includes('API')) {
            return 'high';
        }
        
        // Critical application errors
        if (name === 'TypeError' && message.includes('Cannot read property')) {
            return 'high';
        }
        
        // Medium severity (default)
        return 'medium';
    }

    /**
     * Determine if error should be shown to user
     */
    shouldShowToUser(errorInfo) {
        const { severity, type, message } = errorInfo;
        
        // Always show high severity errors
        if (severity === 'high') {
            return true;
        }
        
        // Show network/API errors
        if (type.includes('API') || type.includes('Network')) {
            return true;
        }
        
        // Show validation errors
        if (message.includes('validation') || message.includes('required')) {
            return true;
        }
        
        return false;
    }

    /**
     * Log error to console and server
     */
    logError(errorInfo) {
        // Console logging
        console.group(`🚨 ${errorInfo.type} [${errorInfo.severity.toUpperCase()}]`);
        console.error('Message:', errorInfo.message);
        console.error('Timestamp:', errorInfo.timestamp);
        if (errorInfo.stack) {
            console.error('Stack:', errorInfo.stack);
        }
        if (Object.keys(errorInfo.context).length > 0) {
            console.error('Context:', errorInfo.context);
        }
        console.groupEnd();

        // Send to server (if in production)
        if (window.APP_CONFIG && !window.APP_CONFIG.DEBUG) {
            this.sendToServer(errorInfo);
        }
    }

    /**
     * Send error to server for logging
     */
    async sendToServer(errorInfo) {
        try {
            const response = await fetch(window.getApiUrl('/errors/log'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    error: errorInfo,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    userId: this.getCurrentUserId()
                })
            });

            if (!response.ok) {
                console.warn('Failed to send error to server:', response.status);
            }
        } catch (err) {
            console.warn('Error sending error to server:', err);
        }
    }

    /**
     * Show user notification
     */
    showUserNotification(errorInfo) {
        const message = this.getUserFriendlyMessage(errorInfo);
        
        if (window.showToast) {
            window.showToast(message, 'error');
        } else if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            // Fallback to alert
            alert(`Error: ${message}`);
        }
    }

    /**
     * Get user-friendly error message
     */
    getUserFriendlyMessage(errorInfo) {
        const { type, message, name } = errorInfo;
        
        // Network errors
        if (type.includes('Network') || name === 'NetworkError') {
            return 'Network connection error. Please check your internet connection and try again.';
        }
        
        // API errors
        if (type.includes('API')) {
            return 'Server error occurred. Please try again later.';
        }
        
        // Authentication errors
        if (message.includes('authentication') || message.includes('unauthorized')) {
            return 'Authentication required. Please log in again.';
        }
        
        // Validation errors
        if (message.includes('validation') || message.includes('required')) {
            return message; // Show validation messages as-is
        }
        
        // Generic error
        return 'An unexpected error occurred. Please try again.';
    }

    /**
     * Add error to queue
     */
    addToQueue(errorInfo) {
        this.errorQueue.unshift(errorInfo);
        
        // Limit queue size
        if (this.errorQueue.length > this.maxErrors) {
            this.errorQueue = this.errorQueue.slice(0, this.maxErrors);
        }
    }

    /**
     * Get recent errors
     */
    getRecentErrors(limit = 10) {
        return this.errorQueue.slice(0, limit);
    }

    /**
     * Clear error queue
     */
    clearErrors() {
        this.errorQueue = [];
    }

    /**
     * Generate unique error ID
     */
    generateErrorId() {
        return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Get current user ID
     */
    getCurrentUserId() {
        try {
            const token = localStorage.getItem('authToken');
            if (token) {
                // Simple token parsing - in production use proper JWT parsing
                return 'user_from_token';
            }
        } catch (err) {
            // Ignore
        }
        return 'anonymous';
    }

    /**
     * Setup React Error Boundary
     */
    setupReactErrorBoundary() {
        // This would be implemented as a React component
        // For now, just log that React is available
        console.log('React error boundary setup available');
    }

    /**
     * Handle API errors specifically
     */
    handleApiError(response, context = {}) {
        const errorInfo = {
            type: 'API Error',
            status: response.status,
            statusText: response.statusText,
            url: response.url,
            context
        };

        if (response.status === 401) {
            errorInfo.message = 'Authentication required';
            errorInfo.action = 'redirect_to_login';
        } else if (response.status === 403) {
            errorInfo.message = 'Access denied';
        } else if (response.status === 404) {
            errorInfo.message = 'Resource not found';
        } else if (response.status >= 500) {
            errorInfo.message = 'Server error';
        } else {
            errorInfo.message = `Request failed with status ${response.status}`;
        }

        return this.handleError(errorInfo, 'API Error', context);
    }

    /**
     * Handle validation errors
     */
    handleValidationError(errors, context = {}) {
        const errorInfo = {
            type: 'Validation Error',
            message: 'Please correct the following errors:',
            errors: errors,
            context
        };

        return this.handleError(errorInfo, 'Validation Error', context);
    }
}

// Create global instance
window.errorHandler = new ErrorHandler();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
}

// Backward compatibility
window.reportError = function(error, context = {}) {
    return window.errorHandler.handleError(error, 'Application Error', context);
};

console.log('Enhanced Error Handler initialized');