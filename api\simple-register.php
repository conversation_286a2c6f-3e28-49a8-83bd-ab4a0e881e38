<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit;
    }
    
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $required_fields = ['name', 'email', 'password', 'company_name'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Field '{$field}' is required");
        }
    }
    
    // Sanitize inputs
    $name = htmlspecialchars(trim($input['name']), ENT_QUOTES, 'UTF-8');
    $email = filter_var(trim($input['email']), FILTER_SANITIZE_EMAIL);
    $password = $input['password'];
    $company_name = htmlspecialchars(trim($input['company_name']), ENT_QUOTES, 'UTF-8');
    $company_size = htmlspecialchars(trim($input['company_size'] ?? ''), ENT_QUOTES, 'UTF-8');
    $industry = htmlspecialchars(trim($input['industry'] ?? ''), ENT_QUOTES, 'UTF-8');
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // Validate password strength
    if (strlen($password) < 8) {
        throw new Exception('Password must be at least 8 characters long');
    }
    
    if (!preg_match('/[A-Z]/', $password) || 
        !preg_match('/[a-z]/', $password) || 
        !preg_match('/[0-9]/', $password)) {
        throw new Exception('Password must contain at least one uppercase letter, one lowercase letter, and one number');
    }
    
    // Connect to database
    $host = 'localhost';
    $username = 'root';
    $db_password = '';
    $database = 'business_saas';
    
    $conn = new mysqli($host, $username, $db_password, $database);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    if (!$stmt) {
        throw new Exception('Database prepare failed: ' . $conn->error);
    }
    
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('Email already registered');
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate unique IDs
        $company_object_id = 'comp_' . time() . '_' . rand(100, 999);
        $user_object_id = 'user_' . time() . '_' . rand(100, 999);
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert company
        $stmt = $conn->prepare("INSERT INTO companies (object_id, name, size, industry, created_at) VALUES (?, ?, ?, ?, NOW())");
        if (!$stmt) {
            throw new Exception('Company insert prepare failed: ' . $conn->error);
        }
        
        $stmt->bind_param("ssss", $company_object_id, $company_name, $company_size, $industry);
        
        if (!$stmt->execute()) {
            throw new Exception('Company insert failed: ' . $stmt->error);
        }
        
        $company_id = $conn->insert_id;
        
        // Insert user
        $stmt = $conn->prepare("INSERT INTO users (object_id, name, email, password, company_id, role, status, created_at) VALUES (?, ?, ?, ?, ?, 'admin', 'active', NOW())");
        if (!$stmt) {
            throw new Exception('User insert prepare failed: ' . $conn->error);
        }
        
        $stmt->bind_param("ssssi", $user_object_id, $name, $email, $hashed_password, $company_id);
        
        if (!$stmt->execute()) {
            throw new Exception('User insert failed: ' . $stmt->error);
        }
        
        $user_id = $conn->insert_id;
        
        // Commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Registration successful',
            'user_id' => $user_id,
            'company_id' => $company_id
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
    
    $conn->close();
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Fatal error: ' . $e->getMessage()
    ]);
}
?>
