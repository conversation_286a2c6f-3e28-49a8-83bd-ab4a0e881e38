<?php
echo "<h1>Debug Current Token</h1>";

// Get a fresh token
echo "<h2>Step 1: Get Fresh Token</h2>";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

$loginContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => json_encode($loginData),
        'timeout' => 10
    ]
]);

$loginResponse = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=login", false, $loginContext);
$loginResult = json_decode($loginResponse, true);

if ($loginResult['success']) {
    $token = $loginResult['tokens']['access_token'];
    echo "✅ Fresh token obtained<br>";
    echo "Token: " . substr($token, 0, 30) . "...<br>";
    
    // Decode the token to see its contents
    $tokenData = json_decode(base64_decode($token), true);
    echo "Token data:<br>";
    echo "- User ID: " . $tokenData['user_id'] . "<br>";
    echo "- Email: " . $tokenData['email'] . "<br>";
    echo "- Role: " . $tokenData['role'] . "<br>";
    echo "- Issued at: " . date('Y-m-d H:i:s', $tokenData['iat']) . "<br>";
    echo "- Expires at: " . date('Y-m-d H:i:s', $tokenData['exp']) . "<br>";
    echo "- Current time: " . date('Y-m-d H:i:s') . "<br>";
    echo "- Is expired: " . ($tokenData['exp'] < time() ? 'YES' : 'NO') . "<br><br>";
    
    // Test this token
    echo "<h2>Step 2: Test Fresh Token</h2>";
    $verifyContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\nAuthorization: Bearer $token\r\n",
            'content' => json_encode(['token' => $token]),
            'timeout' => 10
        ]
    ]);
    
    $verifyResponse = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=verify", false, $verifyContext);
    $verifyResult = json_decode($verifyResponse, true);
    
    if ($verifyResult['success']) {
        echo "✅ Fresh token verification successful<br>";
    } else {
        echo "❌ Fresh token verification failed: " . $verifyResult['error'] . "<br>";
    }
    
    // Create a JavaScript snippet to update localStorage
    echo "<h2>Step 3: Update localStorage</h2>";
    echo "<script>";
    echo "localStorage.setItem('authToken', '$token');";
    echo "console.log('Updated localStorage with fresh token');";
    echo "console.log('Token:', localStorage.getItem('authToken'));";
    echo "alert('Fresh token stored in localStorage. Please refresh the page to test AuthContext.');";
    echo "</script>";
    echo "Fresh token stored in localStorage. Refresh the page to test AuthContext.<br>";
    
} else {
    echo "❌ Failed to get fresh token: " . $loginResult['error'] . "<br>";
}
?>