// Make Modal component globally available
window.Modal = function Modal({ 
    isOpen, 
    onClose, 
    title, 
    children, 
    size = 'md',
    showCloseButton = true,
    closeOnOverlayClick = true
}) {
    try {
        if (!isOpen) return null;

        const sizeClasses = {
            sm: 'max-w-sm',
            md: 'max-w-md',
            lg: 'max-w-lg',
            xl: 'max-w-2xl',
            full: 'max-w-full mx-4'
        };

        const handleOverlayClick = (e) => {
            if (closeOnOverlayClick && e.target === e.currentTarget) {
                onClose();
            }
        };

        return (
            <div
                data-name="modal-overlay"
                className="fixed inset-0 z-50 overflow-y-auto"
                onClick={handleOverlayClick}
            >
                <div className="flex items-center justify-center min-h-screen px-4 py-4 text-center sm:block sm:p-0">
                    <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                        <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
                    </div>

                    <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                    <div
                        data-name="modal-content"
                        className={`
                            inline-block align-bottom bg-white rounded-lg text-left
                            overflow-hidden shadow-xl transform transition-all
                            sm:my-8 sm:align-middle ${sizeClasses[size]} w-full
                            max-h-screen overflow-y-auto
                        `}
                    >
                        <div data-name="modal-header" className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div className="flex items-start justify-between">
                                <h3 className="text-lg font-medium leading-6 text-gray-900">
                                    {title}
                                </h3>
                                {showCloseButton && (
                                    <button
                                        data-name="modal-close-button"
                                        type="button"
                                        className="bg-white rounded-md text-gray-400 hover:text-gray-500"
                                        onClick={onClose}
                                    >
                                        <span className="sr-only">Close</span>
                                        <i className="fas fa-times"></i>
                                    </button>
                                )}
                            </div>
                        </div>
                        <div data-name="modal-body" className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            {children}
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Modal component error:', error);
        reportError(error);
        return null;
    }
}
