// Analytics Dashboard Component for Super Admin
function AnalyticsDashboard({ authContext, setNotification }) {
    const [analytics, setAnalytics] = React.useState({});
    const [loading, setLoading] = React.useState(true);
    const [timeRange, setTimeRange] = React.useState('30d');
    const [activeTab, setActiveTab] = React.useState('overview');

    React.useEffect(() => {
        loadAnalytics();
    }, [timeRange]);

    const loadAnalytics = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl(`/super-admin/analytics?range=${timeRange}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setAnalytics(data.data || {});
                }
            }
        } catch (error) {
            console.error('Error loading analytics:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load analytics data'
            });
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };

    const formatPercentage = (value) => {
        return `${(value || 0).toFixed(1)}%`;
    };

    const getTimeRangeLabel = (range) => {
        const labels = {
            '7d': 'Last 7 Days',
            '30d': 'Last 30 Days',
            '90d': 'Last 90 Days',
            '1y': 'Last Year'
        };
        return labels[range] || 'Last 30 Days';
    };

    if (loading) {
        return (
            <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading analytics...</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Analytics & Monitoring</h3>
                <div className="flex space-x-3">
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="7d">Last 7 Days</option>
                        <option value="30d">Last 30 Days</option>
                        <option value="90d">Last 90 Days</option>
                        <option value="1y">Last Year</option>
                    </select>
                    <button
                        onClick={loadAnalytics}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        <i className="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>

            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                    {[
                        { id: 'overview', name: 'Overview', icon: 'fas fa-chart-line' },
                        { id: 'subscriptions', name: 'Subscriptions', icon: 'fas fa-credit-card' },
                        { id: 'revenue', name: 'Revenue', icon: 'fas fa-dollar-sign' },
                        { id: 'users', name: 'Users', icon: 'fas fa-users' },
                        { id: 'performance', name: 'Performance', icon: 'fas fa-tachometer-alt' }
                    ].map((tab) => (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === tab.id
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            <i className={`${tab.icon} mr-2`}></i>
                            {tab.name}
                        </button>
                    ))}
                </nav>
            </div>

            {/* Overview Tab */}
            {activeTab === 'overview' && (
                <div className="space-y-6">
                    {/* Key Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <MetricCard
                            title="Total Revenue"
                            value={formatCurrency(analytics.revenue?.total)}
                            change={analytics.revenue?.growth}
                            icon="fas fa-dollar-sign"
                            color="green"
                        />
                        <MetricCard
                            title="Active Subscriptions"
                            value={analytics.subscriptions?.active || 0}
                            change={analytics.subscriptions?.growth}
                            icon="fas fa-credit-card"
                            color="blue"
                        />
                        <MetricCard
                            title="Trial Conversions"
                            value={formatPercentage(analytics.trials?.conversion_rate)}
                            change={analytics.trials?.conversion_growth}
                            icon="fas fa-chart-line"
                            color="purple"
                        />
                        <MetricCard
                            title="Total Users"
                            value={analytics.users?.total || 0}
                            change={analytics.users?.growth}
                            icon="fas fa-users"
                            color="orange"
                        />
                    </div>

                    {/* Charts Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <ChartCard
                            title="Revenue Trend"
                            data={analytics.charts?.revenue}
                            type="line"
                        />
                        <ChartCard
                            title="Subscription Status"
                            data={analytics.charts?.subscriptions}
                            type="doughnut"
                        />
                    </div>
                </div>
            )}

            {/* Subscriptions Tab */}
            {activeTab === 'subscriptions' && (
                <SubscriptionAnalytics analytics={analytics} timeRange={timeRange} />
            )}

            {/* Revenue Tab */}
            {activeTab === 'revenue' && (
                <RevenueAnalytics analytics={analytics} timeRange={timeRange} />
            )}

            {/* Users Tab */}
            {activeTab === 'users' && (
                <UserAnalytics analytics={analytics} timeRange={timeRange} />
            )}

            {/* Performance Tab */}
            {activeTab === 'performance' && (
                <PerformanceAnalytics analytics={analytics} timeRange={timeRange} />
            )}
        </div>
    );
}

// Metric Card Component
function MetricCard({ title, value, change, icon, color }) {
    const colorClasses = {
        green: 'bg-green-50 text-green-600',
        blue: 'bg-blue-50 text-blue-600',
        purple: 'bg-purple-50 text-purple-600',
        orange: 'bg-orange-50 text-orange-600'
    };

    const isPositive = change > 0;
    const changeColor = isPositive ? 'text-green-600' : 'text-red-600';
    const changeIcon = isPositive ? 'fas fa-arrow-up' : 'fas fa-arrow-down';

    return (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-600">{title}</p>
                    <p className="text-2xl font-bold text-gray-900">{value}</p>
                    {change !== undefined && (
                        <p className={`text-sm ${changeColor} flex items-center mt-1`}>
                            <i className={`${changeIcon} mr-1`}></i>
                            {Math.abs(change).toFixed(1)}% from last period
                        </p>
                    )}
                </div>
                <div className={`p-3 rounded-full ${colorClasses[color]}`}>
                    <i className={`${icon} text-xl`}></i>
                </div>
            </div>
        </div>
    );
}

// Chart Card Component
function ChartCard({ title, data, type }) {
    return (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">{title}</h4>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                    <i className="fas fa-chart-bar text-gray-400 text-3xl mb-2"></i>
                    <p className="text-gray-500">Chart visualization would go here</p>
                    <p className="text-sm text-gray-400">Integration with Chart.js or similar library</p>
                </div>
            </div>
        </div>
    );
}

// Subscription Analytics Component
function SubscriptionAnalytics({ analytics, timeRange }) {
    const subscriptionData = analytics.subscriptions || {};
    
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <MetricCard
                    title="Active Trials"
                    value={subscriptionData.active_trials || 0}
                    icon="fas fa-clock"
                    color="blue"
                />
                <MetricCard
                    title="Paid Subscriptions"
                    value={subscriptionData.paid_subscriptions || 0}
                    icon="fas fa-credit-card"
                    color="green"
                />
                <MetricCard
                    title="Expired/Cancelled"
                    value={subscriptionData.expired || 0}
                    icon="fas fa-times-circle"
                    color="orange"
                />
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Subscription Breakdown</h4>
                <div className="space-y-4">
                    {(subscriptionData.by_plan || []).map((plan, index) => (
                        <div key={index} className="flex justify-between items-center">
                            <span className="text-gray-700">{plan.plan_name}</span>
                            <div className="flex items-center space-x-4">
                                <span className="text-sm text-gray-500">{plan.count} subscribers</span>
                                <span className="font-medium">{formatCurrency(plan.revenue)}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

// Revenue Analytics Component
function RevenueAnalytics({ analytics, timeRange }) {
    const revenueData = analytics.revenue || {};
    
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <MetricCard
                    title="Monthly Recurring Revenue"
                    value={formatCurrency(revenueData.mrr)}
                    change={revenueData.mrr_growth}
                    icon="fas fa-sync-alt"
                    color="green"
                />
                <MetricCard
                    title="Annual Recurring Revenue"
                    value={formatCurrency(revenueData.arr)}
                    change={revenueData.arr_growth}
                    icon="fas fa-calendar-alt"
                    color="blue"
                />
                <MetricCard
                    title="Average Revenue Per User"
                    value={formatCurrency(revenueData.arpu)}
                    change={revenueData.arpu_growth}
                    icon="fas fa-user-dollar"
                    color="purple"
                />
            </div>

            <ChartCard
                title="Revenue Trend"
                data={revenueData.trend}
                type="line"
            />
        </div>
    );
}

// User Analytics Component
function UserAnalytics({ analytics, timeRange }) {
    const userData = analytics.users || {};
    
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <MetricCard
                    title="New Signups"
                    value={userData.new_signups || 0}
                    change={userData.signup_growth}
                    icon="fas fa-user-plus"
                    color="green"
                />
                <MetricCard
                    title="Active Users"
                    value={userData.active_users || 0}
                    change={userData.active_growth}
                    icon="fas fa-users"
                    color="blue"
                />
                <MetricCard
                    title="Churn Rate"
                    value={formatPercentage(userData.churn_rate)}
                    change={userData.churn_change}
                    icon="fas fa-user-times"
                    color="orange"
                />
                <MetricCard
                    title="Retention Rate"
                    value={formatPercentage(userData.retention_rate)}
                    change={userData.retention_change}
                    icon="fas fa-user-check"
                    color="purple"
                />
            </div>
        </div>
    );
}

// Performance Analytics Component
function PerformanceAnalytics({ analytics, timeRange }) {
    const performanceData = analytics.performance || {};
    
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <MetricCard
                    title="System Uptime"
                    value={formatPercentage(performanceData.uptime || 99.9)}
                    icon="fas fa-server"
                    color="green"
                />
                <MetricCard
                    title="Average Response Time"
                    value={`${performanceData.response_time || 150}ms`}
                    icon="fas fa-tachometer-alt"
                    color="blue"
                />
                <MetricCard
                    title="Error Rate"
                    value={formatPercentage(performanceData.error_rate || 0.1)}
                    icon="fas fa-exclamation-triangle"
                    color="orange"
                />
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">System Health</h4>
                <div className="space-y-4">
                    <div className="flex justify-between items-center">
                        <span className="text-gray-700">Database Performance</span>
                        <span className="text-green-600 font-medium">Excellent</span>
                    </div>
                    <div className="flex justify-between items-center">
                        <span className="text-gray-700">API Response Time</span>
                        <span className="text-green-600 font-medium">Good</span>
                    </div>
                    <div className="flex justify-between items-center">
                        <span className="text-gray-700">Storage Usage</span>
                        <span className="text-yellow-600 font-medium">75% Used</span>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make components globally available
window.AnalyticsDashboard = AnalyticsDashboard;
window.MetricCard = MetricCard;
window.ChartCard = ChartCard;
