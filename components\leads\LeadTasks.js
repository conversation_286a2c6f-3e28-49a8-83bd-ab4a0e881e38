function LeadTasks({ leadId }) {
    const [tasks, setTasks] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [newTask, setNewTask] = React.useState({
        title: '',
        description: '',
        dueDate: '',
        priority: 'medium',
        status: 'pending'
    });

    React.useEffect(() => {
        fetchTasks();
    }, [leadId]);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('authToken');
            const response = await fetch(window.getApiUrl(`/task:${leadId}`), {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error('Failed to fetch tasks');
            }
            
            const data = await response.json();
            setTasks(data.items || []);
        } catch (error) {
            console.error('Error fetching tasks:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleAddTask = async (e) => {
        e.preventDefault();
        if (!newTask.title.trim()) return;

        try {
            const token = localStorage.getItem('authToken');
            const response = await fetch(window.getApiUrl(`/task`), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: newTask.title,
                    description: newTask.description,
                    dueDate: newTask.dueDate,
                    priority: newTask.priority,
                    status: newTask.status,
                    lead_id: leadId
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create task');
            }

            setNewTask({
                title: '',
                description: '',
                dueDate: '',
                priority: 'medium',
                status: 'pending'
            });
            fetchTasks();
        } catch (error) {
            console.error('Error adding task:', error);
        }
    };

    const handleTaskUpdate = async (taskId, updatedData) => {
        try {
            const token = localStorage.getItem('authToken');
            const response = await fetch(window.getApiUrl(`/task/${taskId}`), {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...updatedData,
                    updatedAt: new Date().toISOString()
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update task');
            }

            fetchTasks();
        } catch (error) {
            console.error('Error updating task:', error);
        }
    };

    const handleDeleteTask = async (taskId) => {
        if (window.confirm('Are you sure you want to delete this task?')) {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl(`/task/${taskId}`), {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to delete task');
                }

                // Create activity for task deletion
                await fetch(window.getApiUrl(`/activity`), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'task_deleted',
                        description: 'Task deleted',
                        lead_id: leadId
                    })
                });

                fetchTasks();
            } catch (error) {
                console.error('Error deleting task:', error);
            }
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewTask(prev => ({
            ...prev,
            [name]: value
        }));
    };

    try {
        return (
            <div data-name="lead-tasks" className="space-y-6">
                <form onSubmit={handleAddTask} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Title</label>
                            <input
                                type="text"
                                name="title"
                                value={newTask.title}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="Task title"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Due Date</label>
                            <input
                                type="date"
                                name="dueDate"
                                value={newTask.dueDate}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Description</label>
                        <textarea
                            name="description"
                            value={newTask.description}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Task description"
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Priority</label>
                            <select
                                name="priority"
                                value={newTask.priority}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <select
                                name="status"
                                value={newTask.status}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                        <div className="flex items-end">
                            <button
                                type="submit"
                                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                Add Task
                            </button>
                        </div>
                    </div>
                </form>

                <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Tasks</h3>
                    {loading ? (
                        <div className="text-center py-4">
                            <i className="fas fa-spinner fa-spin text-gray-400"></i>
                            <p className="text-gray-500 mt-2">Loading tasks...</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {tasks.length === 0 ? (
                                <p className="text-gray-500">No tasks yet</p>
                            ) : (
                                tasks.map((task) => (
                                    <TaskItem 
                                        key={task.id || task.objectId} 
                                        task={task} 
                                        onUpdate={handleTaskUpdate}
                                        onDelete={handleDeleteTask}
                                    />
                                ))
                            )}
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('LeadTasks component error:', error);
        return (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">Error loading tasks</p>
            </div>
        );
    }
}
