function CustomerList({ 
    onCustomerClick, 
    onEditCustomer, 
    onDeleteCustomer, 
    refreshKey = 0,
    searchTerm = '',
    filterType = 'all',
    sortBy = 'name',
    sortOrder = 'asc',
    viewMode = 'grid',
    selectedCustomers = [],
    onSelectCustomer,
    onSelectAll
}) {
    try {
        const [customers, setCustomers] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [error, setError] = React.useState(null);
        const [filteredCustomers, setFilteredCustomers] = React.useState([]);
        
        // Get authentication context
        const authContext = React.useContext(window.AuthContext);
        const { isAuthenticated, token } = authContext || {};

        const fetchCustomers = async () => {
            try {
                setLoading(true);
                setError(null);
                const authToken = token || localStorage.getItem('authToken');
                
                if (!authToken) {
                    setError('Authentication required');
                    setLoading(false);
                    return;
                }
                
                const response = await fetch(window.getApiUrl('/customer'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const customerList = data.items || [];
                    setCustomers(customerList);
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Failed to fetch customers');
                }
            } catch (error) {
                console.error('Error fetching customers:', error);
                setError(error.message);
                setCustomers([]);
            } finally {
                setLoading(false);
            }
        };

        // Filter and sort customers
        React.useEffect(() => {
            let filtered = [...customers];

            // Apply search filter
            if (searchTerm) {
                const searchLower = searchTerm.toLowerCase();
                filtered = filtered.filter(customer => {
                    const data = customer.objectData || {};
                    return (
                        (data.name || '').toLowerCase().includes(searchLower) ||
                        (data.email || '').toLowerCase().includes(searchLower) ||
                        (data.company || '').toLowerCase().includes(searchLower) ||
                        (data.phone || '').toLowerCase().includes(searchLower)
                    );
                });
            }

            // Apply type filter
            if (filterType && filterType !== 'all') {
                filtered = filtered.filter(customer => {
                    const data = customer.objectData || {};
                    return data.type === filterType;
                });
            }

            // Apply sorting
            filtered.sort((a, b) => {
                const aData = a.objectData || {};
                const bData = b.objectData || {};
                
                let aValue, bValue;
                
                switch (sortBy) {
                    case 'name':
                        aValue = (aData.name || '').toLowerCase();
                        bValue = (bData.name || '').toLowerCase();
                        break;
                    case 'company':
                        aValue = (aData.company || '').toLowerCase();
                        bValue = (bData.company || '').toLowerCase();
                        break;
                    case 'createdAt':
                        aValue = new Date(a.createdAt || 0);
                        bValue = new Date(b.createdAt || 0);
                        break;
                    default:
                        aValue = (aData.name || '').toLowerCase();
                        bValue = (bData.name || '').toLowerCase();
                }

                if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
                if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
                return 0;
            });

            setFilteredCustomers(filtered);
        }, [customers, searchTerm, filterType, sortBy, sortOrder]);

        // Refresh data when refreshKey changes
        React.useEffect(() => {
            if (isAuthenticated && (token || localStorage.getItem('authToken'))) {
                fetchCustomers();
            }
        }, [isAuthenticated, token, refreshKey]);

        // Handle select all functionality
        const handleSelectAllChange = (e) => {
            if (onSelectAll) {
                onSelectAll(filteredCustomers, e.target.checked);
            }
        };

        // Handle individual customer selection
        const handleCustomerSelect = (customerId, isSelected) => {
            if (onSelectCustomer) {
                onSelectCustomer(customerId, isSelected);
            }
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <div className="text-center">
                        <i className="fas fa-spinner fa-spin fa-2x text-blue-500 mb-4"></i>
                        <p className="text-gray-600">Loading customers...</p>
                    </div>
                </div>
            );
        }

        if (error) {
            return (
                <div className="flex flex-col items-center justify-center h-64 text-red-600">
                    <i className="fas fa-exclamation-triangle fa-2x mb-4"></i>
                    <p className="font-semibold mb-2">Error loading customers</p>
                    <p className="text-sm text-gray-600 mb-4">{error}</p>
                    <Button onClick={fetchCustomers} variant="secondary" size="sm">
                        Try Again
                    </Button>
                </div>
            );
        }

        if (filteredCustomers.length === 0) {
            return (
                <div className="text-center py-12">
                    <i className="fas fa-users fa-3x text-gray-300 mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {searchTerm || filterType !== 'all' ? 'No customers found' : 'No customers yet'}
                    </h3>
                    <p className="text-gray-600 mb-6">
                        {searchTerm || filterType !== 'all' 
                            ? 'Try adjusting your search or filters'
                            : 'Get started by adding your first customer'
                        }
                    </p>
                </div>
            );
        }

        return (
            <div data-name="customer-list">
                {/* Results Summary */}
                <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                        <p className="text-sm text-gray-600">
                            {filteredCustomers.length} customer{filteredCustomers.length !== 1 ? 's' : ''} found
                        </p>
                        {onSelectAll && (
                            <label className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    checked={selectedCustomers.length === filteredCustomers.length && filteredCustomers.length > 0}
                                    onChange={handleSelectAllChange}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <span className="text-sm text-gray-600">Select all</span>
                            </label>
                        )}
                    </div>
                </div>

                {/* Customer Grid/List */}
                <div className={viewMode === 'grid' 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
                    : "space-y-4"
                }>
                    {filteredCustomers.map(customer => (
                        <CustomerCard
                            key={customer.objectId}
                            customer={customer}
                            viewMode={viewMode}
                            isSelected={selectedCustomers.includes(customer.objectId)}
                            onSelect={handleCustomerSelect}
                            onClick={onCustomerClick}
                            onEdit={onEditCustomer}
                            onDelete={onDeleteCustomer}
                        />
                    ))}
                </div>
            </div>
        );
    } catch (error) {
        console.error('CustomerList component error:', error);
        reportError(error);
        return (
            <div className="flex flex-col items-center justify-center h-64 text-red-600">
                <i className="fas fa-exclamation-triangle fa-2x mb-4"></i>
                <p className="font-semibold">Something went wrong</p>
                <p className="text-sm text-gray-600">Please refresh the page</p>
            </div>
        );
    }
}

// Enhanced Customer Card Component
function CustomerCard({ 
    customer, 
    viewMode, 
    isSelected, 
    onSelect, 
    onClick, 
    onEdit, 
    onDelete 
}) {
    const data = customer.objectData || {};
    
    const handleCardClick = (e) => {
        // Don't trigger card click if clicking on action buttons or checkbox
        if (e.target.closest('.customer-actions') || e.target.type === 'checkbox') {
            return;
        }
        if (onClick) onClick(customer);
    };

    const handleSelectChange = (e) => {
        e.stopPropagation();
        if (onSelect) onSelect(customer.objectId, e.target.checked);
    };

    if (viewMode === 'list') {
        return (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-4">
                    {onSelect && (
                        <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={handleSelectChange}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                    )}
                    <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-semibold text-lg">
                                {(data.name || 'U')[0].toUpperCase()}
                            </span>
                        </div>
                    </div>
                    <div className="flex-1 min-w-0 cursor-pointer" onClick={handleCardClick}>
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 truncate">
                                    {data.name || 'Unnamed Customer'}
                                </h3>
                                <p className="text-sm text-gray-600 truncate">
                                    {data.company || 'Individual Customer'}
                                </p>
                            </div>
                            <div className="text-right">
                                <p className="text-sm text-gray-600">{data.email}</p>
                                {data.phone && (
                                    <p className="text-sm text-gray-500">{data.phone}</p>
                                )}
                            </div>
                        </div>
                        <div className="mt-2 flex items-center justify-between">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                data.type === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                                data.type === 'business' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'
                            }`}>
                                {data.type || 'individual'}
                            </span>
                            <div className="customer-actions flex items-center space-x-2">
                                {onEdit && (
                                    <button
                                        onClick={(e) => { e.stopPropagation(); onEdit(customer); }}
                                        className="text-gray-400 hover:text-blue-600 transition-colors"
                                        title="Edit customer"
                                    >
                                        <i className="fas fa-edit"></i>
                                    </button>
                                )}
                                {onDelete && (
                                    <button
                                        onClick={(e) => { e.stopPropagation(); onDelete(customer); }}
                                        className="text-gray-400 hover:text-red-600 transition-colors"
                                        title="Delete customer"
                                    >
                                        <i className="fas fa-trash"></i>
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Grid view
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow relative">
            {onSelect && (
                <div className="absolute top-4 left-4">
                    <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={handleSelectChange}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                </div>
            )}
            <div className="cursor-pointer" onClick={handleCardClick}>
                <div className="flex flex-col items-center text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <span className="text-blue-600 font-semibold text-xl">
                            {(data.name || 'U')[0].toUpperCase()}
                        </span>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2 truncate w-full">
                        {data.name || 'Unnamed Customer'}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3 truncate w-full">
                        {data.company || 'Individual Customer'}
                    </p>
                    <div className="space-y-2 w-full">
                        <div className="flex items-center justify-center text-sm text-gray-500">
                            <i className="fas fa-envelope mr-2"></i>
                            <span className="truncate">{data.email}</span>
                        </div>
                        {data.phone && (
                            <div className="flex items-center justify-center text-sm text-gray-500">
                                <i className="fas fa-phone mr-2"></i>
                                <span>{data.phone}</span>
                            </div>
                        )}
                    </div>
                    <div className="mt-4 flex items-center justify-between w-full">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                            data.type === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                            data.type === 'business' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                        }`}>
                            {data.type || 'individual'}
                        </span>
                        <div className="customer-actions flex items-center space-x-2">
                            {onEdit && (
                                <button
                                    onClick={(e) => { e.stopPropagation(); onEdit(customer); }}
                                    className="text-gray-400 hover:text-blue-600 transition-colors p-1"
                                    title="Edit customer"
                                >
                                    <i className="fas fa-edit"></i>
                                </button>
                            )}
                            {onDelete && (
                                <button
                                    onClick={(e) => { e.stopPropagation(); onDelete(customer); }}
                                    className="text-gray-400 hover:text-red-600 transition-colors p-1"
                                    title="Delete customer"
                                >
                                    <i className="fas fa-trash"></i>
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
