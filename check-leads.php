<?php
require_once 'api/db-config.php';

echo "<h2>Checking Leads in Database</h2>\n";

$sql = "SELECT object_id, name, email, status FROM leads ORDER BY created_at DESC LIMIT 10";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Object ID</th><th>Name</th><th>Email</th><th>Status</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['object_id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
        echo "<td>" . htmlspecialchars($row['status']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>No leads found in database.</p>\n";
    echo "<p>SQL Error: " . $conn->error . "</p>\n";
}

$conn->close();
?>