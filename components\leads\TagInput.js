function TagInput({ tags = [], onChange }) {
    try {
        const [inputValue, setInputValue] = React.useState('');

        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                const newTag = inputValue.trim();
                if (newTag && !tags.includes(newTag)) {
                    const updatedTags = [...tags, newTag];
                    onChange(updatedTags);
                }
                setInputValue('');
            } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
                const updatedTags = tags.slice(0, -1);
                onChange(updatedTags);
            }
        };

        const handleChange = (e) => {
            const value = e.target.value;
            if (value.includes(',')) {
                const newTag = value.replace(',', '').trim();
                if (newTag && !tags.includes(newTag)) {
                    const updatedTags = [...tags, newTag];
                    onChange(updatedTags);
                }
                setInputValue('');
            } else {
                setInputValue(value);
            }
        };

        const removeTag = (tagToRemove) => {
            const updatedTags = tags.filter(tag => tag !== tagToRemove);
            onChange(updatedTags);
        };

        return (
            <div data-name="tag-input" className="relative">
                <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-white min-h-[42px]">
                    {tags.map((tag, index) => (
                        <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                        >
                            {tag}
                            <button
                                type="button"
                                onClick={() => removeTag(tag)}
                                className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none"
                            >
                                <span className="sr-only">Remove tag</span>
                                <i className="fas fa-times text-xs"></i>
                            </button>
                        </span>
                    ))}
                    <input
                        type="text"
                        value={inputValue}
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        className="flex-1 outline-none border-0 focus:ring-0 min-w-[120px] p-0.5"
                        placeholder={tags.length === 0 ? "Add tags..." : ""}
                    />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                    Press enter or use commas to add tags
                </p>
            </div>
        );
    } catch (error) {
        console.error('TagInput component error:', error);
        reportError(error);
        return null;
    }
}
