// Make Input component globally available
window.Input = function Input({
    type = 'text',
    label,
    name,
    value,
    onChange,
    placeholder,
    error,
    required = false,
    disabled = false,
    icon = null,
    className = ''
}) {
    try {
        return (
            <div data-name={`input-group-${name}`} className="mb-4">
                {label && (
                    <label
                        data-name={`input-label-${name}`}
                        htmlFor={name}
                        className="block text-sm font-medium text-gray-700 mb-1"
                    >
                        {label}
                        {required && <span className="text-red-500 ml-1">*</span>}
                    </label>
                )}
                <div className="relative">
                    {icon && (
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i className={`${icon} text-gray-400`}></i>
                        </div>
                    )}
                    <input
                        data-name={`input-${name}`}
                        type={type}
                        name={name}
                        id={name}
                        value={value || ''}
                        onChange={onChange}
                        disabled={disabled}
                        placeholder={placeholder}
                        className={`
                            block w-full rounded-md border-gray-300 shadow-sm
                            focus:border-blue-500 focus:ring-blue-500 sm:text-sm
                            ${icon ? 'pl-10' : ''}
                            ${error ? 'border-red-300' : 'border-gray-300'}
                            ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
                            ${className}
                        `}
                    />
                </div>
                {error && (
                    <p data-name={`input-error-${name}`} className="mt-1 text-sm text-red-600">
                        {error}
                    </p>
                )}
            </div>
        );
    } catch (error) {
        console.error('Input component error:', error);
        reportError(error);
        return null;
    }
}
