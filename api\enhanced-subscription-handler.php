<?php
/**
 * Enhanced Subscription Management Handler
 * Provides comprehensive subscription management with trial handling, usage tracking, and upgrade flows
 */

require_once 'db-config.php';
require_once 'utils/ErrorHandler.php';

class EnhancedSubscriptionHandler {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get current subscription with detailed information
     */
    public function getCurrentSubscription($companyId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    s.*,
                    p.name as plan_name,
                    p.description as plan_description,
                    p.price_monthly,
                    p.price_yearly,
                    p.features,
                    p.limits_data,
                    DATEDIFF(s.end_date, NOW()) as days_remaining,
                    CASE 
                        WHEN s.status = 'trial' AND s.end_date < NOW() THEN 'expired'
                        WHEN s.status = 'active' AND s.end_date < NOW() THEN 'expired'
                        ELSE s.status
                    END as current_status
                FROM subscriptions s
                JOIN pricing_plans p ON s.plan_id = p.id
                WHERE s.company_id = ?
                ORDER BY s.created_at DESC
                LIMIT 1
            ");
            
            $stmt->bind_param("s", $companyId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                // Create trial subscription if none exists
                return $this->createTrialSubscription($companyId);
            }
            
            $subscription = $result->fetch_assoc();
            
            // Parse JSON fields
            $subscription['features'] = json_decode($subscription['features'], true);
            $subscription['limits'] = json_decode($subscription['limits_data'], true);
            
            // Update status if expired
            if ($subscription['current_status'] === 'expired' && $subscription['status'] !== 'expired') {
                $this->updateSubscriptionStatus($subscription['id'], 'expired');
                $subscription['status'] = 'expired';
            }
            
            // Get usage statistics
            $subscription['usage'] = $this->getUsageStatistics($companyId);
            
            return [
                'success' => true,
                'subscription' => $subscription
            ];
            
        } catch (Exception $e) {
            error_log("Get Current Subscription Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to get subscription', 'details' => $e->getMessage()];
        }
    }
    
    /**
     * Get detailed usage statistics
     */
    public function getUsageStatistics($companyId) {
        try {
            $usage = [];
            
            // Get current counts
            $tables = [
                'customers' => 'max_customers',
                'invoices' => 'max_invoices', 
                'quotations' => 'max_quotations',
                'contracts' => 'max_contracts',
                'leads' => 'max_leads',
                'items' => 'max_inventory_items'
            ];
            
            foreach ($tables as $table => $limitKey) {
                $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM $table WHERE company_id = ?");
                $stmt->bind_param("s", $companyId);
                $stmt->execute();
                $result = $stmt->get_result();
                $count = $result->fetch_assoc()['count'];
                
                $usage[$table] = [
                    'current' => (int)$count,
                    'limit_key' => $limitKey
                ];
            }
            
            // Get user count
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM users WHERE company_id = ?");
            $stmt->bind_param("s", $companyId);
            $stmt->execute();
            $result = $stmt->get_result();
            $userCount = $result->fetch_assoc()['count'];
            
            $usage['users'] = [
                'current' => (int)$userCount,
                'limit_key' => 'max_users'
            ];
            
            // Calculate storage usage (simplified)
            $usage['storage'] = [
                'current' => 0.1, // GB - placeholder
                'limit_key' => 'storage_gb'
            ];
            
            return $usage;
            
        } catch (Exception $e) {
            error_log("Get Usage Statistics Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create trial subscription for new company
     */
    public function createTrialSubscription($companyId) {
        try {
            $subscriptionId = 'sub_' . uniqid();
            $expiresAt = date('Y-m-d H:i:s', strtotime('+5 days'));
            
            $stmt = $this->conn->prepare("
                INSERT INTO subscriptions (
                    id, company_id, plan_id, status, starts_at, expires_at, 
                    billing_cycle, created_at
                ) VALUES (?, ?, 'trial', 'trial', NOW(), ?, 'monthly', NOW())
            ");
            
            $stmt->bind_param("sss", $subscriptionId, $companyId, $expiresAt);
            $stmt->execute();
            
            // Get the created subscription
            return $this->getCurrentSubscription($companyId);
            
        } catch (Exception $e) {
            error_log("Create Trial Subscription Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to create trial subscription'];
        }
    }
    
    /**
     * Upgrade subscription to a paid plan
     */
    public function upgradeSubscription($companyId, $planId, $billingCycle = 'monthly') {
        try {
            // Get plan details
            $stmt = $this->conn->prepare("SELECT * FROM pricing_plans WHERE id = ? AND is_active = 1");
            $stmt->bind_param("s", $planId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                return ['success' => false, 'error' => 'Invalid plan selected'];
            }
            
            $plan = $result->fetch_assoc();
            
            // Calculate expiry date
            $expiresAt = $billingCycle === 'yearly' ? 
                date('Y-m-d H:i:s', strtotime('+1 year')) : 
                date('Y-m-d H:i:s', strtotime('+1 month'));
            
            // Get current subscription
            $currentSub = $this->getCurrentSubscription($companyId);
            
            if ($currentSub['success']) {
                // Update existing subscription
                $stmt = $this->conn->prepare("
                    UPDATE subscriptions 
                    SET plan_id = ?, status = 'active', billing_cycle = ?, 
                        expires_at = ?, updated_at = NOW()
                    WHERE company_id = ? AND id = ?
                ");
                $stmt->bind_param("sssss", $planId, $billingCycle, $expiresAt, $companyId, $currentSub['subscription']['id']);
                $stmt->execute();
            } else {
                // Create new subscription
                $subscriptionId = 'sub_' . uniqid();
                $stmt = $this->conn->prepare("
                    INSERT INTO subscriptions (
                        id, company_id, plan_id, status, starts_at, expires_at, 
                        billing_cycle, created_at
                    ) VALUES (?, ?, ?, 'active', NOW(), ?, ?, NOW())
                ");
                $stmt->bind_param("sssss", $subscriptionId, $companyId, $planId, $expiresAt, $billingCycle);
                $stmt->execute();
            }
            
            return [
                'success' => true,
                'message' => 'Subscription upgraded successfully',
                'subscription' => $this->getCurrentSubscription($companyId)
            ];
            
        } catch (Exception $e) {
            error_log("Upgrade Subscription Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to upgrade subscription'];
        }
    }
    
    /**
     * Check if feature is available for current subscription
     */
    public function checkFeatureAccess($companyId, $feature) {
        try {
            $subscription = $this->getCurrentSubscription($companyId);
            
            if (!$subscription['success']) {
                return false;
            }
            
            $sub = $subscription['subscription'];
            
            // Check if subscription is active
            if ($sub['current_status'] !== 'active' && $sub['current_status'] !== 'trial') {
                return false;
            }
            
            // Check if feature is included
            return in_array($feature, $sub['features']);
            
        } catch (Exception $e) {
            error_log("Check Feature Access Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check usage limits
     */
    public function checkUsageLimit($companyId, $limitType) {
        try {
            $subscription = $this->getCurrentSubscription($companyId);
            
            if (!$subscription['success']) {
                return ['allowed' => false, 'reason' => 'No subscription found'];
            }
            
            $sub = $subscription['subscription'];
            $usage = $sub['usage'];
            $limits = $sub['limits'];
            
            // Check if limit exists
            if (!isset($limits[$limitType])) {
                return ['allowed' => true, 'reason' => 'No limit set'];
            }
            
            $limit = $limits[$limitType];
            
            // -1 means unlimited
            if ($limit === -1) {
                return ['allowed' => true, 'reason' => 'Unlimited'];
            }
            
            // Find corresponding usage
            $currentUsage = 0;
            foreach ($usage as $key => $data) {
                if ($data['limit_key'] === $limitType) {
                    $currentUsage = $data['current'];
                    break;
                }
            }
            
            $allowed = $currentUsage < $limit;
            
            return [
                'allowed' => $allowed,
                'current' => $currentUsage,
                'limit' => $limit,
                'remaining' => $allowed ? ($limit - $currentUsage) : 0,
                'reason' => $allowed ? 'Within limits' : 'Limit exceeded'
            ];
            
        } catch (Exception $e) {
            error_log("Check Usage Limit Error: " . $e->getMessage());
            return ['allowed' => false, 'reason' => 'Error checking limits'];
        }
    }
    
    /**
     * Update subscription status
     */
    private function updateSubscriptionStatus($subscriptionId, $status) {
        $stmt = $this->conn->prepare("UPDATE subscriptions SET status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("ss", $status, $subscriptionId);
        $stmt->execute();
    }
    
    /**
     * Get available pricing plans
     */
    public function getAvailablePlans() {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM pricing_plans 
                WHERE is_active = 1 AND is_visible = 1 
                ORDER BY sort_order, price_monthly
            ");
            $stmt->execute();
            $result = $stmt->get_result();
            
            $plans = [];
            while ($row = $result->fetch_assoc()) {
                $row['features'] = json_decode($row['features'], true);
                $row['limits'] = json_decode($row['limits_data'], true);
                $plans[] = $row;
            }
            
            return ['success' => true, 'plans' => $plans];
            
        } catch (Exception $e) {
            error_log("Get Available Plans Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Failed to get plans'];
        }
    }
}

// Handle subscription management requests
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET' || $_SERVER['REQUEST_METHOD'] === 'POST') {
    $subscriptionHandler = new EnhancedSubscriptionHandler($conn);
    
    // Get company ID from auth token (simplified)
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $token = str_replace('Bearer ', '', $token);
    
    // For now, use a default company ID - in production, decode from token
    $companyId = 'super_admin_001'; // This should come from the authenticated user
    
    $action = $_GET['action'] ?? 'current';
    
    switch ($action) {
        case 'current':
            $result = $subscriptionHandler->getCurrentSubscription($companyId);
            break;
            
        case 'usage':
            $usage = $subscriptionHandler->getUsageStatistics($companyId);
            $result = ['success' => true, 'data' => $usage];
            break;
            
        case 'plans':
            $result = $subscriptionHandler->getAvailablePlans();
            break;
            
        case 'upgrade':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                $result = $subscriptionHandler->upgradeSubscription(
                    $companyId,
                    $input['plan_id'] ?? '',
                    $input['billing_cycle'] ?? 'monthly'
                );
            } else {
                $result = ['success' => false, 'error' => 'POST method required'];
            }
            break;
            
        case 'check-feature':
            $feature = $_GET['feature'] ?? '';
            $allowed = $subscriptionHandler->checkFeatureAccess($companyId, $feature);
            $result = ['success' => true, 'allowed' => $allowed];
            break;
            
        case 'check-limit':
            $limitType = $_GET['limit_type'] ?? '';
            $limitCheck = $subscriptionHandler->checkUsageLimit($companyId, $limitType);
            $result = ['success' => true, 'limit_check' => $limitCheck];
            break;
            
        default:
            $result = ['success' => false, 'error' => 'Invalid action'];
    }
    
    echo json_encode($result);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    error_log("Enhanced Subscription Handler Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Subscription service error']);
}
?>