// Business Type Configuration Tests
document.addEventListener('DOMContentLoaded', function() {
    if (typeof TestRunner !== 'undefined') {
        const testRunner = window.testRunner || new TestRunner();
        window.testRunner = testRunner;

        const businessTypeTests = [
            {
                name: 'Business types API should be accessible',
                test: async function() {
                    const response = await testRunner.makeApiCall('/business-types.php');
                    testRunner.expect(response.ok).toBeTruthy();
                }
            },
            {
                name: 'Business types should return valid data structure',
                test: async function() {
                    const response = await testRunner.makeApiCall('/business-types.php');
                    testRunner.expect(response.ok).toBeTruthy();
                    testRunner.expect(response.data.success).toBeTruthy();
                    testRunner.expect(response.data.data).toBeInstanceOf(Array);
                }
            },
            {
                name: 'Business type selector component should exist',
                test: async function() {
                    testRunner.expect(typeof window.BusinessTypeSelector).toBe('function');
                }
            },
            {
                name: 'Business types should have required properties',
                test: async function() {
                    const mockBusinessType = {
                        object_id: 'bt_retail_001',
                        name: 'Retail Business',
                        description: 'General retail and merchandise business',
                        icon: 'fas fa-store',
                        color: 'blue',
                        modules: ['customers', 'inventory', 'sales'],
                        features: ['pos_integration', 'inventory_tracking'],
                        is_active: true
                    };
                    
                    testRunner.expect(mockBusinessType).toHaveProperty('object_id');
                    testRunner.expect(mockBusinessType).toHaveProperty('name');
                    testRunner.expect(mockBusinessType).toHaveProperty('description');
                    testRunner.expect(mockBusinessType).toHaveProperty('modules');
                    testRunner.expect(mockBusinessType.modules).toBeInstanceOf(Array);
                }
            },
            {
                name: 'Business type modules should be properly configured',
                test: async function() {
                    const mockModules = [
                        'customers',
                        'inventory',
                        'sales',
                        'invoicing',
                        'quotations',
                        'contracts',
                        'analytics'
                    ];
                    
                    mockModules.forEach(module => {
                        testRunner.expect(typeof module).toBe('string');
                        testRunner.expect(module.length).toBeGreaterThan(0);
                    });
                }
            },
            {
                name: 'Business type features should be configurable',
                test: async function() {
                    const mockFeatures = {
                        'pos_integration': true,
                        'inventory_tracking': true,
                        'multi_location': false,
                        'advanced_reporting': true
                    };
                    
                    Object.entries(mockFeatures).forEach(([feature, enabled]) => {
                        testRunner.expect(typeof feature).toBe('string');
                        testRunner.expect(typeof enabled).toBe('boolean');
                    });
                }
            },
            {
                name: 'Business type selection should affect plan recommendations',
                test: async function() {
                    const businessType = 'jewellery';
                    const mockPlans = [
                        { id: 'basic', business_types: ['retail', 'jewellery'] },
                        { id: 'premium', business_types: ['jewellery', 'manufacturing'] },
                        { id: 'enterprise', business_types: [] } // Available for all
                    ];
                    
                    const applicablePlans = mockPlans.filter(plan => 
                        plan.business_types.length === 0 || 
                        plan.business_types.includes(businessType)
                    );
                    
                    testRunner.expect(applicablePlans.length).toBe(3);
                    testRunner.expect(applicablePlans.map(p => p.id)).toContain('basic');
                    testRunner.expect(applicablePlans.map(p => p.id)).toContain('premium');
                    testRunner.expect(applicablePlans.map(p => p.id)).toContain('enterprise');
                }
            },
            {
                name: 'Business type configuration should be stored correctly',
                test: async function() {
                    const mockCompanyConfig = {
                        business_type: 'retail',
                        enabled_modules: ['customers', 'inventory', 'sales'],
                        features: {
                            'pos_integration': true,
                            'inventory_tracking': true
                        }
                    };
                    
                    testRunner.expect(mockCompanyConfig.business_type).toBe('retail');
                    testRunner.expect(mockCompanyConfig.enabled_modules).toBeInstanceOf(Array);
                    testRunner.expect(mockCompanyConfig.features).toHaveProperty('pos_integration');
                }
            },
            {
                name: 'Business type icons should be valid Font Awesome classes',
                test: async function() {
                    const mockIcons = [
                        'fas fa-store',
                        'fas fa-gem',
                        'fas fa-graduation-cap',
                        'fas fa-utensils',
                        'fas fa-car'
                    ];
                    
                    mockIcons.forEach(icon => {
                        testRunner.expect(icon).toContain('fas fa-');
                        testRunner.expect(icon.split(' ').length).toBe(2);
                    });
                }
            },
            {
                name: 'Business type colors should be valid CSS classes',
                test: async function() {
                    const validColors = ['blue', 'green', 'purple', 'red', 'yellow', 'indigo', 'pink', 'gray'];
                    const mockColor = 'blue';
                    
                    testRunner.expect(validColors).toContain(mockColor);
                }
            },
            {
                name: 'Business type filtering should work correctly',
                test: async function() {
                    const mockBusinessTypes = [
                        { id: 'retail', is_active: true },
                        { id: 'jewellery', is_active: true },
                        { id: 'deprecated', is_active: false },
                        { id: 'education', is_active: true }
                    ];
                    
                    const activeTypes = mockBusinessTypes.filter(type => type.is_active);
                    testRunner.expect(activeTypes.length).toBe(3);
                    testRunner.expect(activeTypes.map(t => t.id)).not.toContain('deprecated');
                }
            },
            {
                name: 'Business type dashboard customization should work',
                test: async function() {
                    const mockDashboardConfig = {
                        business_type: 'jewellery',
                        widgets: [
                            'inventory_summary',
                            'sales_overview',
                            'customer_insights',
                            'precious_metals_rates'
                        ],
                        layout: 'grid'
                    };
                    
                    testRunner.expect(mockDashboardConfig.widgets).toBeInstanceOf(Array);
                    testRunner.expect(mockDashboardConfig.widgets).toContain('precious_metals_rates');
                    testRunner.expect(mockDashboardConfig.layout).toBe('grid');
                }
            },
            {
                name: 'Business type validation should prevent invalid selections',
                test: async function() {
                    const validBusinessTypes = ['retail', 'jewellery', 'education', 'restaurant', 'automotive'];
                    const userSelection = 'retail';
                    const invalidSelection = 'invalid_type';
                    
                    testRunner.expect(validBusinessTypes).toContain(userSelection);
                    testRunner.expect(validBusinessTypes).not.toContain(invalidSelection);
                }
            },
            {
                name: 'Business type migration should be supported',
                test: async function() {
                    const currentType = 'retail';
                    const newType = 'jewellery';
                    
                    // Mock migration validation
                    const canMigrate = currentType !== newType && 
                                     ['retail', 'jewellery'].includes(currentType) && 
                                     ['retail', 'jewellery'].includes(newType);
                    
                    testRunner.expect(canMigrate).toBeTruthy();
                }
            },
            {
                name: 'Business type specific fields should be configurable',
                test: async function() {
                    const jewelleryFields = [
                        'metal_type',
                        'purity',
                        'weight',
                        'making_charges',
                        'stone_details'
                    ];
                    
                    const retailFields = [
                        'sku',
                        'barcode',
                        'category',
                        'brand',
                        'supplier'
                    ];
                    
                    testRunner.expect(jewelleryFields).toContain('metal_type');
                    testRunner.expect(retailFields).toContain('sku');
                    testRunner.expect(jewelleryFields).not.toContain('sku');
                }
            }
        ];

        testRunner.addTestSuite('Business Type', businessTypeTests);
    }
});
