<?php
/**
 * Pricing Plans API
 * Public endpoint for fetching visible pricing plans
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Fetch visible pricing plans
        $sql = "SELECT id, name, description, short_description, price_monthly, price_yearly, 
                       trial_days, features, limits_data, business_types, is_trial_available, 
                       is_visible, is_popular, sort_order 
                FROM pricing_plans 
                WHERE is_visible = TRUE 
                ORDER BY sort_order ASC, name ASC";
        
        $result = $conn->query($sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . $conn->error);
        }
        
        $plans = [];
        while ($row = $result->fetch_assoc()) {
            // Parse JSON fields
            $row['features'] = json_decode($row['features'] ?? '[]', true);
            $row['limits_data'] = json_decode($row['limits_data'] ?? '{}', true);
            $row['business_types'] = json_decode($row['business_types'] ?? '[]', true);
            
            // Convert boolean fields
            $row['is_trial_available'] = (bool)$row['is_trial_available'];
            $row['is_visible'] = (bool)$row['is_visible'];
            $row['is_popular'] = (bool)$row['is_popular'];
            
            // Calculate savings for yearly billing
            if ($row['price_monthly'] > 0 && $row['price_yearly'] > 0) {
                $monthlyCost = $row['price_monthly'] * 12;
                $yearlyCost = $row['price_yearly'];
                $row['yearly_savings'] = $monthlyCost - $yearlyCost;
                $row['yearly_savings_percentage'] = round(($row['yearly_savings'] / $monthlyCost) * 100);
            } else {
                $row['yearly_savings'] = 0;
                $row['yearly_savings_percentage'] = 0;
            }
            
            $plans[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $plans,
            'count' => count($plans)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Pricing Plans API Error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch pricing plans: ' . $e->getMessage()
    ]);
}
?>
