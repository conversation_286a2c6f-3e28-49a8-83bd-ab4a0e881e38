// Policy Pages Manager Component for Super Admin
function PolicyPagesManager({ authContext, setNotification }) {
    const [policyPages, setPolicyPages] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);
    const [editingPolicy, setEditingPolicy] = React.useState(null);
    const [showPreview, setShowPreview] = React.useState(false);
    const [previewContent, setPreviewContent] = React.useState('');

    React.useEffect(() => {
        loadPolicyPages();
    }, []);

    const loadPolicyPages = async () => {
        try {
            setLoading(true);

            // Debug authentication
            console.log('PolicyPagesManager: Auth context:', authContext);
            console.log('PolicyPagesManager: Token:', authContext?.token);
            console.log('PolicyPagesManager: User:', authContext?.user);

            if (!authContext || !authContext.token) {
                throw new Error('Authentication required');
            }

            const apiUrl = window.getApiUrl('/super-admin/policy-pages');
            console.log('PolicyPagesManager: API URL:', apiUrl);

            const response = await fetch(apiUrl, {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('PolicyPagesManager: Response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('PolicyPagesManager: Response data:', data);
                if (data.success) {
                    // Convert object to array format expected by the component
                    const policyData = data.data || {};
                    const policyArray = Object.keys(policyData).map(key => ({
                        object_id: key,
                        page_type: key,
                        title: policyData[key].title || key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
                        content: policyData[key].content || policyData[key],
                        is_active: true,
                        version: '1.0',
                        effective_date: new Date().toISOString()
                    }));
                    setPolicyPages(policyArray);
                } else {
                    throw new Error(data.message || 'Failed to load policy pages');
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                console.error('PolicyPagesManager: API Error:', errorData);
                throw new Error(errorData.message || `Failed to load policy pages (${response.status})`);
            }
        } catch (error) {
            console.error('PolicyPagesManager: Error loading policy pages:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to load policy pages'
            });
        } finally {
            setLoading(false);
        }
    };

    const savePolicyPage = async (policyData) => {
        setSaving(true);
        try {
            // Convert single policy data to the format expected by the API
            const apiData = {
                [policyData.page_type]: {
                    title: policyData.title,
                    content: policyData.content
                }
            };

            const response = await fetch(window.getApiUrl('/super-admin/policy-pages'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(apiData)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Policy page ${policyData.object_id ? 'updated' : 'created'} successfully`
                    });
                    setEditingPolicy(null);
                    loadPolicyPages(); // Reload data
                } else {
                    setNotification({
                        type: 'error',
                        message: data.message || 'Failed to save policy page'
                    });
                }
            }
        } catch (error) {
            console.error('Error saving policy page:', error);
            setNotification({
                type: 'error',
                message: 'Failed to save policy page'
            });
        } finally {
            setSaving(false);
        }
    };

    const generatePreview = async (policyData) => {
        try {
            const response = await fetch(window.getApiUrl('/policy-pages/preview'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: policyData.content,
                    template_variables: policyData.template_variables
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setPreviewContent(data.data.rendered_content);
                    setShowPreview(true);
                }
            }
        } catch (error) {
            console.error('Error generating preview:', error);
            setNotification({
                type: 'error',
                message: 'Failed to generate preview'
            });
        }
    };

    const getPolicyTypeIcon = (type) => {
        const icons = {
            terms: 'fas fa-file-contract',
            privacy: 'fas fa-shield-alt',
            refund: 'fas fa-undo',
            cookie: 'fas fa-cookie-bite',
            disclaimer: 'fas fa-exclamation-triangle'
        };
        return icons[type] || 'fas fa-file-alt';
    };

    const getPolicyTypeName = (type) => {
        const names = {
            terms: 'Terms & Conditions',
            privacy: 'Privacy Policy',
            refund: 'Refund Policy',
            cookie: 'Cookie Policy',
            disclaimer: 'Disclaimer'
        };
        return names[type] || type.charAt(0).toUpperCase() + type.slice(1);
    };

    if (loading) {
        return (
            <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading policy pages...</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Policy Pages Management</h3>
                <button
                    onClick={() => setEditingPolicy({
                        object_id: '',
                        page_type: 'terms',
                        title: '',
                        content: '',
                        template_variables: {},
                        is_active: true,
                        version: '1.0'
                    })}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Create Policy Page
                </button>
            </div>

            {/* Policy Pages Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {policyPages.map((policy) => (
                    <div key={policy.object_id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                        <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center">
                                <i className={`${getPolicyTypeIcon(policy.page_type)} text-blue-600 mr-3`}></i>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900">{policy.title}</h4>
                                    <p className="text-sm text-gray-600">{getPolicyTypeName(policy.page_type)}</p>
                                </div>
                            </div>
                            <div className="flex space-x-1">
                                {policy.is_active ? (
                                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        Active
                                    </span>
                                ) : (
                                    <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                        Inactive
                                    </span>
                                )}
                            </div>
                        </div>

                        <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-2">Version: {policy.version}</p>
                            <p className="text-sm text-gray-600">
                                Last updated: {new Date(policy.updated_at).toLocaleDateString()}
                            </p>
                        </div>

                        <div className="flex space-x-2">
                            <button
                                onClick={() => setEditingPolicy(policy)}
                                className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-md hover:bg-blue-100 text-sm"
                            >
                                <i className="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button
                                onClick={() => generatePreview(policy)}
                                className="flex-1 bg-gray-50 text-gray-600 px-3 py-2 rounded-md hover:bg-gray-100 text-sm"
                            >
                                <i className="fas fa-eye mr-1"></i>
                                Preview
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {policyPages.length === 0 && (
                <div className="text-center py-12">
                    <i className="fas fa-file-alt text-gray-300 text-4xl mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No policy pages found</h3>
                    <p className="text-gray-600 mb-4">Create your first policy page to get started.</p>
                    <button
                        onClick={() => setEditingPolicy({
                            object_id: '',
                            page_type: 'terms',
                            title: '',
                            content: '',
                            template_variables: {},
                            is_active: true,
                            version: '1.0'
                        })}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Create Policy Page
                    </button>
                </div>
            )}

            {/* Policy Editor Modal */}
            {editingPolicy && (
                <PolicyEditorModal
                    isOpen={!!editingPolicy}
                    onClose={() => setEditingPolicy(null)}
                    policy={editingPolicy}
                    onSave={savePolicyPage}
                    saving={saving}
                />
            )}

            {/* Preview Modal */}
            {showPreview && (
                <PolicyPreviewModal
                    isOpen={showPreview}
                    onClose={() => setShowPreview(false)}
                    content={previewContent}
                />
            )}
        </div>
    );
}

// Policy Editor Modal Component
function PolicyEditorModal({ isOpen, onClose, policy, onSave, saving }) {
    // Provide default values to prevent undefined errors
    const defaultPolicy = {
        object_id: '',
        page_type: 'terms',
        title: '',
        content: '',
        template_variables: {},
        is_active: true,
        version: '1.0',
        effective_date: new Date().toISOString()
    };

    const [formData, setFormData] = React.useState(policy || defaultPolicy);

    React.useEffect(() => {
        setFormData(policy || defaultPolicy);
    }, [policy]);

    const handleSubmit = (e) => {
        e.preventDefault();
        if (formData && onSave) {
            onSave(formData);
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    if (!isOpen || !formData) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            {policy.object_id ? 'Edit Policy Page' : 'Create Policy Page'}
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Page Type *
                                </label>
                                <select
                                    value={formData.page_type}
                                    onChange={(e) => handleInputChange('page_type', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                >
                                    <option value="terms">Terms & Conditions</option>
                                    <option value="privacy">Privacy Policy</option>
                                    <option value="refund">Refund Policy</option>
                                    <option value="cookie">Cookie Policy</option>
                                    <option value="disclaimer">Disclaimer</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Title *
                                </label>
                                <input
                                    type="text"
                                    value={formData.title}
                                    onChange={(e) => handleInputChange('title', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Content *
                            </label>
                            <textarea
                                rows={15}
                                value={formData.content}
                                onChange={(e) => handleInputChange('content', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                placeholder="Enter HTML content with template variables like {{COMPANY_NAME}}, {{COMPANY_EMAIL}}, etc."
                                required
                            />
                            <p className="text-xs text-gray-500 mt-1">
                                Use template variables like {{COMPANY_NAME}}, {{COMPANY_EMAIL}}, {{SUPPORT_EMAIL}} that will be automatically replaced with current values.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Version
                                </label>
                                <input
                                    type="text"
                                    value={formData.version}
                                    onChange={(e) => handleInputChange('version', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex items-center">
                                <label className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={formData.is_active}
                                        onChange={(e) => handleInputChange('is_active', e.target.checked)}
                                        className="mr-2"
                                    />
                                    <span className="text-sm text-gray-700">Active</span>
                                </label>
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                disabled={saving}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                disabled={saving}
                            >
                                {saving ? (
                                    <>
                                        <i className="fas fa-spinner fa-spin mr-2"></i>
                                        Saving...
                                    </>
                                ) : (
                                    policy.object_id ? 'Update Policy' : 'Create Policy'
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

// Policy Preview Modal Component
function PolicyPreviewModal({ isOpen, onClose, content }) {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">Policy Preview</h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-6 max-h-96 overflow-y-auto">
                        <div 
                            className="prose max-w-none"
                            dangerouslySetInnerHTML={{ __html: content }}
                        />
                    </div>

                    <div className="flex justify-end mt-6">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make components globally available
window.PolicyPagesManager = PolicyPagesManager;
window.PolicyEditorModal = PolicyEditorModal;
window.PolicyPreviewModal = PolicyPreviewModal;
