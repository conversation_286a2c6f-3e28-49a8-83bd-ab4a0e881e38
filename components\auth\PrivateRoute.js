function PrivateRoute({ children }) {
    try {
        const { user, loading } = useAuth();
        
        if (loading) {
            return (
                <div className="flex justify-center items-center h-screen">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }
        
        if (!user) {
            window.location.href = '/login';
            return null;
        }
        
        return children;
    } catch (error) {
        console.error('PrivateRoute component error:', error);
        reportError(error);
        return null;
    }
}
