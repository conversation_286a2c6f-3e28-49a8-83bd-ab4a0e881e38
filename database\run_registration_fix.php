<?php
/**
 * Run registration schema fixes
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🔧 Fixing registration database schema...\n";

try {
    // Read and execute the migration
    $sql = file_get_contents(__DIR__ . '/migrations/fix_registration_schema.sql');
    
    // Split by semicolon and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            if ($conn->query($statement) === TRUE) {
                echo "✅ Executed: " . substr($statement, 0, 50) . "...\n";
            } else {
                echo "❌ Error: " . $conn->error . "\n";
                echo "Statement: " . $statement . "\n";
            }
        }
    }
    
    // Check if pricing_plans table has data
    $result = $conn->query("SELECT COUNT(*) as count FROM pricing_plans");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "\n📋 Pricing plans in database: {$row['count']}\n";
    }
    
    // Show companies table structure
    $result = $conn->query("DESCRIBE companies");
    if ($result) {
        echo "\n📋 Companies table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo "   - {$row['Field']}: {$row['Type']}\n";
        }
    }
    
    echo "\n🎉 Registration schema fix completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

$conn->close();
?>
