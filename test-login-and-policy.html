<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login and Policy Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 10px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .form-group { margin: 10px 0; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>Login and Policy Test</h1>
    
    <div class="form-group">
        <label>Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label>Password:</label>
        <input type="password" id="password" value="admin123">
    </div>
    
    <button onclick="login()">Login</button>
    <button onclick="testPolicyAPI()">Test Policy API</button>
    <button onclick="clearAuth()">Clear Auth</button>
    
    <div id="results"></div>

    <script src="config.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            log('Attempting login...');
            
            try {
                const response = await fetch(window.getApiUrl('/auth'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });

                log('Login response status: ' + response.status);
                
                const data = await response.json();
                log('Login response: ' + JSON.stringify(data, null, 2));
                
                if (response.ok && data.success) {
                    // Store authentication data
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    log('Login successful! Token stored.', 'success');
                    log('User: ' + data.user.name + ' (' + data.user.role + ')', 'success');
                } else {
                    log('Login failed: ' + (data.message || data.error), 'error');
                }
                
            } catch (error) {
                log('Login error: ' + error.message, 'error');
            }
        }

        async function testPolicyAPI() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                log('No auth token found. Please login first.', 'error');
                return;
            }

            log('Testing Policy API...');

            try {
                const response = await fetch(window.getApiUrl('/super-admin/policy-pages'), {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log('Policy API Response Status: ' + response.status);
                
                const data = await response.json();
                log('Policy API Response: ' + JSON.stringify(data, null, 2), response.ok ? 'success' : 'error');
                
            } catch (error) {
                log('Policy API Error: ' + error.message, 'error');
            }
        }

        function clearAuth() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            log('Authentication cleared', 'success');
        }

        // Check current auth status on page load
        window.onload = function() {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('user');
            
            if (token && user) {
                log('Already logged in as: ' + JSON.parse(user).name, 'success');
            } else {
                log('Not logged in');
            }
        };
    </script>
</body>
</html>
