<?php
echo "<h1>Simple Login Test</h1>";

$loginData = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

$loginContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => json_encode($loginData),
        'timeout' => 10
    ]
]);

$loginResponse = @file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=login", false, $loginContext);

if ($loginResponse === false) {
    echo "❌ Failed to connect to login endpoint<br>";
    echo "Error: " . error_get_last()['message'] . "<br>";
} else {
    echo "Raw response: " . htmlspecialchars($loginResponse) . "<br><br>";
    
    $loginResult = json_decode($loginResponse, true);
    
    if ($loginResult === null) {
        echo "❌ Failed to parse JSON response<br>";
        echo "JSON Error: " . json_last_error_msg() . "<br>";
    } else {
        echo "Parsed response:<br>";
        echo "<pre>" . print_r($loginResult, true) . "</pre>";
    }
}
?>