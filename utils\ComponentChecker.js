/**
 * Component Checker Utility
 * Checks if all required components are loaded and provides fallbacks
 */

window.ComponentChecker = {
    /**
     * Check if a component is available
     * @param {string} componentName - Name of the component to check
     * @returns {boolean} - True if component is available
     */
    isAvailable: function(componentName) {
        return typeof window[componentName] !== 'undefined';
    },
    
    /**
     * Get a component or its fallback
     * @param {string} componentName - Name of the component to get
     * @param {Function} fallback - Fallback component to use if not available
     * @returns {Function} - The component or its fallback
     */
    getComponent: function(componentName, fallback) {
        return window[componentName] || fallback;
    },
    
    /**
     * Check if all required components are loaded
     * @returns {Object} - Object with status and missing components
     */
    checkRequiredComponents: function() {
        const requiredComponents = [
            'LoadingSpinner',
            'ErrorMessage',
            'NotificationContainer',
            'ApiClient',
            'ValidationUtils'
        ];
        
        const missing = requiredComponents.filter(comp => !this.isAvailable(comp));
        
        return {
            allLoaded: missing.length === 0,
            missing: missing
        };
    },
    
    /**
     * Log component loading status
     */
    logStatus: function() {
        const status = this.checkRequiredComponents();
        
        if (status.allLoaded) {
            console.log('✅ All required components are loaded');
        } else {
            console.warn('⚠️ Some components are missing:', status.missing);
        }
    }
};

// Add to main.js utilities list
if (typeof utilityScripts !== 'undefined') {
    const basePath = window.APP_CONFIG ? window.APP_CONFIG.BASE_PATH : '/biz';
    utilityScripts.push(basePath + '/utils/ComponentChecker.js');
}

// Log component status when loaded
window.addEventListener('utilities-loaded', function() {
    if (window.ComponentChecker) {
        window.ComponentChecker.logStatus();
    }
});