<?php
/**
 * Test login endpoint directly
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'error' => 'Users table does not exist',
            'suggestion' => 'Run setup-database.php first'
        ]);
        exit;
    }
    
    // Check if there are any users
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    $row = $result->fetch_assoc();
    $userCount = $row['count'];
    
    if ($userCount == 0) {
        // Create a test user
        $userId = 'user_' . time() . '_test';
        $passwordHash = password_hash('test123', PASSWORD_DEFAULT);
        $token = bin2hex(random_bytes(32));
        $tokenExpires = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        $sql = "INSERT INTO users (object_id, name, email, password_hash, auth_token, token_expires, status, role) VALUES (?, 'Test User', '<EMAIL>', ?, ?, ?, 'active', 'admin')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $userId, $passwordHash, $token, $tokenExpires);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Test user created successfully',
                'credentials' => [
                    'email' => '<EMAIL>',
                    'password' => 'test123'
                ],
                'user_count' => 1
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Failed to create test user: ' . $conn->error
            ]);
        }
    } else {
        // Show existing users (without passwords)
        $result = $conn->query("SELECT object_id, name, email, role, status FROM users LIMIT 5");
        $users = [];
        while ($row = $result->fetch_assoc()) {
            $users[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Users exist in database',
            'user_count' => $userCount,
            'sample_users' => $users
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
