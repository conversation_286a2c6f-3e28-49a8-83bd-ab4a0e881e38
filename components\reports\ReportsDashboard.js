function ReportsDashboard() {
    try {
        const [reportData, setReportData] = React.useState({
            revenue: {
                total: 0,
                monthly: {}
            },
            customers: {
                total: 0,
                byType: {}
            },
            contracts: {
                total: 0,
                active: 0,
                byType: {}
            },
            invoices: {
                total: 0,
                paid: 0,
                overdue: 0,
                totalValue: 0
            }
        });
        const [loading, setLoading] = React.useState(true);
        const [dateRange, setDateRange] = React.useState('month');

        React.useEffect(() => {
            fetchReportData();
        }, [dateRange]);

        const fetchReportData = async () => {
            try {
                setLoading(true);
                const customersResponse = await trickleListObjects('customer', 100, true);
                const contractsResponse = await trickleListObjects('contract', 100, true);
                const invoicesResponse = await trickleListObjects('invoice', 100, true);

                setReportData({
                    revenue: processRevenueData(invoicesResponse.items),
                    customers: processCustomerData(customersResponse.items),
                    contracts: processContractData(contractsResponse.items),
                    invoices: processInvoiceData(invoicesResponse.items)
                });
            } catch (error) {
                console.error('Error fetching report data:', error);
            } finally {
                setLoading(false);
            }
        };

        const processRevenueData = (invoices) => {
            const paidInvoices = invoices.filter(invoice => invoice.objectData.status === 'paid');
            let totalRevenue = 0;
            let monthlyRevenue = {};

            paidInvoices.forEach(invoice => {
                totalRevenue += invoice.objectData.total;
                const month = new Date(invoice.objectData.paidAt).toLocaleString('default', { month: 'long' });
                monthlyRevenue[month] = (monthlyRevenue[month] || 0) + invoice.objectData.total;
            });

            return {
                total: totalRevenue,
                monthly: monthlyRevenue
            };
        };

        const processCustomerData = (customers) => {
            return {
                total: customers.length,
                byType: customers.reduce((acc, customer) => {
                    const type = customer.objectData.type;
                    acc[type] = (acc[type] || 0) + 1;
                    return acc;
                }, {})
            };
        };

        const processContractData = (contracts) => {
            return {
                total: contracts.length,
                active: contracts.filter(contract => contract.objectData.status === 'signed').length,
                byType: contracts.reduce((acc, contract) => {
                    const type = contract.objectData.type;
                    acc[type] = (acc[type] || 0) + 1;
                    return acc;
                }, {})
            };
        };

        const processInvoiceData = (invoices) => {
            return {
                total: invoices.length,
                paid: invoices.filter(invoice => invoice.objectData.status === 'paid').length,
                overdue: invoices.filter(invoice => invoice.objectData.status === 'overdue').length,
                totalValue: invoices.reduce((total, invoice) => total + invoice.objectData.total, 0)
            };
        };

        return (
            <div data-name="reports-dashboard" className="reports-dashboard">
                <div className="reports-header">
                    <h2>Reports Dashboard</h2>
                    <select
                        value={dateRange}
                        onChange={(e) => setDateRange(e.target.value)}
                        className="date-range-selector"
                    >
                        <option value="month">Last Month</option>
                        <option value="quarter">Last Quarter</option>
                        <option value="year">Last Year</option>
                    </select>
                </div>

                {loading ? (
                    <div className="reports-loading">
                        <i className="fas fa-spinner loading-spinner"></i>
                    </div>
                ) : (
                    <div className="reports-grid">
                        <div className="report-card">
                            <h3>Revenue Overview</h3>
                            <div className="report-metric">
                                <span className="report-metric-label">Total Revenue</span>
                                <span className="report-metric-value currency">{formatCurrency(reportData.revenue.total)}</span>
                            </div>
                            <div className="type-breakdown">
                                <h4>Monthly Revenue</h4>
                                <div className="revenue-chart">
                                    {Object.entries(reportData.revenue.monthly).map(([month, amount]) => (
                                        <div key={month} className="revenue-month">
                                            <span className="revenue-month-name">{month}</span>
                                            <span className="revenue-month-amount">{formatCurrency(amount)}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        <div className="report-card">
                            <h3>Customer Statistics</h3>
                            <div className="report-metric">
                                <span className="report-metric-label">Total Customers</span>
                                <span className="report-metric-value">{reportData.customers.total}</span>
                            </div>
                            <div className="type-breakdown">
                                <h4>By Type</h4>
                                {Object.entries(reportData.customers.byType).map(([type, count]) => (
                                    <div key={type} className="type-item">
                                        <span className="type-name">{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                        <span className="type-count">{count}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="report-card">
                            <h3>Contract Analytics</h3>
                            <div className="stats-grid">
                                <div className="stat-item">
                                    <span className="stat-value">{reportData.contracts.total}</span>
                                    <span className="stat-label">Total Contracts</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-value">{reportData.contracts.active}</span>
                                    <span className="stat-label">Active Contracts</span>
                                </div>
                            </div>
                            <div className="type-breakdown">
                                <h4>By Type</h4>
                                {Object.entries(reportData.contracts.byType).map(([type, count]) => (
                                    <div key={type} className="type-item">
                                        <span className="type-name">{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                        <span className="type-count">{count}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="report-card">
                            <h3>Invoice Summary</h3>
                            <div className="stats-grid">
                                <div className="stat-item">
                                    <span className="stat-value">{reportData.invoices.total}</span>
                                    <span className="stat-label">Total Invoices</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-value">{reportData.invoices.paid}</span>
                                    <span className="stat-label">Paid Invoices</span>
                                </div>
                            </div>
                            <div className="report-metric">
                                <span className="report-metric-label">Overdue Invoices</span>
                                <span className="report-metric-value danger">{reportData.invoices.overdue}</span>
                            </div>
                            <div className="report-metric">
                                <span className="report-metric-label">Total Value</span>
                                <span className="report-metric-value currency">{formatCurrency(reportData.invoices.totalValue)}</span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('ReportsDashboard component error:', error);
        reportError(error);
        return null;
    }
}
