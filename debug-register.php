<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    echo json_encode(['debug' => 'Starting registration process']);
    
    // Check if required files exist
    $configPath = __DIR__ . '/config/config.php';
    $securityPath = __DIR__ . '/utils/SecurityUtils.php';
    $emailPath = __DIR__ . '/utils/EmailUtils.php';

    echo json_encode(['debug' => 'Current directory', 'dir' => __DIR__]);
    
    echo json_encode([
        'debug' => 'File checks',
        'config_exists' => file_exists($configPath),
        'security_exists' => file_exists($securityPath),
        'email_exists' => file_exists($emailPath)
    ]);
    
    if (!file_exists($configPath)) {
        throw new Exception('Config file not found at: ' . $configPath);
    }
    
    require_once $configPath;
    
    if (!file_exists($securityPath)) {
        throw new Exception('SecurityUtils file not found at: ' . $securityPath);
    }
    
    require_once $securityPath;
    
    if (!file_exists($emailPath)) {
        throw new Exception('EmailUtils file not found at: ' . $emailPath);
    }
    
    require_once $emailPath;
    
    echo json_encode(['debug' => 'Files loaded successfully']);
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit;
    }
    
    $rawInput = file_get_contents('php://input');
    echo json_encode(['debug' => 'Raw input', 'input' => $rawInput]);
    
    $input = json_decode($rawInput, true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input. Raw input: ' . $rawInput);
    }
    
    echo json_encode(['debug' => 'Parsed input', 'data' => $input]);
    
    // Validate required fields
    $required_fields = ['name', 'email', 'password', 'company_name'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        throw new Exception('Missing required fields: ' . implode(', ', $missing_fields));
    }
    
    echo json_encode(['debug' => 'All required fields present']);
    
    // Test Config class
    if (!class_exists('Config')) {
        throw new Exception('Config class not found');
    }
    
    Config::load();
    echo json_encode(['debug' => 'Config loaded']);
    
    // Test SecurityUtils class
    if (!class_exists('SecurityUtils')) {
        throw new Exception('SecurityUtils class not found');
    }
    
    echo json_encode(['debug' => 'SecurityUtils available']);
    
    echo json_encode(['success' => true, 'message' => 'Debug completed successfully']);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'error' => 'Fatal error: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
