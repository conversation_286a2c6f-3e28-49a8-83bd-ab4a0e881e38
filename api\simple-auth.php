<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $action = $input['action'] ?? '';

    if ($action !== 'login') {
        throw new Exception('Invalid action');
    }

    // Validate required fields
    if (empty($input['email']) || empty($input['password'])) {
        throw new Exception('Email and password are required');
    }

    $email = trim($input['email']);
    $password = $input['password'];
    $remember_me = $input['remember_me'] ?? false;

    // Connect to database directly
    $conn = new mysqli('localhost', 'root', '', 'business_saas');
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }

    // Get user information (simplified query)
    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('Invalid email or password');
    }

    $user = $result->fetch_assoc();

    // Verify password
    if (!password_verify($password, $user['password_hash'])) {
        throw new Exception('Invalid email or password');
    }
    
    // Generate simple token
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + ($remember_me ? 30 * 24 * 3600 : 24 * 3600));
    
    // Update user with token and last login
    $update_stmt = $conn->prepare("UPDATE users SET auth_token = ?, token_expires = ?, last_login = NOW() WHERE id = ?");
    $update_stmt->bind_param("ssi", $token, $expires, $user['id']);
    $update_stmt->execute();
    
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'token' => $token,
        'user' => [
            'id' => $user['object_id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'company_id' => $user['company_id'],
            'company_name' => null, // Simplified for now
            'role' => $user['role'],
            'email_verified' => (bool)$user['email_verified']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Simple auth error: " . $e->getMessage());
}
?>
