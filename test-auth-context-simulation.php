<?php
echo "<h1>Auth Context Simulation Test</h1>";

// Step 1: Login to get a fresh token
echo "<h2>Step 1: Login</h2>";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

$loginContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => json_encode($loginData),
        'timeout' => 10
    ]
]);

$loginResponse = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=login", false, $loginContext);
$loginResult = json_decode($loginResponse, true);

if ($loginResult['success']) {
    $token = $loginResult['tokens']['access_token'];
    echo "✅ Login successful<br>";
    echo "Token: " . substr($token, 0, 20) . "...<br><br>";
    
    // Step 2: Simulate exactly what AuthContext does
    echo "<h2>Step 2: Simulate AuthContext Request</h2>";
    
    $verifyContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\nAuthorization: Bearer $token\r\n",
            'content' => json_encode(['token' => $token]),
            'timeout' => 10
        ]
    ]);
    
    $verifyResponse = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=verify", false, $verifyContext);
    echo "Raw response: " . htmlspecialchars($verifyResponse) . "<br><br>";
    
    $verifyResult = json_decode($verifyResponse, true);
    
    if ($verifyResult && $verifyResult['success'] && $verifyResult['user']) {
        echo "✅ AuthContext simulation successful<br>";
        echo "User: " . $verifyResult['user']['name'] . " (" . $verifyResult['user']['email'] . ")<br>";
        echo "Role: " . $verifyResult['user']['role'] . "<br>";
        echo "Company: " . $verifyResult['user']['company_name'] . "<br>";
    } else if ($verifyResult && $verifyResult['success'] === false) {
        echo "❌ AuthContext simulation failed: " . $verifyResult['error'] . "<br>";
    } else {
        echo "❌ Invalid response structure<br>";
        echo "Response: " . print_r($verifyResult, true) . "<br>";
    }
    
} else {
    echo "❌ Login failed: " . $loginResult['error'] . "<br>";
}

// Step 3: Test with a fresh browser session simulation
echo "<h2>Step 3: Store Token in 'localStorage' Simulation</h2>";
echo "<script>";
echo "localStorage.setItem('authToken', '$token');";
echo "console.log('Token stored in localStorage:', localStorage.getItem('authToken'));";
echo "</script>";
echo "Token stored in localStorage simulation<br>";
echo "You can now test the AuthContext in the browser with this token.<br>";
?>