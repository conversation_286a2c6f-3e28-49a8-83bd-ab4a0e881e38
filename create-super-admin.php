<?php
/**
 * Create Super Admin User
 * This script creates a super admin user for testing purposes
 */

require_once 'api/db-config.php';

try {
    // Check if super admin already exists
    $checkSql = "SELECT * FROM users WHERE role = 'super_admin' LIMIT 1";
    $result = $conn->query($checkSql);
    
    if ($result && $result->num_rows > 0) {
        $existingAdmin = $result->fetch_assoc();
        echo "✅ Super Admin already exists:\n";
        echo "Name: " . $existingAdmin['name'] . "\n";
        echo "Email: " . $existingAdmin['email'] . "\n";
        echo "Role: " . $existingAdmin['role'] . "\n";
        echo "Object ID: " . $existingAdmin['object_id'] . "\n\n";
        
        // Update password to a known value for testing
        $testPassword = 'admin123';
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);

        $updateSql = "UPDATE users SET password_hash = ? WHERE object_id = ?";
        $stmt = $conn->prepare($updateSql);
        $stmt->bind_param("ss", $hashedPassword, $existingAdmin['object_id']);
        
        if ($stmt->execute()) {
            echo "🔑 Password updated to: $testPassword\n";
        } else {
            echo "❌ Failed to update password\n";
        }
    } else {
        // Create new super admin user
        $objectId = 'user_' . uniqid();
        $name = 'Super Admin';
        $email = '<EMAIL>';
        $password = 'admin123';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $role = 'super_admin';
        $status = 'active';
        
        $sql = "INSERT INTO users (object_id, name, email, password_hash, role, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssss", $objectId, $name, $email, $hashedPassword, $role, $status);
        
        if ($stmt->execute()) {
            echo "✅ Super Admin user created successfully!\n";
            echo "Name: $name\n";
            echo "Email: $email\n";
            echo "Password: $password\n";
            echo "Role: $role\n";
            echo "Object ID: $objectId\n";
        } else {
            echo "❌ Failed to create super admin user: " . $conn->error . "\n";
        }
    }
    
    // Create a test token for API testing
    $tokenSql = "SELECT object_id FROM users WHERE role = 'super_admin' LIMIT 1";
    $result = $conn->query($tokenSql);
    
    if ($result && $result->num_rows > 0) {
        $admin = $result->fetch_assoc();
        $testToken = 'test_super_admin_' . $admin['object_id'];
        
        // Store test token in a simple way (for testing only)
        $tokenData = json_encode([
            'user_id' => $admin['object_id'],
            'role' => 'super_admin',
            'expires' => time() + (24 * 60 * 60) // 24 hours
        ]);
        
        // Create tokens table if it doesn't exist
        $createTokensTable = "CREATE TABLE IF NOT EXISTS test_tokens (
            token VARCHAR(255) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            data TEXT NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createTokensTable);
        
        // Insert test token
        $insertToken = "INSERT INTO test_tokens (token, user_id, data, expires_at) 
                       VALUES (?, ?, ?, FROM_UNIXTIME(?))
                       ON DUPLICATE KEY UPDATE 
                       data = VALUES(data), expires_at = VALUES(expires_at)";
        $stmt = $conn->prepare($insertToken);
        $expires = time() + (24 * 60 * 60);
        $stmt->bind_param("sssi", $testToken, $admin['object_id'], $tokenData, $expires);
        $stmt->execute();
        
        echo "\n🔑 Test Token Created: $testToken\n";
        echo "Use this token for API testing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✨ Setup completed.\n";
?>
