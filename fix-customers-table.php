<?php
/**
 * Fix Customers Table Schema
 * Add missing columns and fix any schema issues
 */

require_once 'api/db-config.php';

echo "🔧 Fixing customers table schema...\n\n";

try {
    // Check current table structure
    $result = $conn->query("DESCRIBE customers");
    $existingColumns = [];
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $existingColumns[] = $row['Field'];
        }
        echo "📋 Current columns: " . implode(', ', $existingColumns) . "\n\n";
    }
    
    // Define required columns that might be missing
    $requiredColumns = [
        'status' => "ADD COLUMN status ENUM('active','inactive') DEFAULT 'active'"
    ];
    
    // Add missing columns
    foreach ($requiredColumns as $column => $alterStatement) {
        if (!in_array($column, $existingColumns)) {
            echo "➕ Adding column: $column\n";
            $sql = "ALTER TABLE customers $alterStatement";
            
            if ($conn->query($sql)) {
                echo "✅ Successfully added column: $column\n";
            } else {
                echo "❌ Failed to add column $column: " . $conn->error . "\n";
            }
        } else {
            echo "✓ Column $column already exists\n";
        }
    }
    
    // Fix type column to be ENUM instead of VARCHAR
    echo "\n🔧 Updating type column to ENUM...\n";
    $sql = "ALTER TABLE customers MODIFY COLUMN type ENUM('individual','business') DEFAULT 'individual'";
    if ($conn->query($sql)) {
        echo "✅ Updated type column to ENUM\n";
    } else {
        echo "❌ Failed to update type column: " . $conn->error . "\n";
    }
    
    // Add indexes for new columns
    $indexes = [
        'idx_customers_status' => "CREATE INDEX idx_customers_status ON customers(status)",
        'idx_customers_type' => "CREATE INDEX idx_customers_type ON customers(type)"
    ];
    
    echo "\n📊 Adding indexes...\n";
    foreach ($indexes as $indexName => $indexSql) {
        // Check if index exists
        $checkIndex = $conn->query("SHOW INDEX FROM customers WHERE Key_name = '$indexName'");
        if ($checkIndex->num_rows == 0) {
            if ($conn->query($indexSql)) {
                echo "✅ Added index: $indexName\n";
            } else {
                echo "❌ Failed to add index $indexName: " . $conn->error . "\n";
            }
        } else {
            echo "✓ Index $indexName already exists\n";
        }
    }
    
    // Show final table structure
    echo "\n📋 Final table structure:\n";
    $result = $conn->query("DESCRIBE customers");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Default']}\n";
        }
    }
    
    echo "\n✅ Customers table schema fixed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
