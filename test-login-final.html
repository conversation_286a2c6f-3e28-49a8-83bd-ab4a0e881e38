<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: bold; color: #333; }
        input[type="email"], input[type="password"] { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 16px; }
        input[type="email"]:focus, input[type="password"]:focus { border-color: #007bff; outline: none; }
        .checkbox-group { display: flex; align-items: center; gap: 8px; }
        button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; width: 100%; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin-top: 20px; padding: 15px; border-radius: 6px; white-space: pre-wrap; }
        .success { background: #d4edda; border: 2px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 2px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 2px solid #bee5eb; color: #0c5460; }
        .token-display { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; word-break: break-all; margin-top: 10px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Login Test - Final Version</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="BhaviGani@56" required>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="rememberMe">
                    <label for="rememberMe">Remember Me</label>
                </div>
            </div>
            
            <button type="submit" id="loginBtn">Sign In</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            const resultDiv = document.getElementById('result');
            const loginBtn = document.getElementById('loginBtn');
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtn.textContent = 'Signing In...';
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = '<span class="status-indicator status-loading"></span>Authenticating...';
            
            try {
                console.log('🔐 Starting login process...');
                console.log('📧 Email:', email);
                console.log('🔒 Remember Me:', rememberMe);
                
                const startTime = Date.now();
                
                const response = await fetch('/biz/api/api.php?endpoint=auth&action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        rememberMe: rememberMe
                    })
                });
                
                const responseTime = Date.now() - startTime;
                console.log('⏱️ Response time:', responseTime + 'ms');
                console.log('📊 Response status:', response.status);
                
                const responseText = await response.text();
                console.log('📄 Raw response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`JSON Parse Error: ${parseError.message}\n\nRaw Response:\n${responseText}`);
                }
                
                console.log('✅ Parsed response:', data);
                
                if (data.success && data.tokens && data.tokens.access_token) {
                    // Success!
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <span class="status-indicator status-success"></span><strong>Login Successful!</strong>
                        
                        <strong>User Information:</strong>
                        • Name: ${data.user.name}
                        • Email: ${data.user.email}
                        • Role: ${data.user.role}
                        • Company: ${data.user.company_name || 'N/A'}
                        
                        <strong>Authentication:</strong>
                        • Response Time: ${responseTime}ms
                        • Token Type: Enhanced Auth Handler
                        • Expires In: ${data.tokens.expires_in} seconds
                        
                        <div class="token-display">
                            <strong>Access Token:</strong>
                            ${data.tokens.access_token.substring(0, 50)}...
                        </div>
                    `;
                    
                    // Store tokens for testing
                    localStorage.setItem('authToken', data.tokens.access_token);
                    if (rememberMe && data.tokens.refresh_token) {
                        localStorage.setItem('refreshToken', data.tokens.refresh_token);
                        localStorage.setItem('rememberMe', 'true');
                    }
                    
                    console.log('💾 Tokens stored in localStorage');
                    
                } else {
                    // Login failed
                    throw new Error(data.error || data.message || 'Login failed - unknown error');
                }
                
            } catch (error) {
                console.error('❌ Login error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <span class="status-indicator status-error"></span><strong>Login Failed</strong>
                    
                    <strong>Error Details:</strong>
                    ${error.message}
                    
                    <strong>Troubleshooting:</strong>
                    • Check if the user exists in the database
                    • Verify the password is correct
                    • Ensure the API endpoint is accessible
                    • Check browser console for more details
                `;
            } finally {
                // Reset button
                loginBtn.disabled = false;
                loginBtn.textContent = 'Sign In';
            }
        });
        
        // Test token on page load
        window.addEventListener('load', function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                console.log('🔑 Found existing token:', token.substring(0, 20) + '...');
            }
        });
    </script>
</body>
</html>
