<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Syntax Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script>
        // Mock functions
        window.getApiUrl = (endpoint) => `http://localhost/biz/api/api.php${endpoint}`;
        
        // Mock AuthContext
        const AuthContext = React.createContext();
        
        function TestApp() {
            const [testResult, setTestResult] = React.useState('Loading...');
            
            React.useEffect(() => {
                try {
                    // Try to load the SuperAdminDashboard script
                    const script = document.createElement('script');
                    script.type = 'text/babel';
                    script.src = '/biz/pages/SuperAdminDashboard.js';
                    script.onload = () => {
                        setTestResult('✅ SuperAdminDashboard.js loaded successfully - No syntax errors!');
                    };
                    script.onerror = (error) => {
                        setTestResult('❌ Failed to load SuperAdminDashboard.js: ' + error.message);
                    };
                    document.head.appendChild(script);
                } catch (error) {
                    setTestResult('❌ Syntax error: ' + error.message);
                }
            }, []);
            
            return (
                <div className="min-h-screen flex items-center justify-center">
                    <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
                        <h1 className="text-2xl font-bold text-gray-900 mb-4">
                            <i className="fas fa-code mr-2 text-blue-600"></i>
                            Dashboard Syntax Test
                        </h1>
                        <div className="p-4 bg-gray-50 rounded-lg">
                            <p className="text-sm">{testResult}</p>
                        </div>
                        <div className="mt-4 text-xs text-gray-500">
                            This test checks if the SuperAdminDashboard.js file has any syntax errors.
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>