// Import and re-export all PDF utility functions
function exportToPDF(element, filename, options = {}) {
    try {
        const html2pdf = window.html2pdf;
        if (!html2pdf) {
            console.error('html2pdf library not loaded');
            return false;
        }

        const defaultOptions = {
            margin: 0,
            filename: filename || 'document.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { 
                scale: 2,
                useCORS: true,
                letterRendering: true,
                logging: false
            },
            jsPDF: { 
                unit: 'mm', 
                format: options.format || 'a4', 
                orientation: options.orientation || 'portrait' 
            },
            pagebreak: { mode: 'avoid-all' }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        if (options.html2canvas) {
            mergedOptions.html2canvas = { ...defaultOptions.html2canvas, ...options.html2canvas };
        }
        
        if (options.jsPDF) {
            mergedOptions.jsPDF = { ...defaultOptions.jsPDF, ...options.jsPDF };
        }

        const clonedElement = element.cloneNode(true);
        const styleElement = document.createElement('style');
        styleElement.textContent = getPDFStyles();
        
        clonedElement.appendChild(styleElement);
        
        const container = document.createElement('div');
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.top = '-9999px';
        container.appendChild(clonedElement);
        document.body.appendChild(container);

        html2pdf()
            .from(clonedElement)
            .set(mergedOptions)
            .save();
            
        setTimeout(() => {
            document.body.removeChild(container);
        }, 100);
        
        return true;
    } catch (error) {
        console.error('Error generating PDF:', error);
        return false;
    }
}

function getPDFStyles() {
    return `
        .template-container {
            box-shadow: none !important;
            padding: 15mm !important;
            max-width: 100% !important;
            width: 210mm !important;
            min-height: 297mm !important;
            margin: 0 !important;
            font-size: 10pt !important;
        }
        .template-container::before {
            display: none !important;
        }
        .company-logo {
            max-height: 50px !important;
        }
        .signature-image {
            max-height: 40px !important;
        }
        .items-table th, .items-table td {
            padding: 4px 8px !important;
            font-size: 9pt !important;
        }
        .info-section {
            padding: 8px !important;
            margin-bottom: 10px !important;
        }
        .signature-section {
            margin-top: 15px !important;
        }
        .template-footer {
            margin-top: 15px !important;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        table th {
            background-color: #f3f4f6 !important;
            color: #374151;
            font-weight: 600;
            text-align: left;
            padding: 0.5rem 0.75rem;
        }
        
        table td {
            border-bottom: 1px solid #e5e7eb;
            padding: 0.5rem 0.75rem;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-paid, .status-accepted, .status-signed {
            background-color: #d1fae5 !important;
            color: #065f46 !important;
        }
        
        .status-overdue, .status-rejected {
            background-color: #fee2e2 !important;
            color: #991b1b !important;
        }
        
        .status-draft {
            background-color: #f3f4f6 !important;
            color: #4b5563 !important;
        }
        
        .status-sent, .status-pending {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }
        
        .watermark {
            opacity: 0.07 !important;
            font-size: 4rem !important;
        }
    `;
}

function printDocumentAdvanced(element, options = {}) {
    try {
        const defaultOptions = {
            title: 'Print Document',
            hideControls: false,
            scaleFactor: 1.0
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        const clonedElement = element.cloneNode(true);
        const styleElement = document.createElement('style');
        styleElement.textContent = getPrintStyles(mergedOptions);
        clonedElement.appendChild(styleElement);
        
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!printWindow) {
            alert('Please allow pop-ups for printing functionality');
            return false;
        }
        
        printWindow.document.open();
        printWindow.document.write(getPrintWindowHTML(mergedOptions));
        printWindow.document.close();
        
        printWindow.onload = function() {
            const printContent = printWindow.document.getElementById('print-content');
            printContent.appendChild(clonedElement);
            
            if (mergedOptions.hideControls) {
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            }
        };
        
        return true;
    } catch (error) {
        console.error('Error printing document:', error);
        return false;
    }
}

function getPrintStyles(options) {
    return `
        @media print {
            @page {
                size: ${options.paperSize || 'A4'} ${options.orientation || 'portrait'};
                margin: 0;
            }
            
            body {
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .template-container {
                box-shadow: none;
                border-radius: 0;
                padding: 15mm;
                width: 210mm;
                min-height: 297mm;
                margin: 0;
                font-size: ${options.scaleFactor * 10}pt;
                transform: scale(${options.scaleFactor});
                transform-origin: top left;
            }
            
            .template-container::before {
                display: none;
            }
            
            .items-table,
            .totals-section,
            .signature-section,
            .notes-section {
                page-break-inside: avoid;
            }
            
            .items-table th {
                background-color: #f3f4f6 !important;
            }
            
            .status-paid, .status-accepted, .status-signed {
                background-color: #d1fae5 !important;
            }
            
            .status-overdue, .status-rejected {
                background-color: #fee2e2 !important;
            }
            
            .status-draft {
                background-color: #f3f4f6 !important;
            }
            
            .status-sent, .status-pending {
                background-color: #dbeafe !important;
            }
            
            ${options.hideControls ? `
                @page {
                    margin: 0;
                }
                
                html, body {
                    height: 100%;
                    overflow: hidden;
                }
            ` : ''}
        }
    `;
}

function getPrintWindowHTML(options) {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>${options.title}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    background-color: #f3f4f6;
                }
                
                .print-container {
                    padding: 20px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }
                
                .print-controls {
                    margin-bottom: 20px;
                    background-color: white;
                    padding: 10px 20px;
                    border-radius: 8px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    justify-content: space-between;
                    width: 100%;
                    max-width: 800px;
                }
                
                .print-button {
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 500;
                }
                
                .close-button {
                    background-color: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 500;
                }
                
                @media print {
                    .print-controls {
                        display: none;
                    }
                    
                    .print-container {
                        padding: 0;
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-container">
                ${!options.hideControls ? `
                <div class="print-controls">
                    <h2>${options.title}</h2>
                    <div>
                        <button class="print-button" onclick="window.print()">Print</button>
                        <button class="close-button" onclick="window.close()">Close</button>
                    </div>
                </div>
                ` : ''}
                <div id="print-content"></div>
            </div>
        </body>
        </html>
    `;
}

function printFormattedTable(tableElement, title = 'Table Data') {
    try {
        if (!tableElement) {
            console.error('No table element provided');
            return false;
        }
        
        const clonedTable = tableElement.cloneNode(true);
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!printWindow) {
            alert('Please allow pop-ups for printing functionality');
            return false;
        }
        
        printWindow.document.open();
        printWindow.document.write(getTablePrintTemplate(title));
        printWindow.document.close();
        
        printWindow.onload = function() {
            const tableContainer = printWindow.document.getElementById('table-container');
            tableContainer.appendChild(clonedTable);
        };
        
        return true;
    } catch (error) {
        console.error('Error printing table:', error);
        return false;
    }
}

function getTablePrintTemplate(title) {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>${title}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                }
                
                h1 {
                    font-size: 18px;
                    margin-bottom: 20px;
                    text-align: center;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }
                
                table th {
                    background-color: #f3f4f6;
                    padding: 10px;
                    text-align: left;
                    font-weight: bold;
                    border-bottom: 2px solid #e5e7eb;
                }
                
                table td {
                    padding: 8px 10px;
                    border-bottom: 1px solid #e5e7eb;
                }
                
                table tr:nth-child(even) {
                    background-color: #f9fafb;
                }
                
                .print-controls {
                    margin-bottom: 20px;
                    text-align: right;
                }
                
                .print-button {
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 500;
                    margin-right: 10px;
                }
                
                .close-button {
                    background-color: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 500;
                }
                
                @media print {
                    .print-controls {
                        display: none;
                    }
                    
                    body {
                        padding: 0;
                    }
                    
                    table {
                        page-break-inside: auto;
                    }
                    
                    tr {
                        page-break-inside: avoid;
                        page-break-after: auto;
                    }
                    
                    thead {
                        display: table-header-group;
                    }
                    
                    tfoot {
                        display: table-footer-group;
                    }
                    
                    table th {
                        background-color: #f3f4f6 !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                    
                    table tr:nth-child(even) {
                        background-color: #f9fafb !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-controls">
                <button class="print-button" onclick="window.print()">Print</button>
                <button class="close-button" onclick="window.close()">Close</button>
            </div>
            <h1>${title}</h1>
            <div id="table-container"></div>
            <div class="print-footer">
                <p>Printed on ${new Date().toLocaleString()}</p>
            </div>
        </body>
        </html>
    `;
}

function addWatermark(element, text, options = {}) {
    try {
        const defaultOptions = {
            color: '#1e3a8a',
            opacity: 0.05,
            fontSize: '5rem',
            fontWeight: 900,
            rotation: -45,
            zIndex: 10
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        const watermark = document.createElement('div');
        watermark.className = 'watermark';
        watermark.textContent = text;
        watermark.style.position = 'absolute';
        watermark.style.top = '50%';
        watermark.style.left = '50%';
        watermark.style.transform = `translate(-50%, -50%) rotate(${mergedOptions.rotation}deg)`;
        watermark.style.fontSize = mergedOptions.fontSize;
        watermark.style.opacity = mergedOptions.opacity;
        watermark.style.fontWeight = mergedOptions.fontWeight;
        watermark.style.color = mergedOptions.color;
        watermark.style.whiteSpace = 'nowrap';
        watermark.style.pointerEvents = 'none';
        watermark.style.textTransform = 'uppercase';
        watermark.style.zIndex = mergedOptions.zIndex;
        
        if (getComputedStyle(element).position === 'static') {
            element.style.position = 'relative';
        }
        
        element.appendChild(watermark);
        return watermark;
    } catch (error) {
        console.error('Error adding watermark:', error);
        return null;
    }
}

function generateQRCode(data, size = 100) {
    try {
        if (typeof QRCode === 'undefined') {
            console.error('QRCode library not loaded');
            return null;
        }
        
        const container = document.createElement('div');
        
        new QRCode(container, {
            text: data,
            width: size,
            height: size,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });
        
        const qrImage = container.querySelector('img');
        if (!qrImage) {
            console.error('Failed to generate QR code');
            return null;
        }
        
        return qrImage.src;
    } catch (error) {
        console.error('Error generating QR code:', error);
        return null;
    }
}
