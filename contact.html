<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Bizma</title>
    <meta name="description" content="Contact Bizma - Get in touch with our business management platform team">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Toast notifications -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body>
    <div id="root"></div>

    <!-- Toast utility -->
    <script>
        window.toast = {
            success: (message) => {
                Toastify({
                    text: message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#10B981",
                }).showToast();
            },
            error: (message) => {
                Toastify({
                    text: message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#EF4444",
                }).showToast();
            }
        };
    </script>

    <!-- Load Layout Components -->
    <script type="text/babel" src="components/layout/WebsiteHeader.js"></script>
    <script type="text/babel" src="components/layout/WebsiteFooter.js"></script>

    <!-- Load Contact Us Component -->
    <script type="text/babel" src="pages/ContactUs.js"></script>
    
    <script type="text/babel">
        const { createRoot } = ReactDOM;
        const root = createRoot(document.getElementById('root'));
        root.render(<ContactUs />);
    </script>
</body>
</html>
