<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login.html Fix</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Login.html Fix</h1>
        <p>Testing the updated login.html to ensure it uses the enhanced auth handler correctly.</p>
        
        <div>
            <h3>Test Enhanced Auth Handler Login</h3>
            <button onclick="testLoginEndpoint()">Test Login Endpoint</button>
            <div id="testStatus"></div>
        </div>

        <div>
            <h3>Simulate Login.html Flow</h3>
            <button onclick="simulateLoginHtml()">Simulate Login.html</button>
            <div id="simulateStatus"></div>
        </div>

        <div>
            <h3>Verify Token After Login</h3>
            <button onclick="verifyStoredToken()">Verify Stored Token</button>
            <div id="verifyStatus"></div>
        </div>

        <div>
            <h3>Instructions</h3>
            <div class="info">
                <strong>After all tests pass:</strong>
                <ol>
                    <li>Go to <a href="/biz/login.html" target="_blank">login.html</a></li>
                    <li>Login with: <EMAIL> / admin123</li>
                    <li>Should redirect to app.html without "Invalid token" errors</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testLoginEndpoint() {
            showStatus('testStatus', '🔄 Testing enhanced auth handler login endpoint...', 'info');
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123',
                        rememberMe: false
                    })
                });

                const data = await response.json();
                
                if (data.success && data.tokens && data.tokens.access_token) {
                    showStatus('testStatus', `
                        ✅ <strong>Enhanced auth handler working correctly!</strong><br>
                        User: ${data.user.name}<br>
                        Email: ${data.user.email}<br>
                        Role: ${data.user.role}<br>
                        Access Token: ${data.tokens.access_token.substring(0, 30)}...<br>
                        Refresh Token: ${data.tokens.refresh_token.substring(0, 30)}...<br>
                        Expires In: ${data.tokens.expires_in} seconds
                    `, 'success');
                } else {
                    showStatus('testStatus', `❌ Login failed: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus('testStatus', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function simulateLoginHtml() {
            showStatus('simulateStatus', '🎭 Simulating login.html flow...', 'info');
            
            // Clear any existing tokens
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('rememberMe');
            
            try {
                // Simulate the exact request that login.html makes
                const formData = {
                    email: '<EMAIL>',
                    password: 'admin123',
                    rememberMe: false
                };

                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: formData.email,
                        password: formData.password,
                        rememberMe: formData.rememberMe
                    })
                });

                const responseText = await response.text();
                let data;

                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    showStatus('simulateStatus', `❌ JSON Parse Error: ${parseError.message}`, 'error');
                    return;
                }

                if (data.success && data.tokens && data.tokens.access_token) {
                    // Store tokens exactly like login.html does
                    localStorage.setItem('authToken', data.tokens.access_token);
                    
                    if (formData.rememberMe && data.tokens.refresh_token) {
                        localStorage.setItem('refreshToken', data.tokens.refresh_token);
                        localStorage.setItem('rememberMe', 'true');
                    }

                    showStatus('simulateStatus', `
                        ✅ <strong>Login.html simulation successful!</strong><br>
                        Token stored in localStorage: ✅<br>
                        User: ${data.user.name}<br>
                        Ready to redirect to app.html<br><br>
                        <em>Login.html should now work correctly!</em>
                    `, 'success');
                } else {
                    showStatus('simulateStatus', `❌ Login failed: ${data.error || data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus('simulateStatus', `❌ Simulation error: ${error.message}`, 'error');
            }
        }

        async function verifyStoredToken() {
            showStatus('verifyStatus', '🔍 Verifying stored token...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('verifyStatus', '❌ No token found in localStorage. Run simulation first.', 'error');
                return;
            }

            try {
                // Test the token with AuthContext verification
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                const data = await response.json();
                
                if (data.success && data.user) {
                    showStatus('verifyStatus', `
                        ✅ <strong>Token verification successful!</strong><br>
                        User: ${data.user.name} (${data.user.email})<br>
                        Role: ${data.user.role}<br>
                        Company: ${data.user.company_name}<br><br>
                        <strong>🎉 AuthContext will work correctly!</strong><br>
                        No more "Invalid token" errors!
                    `, 'success');
                } else {
                    showStatus('verifyStatus', `❌ Token verification failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus('verifyStatus', `❌ Verification error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>