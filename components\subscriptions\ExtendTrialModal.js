// ExtendTrialModal Component
function ExtendTrialModal({ subscription, daysToAdd, setDaysToAdd, onCancel, onConfirm, processing }) {
    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
    
    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'bg-white rounded-lg shadow-xl p-6 w-full max-w-md';
    
    // Title
    const title = document.createElement('h3');
    title.className = 'text-lg font-medium text-gray-900 mb-4';
    title.textContent = 'Extend Trial Period';
    
    // Description
    const description = document.createElement('div');
    description.className = 'mb-4';
    
    const descText = document.createElement('p');
    descText.className = 'text-sm text-gray-600 mb-2';
    descText.innerHTML = `Extending trial for <span class="font-semibold">${subscription?.company_name || 'this subscription'}</span>`;
    
    const label = document.createElement('label');
    label.className = 'block text-sm font-medium text-gray-700 mb-1';
    label.textContent = 'Days to add';
    
    const select = document.createElement('select');
    select.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500';
    select.value = daysToAdd || 7;
    
    // Add options
    [7, 14, 30].forEach(days => {
        const option = document.createElement('option');
        option.value = days;
        option.textContent = `${days} days`;
        select.appendChild(option);
    });
    
    select.onchange = (e) => {
        if (setDaysToAdd) {
            setDaysToAdd(parseInt(e.target.value));
        }
    };
    
    description.appendChild(descText);
    description.appendChild(label);
    description.appendChild(select);
    
    // Buttons
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'flex justify-end space-x-3 mt-6';
    
    const cancelBtn = document.createElement('button');
    cancelBtn.type = 'button';
    cancelBtn.className = 'px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500';
    cancelBtn.textContent = 'Cancel';
    cancelBtn.onclick = onCancel;
    
    const confirmBtn = document.createElement('button');
    confirmBtn.type = 'button';
    confirmBtn.className = 'px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50';
    confirmBtn.disabled = processing;
    confirmBtn.textContent = processing ? 'Extending...' : 'Extend Trial';
    confirmBtn.onclick = onConfirm;
    
    buttonContainer.appendChild(cancelBtn);
    buttonContainer.appendChild(confirmBtn);
    
    // Assemble modal
    modal.appendChild(title);
    modal.appendChild(description);
    modal.appendChild(buttonContainer);
    overlay.appendChild(modal);
    
    // Close on overlay click
    overlay.onclick = (e) => {
        if (e.target === overlay) {
            onCancel();
        }
    };
    
    return overlay;
}

// Make ExtendTrialModal globally available
window.ExtendTrialModal = ExtendTrialModal;
