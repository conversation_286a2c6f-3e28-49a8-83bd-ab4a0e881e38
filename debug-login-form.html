<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login Form - Bizma</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Debug Login Form</h1>
        <p>Test login with detailed debugging information</p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="rememberMe">
                    <label for="rememberMe">Remember me</label>
                </div>
            </div>
            
            <button type="submit" id="submitBtn">Sign In</button>
        </form>
        
        <div id="status"></div>
        <div id="debug"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            const submitBtn = document.getElementById('submitBtn');
            const statusDiv = document.getElementById('status');
            const debugDiv = document.getElementById('debug');
            
            // Clear previous status
            statusDiv.innerHTML = '';
            debugDiv.innerHTML = '';
            
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Signing in...';
            
            // Show initial status
            statusDiv.innerHTML = '<div class="info">Attempting to sign in...</div>';
            
            const debugLog = [];
            
            try {
                debugLog.push('=== LOGIN ATTEMPT ===');
                debugLog.push(`Email: ${email}`);
                debugLog.push(`Password: ${'*'.repeat(password.length)}`);
                debugLog.push(`Remember Me: ${rememberMe}`);
                debugLog.push('');
                
                const requestData = {
                    action: 'login',
                    email: email,
                    password: password,
                    remember_me: rememberMe
                };
                
                debugLog.push('Request Data:');
                debugLog.push(JSON.stringify(requestData, null, 2));
                debugLog.push('');
                
                debugLog.push('Sending request to: api/simple-auth.php');
                debugDiv.innerHTML = debugLog.join('\n');

                const response = await fetch('api/simple-auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                debugLog.push(`Response Status: ${response.status} ${response.statusText}`);
                debugLog.push(`Response Headers:`);
                for (let [key, value] of response.headers.entries()) {
                    debugLog.push(`  ${key}: ${value}`);
                }
                debugLog.push('');
                
                const responseText = await response.text();
                debugLog.push('Raw Response:');
                debugLog.push(responseText);
                debugLog.push('');
                
                debugDiv.innerHTML = debugLog.join('\n');
                
                let data;
                try {
                    data = JSON.parse(responseText);
                    debugLog.push('Parsed JSON Response:');
                    debugLog.push(JSON.stringify(data, null, 2));
                } catch (parseError) {
                    debugLog.push('JSON Parse Error:');
                    debugLog.push(parseError.message);
                    throw new Error('Invalid JSON response: ' + parseError.message);
                }
                
                debugDiv.innerHTML = debugLog.join('\n');
                
                if (data.success) {
                    statusDiv.innerHTML = '<div class="success">✅ Login successful!</div>';
                    
                    debugLog.push('');
                    debugLog.push('=== LOGIN SUCCESS ===');
                    
                    if (data.token) {
                        localStorage.setItem('auth_token', data.token);
                        debugLog.push('Token stored in localStorage');
                    }
                    
                    if (data.user) {
                        debugLog.push('User data received:');
                        debugLog.push(JSON.stringify(data.user, null, 2));
                    }
                    
                    debugLog.push('');
                    debugLog.push('Redirecting to app in 3 seconds...');
                    debugDiv.innerHTML = debugLog.join('\n');

                    setTimeout(() => {
                        window.location.href = 'app.html';
                    }, 3000);
                    
                } else {
                    statusDiv.innerHTML = `<div class="error">❌ Login failed: ${data.message || 'Unknown error'}</div>`;
                    debugLog.push('');
                    debugLog.push('=== LOGIN FAILED ===');
                    debugLog.push(`Error: ${data.message || 'Unknown error'}`);
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                debugLog.push('');
                debugLog.push('=== NETWORK ERROR ===');
                debugLog.push(`Error: ${error.message}`);
                debugLog.push(`Stack: ${error.stack}`);
            } finally {
                debugDiv.innerHTML = debugLog.join('\n');
                submitBtn.disabled = false;
                submitBtn.textContent = 'Sign In';
            }
        });
    </script>
</body>
</html>
