/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split(\".\").map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, \"access_token\", \"code\", \"error_description\", \"error_uri\", \"error\", \"expires_in\", \"id_token\", \"iss\", \"response\", \"session_state\", \"state\", \"token_type\");\n}\nfunction authorizationHeaderValue(token, tokenType = \"Bearer\") {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: \"openid\",\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === \"claims\" && typeof value === \"object\") {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === \"resource\" && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== \"string\") {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n        throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes(\"client_secret_post\")) {\n                properties.token_endpoint_auth_method = \"client_secret_post\";\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError(\"provide a redirect_uri or redirect_uris, not both\");\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError(\"provide a response_type or response_types, not both\");\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== \"string\" || !metadata.client_id) {\n            throw new TypeError(\"client_id is required\");\n        }\n        const properties = {\n            grant_types: [\n                \"authorization_code\"\n            ],\n            id_token_signed_response_alg: \"RS256\",\n            authorization_signed_response_alg: \"RS256\",\n            response_types: [\n                \"code\"\n            ],\n            token_endpoint_auth_method: \"client_secret_basic\",\n            ...this.fapi1() ? {\n                grant_types: [\n                    \"authorization_code\",\n                    \"implicit\"\n                ],\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                response_types: [\n                    \"code id_token\"\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: \"PS256\",\n                authorization_signed_response_alg: \"PS256\",\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case \"self_signed_tls_client_auth\":\n                case \"tls_client_auth\":\n                    break;\n                case \"private_key_jwt\":\n                    if (!jwks) {\n                        throw new TypeError(\"jwks is required\");\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError(\"token_endpoint_auth_method is required\");\n                default:\n                    throw new TypeError(\"invalid or unsupported token_endpoint_auth_method\");\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError(\"either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true\");\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport(\"token\", this.issuer, properties);\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        assertIssuerConfiguration(this.issuer, \"authorization_endpoint\");\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, \"%20\");\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError(\"params must be a plain object\");\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join(\"\\n\");\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, \"end_session_endpoint\");\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === \"string\";\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError(\"#callbackParams only accepts string urls, http.IncomingMessage or a lookalike\");\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case \"GET\":\n                    return pickCb(getSearchParams(input.url));\n                case \"POST\":\n                    if (input.body === undefined) {\n                        throw new TypeError(\"incoming message body missing, include a body parser prior to this method call\");\n                    }\n                    switch(typeof input.body){\n                        case \"object\":\n                        case \"string\":\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString(\"utf-8\")));\n                            }\n                            if (typeof input.body === \"string\") {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError(\"invalid IncomingMessage body object\");\n                    }\n                default:\n                    throw new TypeError(\"invalid IncomingMessage method\");\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            id_token: [\n                \"id_token\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"authorization\", checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, \"token\", checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"expected a JARM response\",\n                checks,\n                params\n            });\n        } else if (\"response\" in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError(\"checks.state argument is missing\");\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: \"state missing from the response\",\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    \"state mismatch, expected %s, got: %s\",\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if (\"iss\" in params) {\n            assertIssuerConfiguration(this.issuer, \"issuer\");\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        \"iss mismatch, expected %s, got: %s\",\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !(\"id_token\" in params) && !(\"response\" in parameters)) {\n            throw new RPError({\n                message: \"iss missing from the response\",\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === \"string\" && params.id_token.length) {\n            throw new RPError({\n                message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                \"code\"\n            ],\n            token: [\n                \"access_token\",\n                \"token_type\"\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(\" \")){\n                if (type === \"none\") {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: \"authorization_code\",\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === \"string\" && tokenset.id_token.length) {\n                throw new RPError({\n                    message: \"id_token detected in the response, you must use client.callback() instead of client.oauthCallback()\",\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = \"A128CBC-HS256\") {\n        const header = JSON.parse(base64url.decode(jwe.split(\".\")[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWE enc received, expected %s, got: %s\",\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: \"enc\"\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === \"dir\" ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: \"failed to decrypt JWE\",\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError(\"id_token not present in TokenSet\");\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === \"number\" || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: \"missing required JWT property auth_time\",\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== \"number\") {\n                throw new RPError({\n                    message: \"JWT auth_time claim must be a JSON numeric value\",\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === \"number\" && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    \"too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i\",\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    \"nonce mismatch, expected %s, got: %s\",\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === \"authorization\") {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: \"missing required property at_hash\",\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: \"missing required property c_hash\",\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: \"missing required property s_hash\",\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: \"s_hash\",\n                        source: \"state\"\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    \"JWT issued too far in the past, now %i, iat %i\",\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"at_hash\",\n                    source: \"access_token\"\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: \"c_hash\",\n                    source: \"code\"\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        \"iss\",\n        \"sub\",\n        \"aud\",\n        \"exp\",\n        \"iat\"\n    ]) {\n        const isSelfIssued = this.issuer.issuer === \"https://self-issued.me\";\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    \"failed to decode JWT (%s: %s)\",\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    \"unexpected JWT alg received, expected %s, got: %s\",\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                \"sub_jwk\"\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace(\"{tenantid}\", payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        \"unexpected iss value, expected %s, got: %s\",\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== \"number\") {\n                throw new RPError({\n                    message: \"JWT iat claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== \"number\") {\n                throw new RPError({\n                    message: \"JWT nbf claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        \"JWT not active yet, now %i, nbf %i\",\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== \"number\") {\n                throw new RPError({\n                    message: \"JWT exp claim must be a JSON numeric value\",\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        \"JWT expired, now %i, exp %i\",\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: \"missing required JWT property azp\",\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            \"aud is missing the client_id, expected %s to be included in %j\",\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        \"aud mismatch, expected %s, got: %s\",\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === \"string\") {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        \"azp mismatch, got: %s\",\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, \"public\");\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: \"failed to use sub_jwk claim as an asymmetric JSON Web Key\",\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: \"failed to match the subject with sub_jwk\",\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith(\"HS\")) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== \"none\") {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: \"sig\"\n            });\n        }\n        if (!keys && header.alg === \"none\") {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: \"failed to validate JWT signature\",\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError(\"refresh_token not present in TokenSet\");\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: \"refresh_token\",\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, \"token\", skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            \"sub mismatch, expected %s, got: %s\",\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? \"DPoP\" : accessToken instanceof TokenSet ? accessToken.token_type : \"Bearer\" } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError(\"access_token not present in TokenSet\");\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError(\"no access token provided\");\n        } else if (typeof accessToken !== \"string\") {\n            throw new TypeError(\"invalid access token provided\");\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: \"buffer\",\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers[\"www-authenticate\"];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith(\"dpop \") && parseWwwAuthenticate(wwwAuthenticate).error === \"use_dpop_nonce\") {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = \"GET\", via = \"header\", tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"userinfo_endpoint\");\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== \"GET\" && options.method !== \"POST\") {\n            throw new TypeError(\"#userinfo() method can only be POST or a GET\");\n        }\n        if (via === \"body\" && options.method !== \"POST\") {\n            throw new TypeError(\"can only send body on POST\");\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: \"application/jwt\"\n            };\n        } else {\n            options.headers = {\n                Accept: \"application/json\"\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === \"body\") {\n            options.headers.Authorization = undefined;\n            options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n            options.body = new URLSearchParams();\n            options.body.append(\"access_token\", accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === \"GET\") {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers[\"Content-Type\"] = \"application/x-www-form-urlencoded\";\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers[\"content-type\"])) {\n                throw new RPError({\n                    message: \"expected application/jwt response from the userinfo_endpoint\",\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: \"failed to parse userinfo JWE payload as JSON\",\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, \"response\", {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        \"userinfo sub mismatch, expected %s, got: %s\",\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? \"sha256\" : len <= 384 ? \"sha384\" : len <= 512 ? \"sha512\" : false;\n        if (!hash) {\n            throw new Error(\"unsupported symmetric encryption key derivation\");\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError(\"client_secret is required\");\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const response = await authenticatedPost.call(this, \"token\", {\n            form: body,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === \"use_dpop_nonce\") {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, \"device_authorization_endpoint\");\n        assertIssuerConfiguration(this.issuer, \"token_endpoint\");\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, \"device_authorization\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"revocation_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"revocation\", {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"introspection_endpoint\");\n        if (hint !== undefined && typeof hint !== \"string\") {\n            throw new TypeError(\"hint must be a string\");\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, \"introspection\", {\n            form,\n            responseType: \"json\"\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, \"registration_endpoint\");\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: \"application/json\",\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: \"json\",\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: \"POST\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: registrationClientUri,\n            responseType: \"json\",\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: \"application/json\"\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || \"none\", encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || \"A128CBC-HS256\" } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError(\"requestObject must be a plain object\");\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: \"oauth-authz-req+jwt\"\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === \"none\") {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                \"\"\n            ].join(\".\");\n        } else {\n            const symmetric = signingAlgorithm.startsWith(\"HS\");\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: \"sig\"\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: \"oauth-authz-req+jwt\"\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: \"enc\"\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === \"dir\" ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, \"pushed_authorization_request_endpoint\");\n        const body = {\n            ...\"request\" in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, \"pushed_authorization_request\", {\n            responseType: \"json\",\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: \"token\"\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!(\"expires_in\" in responseBody)) {\n            throw new RPError({\n                message: \"expected expires_in in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== \"number\") {\n            throw new RPError({\n                message: \"invalid expires_in value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (!(\"request_uri\" in responseBody)) {\n            throw new RPError({\n                message: \"expected request_uri in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== \"string\") {\n            throw new RPError({\n                message: \"invalid request_uri value in Pushed Authorization Successful Response\",\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === \"FAPI1Client\";\n    }\n    fapi2() {\n        return this.constructor.name === \"FAPI2Client\";\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            \"iss\",\n            \"exp\",\n            \"aud\"\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError(\"payload must be a plain object\");\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === \"node:crypto\") {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError(\"unrecognized crypto runtime\");\n        }\n        if (privateKey.type !== \"private\") {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError(\"could not determine DPoP JWS Algorithm\");\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash(\"sha256\").update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: \"dpop+jwt\",\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case \"Ed25519\":\n        case \"Ed448\":\n            return \"EdDSA\";\n        case \"ECDSA\":\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case \"P-256\":\n                        return \"ES256\";\n                    case \"P-384\":\n                        return \"ES384\";\n                    case \"P-521\":\n                        return \"ES512\";\n                    default:\n                        break;\n                }\n                break;\n            }\n        case \"RSASSA-PKCS1-v1_5\":\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case \"RSA-PSS\":\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError(\"unsupported DPoP private key\");\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === \"node:crypto\") {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === \"CryptoKey\") {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case \"ed25519\":\n            case \"ed448\":\n                return \"EdDSA\";\n            case \"ec\":\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case \"rsa\":\n            case rsaPssParams && \"rsa-pss\":\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError(\"unsupported DPoP private key\");\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === \"object\" && privateKeyInput.format === \"jwk\" && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === \"rsa-pss\") {\n                candidates = candidates.filter((value)=>value.startsWith(\"PS\"));\n            }\n            return [\n                \"PS256\",\n                \"PS384\",\n                \"PS512\",\n                \"RS256\",\n                \"RS384\",\n                \"RS384\"\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return \"PS256\";\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.key.crv){\n            case \"P-256\":\n                return \"ES256\";\n            case \"secp256k1\":\n                return \"ES256K\";\n            case \"P-384\":\n                return \"ES384\";\n            case \"P-512\":\n                return \"ES512\";\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: \"der\",\n            type: \"pkcs8\"\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return \"ES256\";\n        }\n        if (curveOid.equals(p384)) {\n            return \"ES384\";\n        }\n        if (curveOid.equals(p521)) {\n            return \"ES512\";\n        }\n        if (curveOid.equals(secp256k1)) {\n            return \"ES256K\";\n        }\n        throw new TypeError(\"unsupported DPoP private key curve\");\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === \"node:crypto\" && typeof privateKeyInput === \"object\" && typeof privateKeyInput.key === \"object\" && privateKeyInput.format === \"jwk\") {\n        return pick(privateKeyInput.key, \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), \"kty\", \"crv\", \"x\", \"y\", \"e\", \"n\");\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === \"WebCryptoAPI\") {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass DeviceFlowHandle {\n    #aborted;\n    #client;\n    #clientAssertionPayload;\n    #DPoP;\n    #exchangeBody;\n    #expires_at;\n    #interval;\n    #maxAge;\n    #response;\n    constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }){\n        [\n            \"verification_uri\",\n            \"user_code\",\n            \"device_code\"\n        ].forEach((prop)=>{\n            if (typeof response[prop] !== \"string\" || !response[prop]) {\n                throw new RPError(`expected ${prop} string to be returned by Device Authorization Response, got %j`, response[prop]);\n            }\n        });\n        if (!Number.isSafeInteger(response.expires_in)) {\n            throw new RPError(\"expected expires_in number to be returned by Device Authorization Response, got %j\", response.expires_in);\n        }\n        this.#expires_at = now() + response.expires_in;\n        this.#client = client;\n        this.#DPoP = DPoP;\n        this.#maxAge = maxAge;\n        this.#exchangeBody = exchangeBody;\n        this.#clientAssertionPayload = clientAssertionPayload;\n        this.#response = response;\n        this.#interval = response.interval * 1000 || 5000;\n    }\n    abort() {\n        this.#aborted = true;\n    }\n    async poll({ signal } = {}) {\n        if (signal && signal.aborted || this.#aborted) {\n            throw new RPError(\"polling aborted\");\n        }\n        if (this.expired()) {\n            throw new RPError(\"the device code %j has expired and the device authorization session has concluded\", this.device_code);\n        }\n        await new Promise((resolve)=>setTimeout(resolve, this.#interval));\n        let tokenset;\n        try {\n            tokenset = await this.#client.grant({\n                ...this.#exchangeBody,\n                grant_type: \"urn:ietf:params:oauth:grant-type:device_code\",\n                device_code: this.device_code\n            }, {\n                clientAssertionPayload: this.#clientAssertionPayload,\n                DPoP: this.#DPoP\n            });\n        } catch (err) {\n            switch(err instanceof OPError && err.error){\n                case \"slow_down\":\n                    this.#interval += 5000;\n                case \"authorization_pending\":\n                    return this.poll({\n                        signal\n                    });\n                default:\n                    throw err;\n            }\n        }\n        if (\"id_token\" in tokenset) {\n            await this.#client.decryptIdToken(tokenset);\n            await this.#client.validateIdToken(tokenset, undefined, \"token\", this.#maxAge);\n        }\n        return tokenset;\n    }\n    get device_code() {\n        return this.#response.device_code;\n    }\n    get user_code() {\n        return this.#response.user_code;\n    }\n    get verification_uri() {\n        return this.#response.verification_uri;\n    }\n    get verification_uri_complete() {\n        return this.#response.verification_uri_complete;\n    }\n    get expires_in() {\n        return Math.max.apply(null, [\n            this.#expires_at - now(),\n            0\n        ]);\n    }\n    expired() {\n        return this.expires_in === 0;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.#response, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n}\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\nclass OPError extends Error {\n    constructor({ error_description, error, error_uri, session_state, state, scope }, response){\n        super(!error_description ? error : `${error} (${error_description})`);\n        Object.assign(this, {\n            error\n        }, error_description && {\n            error_description\n        }, error_uri && {\n            error_uri\n        }, state && {\n            state\n        }, scope && {\n            scope\n        }, session_state && {\n            session_state\n        });\n        if (response) {\n            Object.defineProperty(this, \"response\", {\n                value: response\n            });\n        }\n        this.name = this.constructor.name;\n        Error.captureStackTrace(this, this.constructor);\n    }\n}\nclass RPError extends Error {\n    constructor(...args){\n        if (typeof args[0] === \"string\") {\n            super(format(...args));\n        } else {\n            const { message, printf, response, ...rest } = args[0];\n            if (printf) {\n                super(format(...printf));\n            } else {\n                super(message);\n            }\n            Object.assign(this, rest);\n            if (response) {\n                Object.defineProperty(this, \"response\", {\n                    value: response\n                });\n            }\n        }\n        this.name = this.constructor.name;\n        Error.captureStackTrace(this, this.constructor);\n    }\n}\nmodule.exports = {\n    OPError,\n    RPError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZXJyb3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU0sRUFBRUEsTUFBTSxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDO0FBRTNCLE1BQU1DLGdCQUFnQkM7SUFDcEJDLFlBQVksRUFBRUMsaUJBQWlCLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxhQUFhLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFLEVBQUVDLFFBQVEsQ0FBRTtRQUMxRixLQUFLLENBQUMsQ0FBQ04sb0JBQW9CQyxRQUFRLENBQUMsRUFBRUEsTUFBTSxFQUFFLEVBQUVELGtCQUFrQixDQUFDLENBQUM7UUFFcEVPLE9BQU9DLE1BQU0sQ0FDWCxJQUFJLEVBQ0o7WUFBRVA7UUFBTSxHQUNSRCxxQkFBcUI7WUFBRUE7UUFBa0IsR0FDekNFLGFBQWE7WUFBRUE7UUFBVSxHQUN6QkUsU0FBUztZQUFFQTtRQUFNLEdBQ2pCQyxTQUFTO1lBQUVBO1FBQU0sR0FDakJGLGlCQUFpQjtZQUFFQTtRQUFjO1FBR25DLElBQUlHLFVBQVU7WUFDWkMsT0FBT0UsY0FBYyxDQUFDLElBQUksRUFBRSxZQUFZO2dCQUN0Q0MsT0FBT0o7WUFDVDtRQUNGO1FBRUEsSUFBSSxDQUFDSyxJQUFJLEdBQUcsSUFBSSxDQUFDWixXQUFXLENBQUNZLElBQUk7UUFDakNiLE1BQU1jLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNiLFdBQVc7SUFDaEQ7QUFDRjtBQUVBLE1BQU1jLGdCQUFnQmY7SUFDcEJDLFlBQVksR0FBR2UsSUFBSSxDQUFFO1FBQ25CLElBQUksT0FBT0EsSUFBSSxDQUFDLEVBQUUsS0FBSyxVQUFVO1lBQy9CLEtBQUssQ0FBQ25CLFVBQVVtQjtRQUNsQixPQUFPO1lBQ0wsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE1BQU0sRUFBRVYsUUFBUSxFQUFFLEdBQUdXLE1BQU0sR0FBR0gsSUFBSSxDQUFDLEVBQUU7WUFDdEQsSUFBSUUsUUFBUTtnQkFDVixLQUFLLENBQUNyQixVQUFVcUI7WUFDbEIsT0FBTztnQkFDTCxLQUFLLENBQUNEO1lBQ1I7WUFDQVIsT0FBT0MsTUFBTSxDQUFDLElBQUksRUFBRVM7WUFDcEIsSUFBSVgsVUFBVTtnQkFDWkMsT0FBT0UsY0FBYyxDQUFDLElBQUksRUFBRSxZQUFZO29CQUN0Q0MsT0FBT0o7Z0JBQ1Q7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDSyxJQUFJLEdBQUcsSUFBSSxDQUFDWixXQUFXLENBQUNZLElBQUk7UUFDakNiLE1BQU1jLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNiLFdBQVc7SUFDaEQ7QUFDRjtBQUVBbUIsT0FBT0MsT0FBTyxHQUFHO0lBQ2Z0QjtJQUNBZ0I7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9lcnJvcnMuanM/YTc0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGZvcm1hdCB9ID0gcmVxdWlyZSgndXRpbCcpO1xuXG5jbGFzcyBPUEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcih7IGVycm9yX2Rlc2NyaXB0aW9uLCBlcnJvciwgZXJyb3JfdXJpLCBzZXNzaW9uX3N0YXRlLCBzdGF0ZSwgc2NvcGUgfSwgcmVzcG9uc2UpIHtcbiAgICBzdXBlcighZXJyb3JfZGVzY3JpcHRpb24gPyBlcnJvciA6IGAke2Vycm9yfSAoJHtlcnJvcl9kZXNjcmlwdGlvbn0pYCk7XG5cbiAgICBPYmplY3QuYXNzaWduKFxuICAgICAgdGhpcyxcbiAgICAgIHsgZXJyb3IgfSxcbiAgICAgIGVycm9yX2Rlc2NyaXB0aW9uICYmIHsgZXJyb3JfZGVzY3JpcHRpb24gfSxcbiAgICAgIGVycm9yX3VyaSAmJiB7IGVycm9yX3VyaSB9LFxuICAgICAgc3RhdGUgJiYgeyBzdGF0ZSB9LFxuICAgICAgc2NvcGUgJiYgeyBzY29wZSB9LFxuICAgICAgc2Vzc2lvbl9zdGF0ZSAmJiB7IHNlc3Npb25fc3RhdGUgfSxcbiAgICApO1xuXG4gICAgaWYgKHJlc3BvbnNlKSB7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ3Jlc3BvbnNlJywge1xuICAgICAgICB2YWx1ZTogcmVzcG9uc2UsXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICB0aGlzLm5hbWUgPSB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbn1cblxuY2xhc3MgUlBFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IoLi4uYXJncykge1xuICAgIGlmICh0eXBlb2YgYXJnc1swXSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHN1cGVyKGZvcm1hdCguLi5hcmdzKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHsgbWVzc2FnZSwgcHJpbnRmLCByZXNwb25zZSwgLi4ucmVzdCB9ID0gYXJnc1swXTtcbiAgICAgIGlmIChwcmludGYpIHtcbiAgICAgICAgc3VwZXIoZm9ybWF0KC4uLnByaW50ZikpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICB9XG4gICAgICBPYmplY3QuYXNzaWduKHRoaXMsIHJlc3QpO1xuICAgICAgaWYgKHJlc3BvbnNlKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCAncmVzcG9uc2UnLCB7XG4gICAgICAgICAgdmFsdWU6IHJlc3BvbnNlLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLm5hbWUgPSB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIE9QRXJyb3IsXG4gIFJQRXJyb3IsXG59O1xuIl0sIm5hbWVzIjpbImZvcm1hdCIsInJlcXVpcmUiLCJPUEVycm9yIiwiRXJyb3IiLCJjb25zdHJ1Y3RvciIsImVycm9yX2Rlc2NyaXB0aW9uIiwiZXJyb3IiLCJlcnJvcl91cmkiLCJzZXNzaW9uX3N0YXRlIiwic3RhdGUiLCJzY29wZSIsInJlc3BvbnNlIiwiT2JqZWN0IiwiYXNzaWduIiwiZGVmaW5lUHJvcGVydHkiLCJ2YWx1ZSIsIm5hbWUiLCJjYXB0dXJlU3RhY2tUcmFjZSIsIlJQRXJyb3IiLCJhcmdzIiwibWVzc2FnZSIsInByaW50ZiIsInJlc3QiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n    if (properties[eam] && properties[eam].endsWith(\"_jwt\") && !properties[easa] && !issuer[easavs]) {\n        throw new TypeError(`${easavs} must be configured on the issuer if ${easa} is not defined on a client`);\n    }\n}\nfunction assertIssuerConfiguration(issuer, endpoint) {\n    if (!issuer[endpoint]) {\n        throw new TypeError(`${endpoint} must be configured on the issuer`);\n    }\n}\nmodule.exports = {\n    assertSigningAlgValuesSupport,\n    assertIssuerConfiguration\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding(\"base64url\")) {\n    encode = (input, encoding = \"utf8\")=>Buffer.from(input, encoding).toString(\"base64url\");\n} else {\n    const fromBase64 = (base64)=>base64.replace(/=/g, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n    encode = (input, encoding = \"utf8\")=>fromBase64(Buffer.from(input, encoding).toString(\"base64\"));\n}\nconst decode = (input)=>Buffer.from(input, \"base64\");\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUEsSUFBSUE7QUFDSixJQUFJQyxPQUFPQyxVQUFVLENBQUMsY0FBYztJQUNsQ0YsU0FBUyxDQUFDRyxPQUFPQyxXQUFXLE1BQU0sR0FBS0gsT0FBT0ksSUFBSSxDQUFDRixPQUFPQyxVQUFVRSxRQUFRLENBQUM7QUFDL0UsT0FBTztJQUNMLE1BQU1DLGFBQWEsQ0FBQ0MsU0FBV0EsT0FBT0MsT0FBTyxDQUFDLE1BQU0sSUFBSUEsT0FBTyxDQUFDLE9BQU8sS0FBS0EsT0FBTyxDQUFDLE9BQU87SUFDM0ZULFNBQVMsQ0FBQ0csT0FBT0MsV0FBVyxNQUFNLEdBQ2hDRyxXQUFXTixPQUFPSSxJQUFJLENBQUNGLE9BQU9DLFVBQVVFLFFBQVEsQ0FBQztBQUNyRDtBQUVBLE1BQU1JLFNBQVMsQ0FBQ1AsUUFBVUYsT0FBT0ksSUFBSSxDQUFDRixPQUFPO0FBRTdDUSxxQkFBcUIsR0FBR0Q7QUFDeEJDLHFCQUFxQixHQUFHWCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2Jhc2U2NHVybC5qcz8zYjVlIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlbmNvZGU7XG5pZiAoQnVmZmVyLmlzRW5jb2RpbmcoJ2Jhc2U2NHVybCcpKSB7XG4gIGVuY29kZSA9IChpbnB1dCwgZW5jb2RpbmcgPSAndXRmOCcpID0+IEJ1ZmZlci5mcm9tKGlucHV0LCBlbmNvZGluZykudG9TdHJpbmcoJ2Jhc2U2NHVybCcpO1xufSBlbHNlIHtcbiAgY29uc3QgZnJvbUJhc2U2NCA9IChiYXNlNjQpID0+IGJhc2U2NC5yZXBsYWNlKC89L2csICcnKS5yZXBsYWNlKC9cXCsvZywgJy0nKS5yZXBsYWNlKC9cXC8vZywgJ18nKTtcbiAgZW5jb2RlID0gKGlucHV0LCBlbmNvZGluZyA9ICd1dGY4JykgPT5cbiAgICBmcm9tQmFzZTY0KEJ1ZmZlci5mcm9tKGlucHV0LCBlbmNvZGluZykudG9TdHJpbmcoJ2Jhc2U2NCcpKTtcbn1cblxuY29uc3QgZGVjb2RlID0gKGlucHV0KSA9PiBCdWZmZXIuZnJvbShpbnB1dCwgJ2Jhc2U2NCcpO1xuXG5tb2R1bGUuZXhwb3J0cy5kZWNvZGUgPSBkZWNvZGU7XG5tb2R1bGUuZXhwb3J0cy5lbmNvZGUgPSBlbmNvZGU7XG4iXSwibmFtZXMiOlsiZW5jb2RlIiwiQnVmZmVyIiwiaXNFbmNvZGluZyIsImlucHV0IiwiZW5jb2RpbmciLCJmcm9tIiwidG9TdHJpbmciLCJmcm9tQmFzZTY0IiwiYmFzZTY0IiwicmVwbGFjZSIsImRlY29kZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value)=>encodeURIComponent(value).replace(/%20/g, \"+\");\nasync function clientAssertion(endpoint, payload) {\n    let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n    if (!alg) {\n        assertIssuerConfiguration(this.issuer, `${endpoint}_endpoint_auth_signing_alg_values_supported`);\n    }\n    if (this[`${endpoint}_endpoint_auth_method`] === \"client_secret_jwt\") {\n        if (!alg) {\n            const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n            alg = Array.isArray(supported) && supported.find((signAlg)=>/^HS(?:256|384|512)/.test(signAlg));\n        }\n        if (!alg) {\n            throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n        }\n        return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n            alg\n        }).sign(this.secretForAlg(alg));\n    }\n    const keystore = await keystores.get(this);\n    if (!keystore) {\n        throw new TypeError(\"no client jwks provided for signing a client assertion with\");\n    }\n    if (!alg) {\n        const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n        alg = Array.isArray(supported) && supported.find((signAlg)=>keystore.get({\n                alg: signAlg,\n                use: \"sig\"\n            }));\n    }\n    if (!alg) {\n        throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n    }\n    const key = keystore.get({\n        alg,\n        use: \"sig\"\n    });\n    if (!key) {\n        throw new RPError(`no key found in client jwks to sign a client assertion with using alg ${alg}`);\n    }\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n        alg,\n        kid: key.jwk && key.jwk.kid\n    }).sign(await key.keyObject(alg));\n}\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n    const authMethod = this[`${endpoint}_endpoint_auth_method`];\n    switch(authMethod){\n        case \"self_signed_tls_client_auth\":\n        case \"tls_client_auth\":\n        case \"none\":\n            return {\n                form: {\n                    client_id: this.client_id\n                }\n            };\n        case \"client_secret_post\":\n            if (typeof this.client_secret !== \"string\") {\n                throw new TypeError(\"client_secret_post client authentication method requires a client_secret\");\n            }\n            return {\n                form: {\n                    client_id: this.client_id,\n                    client_secret: this.client_secret\n                }\n            };\n        case \"private_key_jwt\":\n        case \"client_secret_jwt\":\n            {\n                const timestamp = now();\n                const assertion = await clientAssertion.call(this, endpoint, {\n                    iat: timestamp,\n                    exp: timestamp + 60,\n                    jti: random(),\n                    iss: this.client_id,\n                    sub: this.client_id,\n                    aud: this.issuer.issuer,\n                    ...clientAssertionPayload\n                });\n                return {\n                    form: {\n                        client_id: this.client_id,\n                        client_assertion: assertion,\n                        client_assertion_type: \"urn:ietf:params:oauth:client-assertion-type:jwt-bearer\"\n                    }\n                };\n            }\n        case \"client_secret_basic\":\n            {\n                // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n                // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n                // > The client identifier is encoded using the\n                // > \"application/x-www-form-urlencoded\" encoding algorithm per\n                // > Appendix B, and the encoded value is used as the username; the client\n                // > password is encoded using the same algorithm and used as the\n                // > password.\n                if (typeof this.client_secret !== \"string\") {\n                    throw new TypeError(\"client_secret_basic client authentication method requires a client_secret\");\n                }\n                const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n                const value = Buffer.from(encoded).toString(\"base64\");\n                return {\n                    headers: {\n                        Authorization: `Basic ${value}`\n                    }\n                };\n            }\n        default:\n            {\n                throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n            }\n    }\n}\nfunction resolveResponseType() {\n    const { length, 0: value } = this.response_types;\n    if (length === 1) {\n        return value;\n    }\n    return undefined;\n}\nfunction resolveRedirectUri() {\n    const { length, 0: value } = this.redirect_uris || [];\n    if (length === 1) {\n        return value;\n    }\n    return undefined;\n}\nasync function authenticatedPost(endpoint, opts, { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {}) {\n    const auth = await authFor.call(this, endpointAuthMethod, {\n        clientAssertionPayload\n    });\n    const requestOpts = merge(opts, auth);\n    const mTLS = this[`${endpointAuthMethod}_endpoint_auth_method`].includes(\"tls_client_auth\") || endpoint === \"token\" && this.tls_client_certificate_bound_access_tokens;\n    let targetUrl;\n    if (mTLS && this.issuer.mtls_endpoint_aliases) {\n        targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n    }\n    targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n    if (\"form\" in requestOpts) {\n        for (const [key, value] of Object.entries(requestOpts.form)){\n            if (typeof value === \"undefined\") {\n                delete requestOpts.form[key];\n            }\n        }\n    }\n    return request.call(this, {\n        ...requestOpts,\n        method: \"POST\",\n        url: targetUrl,\n        headers: {\n            ...endpoint !== \"revocation\" ? {\n                Accept: \"application/json\"\n            } : undefined,\n            ...requestOpts.headers\n        }\n    }, {\n        mTLS,\n        DPoP\n    });\n}\nmodule.exports = {\n    resolveResponseType,\n    resolveRedirectUri,\n    authFor,\n    authenticatedPost\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\nmodule.exports = {\n    CLOCK_TOLERANCE,\n    HTTP_OPTIONS\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanM/ZTA1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBIVFRQX09QVElPTlMgPSBTeW1ib2woKTtcbmNvbnN0IENMT0NLX1RPTEVSQU5DRSA9IFN5bWJvbCgpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQ0xPQ0tfVE9MRVJBTkNFLFxuICBIVFRQX09QVElPTlMsXG59O1xuIl0sIm5hbWVzIjpbIkhUVFBfT1BUSU9OUyIsIlN5bWJvbCIsIkNMT0NLX1RPTEVSQU5DRSIsIm1vZHVsZSIsImV4cG9ydHMiXSwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLGVBQWVDO0FBQ3JCLE1BQU1DLGtCQUFrQkQ7QUFFeEJFLE9BQU9DLE9BQU8sR0FBRztJQUNmRjtJQUNBRjtBQUNGIiwiZmlsZSI6Iihyc2MpLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvY29uc3RzLmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nmodule.exports = (token)=>{\n    if (typeof token !== \"string\" || !token) {\n        throw new TypeError(\"JWT must be a string\");\n    }\n    const { 0: header, 1: payload, 2: signature, length } = token.split(\".\");\n    if (length === 5) {\n        throw new TypeError(\"encrypted JWTs cannot be decoded\");\n    }\n    if (length !== 3) {\n        throw new Error(\"JWTs must have three components\");\n    }\n    try {\n        return {\n            header: JSON.parse(base64url.decode(header)),\n            payload: JSON.parse(base64url.decode(payload)),\n            signature\n        };\n    } catch (err) {\n        throw new Error(\"JWT is malformed\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWNvZGVfand0LmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFlBQVlDLG1CQUFPQSxDQUFDO0FBRTFCQyxPQUFPQyxPQUFPLEdBQUcsQ0FBQ0M7SUFDaEIsSUFBSSxPQUFPQSxVQUFVLFlBQVksQ0FBQ0EsT0FBTztRQUN2QyxNQUFNLElBQUlDLFVBQVU7SUFDdEI7SUFFQSxNQUFNLEVBQUUsR0FBR0MsTUFBTSxFQUFFLEdBQUdDLE9BQU8sRUFBRSxHQUFHQyxTQUFTLEVBQUVDLE1BQU0sRUFBRSxHQUFHTCxNQUFNTSxLQUFLLENBQUM7SUFFcEUsSUFBSUQsV0FBVyxHQUFHO1FBQ2hCLE1BQU0sSUFBSUosVUFBVTtJQUN0QjtJQUVBLElBQUlJLFdBQVcsR0FBRztRQUNoQixNQUFNLElBQUlFLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0YsT0FBTztZQUNMTCxRQUFRTSxLQUFLQyxLQUFLLENBQUNiLFVBQVVjLE1BQU0sQ0FBQ1I7WUFDcENDLFNBQVNLLEtBQUtDLEtBQUssQ0FBQ2IsVUFBVWMsTUFBTSxDQUFDUDtZQUNyQ0M7UUFDRjtJQUNGLEVBQUUsT0FBT08sS0FBSztRQUNaLE1BQU0sSUFBSUosTUFBTTtJQUNsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvZGVjb2RlX2p3dC5qcz81NjAzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vYmFzZTY0dXJsJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gKHRva2VuKSA9PiB7XG4gIGlmICh0eXBlb2YgdG9rZW4gIT09ICdzdHJpbmcnIHx8ICF0b2tlbikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0pXVCBtdXN0IGJlIGEgc3RyaW5nJyk7XG4gIH1cblxuICBjb25zdCB7IDA6IGhlYWRlciwgMTogcGF5bG9hZCwgMjogc2lnbmF0dXJlLCBsZW5ndGggfSA9IHRva2VuLnNwbGl0KCcuJyk7XG5cbiAgaWYgKGxlbmd0aCA9PT0gNSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2VuY3J5cHRlZCBKV1RzIGNhbm5vdCBiZSBkZWNvZGVkJyk7XG4gIH1cblxuICBpZiAobGVuZ3RoICE9PSAzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdKV1RzIG11c3QgaGF2ZSB0aHJlZSBjb21wb25lbnRzJyk7XG4gIH1cblxuICB0cnkge1xuICAgIHJldHVybiB7XG4gICAgICBoZWFkZXI6IEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZShoZWFkZXIpKSxcbiAgICAgIHBheWxvYWQ6IEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZShwYXlsb2FkKSksXG4gICAgICBzaWduYXR1cmUsXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdKV1QgaXMgbWFsZm9ybWVkJyk7XG4gIH1cbn07XG4iXSwibmFtZXMiOlsiYmFzZTY0dXJsIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJ0b2tlbiIsIlR5cGVFcnJvciIsImhlYWRlciIsInBheWxvYWQiLCJzaWduYXR1cmUiLCJsZW5ndGgiLCJzcGxpdCIsIkVycm9yIiwiSlNPTiIsInBhcnNlIiwiZGVjb2RlIiwiZXJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj)=>JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzPzhlNGIiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBnbG9iYWxUaGlzLnN0cnVjdHVyZWRDbG9uZSB8fCAoKG9iaikgPT4gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShvYmopKSk7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImdsb2JhbFRoaXMiLCJzdHJ1Y3R1cmVkQ2xvbmUiLCJvYmoiLCJKU09OIiwicGFyc2UiLCJzdHJpbmdpZnkiXSwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUdDLFdBQVdDLGVBQWUsSUFBSyxDQUFBLENBQUNDLE1BQVFDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsU0FBUyxDQUFDSCxLQUFJIiwiZmlsZSI6Iihyc2MpLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2hlbHBlcnMvZGVlcF9jbG9uZS5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction defaults(deep, target, ...sources) {\n    for (const source of sources){\n        if (!isPlainObject(source)) {\n            continue;\n        }\n        for (const [key, value] of Object.entries(source)){\n            /* istanbul ignore if */ if (key === \"__proto__\" || key === \"constructor\") {\n                continue;\n            }\n            if (typeof target[key] === \"undefined\" && typeof value !== \"undefined\") {\n                target[key] = value;\n            }\n            if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n                defaults(true, target[key], value);\n            }\n        }\n    }\n    return target;\n}\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst random = (bytes = 32)=>base64url.encode(randomBytes(bytes));\nmodule.exports = {\n    random,\n    state: random,\n    nonce: random,\n    codeVerifier: random,\n    codeChallenge: (codeVerifier)=>base64url.encode(createHash(\"sha256\").update(codeVerifier).digest())\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU0sRUFBRUEsVUFBVSxFQUFFQyxXQUFXLEVBQUUsR0FBR0MsbUJBQU9BLENBQUM7QUFFNUMsTUFBTUMsWUFBWUQsbUJBQU9BLENBQUM7QUFFMUIsTUFBTUUsU0FBUyxDQUFDQyxRQUFRLEVBQUUsR0FBS0YsVUFBVUcsTUFBTSxDQUFDTCxZQUFZSTtBQUU1REUsT0FBT0MsT0FBTyxHQUFHO0lBQ2ZKO0lBQ0FLLE9BQU9MO0lBQ1BNLE9BQU9OO0lBQ1BPLGNBQWNQO0lBQ2RRLGVBQWUsQ0FBQ0QsZUFDZFIsVUFBVUcsTUFBTSxDQUFDTixXQUFXLFVBQVVhLE1BQU0sQ0FBQ0YsY0FBY0csTUFBTTtBQUNyRSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL2dlbmVyYXRvcnMuanM/ODJmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGNyZWF0ZUhhc2gsIHJhbmRvbUJ5dGVzIH0gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxuY29uc3QgYmFzZTY0dXJsID0gcmVxdWlyZSgnLi9iYXNlNjR1cmwnKTtcblxuY29uc3QgcmFuZG9tID0gKGJ5dGVzID0gMzIpID0+IGJhc2U2NHVybC5lbmNvZGUocmFuZG9tQnl0ZXMoYnl0ZXMpKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIHJhbmRvbSxcbiAgc3RhdGU6IHJhbmRvbSxcbiAgbm9uY2U6IHJhbmRvbSxcbiAgY29kZVZlcmlmaWVyOiByYW5kb20sXG4gIGNvZGVDaGFsbGVuZ2U6IChjb2RlVmVyaWZpZXIpID0+XG4gICAgYmFzZTY0dXJsLmVuY29kZShjcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoY29kZVZlcmlmaWVyKS5kaWdlc3QoKSksXG59O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUhhc2giLCJyYW5kb21CeXRlcyIsInJlcXVpcmUiLCJiYXNlNjR1cmwiLCJyYW5kb20iLCJieXRlcyIsImVuY29kZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzdGF0ZSIsIm5vbmNlIiwiY29kZVZlcmlmaWVyIiwiY29kZUNoYWxsZW5nZSIsInVwZGF0ZSIsImRpZ2VzdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nmodule.exports = util.types.isKeyObject || ((obj)=>obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLE9BQU9DLG1CQUFPQSxDQUFDO0FBQ3JCLE1BQU1DLFNBQVNELG1CQUFPQSxDQUFDO0FBRXZCRSxPQUFPQyxPQUFPLEdBQUdKLEtBQUtLLEtBQUssQ0FBQ0MsV0FBVyxJQUFLLEVBQUNDLE1BQVFBLE9BQU9BLGVBQWVMLE9BQU9NLFNBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzP2EwMGIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdXRpbCA9IHJlcXVpcmUoJ3V0aWwnKTtcbmNvbnN0IGNyeXB0byA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHV0aWwudHlwZXMuaXNLZXlPYmplY3QgfHwgKChvYmopID0+IG9iaiAmJiBvYmogaW5zdGFuY2VvZiBjcnlwdG8uS2V5T2JqZWN0KTtcbiJdLCJuYW1lcyI6WyJ1dGlsIiwicmVxdWlyZSIsImNyeXB0byIsIm1vZHVsZSIsImV4cG9ydHMiLCJ0eXBlcyIsImlzS2V5T2JqZWN0Iiwib2JqIiwiS2V5T2JqZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a)=>!!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanM/ZTNlOCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IChhKSA9PiAhIWEgJiYgYS5jb25zdHJ1Y3RvciA9PT0gT2JqZWN0O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJhIiwiY29uc3RydWN0b3IiLCJPYmplY3QiXSwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUcsQ0FBQ0MsSUFBTSxDQUFDLENBQUNBLEtBQUtBLEVBQUVDLFdBQVcsS0FBS0MiLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx)=>{\n    if (!caches.has(ctx)) {\n        caches.set(ctx, new LRU({\n            max: 100\n        }));\n    }\n    return caches.get(ctx);\n};\nasync function getKeyStore(reload = false) {\n    assertIssuerConfiguration(this, \"jwks_uri\");\n    const keystore = keystores.get(this);\n    const cache = lrus(this);\n    if (reload || !keystore) {\n        if (inFlight.has(this)) {\n            return inFlight.get(this);\n        }\n        cache.reset();\n        inFlight.set(this, (async ()=>{\n            const response = await request.call(this, {\n                method: \"GET\",\n                responseType: \"json\",\n                url: this.jwks_uri,\n                headers: {\n                    Accept: \"application/json, application/jwk-set+json\"\n                }\n            }).finally(()=>{\n                inFlight.delete(this);\n            });\n            const jwks = processResponse(response);\n            const joseKeyStore = KeyStore.fromJWKS(jwks, {\n                onlyPublic: true\n            });\n            cache.set(\"throttle\", true, 60 * 1000);\n            keystores.set(this, joseKeyStore);\n            return joseKeyStore;\n        })());\n        return inFlight.get(this);\n    }\n    return keystore;\n}\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n    const cache = lrus(this);\n    const def = {\n        kid,\n        kty,\n        alg,\n        use\n    };\n    const defHash = objectHash(def, {\n        algorithm: \"sha256\",\n        ignoreUnknown: true,\n        unorderedArrays: true,\n        unorderedSets: true,\n        respectType: false\n    });\n    // refresh keystore on every unknown key but also only upto once every minute\n    const freshJwksUri = cache.get(defHash) || cache.get(\"throttle\");\n    const keystore = await getKeyStore.call(this, !freshJwksUri);\n    const keys = keystore.all(def);\n    delete def.use;\n    if (keys.length === 0) {\n        throw new RPError({\n            printf: [\n                \"no valid key found in issuer's jwks_uri for key parameters %j\",\n                def\n            ],\n            jwks: keystore\n        });\n    }\n    if (!allowMulti && keys.length > 1 && !kid) {\n        throw new RPError({\n            printf: [\n                \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n                def\n            ],\n            jwks: keystore\n        });\n    }\n    cache.set(defHash, true);\n    return keys;\n}\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst internal = Symbol();\nconst keyscore = (key, { alg, use })=>{\n    let score = 0;\n    if (alg && key.alg) {\n        score++;\n    }\n    if (use && key.use) {\n        score++;\n    }\n    return score;\n};\nfunction getKtyFromAlg(alg) {\n    switch(typeof alg === \"string\" && alg.slice(0, 2)){\n        case \"RS\":\n        case \"PS\":\n            return \"RSA\";\n        case \"ES\":\n            return \"EC\";\n        case \"Ed\":\n            return \"OKP\";\n        default:\n            return undefined;\n    }\n}\nfunction getAlgorithms(use, alg, kty, crv) {\n    // Ed25519, Ed448, and secp256k1 always have \"alg\"\n    // OKP always has \"use\"\n    if (alg) {\n        return new Set([\n            alg\n        ]);\n    }\n    switch(kty){\n        case \"EC\":\n            {\n                let algs = [];\n                if (use === \"enc\" || use === undefined) {\n                    algs = algs.concat([\n                        \"ECDH-ES\",\n                        \"ECDH-ES+A128KW\",\n                        \"ECDH-ES+A192KW\",\n                        \"ECDH-ES+A256KW\"\n                    ]);\n                }\n                if (use === \"sig\" || use === undefined) {\n                    switch(crv){\n                        case \"P-256\":\n                        case \"P-384\":\n                            algs = algs.concat([\n                                `ES${crv.slice(-3)}`\n                            ]);\n                            break;\n                        case \"P-521\":\n                            algs = algs.concat([\n                                \"ES512\"\n                            ]);\n                            break;\n                        case \"secp256k1\":\n                            if (jose.cryptoRuntime === \"node:crypto\") {\n                                algs = algs.concat([\n                                    \"ES256K\"\n                                ]);\n                            }\n                            break;\n                    }\n                }\n                return new Set(algs);\n            }\n        case \"OKP\":\n            {\n                return new Set([\n                    \"ECDH-ES\",\n                    \"ECDH-ES+A128KW\",\n                    \"ECDH-ES+A192KW\",\n                    \"ECDH-ES+A256KW\"\n                ]);\n            }\n        case \"RSA\":\n            {\n                let algs = [];\n                if (use === \"enc\" || use === undefined) {\n                    algs = algs.concat([\n                        \"RSA-OAEP\",\n                        \"RSA-OAEP-256\",\n                        \"RSA-OAEP-384\",\n                        \"RSA-OAEP-512\"\n                    ]);\n                    if (jose.cryptoRuntime === \"node:crypto\") {\n                        algs = algs.concat([\n                            \"RSA1_5\"\n                        ]);\n                    }\n                }\n                if (use === \"sig\" || use === undefined) {\n                    algs = algs.concat([\n                        \"PS256\",\n                        \"PS384\",\n                        \"PS512\",\n                        \"RS256\",\n                        \"RS384\",\n                        \"RS512\"\n                    ]);\n                }\n                return new Set(algs);\n            }\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nmodule.exports = class KeyStore {\n    #keys;\n    constructor(i, keys){\n        if (i !== internal) throw new Error(\"invalid constructor call\");\n        this.#keys = keys;\n    }\n    toJWKS() {\n        return {\n            keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } })=>jwk)\n        };\n    }\n    all({ alg, kid, use } = {}) {\n        if (!use || !alg) {\n            throw new Error();\n        }\n        const kty = getKtyFromAlg(alg);\n        const search = {\n            alg,\n            use\n        };\n        return this.filter((key)=>{\n            let candidate = true;\n            if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n                candidate = false;\n            }\n            if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n                candidate = false;\n            }\n            if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n                candidate = false;\n            }\n            if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n                candidate = false;\n            } else if (!key.algorithms.has(alg)) {\n                candidate = false;\n            }\n            return candidate;\n        }).sort((first, second)=>keyscore(second, search) - keyscore(first, search));\n    }\n    get(...args) {\n        return this.all(...args)[0];\n    }\n    static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n        if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !(\"kty\" in k))) {\n            throw new TypeError(\"jwks must be a JSON Web Key Set formatted object\");\n        }\n        const keys = [];\n        for (let jwk of jwks.keys){\n            jwk = clone(jwk);\n            const { kty, kid, crv } = jwk;\n            let { alg, use } = jwk;\n            if (typeof kty !== \"string\" || !kty) {\n                continue;\n            }\n            if (use !== undefined && use !== \"sig\" && use !== \"enc\") {\n                continue;\n            }\n            if (typeof alg !== \"string\" && alg !== undefined) {\n                continue;\n            }\n            if (typeof kid !== \"string\" && kid !== undefined) {\n                continue;\n            }\n            if (kty === \"EC\" && use === \"sig\") {\n                switch(crv){\n                    case \"P-256\":\n                        alg = \"ES256\";\n                        break;\n                    case \"P-384\":\n                        alg = \"ES384\";\n                        break;\n                    case \"P-521\":\n                        alg = \"ES512\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (crv === \"secp256k1\") {\n                use = \"sig\";\n                alg = \"ES256K\";\n            }\n            if (kty === \"OKP\") {\n                switch(crv){\n                    case \"Ed25519\":\n                    case \"Ed448\":\n                        use = \"sig\";\n                        alg = \"EdDSA\";\n                        break;\n                    case \"X25519\":\n                    case \"X448\":\n                        use = \"enc\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (alg && !use) {\n                switch(true){\n                    case alg.startsWith(\"ECDH\"):\n                        use = \"enc\";\n                        break;\n                    case alg.startsWith(\"RSA\"):\n                        use = \"enc\";\n                        break;\n                    default:\n                        break;\n                }\n            }\n            if (onlyPrivate && (jwk.kty === \"oct\" || !jwk.d)) {\n                throw new Error(\"jwks must only contain private keys\");\n            }\n            if (onlyPublic && (jwk.d || jwk.k)) {\n                continue;\n            }\n            keys.push({\n                jwk: {\n                    ...jwk,\n                    alg,\n                    use\n                },\n                async keyObject (alg) {\n                    if (this[alg]) {\n                        return this[alg];\n                    }\n                    const keyObject = await jose.importJWK(this.jwk, alg);\n                    this[alg] = keyObject;\n                    return keyObject;\n                },\n                get algorithms () {\n                    Object.defineProperty(this, \"algorithms\", {\n                        value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n                        enumerable: true,\n                        configurable: false\n                    });\n                    return this.algorithms;\n                }\n            });\n        }\n        return new this(internal, keys);\n    }\n    filter(...args) {\n        return this.#keys.filter(...args);\n    }\n    find(...args) {\n        return this.#keys.find(...args);\n    }\n    every(...args) {\n        return this.#keys.every(...args);\n    }\n    some(...args) {\n        return this.#keys.some(...args);\n    }\n    map(...args) {\n        return this.#keys.map(...args);\n    }\n    forEach(...args) {\n        return this.#keys.forEach(...args);\n    }\n    reduce(...args) {\n        return this.#keys.reduce(...args);\n    }\n    sort(...args) {\n        return this.#keys.sort(...args);\n    }\n    *[Symbol.iterator]() {\n        for (const key of this.#keys){\n            yield key;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction merge(target, ...sources) {\n    for (const source of sources){\n        if (!isPlainObject(source)) {\n            continue;\n        }\n        for (const [key, value] of Object.entries(source)){\n            /* istanbul ignore if */ if (key === \"__proto__\" || key === \"constructor\") {\n                continue;\n            }\n            if (isPlainObject(target[key]) && isPlainObject(value)) {\n                target[key] = merge(target[key], value);\n            } else if (typeof value !== \"undefined\") {\n                target[key] = value;\n            }\n        }\n    }\n    return target;\n}\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxnQkFBZ0JDLG1CQUFPQSxDQUFDO0FBRTlCLFNBQVNDLE1BQU1DLE1BQU0sRUFBRSxHQUFHQyxPQUFPO0lBQy9CLEtBQUssTUFBTUMsVUFBVUQsUUFBUztRQUM1QixJQUFJLENBQUNKLGNBQWNLLFNBQVM7WUFDMUI7UUFDRjtRQUNBLEtBQUssTUFBTSxDQUFDQyxLQUFLQyxNQUFNLElBQUlDLE9BQU9DLE9BQU8sQ0FBQ0osUUFBUztZQUNqRCxzQkFBc0IsR0FDdEIsSUFBSUMsUUFBUSxlQUFlQSxRQUFRLGVBQWU7Z0JBQ2hEO1lBQ0Y7WUFDQSxJQUFJTixjQUFjRyxNQUFNLENBQUNHLElBQUksS0FBS04sY0FBY08sUUFBUTtnQkFDdERKLE1BQU0sQ0FBQ0csSUFBSSxHQUFHSixNQUFNQyxNQUFNLENBQUNHLElBQUksRUFBRUM7WUFDbkMsT0FBTyxJQUFJLE9BQU9BLFVBQVUsYUFBYTtnQkFDdkNKLE1BQU0sQ0FBQ0csSUFBSSxHQUFHQztZQUNoQjtRQUNGO0lBQ0Y7SUFFQSxPQUFPSjtBQUNUO0FBRUFPLE9BQU9DLE9BQU8sR0FBR1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcz9hNThmIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUGxhaW5PYmplY3QgPSByZXF1aXJlKCcuL2lzX3BsYWluX29iamVjdCcpO1xuXG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIC4uLnNvdXJjZXMpIHtcbiAgZm9yIChjb25zdCBzb3VyY2Ugb2Ygc291cmNlcykge1xuICAgIGlmICghaXNQbGFpbk9iamVjdChzb3VyY2UpKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc291cmNlKSkge1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIGlmICovXG4gICAgICBpZiAoa2V5ID09PSAnX19wcm90b19fJyB8fCBrZXkgPT09ICdjb25zdHJ1Y3RvcicpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBpZiAoaXNQbGFpbk9iamVjdCh0YXJnZXRba2V5XSkgJiYgaXNQbGFpbk9iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSBtZXJnZSh0YXJnZXRba2V5XSwgdmFsdWUpO1xuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtZXJnZTtcbiJdLCJuYW1lcyI6WyJpc1BsYWluT2JqZWN0IiwicmVxdWlyZSIsIm1lcmdlIiwidGFyZ2V0Iiwic291cmNlcyIsInNvdXJjZSIsImtleSIsInZhbHVlIiwiT2JqZWN0IiwiZW50cmllcyIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n    const obj = {};\n    for (const path of paths){\n        if (object[path] !== undefined) {\n            obj[path] = object[path];\n        }\n    }\n    return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzPzY4NjciXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBwaWNrKG9iamVjdCwgLi4ucGF0aHMpIHtcbiAgY29uc3Qgb2JqID0ge307XG4gIGZvciAoY29uc3QgcGF0aCBvZiBwYXRocykge1xuICAgIGlmIChvYmplY3RbcGF0aF0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgb2JqW3BhdGhdID0gb2JqZWN0W3BhdGhdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gb2JqO1xufTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwicGljayIsIm9iamVjdCIsInBhdGhzIiwib2JqIiwicGF0aCIsInVuZGVmaW5lZCJdLCJtYXBwaW5ncyI6IkFBQUFBLE9BQU9DLE9BQU8sR0FBRyxTQUFTQyxLQUFLQyxNQUFNLEVBQUUsR0FBR0MsS0FBSztJQUM3QyxNQUFNQyxNQUFNLENBQUM7SUFDYixLQUFLLE1BQU1DLFFBQVFGLE1BQU87UUFDeEIsSUFBSUQsTUFBTSxDQUFDRyxLQUFLLEtBQUtDLFdBQVc7WUFDOUJGLEdBQUcsQ0FBQ0MsS0FBSyxHQUFHSCxNQUFNLENBQUNHLEtBQUs7UUFDMUI7SUFDRjtJQUNBLE9BQU9EO0FBQ1QiLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst throwAuthenticateErrors = (response)=>{\n    const params = parseWwwAuthenticate(response.headers[\"www-authenticate\"]);\n    if (params.error) {\n        throw new OPError(params, response);\n    }\n};\nconst isStandardBodyError = (response)=>{\n    let result = false;\n    try {\n        let jsonbody;\n        if (typeof response.body !== \"object\" || Buffer.isBuffer(response.body)) {\n            jsonbody = JSON.parse(response.body);\n        } else {\n            jsonbody = response.body;\n        }\n        result = typeof jsonbody.error === \"string\" && jsonbody.error.length;\n        if (result) Object.defineProperty(response, \"body\", {\n            value: jsonbody,\n            configurable: true\n        });\n    } catch (err) {}\n    return result;\n};\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n    if (response.statusCode !== statusCode) {\n        if (bearer) {\n            throwAuthenticateErrors(response);\n        }\n        if (isStandardBodyError(response)) {\n            throw new OPError(response.body, response);\n        }\n        throw new OPError({\n            error: format(\"expected %i %s, got: %i %s\", statusCode, STATUS_CODES[statusCode], response.statusCode, STATUS_CODES[response.statusCode])\n        }, response);\n    }\n    if (body && !response.body) {\n        throw new OPError({\n            error: format(\"expected %i %s with body but no body was returned\", statusCode, STATUS_CODES[statusCode])\n        }, response);\n    }\n    return response.body;\n}\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\nconst allowed = [\n    \"agent\",\n    \"ca\",\n    \"cert\",\n    \"crl\",\n    \"headers\",\n    \"key\",\n    \"lookup\",\n    \"passphrase\",\n    \"pfx\",\n    \"timeout\"\n];\nconst setDefaults = (props, options)=>{\n    DEFAULT_HTTP_OPTIONS = defaultsDeep({}, props.length ? pick(options, ...props) : options, DEFAULT_HTTP_OPTIONS);\n};\nsetDefaults([], {\n    headers: {\n        \"User-Agent\": `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n        \"Accept-Encoding\": \"identity\"\n    },\n    timeout: 3500\n});\nfunction send(req, body, contentType) {\n    if (contentType) {\n        req.removeHeader(\"content-type\");\n        req.setHeader(\"content-type\", contentType);\n    }\n    if (body) {\n        req.removeHeader(\"content-length\");\n        req.setHeader(\"content-length\", Buffer.byteLength(body));\n        req.write(body);\n    }\n    req.end();\n}\nconst nonces = new LRU({\n    max: 100\n});\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n    let url;\n    try {\n        url = new URL(options.url);\n        delete options.url;\n        assert(/^(https?:)$/.test(url.protocol));\n    } catch (err) {\n        throw new TypeError(\"only valid absolute URLs can be requested\");\n    }\n    const optsFn = this[HTTP_OPTIONS];\n    let opts = options;\n    const nonceKey = `${url.origin}${url.pathname}`;\n    if (DPoP && \"dpopProof\" in this) {\n        opts.headers = opts.headers || {};\n        opts.headers.DPoP = await this.dpopProof({\n            htu: `${url.origin}${url.pathname}`,\n            htm: options.method || \"GET\",\n            nonce: nonces.get(nonceKey)\n        }, DPoP, accessToken);\n    }\n    let userOptions;\n    if (optsFn) {\n        userOptions = pick(optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)), ...allowed);\n    }\n    opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n    if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n        throw new TypeError(\"mutual-TLS certificate and key not set\");\n    }\n    if (opts.searchParams) {\n        for (const [key, value] of Object.entries(opts.searchParams)){\n            url.searchParams.delete(key);\n            url.searchParams.set(key, value);\n        }\n    }\n    let responseType;\n    let form;\n    let json;\n    let body;\n    ({ form, responseType, json, body, ...opts } = opts);\n    for (const [key, value] of Object.entries(opts.headers || {})){\n        if (value === undefined) {\n            delete opts.headers[key];\n        }\n    }\n    let response;\n    const req = (url.protocol === \"https:\" ? https.request : http.request)(url.href, opts);\n    return (async ()=>{\n        if (json) {\n            send(req, JSON.stringify(json), \"application/json\");\n        } else if (form) {\n            send(req, querystring.stringify(form), \"application/x-www-form-urlencoded\");\n        } else if (body) {\n            send(req, body);\n        } else {\n            send(req);\n        }\n        [response] = await Promise.race([\n            once(req, \"response\"),\n            once(req, \"timeout\")\n        ]);\n        // timeout reached\n        if (!response) {\n            req.destroy();\n            throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n        }\n        const parts = [];\n        for await (const part of response){\n            parts.push(part);\n        }\n        if (parts.length) {\n            switch(responseType){\n                case \"json\":\n                    {\n                        Object.defineProperty(response, \"body\", {\n                            get () {\n                                let value = Buffer.concat(parts);\n                                try {\n                                    value = JSON.parse(value);\n                                } catch (err) {\n                                    Object.defineProperty(err, \"response\", {\n                                        value: response\n                                    });\n                                    throw err;\n                                } finally{\n                                    Object.defineProperty(response, \"body\", {\n                                        value,\n                                        configurable: true\n                                    });\n                                }\n                                return value;\n                            },\n                            configurable: true\n                        });\n                        break;\n                    }\n                case undefined:\n                case \"buffer\":\n                    {\n                        Object.defineProperty(response, \"body\", {\n                            get () {\n                                const value = Buffer.concat(parts);\n                                Object.defineProperty(response, \"body\", {\n                                    value,\n                                    configurable: true\n                                });\n                                return value;\n                            },\n                            configurable: true\n                        });\n                        break;\n                    }\n                default:\n                    throw new TypeError(\"unsupported responseType request option\");\n            }\n        }\n        return response;\n    })().catch((err)=>{\n        if (response) Object.defineProperty(err, \"response\", {\n            value: response\n        });\n        throw err;\n    }).finally(()=>{\n        const dpopNonce = response && response.headers[\"dpop-nonce\"];\n        if (dpopNonce && NQCHAR.test(dpopNonce)) {\n            nonces.set(nonceKey, dpopNonce);\n        }\n    });\n};\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = ()=>Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcz80ZTdmIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gKCkgPT4gTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsIk1hdGgiLCJmbG9vciIsIkRhdGUiLCJub3ciXSwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUcsSUFBTUMsS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUsiLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBQSx3QkFBd0IsR0FBRyxJQUFJRyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHRqcy1zYWFzLWFwcC8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3dlYWtfY2FjaGUuanM/YzkzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cy5rZXlzdG9yZXMgPSBuZXcgV2Vha01hcCgpO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJrZXlzdG9yZXMiLCJXZWFrTWFwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\nconst PORT = /^\\d+$/;\nfunction hasScheme(input) {\n    if (input.includes(\"://\")) return true;\n    const authority = input.replace(/(\\/|\\?)/g, \"#\").split(\"#\")[0];\n    if (authority.includes(\":\")) {\n        const index = authority.indexOf(\":\");\n        const hostOrPort = authority.slice(index + 1);\n        if (!PORT.test(hostOrPort)) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction acctSchemeAssumed(input) {\n    if (!input.includes(\"@\")) return false;\n    const parts = input.split(\"@\");\n    const host = parts[parts.length - 1];\n    return !(host.includes(\":\") || host.includes(\"/\") || host.includes(\"?\"));\n}\nfunction normalize(input) {\n    if (typeof input !== \"string\") {\n        throw new TypeError(\"input must be a string\");\n    }\n    let output;\n    if (hasScheme(input)) {\n        output = input;\n    } else if (acctSchemeAssumed(input)) {\n        output = `acct:${input}`;\n    } else {\n        output = `https://${input}`;\n    }\n    return output.split(\"#\")[0];\n}\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\nmodule.exports = (wwwAuthenticate)=>{\n    const params = {};\n    try {\n        while(REGEXP.exec(wwwAuthenticate) !== null){\n            if (RegExp.$1 && RegExp.$2) {\n                params[RegExp.$1] = RegExp.$2.slice(1, -1);\n            }\n        }\n    } catch (err) {}\n    return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcz9mMDBkIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFJFR0VYUCA9IC8oXFx3Kyk9KFwiW15cIl0qXCIpL2c7XG5cbm1vZHVsZS5leHBvcnRzID0gKHd3d0F1dGhlbnRpY2F0ZSkgPT4ge1xuICBjb25zdCBwYXJhbXMgPSB7fTtcbiAgdHJ5IHtcbiAgICB3aGlsZSAoUkVHRVhQLmV4ZWMod3d3QXV0aGVudGljYXRlKSAhPT0gbnVsbCkge1xuICAgICAgaWYgKFJlZ0V4cC4kMSAmJiBSZWdFeHAuJDIpIHtcbiAgICAgICAgcGFyYW1zW1JlZ0V4cC4kMV0gPSBSZWdFeHAuJDIuc2xpY2UoMSwgLTEpO1xuICAgICAgfVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyKSB7fVxuXG4gIHJldHVybiBwYXJhbXM7XG59O1xuIl0sIm5hbWVzIjpbIlJFR0VYUCIsIm1vZHVsZSIsImV4cG9ydHMiLCJ3d3dBdXRoZW50aWNhdGUiLCJwYXJhbXMiLCJleGVjIiwiUmVnRXhwIiwiJDEiLCIkMiIsInNsaWNlIiwiZXJyIl0sIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxTQUFTO0FBRWZDLE9BQU9DLE9BQU8sR0FBRyxDQUFDQztJQUNoQixNQUFNQyxTQUFTLENBQUM7SUFDaEIsSUFBSTtRQUNGLE1BQU9KLE9BQU9LLElBQUksQ0FBQ0YscUJBQXFCLEtBQU07WUFDNUMsSUFBSUcsT0FBT0MsRUFBRSxJQUFJRCxPQUFPRSxFQUFFLEVBQUU7Z0JBQzFCSixNQUFNLENBQUNFLE9BQU9DLEVBQUUsQ0FBQyxHQUFHRCxPQUFPRSxFQUFFLENBQUNDLEtBQUssQ0FBQyxHQUFHLENBQUM7WUFDMUM7UUFDRjtJQUNGLEVBQUUsT0FBT0MsS0FBSyxDQUFDO0lBRWYsT0FBT047QUFDVCIsImZpbGUiOiIocnNjKS8uL25vZGVfbW9kdWxlcy9vcGVuaWQtY2xpZW50L2xpYi9oZWxwZXJzL3d3d19hdXRoZW50aWNhdGVfcGFyc2VyLmpzIiwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nmodule.exports = {\n    Issuer,\n    Strategy,\n    TokenSet,\n    errors: {\n        OPError,\n        RPError\n    },\n    custom: {\n        setHttpOptionsDefaults: setDefaults,\n        http_options: HTTP_OPTIONS,\n        clock_tolerance: CLOCK_TOLERANCE\n    },\n    generators\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsTUFBTUEsU0FBU0MsbUJBQU9BLENBQUM7QUFDdkIsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRSxHQUFHRixtQkFBT0EsQ0FBQztBQUNyQyxNQUFNRyxXQUFXSCxtQkFBT0EsQ0FBQztBQUN6QixNQUFNSSxXQUFXSixtQkFBT0EsQ0FBQztBQUN6QixNQUFNLEVBQUVLLGVBQWUsRUFBRUMsWUFBWSxFQUFFLEdBQUdOLG1CQUFPQSxDQUFDO0FBQ2xELE1BQU1PLGFBQWFQLG1CQUFPQSxDQUFDO0FBQzNCLE1BQU0sRUFBRVEsV0FBVyxFQUFFLEdBQUdSLG1CQUFPQSxDQUFDO0FBRWhDUyxPQUFPQyxPQUFPLEdBQUc7SUFDZlg7SUFDQUk7SUFDQUM7SUFDQU8sUUFBUTtRQUNOVjtRQUNBQztJQUNGO0lBQ0FVLFFBQVE7UUFDTkMsd0JBQXdCTDtRQUN4Qk0sY0FBY1I7UUFDZFMsaUJBQWlCVjtJQUNuQjtJQUNBRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dGpzLXNhYXMtYXBwLy4vbm9kZV9tb2R1bGVzL29wZW5pZC1jbGllbnQvbGliL2luZGV4LmpzP2NmOTEiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSXNzdWVyID0gcmVxdWlyZSgnLi9pc3N1ZXInKTtcbmNvbnN0IHsgT1BFcnJvciwgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi9lcnJvcnMnKTtcbmNvbnN0IFN0cmF0ZWd5ID0gcmVxdWlyZSgnLi9wYXNzcG9ydF9zdHJhdGVneScpO1xuY29uc3QgVG9rZW5TZXQgPSByZXF1aXJlKCcuL3Rva2VuX3NldCcpO1xuY29uc3QgeyBDTE9DS19UT0xFUkFOQ0UsIEhUVFBfT1BUSU9OUyB9ID0gcmVxdWlyZSgnLi9oZWxwZXJzL2NvbnN0cycpO1xuY29uc3QgZ2VuZXJhdG9ycyA9IHJlcXVpcmUoJy4vaGVscGVycy9nZW5lcmF0b3JzJyk7XG5jb25zdCB7IHNldERlZmF1bHRzIH0gPSByZXF1aXJlKCcuL2hlbHBlcnMvcmVxdWVzdCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgSXNzdWVyLFxuICBTdHJhdGVneSxcbiAgVG9rZW5TZXQsXG4gIGVycm9yczoge1xuICAgIE9QRXJyb3IsXG4gICAgUlBFcnJvcixcbiAgfSxcbiAgY3VzdG9tOiB7XG4gICAgc2V0SHR0cE9wdGlvbnNEZWZhdWx0czogc2V0RGVmYXVsdHMsXG4gICAgaHR0cF9vcHRpb25zOiBIVFRQX09QVElPTlMsXG4gICAgY2xvY2tfdG9sZXJhbmNlOiBDTE9DS19UT0xFUkFOQ0UsXG4gIH0sXG4gIGdlbmVyYXRvcnMsXG59O1xuIl0sIm5hbWVzIjpbIklzc3VlciIsInJlcXVpcmUiLCJPUEVycm9yIiwiUlBFcnJvciIsIlN0cmF0ZWd5IiwiVG9rZW5TZXQiLCJDTE9DS19UT0xFUkFOQ0UiLCJIVFRQX09QVElPTlMiLCJnZW5lcmF0b3JzIiwic2V0RGVmYXVsdHMiLCJtb2R1bGUiLCJleHBvcnRzIiwiZXJyb3JzIiwiY3VzdG9tIiwic2V0SHR0cE9wdGlvbnNEZWZhdWx0cyIsImh0dHBfb3B0aW9ucyIsImNsb2NrX3RvbGVyYW5jZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst AAD_MULTITENANT_DISCOVERY = [\n    \"https://login.microsoftonline.com/common/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration\",\n    \"https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration\"\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n    claim_types_supported: [\n        \"normal\"\n    ],\n    claims_parameter_supported: false,\n    grant_types_supported: [\n        \"authorization_code\",\n        \"implicit\"\n    ],\n    request_parameter_supported: false,\n    request_uri_parameter_supported: true,\n    require_request_uri_registration: false,\n    response_modes_supported: [\n        \"query\",\n        \"fragment\"\n    ],\n    token_endpoint_auth_methods_supported: [\n        \"client_secret_basic\"\n    ]\n};\nclass Issuer {\n    #metadata;\n    constructor(meta = {}){\n        const aadIssValidation = meta[AAD_MULTITENANT];\n        delete meta[AAD_MULTITENANT];\n        [\n            \"introspection\",\n            \"revocation\"\n        ].forEach((endpoint)=>{\n            // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n            // are defined\n            if (meta[`${endpoint}_endpoint`] && meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined && meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined) {\n                if (meta.token_endpoint_auth_methods_supported) {\n                    meta[`${endpoint}_endpoint_auth_methods_supported`] = meta.token_endpoint_auth_methods_supported;\n                }\n                if (meta.token_endpoint_auth_signing_alg_values_supported) {\n                    meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] = meta.token_endpoint_auth_signing_alg_values_supported;\n                }\n            }\n        });\n        this.#metadata = new Map();\n        Object.entries(meta).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        registry.set(this.issuer, this);\n        const Client = getClient(this, aadIssValidation);\n        Object.defineProperties(this, {\n            Client: {\n                value: Client,\n                enumerable: true\n            },\n            FAPI1Client: {\n                value: class FAPI1Client extends Client {\n                },\n                enumerable: true\n            },\n            FAPI2Client: {\n                value: class FAPI2Client extends Client {\n                },\n                enumerable: true\n            }\n        });\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async webfinger(input) {\n        const resource = webfingerNormalize(input);\n        const { host } = url.parse(resource);\n        const webfingerUrl = `https://${host}/.well-known/webfinger`;\n        const response = await request.call(this, {\n            method: \"GET\",\n            url: webfingerUrl,\n            responseType: \"json\",\n            searchParams: {\n                resource,\n                rel: \"http://openid.net/specs/connect/1.0/issuer\"\n            },\n            headers: {\n                Accept: \"application/json\"\n            }\n        });\n        const body = processResponse(response);\n        const location = Array.isArray(body.links) && body.links.find((link)=>typeof link === \"object\" && link.rel === \"http://openid.net/specs/connect/1.0/issuer\" && link.href);\n        if (!location) {\n            throw new RPError({\n                message: \"no issuer found in webfinger response\",\n                body\n            });\n        }\n        if (typeof location.href !== \"string\" || !location.href.startsWith(\"https://\")) {\n            throw new RPError({\n                printf: [\n                    \"invalid issuer location %s\",\n                    location.href\n                ],\n                body\n            });\n        }\n        const expectedIssuer = location.href;\n        if (registry.has(expectedIssuer)) {\n            return registry.get(expectedIssuer);\n        }\n        const issuer = await this.discover(expectedIssuer);\n        if (issuer.issuer !== expectedIssuer) {\n            registry.del(issuer.issuer);\n            throw new RPError(\"discovered issuer mismatch, expected %s, got: %s\", expectedIssuer, issuer.issuer);\n        }\n        return issuer;\n    }\n    static async discover(uri) {\n        const wellKnownUri = resolveWellKnownUri(uri);\n        const response = await request.call(this, {\n            method: \"GET\",\n            responseType: \"json\",\n            url: wellKnownUri,\n            headers: {\n                Accept: \"application/json\"\n            }\n        });\n        const body = processResponse(response);\n        return new Issuer({\n            ...ISSUER_DEFAULTS,\n            ...body,\n            [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL)=>wellKnownUri.startsWith(discoveryURL))\n        });\n    }\n    async reloadJwksUri() {\n        await keystore.call(this, true);\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n}\nfunction resolveWellKnownUri(uri) {\n    const parsed = url.parse(uri);\n    if (parsed.pathname.includes(\"/.well-known/\")) {\n        return uri;\n    } else {\n        let pathname;\n        if (parsed.pathname.endsWith(\"/\")) {\n            pathname = `${parsed.pathname}.well-known/openid-configuration`;\n        } else {\n            pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n        }\n        return url.format({\n            ...parsed,\n            pathname\n        });\n    }\n}\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nmodule.exports = new LRU({\n    max: 100\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLE1BQU1DLG1CQUFPQSxDQUFDO0FBRXBCQyxPQUFPQyxPQUFPLEdBQUcsSUFBSUgsSUFBSTtJQUFFSSxLQUFLO0FBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0anMtc2Fhcy1hcHAvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzP2IwNmQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTFJVID0gcmVxdWlyZSgnbHJ1LWNhY2hlJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gbmV3IExSVSh7IG1heDogMTAwIH0pO1xuIl0sIm5hbWVzIjpbIkxSVSIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwibWF4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nfunction verified(err, user, info = {}) {\n    if (err) {\n        this.error(err);\n    } else if (!user) {\n        this.fail(info);\n    } else {\n        this.success(user, info);\n    }\n}\nfunction OpenIDConnectStrategy({ client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {}, verify) {\n    if (!(client instanceof BaseClient)) {\n        throw new TypeError(\"client must be an instance of openid-client Client\");\n    }\n    if (typeof verify !== \"function\") {\n        throw new TypeError(\"verify callback must be a function\");\n    }\n    if (!client.issuer || !client.issuer.issuer) {\n        throw new TypeError(\"client must have an issuer with an identifier\");\n    }\n    this._client = client;\n    this._issuer = client.issuer;\n    this._verify = verify;\n    this._passReqToCallback = passReqToCallback;\n    this._usePKCE = usePKCE;\n    this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n    this._params = cloneDeep(params);\n    // state and nonce are handled in authenticate()\n    delete this._params.state;\n    delete this._params.nonce;\n    this._extras = cloneDeep(extras);\n    if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n    if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n    if (!this._params.scope) this._params.scope = \"openid\";\n    if (this._usePKCE === true) {\n        const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported) ? this._issuer.code_challenge_methods_supported : false;\n        if (supportedMethods && supportedMethods.includes(\"S256\")) {\n            this._usePKCE = \"S256\";\n        } else if (supportedMethods && supportedMethods.includes(\"plain\")) {\n            this._usePKCE = \"plain\";\n        } else if (supportedMethods) {\n            throw new TypeError(\"neither code_challenge_method supported by the client is supported by the issuer\");\n        } else {\n            this._usePKCE = \"S256\";\n        }\n    } else if (typeof this._usePKCE === \"string\" && ![\n        \"plain\",\n        \"S256\"\n    ].includes(this._usePKCE)) {\n        throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n    }\n    this.name = url.parse(client.issuer.issuer).hostname;\n}\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n    (async ()=>{\n        const client = this._client;\n        if (!req.session) {\n            throw new TypeError(\"authentication requires session support\");\n        }\n        const reqParams = client.callbackParams(req);\n        const sessionKey = this._key;\n        const { 0: parameter, length } = Object.keys(reqParams);\n        /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */ if (length === 0 || length === 1 && parameter === \"iss\") {\n            // provide options object with extra authentication parameters\n            const params = {\n                state: random(),\n                ...this._params,\n                ...options\n            };\n            if (!params.nonce && params.response_type.includes(\"id_token\")) {\n                params.nonce = random();\n            }\n            req.session[sessionKey] = pick(params, \"nonce\", \"state\", \"max_age\", \"response_type\");\n            if (this._usePKCE && params.response_type.includes(\"code\")) {\n                const verifier = random();\n                req.session[sessionKey].code_verifier = verifier;\n                switch(this._usePKCE){\n                    case \"S256\":\n                        params.code_challenge = codeChallenge(verifier);\n                        params.code_challenge_method = \"S256\";\n                        break;\n                    case \"plain\":\n                        params.code_challenge = verifier;\n                        break;\n                }\n            }\n            this.redirect(client.authorizationUrl(params));\n            return;\n        }\n        /* end authentication request */ /* start authentication response */ const session = req.session[sessionKey];\n        if (Object.keys(session || {}).length === 0) {\n            throw new Error(format('did not find expected authorization request details in session, req.session[\"%s\"] is %j', sessionKey, session));\n        }\n        const { state, nonce, max_age: maxAge, code_verifier: codeVerifier, response_type: responseType } = session;\n        try {\n            delete req.session[sessionKey];\n        } catch (err) {}\n        const opts = {\n            redirect_uri: this._params.redirect_uri,\n            ...options\n        };\n        const checks = {\n            state,\n            nonce,\n            max_age: maxAge,\n            code_verifier: codeVerifier,\n            response_type: responseType\n        };\n        const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n        const passReq = this._passReqToCallback;\n        const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n        const args = [\n            tokenset,\n            verified.bind(this)\n        ];\n        if (loadUserinfo) {\n            if (!tokenset.access_token) {\n                throw new RPError({\n                    message: \"expected access_token to be returned when asking for userinfo in verify callback\",\n                    tokenset\n                });\n            }\n            const userinfo = await client.userinfo(tokenset);\n            args.splice(1, 0, userinfo);\n        }\n        if (passReq) {\n            args.unshift(req);\n        }\n        this._verify(...args);\n    /* end authentication response */ })().catch((error)=>{\n        if (error instanceof OPError && error.error !== \"server_error\" && !error.error.startsWith(\"invalid\") || error instanceof RPError) {\n            this.fail(error);\n        } else {\n            this.error(error);\n        }\n    });\n};\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass TokenSet {\n    constructor(values){\n        Object.assign(this, values);\n        const { constructor, ...properties } = Object.getOwnPropertyDescriptors(this.constructor.prototype);\n        Object.defineProperties(this, properties);\n    }\n    set expires_in(value) {\n        this.expires_at = now() + Number(value);\n    }\n    get expires_in() {\n        return Math.max.apply(null, [\n            this.expires_at - now(),\n            0\n        ]);\n    }\n    expired() {\n        return this.expires_in === 0;\n    }\n    claims() {\n        if (!this.id_token) {\n            throw new TypeError(\"id_token not present in TokenSet\");\n        }\n        return JSON.parse(base64url.decode(this.id_token.split(\".\")[1]));\n    }\n}\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;