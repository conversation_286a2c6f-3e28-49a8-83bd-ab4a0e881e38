<?php
/**
 * Database Setup Script
 * Creates necessary tables for the Business Management SaaS
 */

require_once 'db-config.php';

echo "🔧 Setting up database tables...\n\n";

// Create users table
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255),
    password_hash VARCHAR(255),
    company_id VARCHAR(50),
    role VARCHAR(20) DEFAULT 'user',
    permissions TEXT,
    auth_token VARCHAR(100),
    token_expires DATETIME,
    status VARCHAR(20) DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login DATETIME,
    login_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_object_id (object_id),
    INDEX idx_auth_token (auth_token),
    INDEX idx_company_id (company_id)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Users table created successfully\n";
} else {
    echo "❌ Error creating users table: " . $conn->error . "\n";
}

// Create companies table
$sql = "CREATE TABLE IF NOT EXISTS companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    owner_id VARCHAR(50),
    members TEXT,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_owner_id (owner_id)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Companies table created successfully\n";
} else {
    echo "❌ Error creating companies table: " . $conn->error . "\n";
}

// Create customers table
$sql = "CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    status VARCHAR(20) DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_email (email)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Customers table created successfully\n";
} else {
    echo "❌ Error creating customers table: " . $conn->error . "\n";
}

// Create items table
$sql = "CREATE TABLE IF NOT EXISTS items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0.00,
    cost DECIMAL(10,2) DEFAULT 0.00,
    sku VARCHAR(50),
    category VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_sku (sku)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Items table created successfully\n";
} else {
    echo "❌ Error creating items table: " . $conn->error . "\n";
}

// Create invoices table
$sql = "CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    customer_id VARCHAR(50) NOT NULL,
    invoice_number VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    issue_date DATE,
    due_date DATE,
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    items TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_invoice_number (invoice_number)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Invoices table created successfully\n";
} else {
    echo "❌ Error creating invoices table: " . $conn->error . "\n";
}

// Create quotations table
$sql = "CREATE TABLE IF NOT EXISTS quotations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    customer_id VARCHAR(50) NOT NULL,
    quote_number VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    issue_date DATE,
    expiry_date DATE,
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    items TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_quote_number (quote_number)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Quotations table created successfully\n";
} else {
    echo "❌ Error creating quotations table: " . $conn->error . "\n";
}

// Create contracts table
$sql = "CREATE TABLE IF NOT EXISTS contracts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    customer_id VARCHAR(50) NOT NULL,
    contract_number VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    start_date DATE,
    end_date DATE,
    value DECIMAL(10,2) DEFAULT 0.00,
    content TEXT,
    terms TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_contract_number (contract_number)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Contracts table created successfully\n";
} else {
    echo "❌ Error creating contracts table: " . $conn->error . "\n";
}

// Create leads table
$sql = "CREATE TABLE IF NOT EXISTS leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    company VARCHAR(100),
    status VARCHAR(20) DEFAULT 'new',
    source VARCHAR(50),
    value DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_email (email),
    INDEX idx_status (status)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Leads table created successfully\n";
} else {
    echo "❌ Error creating leads table: " . $conn->error . "\n";
}

// Create subscriptions table
$sql = "CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    customer_id VARCHAR(50) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    amount DECIMAL(10,2) DEFAULT 0.00,
    start_date DATE,
    end_date DATE,
    next_billing_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status)
)";

if ($conn->query($sql) === TRUE) {
    echo "✅ Subscriptions table created successfully\n";
} else {
    echo "❌ Error creating subscriptions table: " . $conn->error . "\n";
}

// Check if we need to create a demo user
$sql = "SELECT COUNT(*) as count FROM users";
$result = $conn->query($sql);
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    echo "\n🔧 Creating demo user...\n";
    
    // Create demo company
    $companyId = 'company_demo_' . time();
    $sql = "INSERT INTO companies (object_id, name, email, owner_id, status) VALUES (?, 'Demo Company', '<EMAIL>', 'user_demo', 'active')";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $companyId);
    $stmt->execute();
    
    // Create demo user
    $userId = 'user_demo_' . time();
    $passwordHash = password_hash('demo123', PASSWORD_DEFAULT);
    $token = bin2hex(random_bytes(32));
    $tokenExpires = date('Y-m-d H:i:s', strtotime('+24 hours'));
    
    $sql = "INSERT INTO users (object_id, name, email, password_hash, company_id, auth_token, token_expires, status, email_verified, role) VALUES (?, 'Demo User', '<EMAIL>', ?, ?, ?, ?, 'active', TRUE, 'admin')";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssss", $userId, $passwordHash, $companyId, $token, $tokenExpires);
    
    if ($stmt->execute()) {
        echo "✅ Demo user created successfully\n";
        echo "   📧 Email: <EMAIL>\n";
        echo "   🔑 Password: demo123\n";
    } else {
        echo "❌ Error creating demo user: " . $conn->error . "\n";
    }
}

echo "\n🎉 Database setup completed!\n";
echo "📊 Database Status:\n";

// Show table status
$tables = ['users', 'companies', 'customers', 'items', 'invoices', 'quotations', 'contracts', 'leads', 'subscriptions'];
foreach ($tables as $table) {
    $sql = "SELECT COUNT(*) as count FROM $table";
    $result = $conn->query($sql);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "   📋 $table: {$row['count']} records\n";
    }
}

$conn->close();
?>