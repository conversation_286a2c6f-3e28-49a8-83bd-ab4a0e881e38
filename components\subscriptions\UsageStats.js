function UsageStats({ plan }) {
    try {
        if (!plan) return null;

        // Mock current usage - in a real app, this would come from the backend
        const currentLeads = 50;
        const currentUsers = 2;

        const calculatePercentage = (current, max) => {
            if (max === Infinity) return 10; // Show a small bar for unlimited plans
            return Math.min((current / max) * 100, 100);
        };

        const formatLimit = (limit) => {
            return limit === Infinity ? 'Unlimited' : limit;
        };

        return (
            <div data-name="usage-stats" className="mt-8 bg-white rounded-lg shadow p-6">
                <h2 className="text-2xl font-bold mb-4">Usage Statistics</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 className="text-sm font-medium text-gray-500">Leads Used</h3>
                        <div className="mt-2">
                            <div className="flex items-center justify-between mb-1">
                                <span className="text-sm font-medium text-gray-700">
                                    {currentLeads}/{formatLimit(plan.maxLeads)}
                                </span>
                                <span className="text-sm font-medium text-gray-500">
                                    {plan.maxLeads === Infinity ? '∞' : `${Math.round(calculatePercentage(currentLeads, plan.maxLeads))}%`}
                                </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-blue-600 h-2 rounded-full"
                                    style={{ width: `${calculatePercentage(currentLeads, plan.maxLeads)}%` }}
                                ></div>
                            </div>
                            {currentLeads >= plan.maxLeads && plan.maxLeads !== Infinity && (
                                <p className="mt-1 text-xs text-red-600">
                                    <i className="fas fa-exclamation-circle mr-1"></i>
                                    You've reached your leads limit
                                </p>
                            )}
                        </div>
                    </div>

                    <div>
                        <h3 className="text-sm font-medium text-gray-500">Team Members</h3>
                        <div className="mt-2">
                            <div className="flex items-center justify-between mb-1">
                                <span className="text-sm font-medium text-gray-700">
                                    {currentUsers}/{formatLimit(plan.maxUsers)}
                                </span>
                                <span className="text-sm font-medium text-gray-500">
                                    {plan.maxUsers === Infinity ? '∞' : `${Math.round(calculatePercentage(currentUsers, plan.maxUsers))}%`}
                                </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-green-600 h-2 rounded-full"
                                    style={{ width: `${calculatePercentage(currentUsers, plan.maxUsers)}%` }}
                                ></div>
                            </div>
                            {currentUsers >= plan.maxUsers && plan.maxUsers !== Infinity && (
                                <p className="mt-1 text-xs text-red-600">
                                    <i className="fas fa-exclamation-circle mr-1"></i>
                                    You've reached your team member limit
                                </p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Additional Usage Information */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-500">Storage Used</div>
                        <div className="mt-1 flex justify-between items-baseline">
                            <div className="text-2xl font-semibold">2.5 GB</div>
                            <div className="text-sm text-gray-500">of {formatLimit(plan.storage)} GB</div>
                        </div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-500">API Calls</div>
                        <div className="mt-1 flex justify-between items-baseline">
                            <div className="text-2xl font-semibold">8.2K</div>
                            <div className="text-sm text-gray-500">of 10K/month</div>
                        </div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="text-sm font-medium text-gray-500">Email Credits</div>
                        <div className="mt-1 flex justify-between items-baseline">
                            <div className="text-2xl font-semibold">450</div>
                            <div className="text-sm text-gray-500">of 1000/month</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('UsageStats component error:', error);
        reportError(error);
        return null;
    }
}
