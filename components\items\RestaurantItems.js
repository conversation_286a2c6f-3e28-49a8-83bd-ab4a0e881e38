function RestaurantItems() {
    try {
        const restaurantItems = [
            // Food Items
            {
                name: "Chicken Biryani",
                description: "Aromatic basmati rice cooked with tender chicken pieces, spices, and herbs. Served with raita and pickle.",
                price: 250,
                category: "Food",
                subcategory: "Main Course",
                isRecurring: false,
                itemType: "product",
                unit: "plate"
            },
            {
                name: "Vegetable Biryani",
                description: "Fragrant basmati rice cooked with mixed vegetables, spices, and herbs. Served with raita and pickle.",
                price: 200,
                category: "Food",
                subcategory: "Main Course",
                isRecurring: false,
                itemType: "product",
                unit: "plate"
            },
            {
                name: "Butter Chicken",
                description: "Tender chicken pieces in rich, creamy tomato-based curry. Served with naan or rice.",
                price: 280,
                category: "Food",
                subcategory: "Main Course",
                isRecurring: false,
                itemType: "product",
                unit: "plate"
            },
            {
                name: "Paneer Butter Masala",
                description: "Soft cottage cheese cubes in creamy tomato gravy with butter and spices.",
                price: 220,
                category: "Food",
                subcategory: "Main Course",
                isRecurring: false,
                itemType: "product",
                unit: "plate"
            },
            {
                name: "Dal Tadka",
                description: "Yellow lentils tempered with cumin, garlic, and spices. Served with rice or roti.",
                price: 150,
                category: "Food",
                subcategory: "Main Course",
                isRecurring: false,
                itemType: "product",
                unit: "bowl"
            },
            {
                name: "Masala Dosa",
                description: "Crispy rice and lentil crepe filled with spiced potato curry. Served with sambar and chutney.",
                price: 120,
                category: "Food",
                subcategory: "South Indian",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Idli Sambar",
                description: "Steamed rice cakes served with lentil curry and coconut chutney.",
                price: 80,
                category: "Food",
                subcategory: "South Indian",
                isRecurring: false,
                itemType: "product",
                unit: "plate"
            },
            {
                name: "Chicken Tikka",
                description: "Marinated chicken pieces grilled in tandoor oven with spices and herbs.",
                price: 300,
                category: "Food",
                subcategory: "Starters",
                isRecurring: false,
                itemType: "product",
                unit: "plate"
            },
            {
                name: "Vegetable Samosa",
                description: "Crispy pastry filled with spiced potatoes and peas. Served with mint chutney.",
                price: 40,
                category: "Food",
                subcategory: "Starters",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Naan Bread",
                description: "Soft, fluffy Indian bread baked in tandoor oven.",
                price: 50,
                category: "Food",
                subcategory: "Breads",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Garlic Naan",
                description: "Naan bread topped with fresh garlic and herbs.",
                price: 60,
                category: "Food",
                subcategory: "Breads",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Basmati Rice",
                description: "Steamed aromatic basmati rice.",
                price: 80,
                category: "Food",
                subcategory: "Rice",
                isRecurring: false,
                itemType: "product",
                unit: "bowl"
            },
            
            // Beverages
            {
                name: "Fresh Lime Soda",
                description: "Refreshing lime juice with soda water and mint.",
                price: 60,
                category: "Beverages",
                subcategory: "Cold Drinks",
                isRecurring: false,
                itemType: "product",
                unit: "glass"
            },
            {
                name: "Mango Lassi",
                description: "Creamy yogurt drink blended with fresh mango pulp.",
                price: 80,
                category: "Beverages",
                subcategory: "Cold Drinks",
                isRecurring: false,
                itemType: "product",
                unit: "glass"
            },
            {
                name: "Masala Chai",
                description: "Traditional Indian tea brewed with spices and milk.",
                price: 30,
                category: "Beverages",
                subcategory: "Hot Drinks",
                isRecurring: false,
                itemType: "product",
                unit: "cup"
            },
            {
                name: "Filter Coffee",
                description: "South Indian style coffee brewed with chicory.",
                price: 40,
                category: "Beverages",
                subcategory: "Hot Drinks",
                isRecurring: false,
                itemType: "product",
                unit: "cup"
            },
            
            // Desserts
            {
                name: "Gulab Jamun",
                description: "Soft milk dumplings in sweet rose-flavored syrup.",
                price: 60,
                category: "Desserts",
                subcategory: "Indian Sweets",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Kulfi",
                description: "Traditional Indian ice cream flavored with cardamom and pistachios.",
                price: 70,
                category: "Desserts",
                subcategory: "Ice Cream",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            
            // Services
            {
                name: "Home Delivery",
                description: "Food delivery service within 5km radius.",
                price: 30,
                category: "Services",
                subcategory: "Delivery",
                isRecurring: false,
                itemType: "service",
                unit: "order"
            },
            {
                name: "Catering Service - Basic",
                description: "Catering service for events up to 50 people with basic menu.",
                price: 300,
                category: "Services",
                subcategory: "Catering",
                isRecurring: false,
                itemType: "service",
                unit: "person"
            },
            {
                name: "Catering Service - Premium",
                description: "Premium catering service for events up to 100 people with extensive menu.",
                price: 500,
                category: "Services",
                subcategory: "Catering",
                isRecurring: false,
                itemType: "service",
                unit: "person"
            }
        ];

        const [loading, setLoading] = React.useState(false);
        const [addedItems, setAddedItems] = React.useState([]);
        const [notification, setNotification] = React.useState(null);

        const setupCategoriesAndItems = async () => {
            try {
                setLoading(true);
                
                // Get unique categories and subcategories
                const categories = [...new Set(restaurantItems.map(item => item.category))];
                const categoryMap = {};
                
                for (const categoryName of categories) {
                    // Check if category exists
                    const categoriesResponse = await trickleListObjects('item_category', 100, true);
                    let category = categoriesResponse.items.find(
                        cat => cat.objectData.name === categoryName
                    );
                    
                    if (!category) {
                        const categoryData = {
                            name: categoryName,
                            description: `${categoryName} items for restaurant`,
                            createdAt: new Date().toISOString()
                        };
                        
                        const newCategory = await trickleCreateObject('item_category', categoryData);
                        category = {
                            objectId: newCategory.objectId,
                            objectData: categoryData
                        };
                    }
                    
                    categoryMap[categoryName] = category.objectId;
                    
                    // Create subcategories for this category
                    const subcategories = [...new Set(
                        restaurantItems
                            .filter(item => item.category === categoryName)
                            .map(item => item.subcategory)
                    )];
                    
                    for (const subcategoryName of subcategories) {
                        const subcategoriesResponse = await trickleListObjects(`item_subcategory:${category.objectId}`, 100, true);
                        let existingSubcategory = subcategoriesResponse.items.find(
                            subcat => subcat.objectData.name === subcategoryName
                        );
                        
                        if (!existingSubcategory) {
                            const subcategoryData = {
                                name: subcategoryName,
                                description: `${subcategoryName} items`,
                                createdAt: new Date().toISOString()
                            };
                            
                            await trickleCreateObject(
                                `item_subcategory:${category.objectId}`, 
                                subcategoryData
                            );
                        }
                    }
                }
                
                // Create items
                for (const item of restaurantItems) {
                    const itemsResponse = await trickleListObjects('item', 100, true);
                    const existingItem = itemsResponse.items.find(
                        existingItem => existingItem.objectData.name === item.name
                    );
                    
                    if (!existingItem) {
                        const itemData = {
                            ...item,
                            category: categoryMap[item.category],
                            isActive: true,
                            createdAt: new Date().toISOString()
                        };
                        
                        await trickleCreateObject('item', itemData);
                        setAddedItems(prev => [...prev, item.name]);
                    }
                }
                
                setNotification({
                    type: 'success',
                    message: `Successfully added restaurant items`
                });
                
            } catch (error) {
                console.error('Error setting up restaurant items:', error);
                setNotification({
                    type: 'error',
                    message: 'Error adding restaurant items'
                });
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="restaurant-items" className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">Restaurant Menu Items</h2>
                    <p className="mb-6">
                        Add a complete restaurant menu to your items catalog. This includes food items, beverages, desserts, and services.
                    </p>
                    
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Items that will be added:</h3>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                            <li>Main Course Items (Biryani, Curries, Dal)</li>
                            <li>South Indian Items (Dosa, Idli)</li>
                            <li>Starters & Appetizers</li>
                            <li>Breads & Rice</li>
                            <li>Beverages (Hot & Cold)</li>
                            <li>Desserts & Sweets</li>
                            <li>Services (Delivery, Catering)</li>
                        </ul>
                    </div>
                    
                    <div className="flex justify-center">
                        <Button
                            onClick={setupCategoriesAndItems}
                            loading={loading}
                            disabled={loading}
                            icon="fas fa-utensils"
                        >
                            Add Restaurant Menu Items
                        </Button>
                    </div>
                    
                    {addedItems.length > 0 && (
                        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                            <h4 className="font-medium text-green-700 mb-2">Successfully added:</h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm text-green-600">
                                {addedItems.map((item, index) => (
                                    <li key={index}>{item}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
                
                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('RestaurantItems component error:', error);
        reportError(error);
        return null;
    }
}
