<?php
// Test script for login API
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simulate a login request
$loginData = [
    'action' => 'login',
    'email' => '<EMAIL>',
    'password' => 'admin123', // Try a common admin password
    'remember_me' => true
];

// Convert to JSON
$jsonData = json_encode($loginData);

// Set up cURL
$ch = curl_init('http://localhost/biz/api/api.php/auth');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// Output results
echo "HTTP Code: " . $httpCode . "\n";
if ($error) {
    echo "cURL Error: " . $error . "\n";
}
echo "Response:\n";
echo $response . "\n";

// Parse the response
$responseData = json_decode($response, true);
echo "Parsed Response:\n";
print_r($responseData);
?>