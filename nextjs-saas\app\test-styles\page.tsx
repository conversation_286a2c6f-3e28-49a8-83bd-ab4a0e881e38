export default function TestStylesPage() {
  return (
    <div className="min-h-screen bg-blue-500 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8">Style Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Card 1</h2>
            <p className="text-gray-600">This should be a white card with shadow</p>
            <button className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Button
            </button>
          </div>
          
          <div className="bg-green-100 rounded-lg border-2 border-green-500 p-6">
            <h2 className="text-xl font-semibold text-green-800 mb-4">Card 2</h2>
            <p className="text-green-700">This should be a green card</p>
            <div className="flex space-x-2 mt-4">
              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
              <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
            <h2 className="text-xl font-semibold mb-4">Card 3</h2>
            <p>This should be a gradient card</p>
            <div className="mt-4 flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              <span>Loading...</span>
            </div>
          </div>
        </div>
        
        <div className="mt-8 bg-white rounded-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Typography Test</h2>
          <p className="text-lg text-gray-700 mb-2">Large text</p>
          <p className="text-base text-gray-600 mb-2">Base text</p>
          <p className="text-sm text-gray-500 mb-2">Small text</p>
          <p className="text-xs text-gray-400">Extra small text</p>
        </div>
      </div>
    </div>
  )
}
