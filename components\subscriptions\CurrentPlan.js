function CurrentPlan({ plan }) {
    try {
        if (!plan) return null;

        return (
            <div data-name="current-plan" className="mt-12 bg-white rounded-lg shadow p-6">
                <h2 className="text-2xl font-bold mb-4">Current Subscription</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                        <h3 className="text-sm font-medium text-gray-500">Plan</h3>
                        <p className="mt-1 text-lg font-semibold">{plan.planName}</p>
                        <p className="text-sm text-gray-500">
                            {plan.maxLeads === Infinity ? 'Unlimited' : plan.maxLeads} leads
                        </p>
                    </div>
                    <div>
                        <h3 className="text-sm font-medium text-gray-500">Status</h3>
                        <p className="mt-1">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {plan.status}
                            </span>
                        </p>
                    </div>
                    <div>
                        <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                        <p className="mt-1 text-lg font-semibold">
                            {formatDate(plan.startDate)}
                        </p>
                        <p className="text-sm text-gray-500">
                            Auto-renews {plan.billingPeriod}
                        </p>
                    </div>
                    <div>
                        <h3 className="text-sm font-medium text-gray-500">Price</h3>
                        <p className="mt-1 text-lg font-semibold">
                            ₹{plan.price}/{plan.billingPeriod}
                        </p>
                    </div>
                </div>

                <div className="mt-6 border-t pt-6">
                    <h3 className="text-sm font-medium text-gray-500 mb-4">Plan Features</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {plan.features.map((feature, index) => (
                            <div key={index} className="flex items-center">
                                <i className="fas fa-check text-green-500 mr-2"></i>
                                <span className="text-gray-600">{feature}</span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('CurrentPlan component error:', error);
        reportError(error);
        return null;
    }
}
