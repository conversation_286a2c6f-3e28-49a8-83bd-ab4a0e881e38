<?php
/**
 * Configuration Management System
 * Loads environment variables and provides secure configuration access
 */

class Config {
    private static $config = [];
    private static $loaded = false;

    /**
     * Load configuration from environment file
     */
    public static function load() {
        if (self::$loaded) {
            return;
        }

        // Load .env file if it exists
        $envFile = __DIR__ . '/../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '#') === 0) {
                    continue; // Skip comments and empty lines
                }

                if (strpos($line, '=') === false) {
                    continue; // Skip lines without = sign
                }

                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^"(.*)"$/', $value, $matches)) {
                    $value = $matches[1];
                } elseif (preg_match("/^'(.*)'$/", $value, $matches)) {
                    $value = $matches[1];
                }
                
                self::$config[$name] = $value;
                
                // Also set as environment variable
                if (!getenv($name)) {
                    putenv("$name=$value");
                }
            }
        }

        // Load from actual environment variables (takes precedence)
        foreach ($_ENV as $key => $value) {
            self::$config[$key] = $value;
        }

        self::$loaded = true;
    }

    /**
     * Get configuration value
     */
    public static function get($key, $default = null) {
        self::load();
        
        // Try environment variable first
        $envValue = getenv($key);
        if ($envValue !== false) {
            return $envValue;
        }
        
        // Then try loaded config
        return isset(self::$config[$key]) ? self::$config[$key] : $default;
    }

    /**
     * Get database configuration
     */
    public static function getDatabase() {
        return [
            'host' => self::get('DB_HOST', 'localhost'),
            'username' => self::get('DB_USERNAME', 'root'),
            'password' => self::get('DB_PASSWORD', ''),
            'database' => self::get('DB_NAME', 'business_saas'),
            'charset' => 'utf8mb4'
        ];
    }

    /**
     * Get PhonePe configuration
     */
    public static function getPhonePe() {
        return [
            'merchant_id' => self::get('PHONEPE_MERCHANT_ID'),
            'salt_key' => self::get('PHONEPE_SALT_KEY'),
            'salt_index' => self::get('PHONEPE_SALT_INDEX', '1'),
            'environment' => self::get('PHONEPE_ENVIRONMENT', 'sandbox'),
            'callback_url' => self::get('PHONEPE_CALLBACK_URL'),
            'redirect_url' => self::get('PHONEPE_REDIRECT_URL')
        ];
    }

    /**
     * Get application configuration
     */
    public static function getApp() {
        return [
            'env' => self::get('APP_ENV', 'development'),
            'debug' => self::get('APP_DEBUG', 'true') === 'true',
            'url' => self::get('APP_URL', 'http://localhost/biz'),
            'name' => self::get('APP_NAME', 'Business Management SaaS')
        ];
    }

    /**
     * Check if we're in production environment
     */
    public static function isProduction() {
        return self::get('APP_ENV') === 'production';
    }

    /**
     * Check if debug mode is enabled
     */
    public static function isDebug() {
        return self::get('APP_DEBUG', 'false') === 'true';
    }
}

// Auto-load configuration
Config::load();
?>
