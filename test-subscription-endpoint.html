<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subscription Endpoint</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .auth-section { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Subscription Endpoint</h1>
        
        <div class="auth-section">
            <h3>Authentication Status</h3>
            <p id="auth-status">Checking authentication...</p>
            <button onclick="checkAuth()">Check Auth Token</button>
        </div>
        
        <div class="test-section">
            <h2>Test 1: Query Parameter Method</h2>
            <button onclick="testQueryParams()">Test /api/api.php?endpoint=subscription&limit=10</button>
            <div id="query-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Path Info Method</h2>
            <button onclick="testPathInfo()">Test /api/api.php/subscription?limit=10</button>
            <div id="path-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Direct Subscription Handler</h2>
            <button onclick="testDirectHandler()">Test subscription handler directly</button>
            <div id="direct-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 4: Enhanced Subscription Handler</h2>
            <button onclick="testEnhancedHandler()">Test enhanced subscription handler</button>
            <div id="enhanced-result"></div>
        </div>
    </div>

    <script>
        // Check authentication status on load
        window.addEventListener('load', checkAuth);
        
        function checkAuth() {
            const authStatus = document.getElementById('auth-status');
            const token = localStorage.getItem('authToken');
            
            if (token) {
                authStatus.innerHTML = `✅ Auth token found: ${token.substring(0, 20)}...`;
            } else {
                authStatus.innerHTML = `❌ No auth token found. Please login first.`;
            }
        }
        
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return fetch(url, {
                ...options,
                headers
            });
        }
        
        async function testQueryParams() {
            const resultDiv = document.getElementById('query-result');
            resultDiv.innerHTML = '<p>Testing query parameter method...</p>';
            
            try {
                const response = await makeAuthenticatedRequest('/biz/api/api.php?endpoint=subscription&limit=10');
                
                const text = await response.text();
                console.log('Query params response:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success !== false ? '<p><strong>✅ Request Successful!</strong></p>' : '<p><strong>❌ Request Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testPathInfo() {
            const resultDiv = document.getElementById('path-result');
            resultDiv.innerHTML = '<p>Testing path info method...</p>';
            
            try {
                const response = await makeAuthenticatedRequest('/biz/api/api.php/subscription?limit=10');
                
                const text = await response.text();
                console.log('Path info response:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success !== false ? '<p><strong>✅ Request Successful!</strong></p>' : '<p><strong>❌ Request Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testDirectHandler() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<p>Testing direct subscription handler...</p>';
            
            try {
                const response = await makeAuthenticatedRequest('/biz/api/handlers/subscription-handler.php?limit=10');
                
                const text = await response.text();
                console.log('Direct handler response:', text);
                
                resultDiv.className = 'test-section info';
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testEnhancedHandler() {
            const resultDiv = document.getElementById('enhanced-result');
            resultDiv.innerHTML = '<p>Testing enhanced subscription handler...</p>';
            
            try {
                const response = await makeAuthenticatedRequest('/biz/api/api.php?endpoint=subscription-management&action=current');
                
                const text = await response.text();
                console.log('Enhanced handler response:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success !== false ? '<p><strong>✅ Request Successful!</strong></p>' : '<p><strong>❌ Request Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
