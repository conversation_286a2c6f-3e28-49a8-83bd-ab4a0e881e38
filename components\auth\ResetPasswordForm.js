function ResetPasswordForm() {
    try {
        const [formData, setFormData] = React.useState({
            password: '',
            confirmPassword: ''
        });
        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [success, setSuccess] = React.useState(false);

        // Get token from URL
        const token = new URLSearchParams(window.location.search).get('token');

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.password) {
                newErrors.password = 'Password is required';
            } else if (!isPasswordStrong(formData.password)) {
                newErrors.password = 'Password must be at least 8 characters with letters, numbers, and special characters';
            }
            if (formData.password !== formData.confirmPassword) {
                newErrors.confirmPassword = 'Passwords do not match';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                await trickleCreateObject('auth', {
                    type: 'reset-password',
                    token,
                    password: formData.password
                });
                setSuccess(true);
            } catch (error) {
                console.error('Reset password error:', error);
                setErrors({ submit: 'Failed to reset password. Please try again.' });
            } finally {
                setLoading(false);
            }
        };

        if (!token) {
            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-md w-full text-center">
                        <div className="rounded-lg bg-white p-8 shadow-sm">
                            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                                <i className="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <h2 className="mt-4 text-2xl font-semibold text-gray-900">Invalid Reset Link</h2>
                            <p className="mt-2 text-gray-600">
                                The password reset link is invalid or has expired.
                            </p>
                            <div className="mt-6">
                                <a
                                    href="/forgot-password"
                                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                                >
                                    Request a new reset link
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        if (success) {
            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-md w-full text-center">
                        <div className="rounded-lg bg-white p-8 shadow-sm">
                            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                                <i className="fas fa-check text-green-600 text-xl"></i>
                            </div>
                            <h2 className="mt-4 text-2xl font-semibold text-gray-900">Password Reset Complete</h2>
                            <p className="mt-2 text-gray-600">
                                Your password has been successfully reset.
                            </p>
                            <div className="mt-6">
                                <a
                                    href="/login"
                                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                                >
                                    Back to login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        return (
            <div data-name="reset-password-form" className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div>
                        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Reset your password
                        </h2>
                        <p className="mt-2 text-center text-sm text-gray-600">
                            Please enter your new password below.
                        </p>
                    </div>

                    <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                        <div className="rounded-md shadow-sm -space-y-px">
                            <div>
                                <label htmlFor="password" className="sr-only">New Password</label>
                                <input
                                    id="password"
                                    name="password"
                                    type="password"
                                    required
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.password ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="New Password"
                                />
                                {errors.password && (
                                    <p className="mt-1 text-xs text-red-600">{errors.password}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="confirm-password" className="sr-only">Confirm New Password</label>
                                <input
                                    id="confirm-password"
                                    name="confirmPassword"
                                    type="password"
                                    required
                                    value={formData.confirmPassword}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Confirm New Password"
                                />
                                {errors.confirmPassword && (
                                    <p className="mt-1 text-xs text-red-600">{errors.confirmPassword}</p>
                                )}
                            </div>
                        </div>

                        {errors.submit && (
                            <div className="text-red-600 text-sm text-center">
                                {errors.submit}
                            </div>
                        )}

                        <div>
                            <Button
                                type="submit"
                                loading={loading}
                                disabled={loading}
                                className="w-full"
                            >
                                Reset Password
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ResetPasswordForm component error:', error);
        reportError(error);
        return null;
    }
}
