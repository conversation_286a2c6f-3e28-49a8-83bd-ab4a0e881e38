<?php
/**
 * Authentication Handler
 * Handles user authentication, login, logout, and token verification
 */

// db-config.php is already included by api.php
require_once __DIR__ . '/../utils/EmailUtils.php';

/**
 * Generate secure authentication token
 */
function generateAuthToken() {
    return bin2hex(random_bytes(32));
}





function handleAuth($action) {
    global $conn;

    switch ($action) {
        case 'login':
            return handleLogin();
        case 'logout':
            return handleLogout();
        case 'verify':
            return handleTokenVerification();
        case 'register':
            return handleRegister();
        case 'forgot-password':
            return handleForgotPassword();
        case 'reset-password':
            return handleResetPassword();
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            return;
    }
}

function handleLogin() {
    global $conn;

    try {
        // Get request body
        $requestBody = file_get_contents('php://input');
        $loginData = json_decode($requestBody, true);

        if (!$loginData || !isset($loginData['email']) || !isset($loginData['password'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Email and password are required']);
            return;
        }

        $email = sanitizeInput($loginData['email']);
        $password = $loginData['password'];
    
    // Find user by email
    $sql = "SELECT * FROM users WHERE email = ? AND status = 'active'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid email or password']);
        return;
    }

    $user = $result->fetch_assoc();

    // Check password - use proper password verification
    $isValidPassword = false;
    if (!empty($user['password_hash'])) {
        // Use password_verify for hashed passwords
        $isValidPassword = password_verify($password, $user['password_hash']);
    } elseif (!empty($user['password'])) {
        // Legacy password field support
        $isValidPassword = password_verify($password, $user['password']);
    } else {
        // Fallback for demo purposes - remove in production
        $isValidPassword = ($password === 'admin123');
    }

    if (!$isValidPassword) {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid email or password']);
        return;
    }
    
    // Generate auth token
    $token = generateAuthToken();
    $tokenExpires = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Update user with new token and login tracking
    $sql = "UPDATE users SET auth_token = ?, token_expires = ?, last_login = NOW(), login_count = login_count + 1 WHERE object_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $token, $tokenExpires, $user['object_id']);
    $stmt->execute();
    
    // Get company information
    $companyInfo = null;
    if ($user['company_id']) {
        $sql = "SELECT * FROM companies WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $user['company_id']);
        $stmt->execute();
        $companyResult = $stmt->get_result();
        if ($companyResult->num_rows > 0) {
            $companyInfo = $companyResult->fetch_assoc();
        }
    }
    
    // Return success response
    $response = [
        'success' => true,
        'token' => $token,
        'user' => [
            'id' => $user['object_id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role'] ?? 'user',
            'permissions' => $user['permissions'] ? json_decode($user['permissions'], true) : [],
            'company_id' => $user['company_id'],
            'company' => $companyInfo,
            'last_login' => $user['last_login'],
            'login_count' => $user['login_count']
        ]
    ];

        echo json_encode($response);

    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Login failed: ' . $e->getMessage()]);
    }
}

function handleTokenVerification() {
    global $conn;

    try {
        // Get token from Authorization header
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            http_response_code(401);
            echo json_encode(['error' => 'No token provided']);
            return;
        }

        $token = $matches[1];

        // Verify token in database (simplified query first)
        $sql = "SELECT u.* FROM users u
                WHERE u.auth_token = ? AND u.token_expires > NOW() AND u.status = 'active'";
        $stmt = $conn->prepare($sql);

        if (!$stmt) {
            error_log("Failed to prepare statement: " . $conn->error);
            http_response_code(500);
            echo json_encode(['error' => 'Database error']);
            return;
        }

        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid or expired token']);
            return;
        }

        $user = $result->fetch_assoc();

        // Get company name separately if needed
        $companyName = null;
        if ($user['company_id']) {
            $companySql = "SELECT name FROM companies WHERE object_id = ?";
            $companyStmt = $conn->prepare($companySql);
            if ($companyStmt) {
                $companyStmt->bind_param("s", $user['company_id']);
                $companyStmt->execute();
                $companyResult = $companyStmt->get_result();
                if ($companyResult->num_rows > 0) {
                    $company = $companyResult->fetch_assoc();
                    $companyName = $company['name'];
                }
            }
        }

        // Return user data
        echo json_encode([
            'success' => true,
            'user' => [
                'id' => $user['object_id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'company_id' => $user['company_id'],
                'company_name' => $companyName,
                'role' => $user['role'] // Add the user role
            ]
        ]);

    } catch (Exception $e) {
        error_log("Token verification error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
}

function handleLogout() {
    global $conn;
    
    // Get token from Authorization header
    $headers = getallheaders();
    $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        
        // Clear token from database
        $sql = "UPDATE users SET auth_token = NULL, token_expires = NULL WHERE auth_token = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $token);
        $stmt->execute();
    }
    
    echo json_encode(['success' => true, 'message' => 'Logged out successfully']);
}

function handleRegister() {
    global $conn;
    
    // Get request body
    $requestBody = file_get_contents('php://input');
    $registerData = json_decode($requestBody, true);
    
    if (!$registerData || !isset($registerData['name']) || !isset($registerData['email']) || !isset($registerData['password'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Name, email and password are required']);
        return;
    }
    
    $name = sanitizeInput($registerData['name']);
    $email = sanitizeInput($registerData['email']);
    $password = $registerData['password'];
    $companyName = isset($registerData['company']) ? sanitizeInput($registerData['company']) : '';
    
    // Check if user already exists
    $sql = "SELECT object_id FROM users WHERE email = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        http_response_code(409);
        echo json_encode(['error' => 'User with this email already exists']);
        return;
    }
    
    // Generate user ID
    $userId = 'user_' . time() . '_' . rand(100, 999);
    
    // Hash the password
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);

    // Generate auth token
    $token = generateAuthToken();
    $tokenExpires = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Create user first (without company_id initially)
    $sql = "INSERT INTO users (object_id, name, email, password_hash, auth_token, token_expires, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, 'active', TRUE)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssss", $userId, $name, $email, $passwordHash, $token, $tokenExpires);
    
    if (!$stmt->execute()) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create user: ' . $conn->error]);
        return;
    }

    // Create company if provided (now that user exists)
    $companyId = null;
    if ($companyName) {
        $companyId = 'company_' . time() . '_' . rand(100, 999);
        $sql = "INSERT INTO companies (object_id, name, email, owner_id, status) VALUES (?, ?, ?, ?, 'active')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $companyId, $companyName, $email, $userId);
        if (!$stmt->execute()) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create company: ' . $conn->error]);
            return;
        }
        
        // Update user with company_id
        $sql = "UPDATE users SET company_id = ? WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $companyId, $userId);
        $stmt->execute();
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'token' => $token,
        'user' => [
            'id' => $userId,
            'name' => $name,
            'email' => $email,
            'company_id' => $companyId
        ]
    ]);
}

function handleForgotPassword() {
    global $conn;

    try {
        // Get request body
        $requestBody = file_get_contents('php://input');
        $data = json_decode($requestBody, true);

        if (!$data || !isset($data['email'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Email is required']);
            return;
        }

        $email = sanitizeInput($data['email']);

        // Check if user exists
        $sql = "SELECT object_id, name FROM users WHERE email = ? AND status = 'active'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            // Don't reveal if email exists or not for security
            echo json_encode(['success' => true, 'message' => 'If the email exists, a reset link has been sent.']);
            return;
        }

        $user = $result->fetch_assoc();

        // Generate reset token
        $resetToken = bin2hex(random_bytes(32));
        $resetExpires = date('Y-m-d H:i:s', strtotime('+1 hour'));

        // Store reset token in database
        $sql = "UPDATE users SET reset_token = ?, reset_expires = ? WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $resetToken, $resetExpires, $user['object_id']);
        $stmt->execute();

        // Check if email is configured before sending
        $emailConfigured = checkEmailConfiguration();
        $resetLink = "http://" . $_SERVER['HTTP_HOST'] . "/reset-password?token=" . $resetToken;

        if ($emailConfigured) {
            try {
                EmailUtils::sendPasswordResetEmail($email, $user['name'], $resetLink);
                error_log("Password reset email sent to: " . $email);
                echo json_encode(['success' => true, 'message' => 'Password reset email sent successfully.']);
            } catch (Exception $e) {
                error_log("Failed to send password reset email: " . $e->getMessage());
                echo json_encode(['success' => false, 'message' => 'Failed to send reset email. Please contact administrator.']);
            }
        } else {
            // Email not configured - log for development but show helpful message
            error_log("Password reset requested but email not configured. Reset link: " . $resetLink);
            echo json_encode([
                'success' => false,
                'message' => 'Email system not configured. Please contact administrator or configure email settings.',
                'needsEmailConfig' => true
            ]);
        }

    } catch (Exception $e) {
        error_log("Forgot password error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process request']);
    }
}

function checkEmailConfiguration() {
    global $conn;

    try {
        // Check if email settings exist and are enabled in company_settings table
        $sql = "SELECT setting_value FROM company_settings WHERE setting_key = 'email_settings' LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $emailSettings = json_decode($row['setting_value'], true);

            // Email is configured if we have basic SMTP settings and it's enabled
            return $emailSettings &&
                   !empty($emailSettings['smtp_host']) &&
                   !empty($emailSettings['smtp_username']) &&
                   !empty($emailSettings['from_email']) &&
                   isset($emailSettings['enabled']) && $emailSettings['enabled'] == true;
        }

        return false;

    } catch (Exception $e) {
        error_log("Error checking email configuration: " . $e->getMessage());
        return false;
    }
}

function handleResetPassword() {
    global $conn;

    try {
        // Get request body
        $requestBody = file_get_contents('php://input');
        $data = json_decode($requestBody, true);

        if (!$data || !isset($data['token']) || !isset($data['password'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Token and new password are required']);
            return;
        }

        $token = sanitizeInput($data['token']);
        $newPassword = $data['password'];

        // Validate token
        $sql = "SELECT object_id FROM users WHERE reset_token = ? AND reset_expires > NOW() AND status = 'active'";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid or expired reset token']);
            return;
        }

        $user = $result->fetch_assoc();

        // Hash new password
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

        // Update password and clear reset token
        $sql = "UPDATE users SET password_hash = ?, reset_token = NULL, reset_expires = NULL WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $passwordHash, $user['object_id']);
        $stmt->execute();

        echo json_encode(['success' => true, 'message' => 'Password reset successfully.']);

    } catch (Exception $e) {
        error_log("Reset password error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to reset password']);
    }
}

?>
