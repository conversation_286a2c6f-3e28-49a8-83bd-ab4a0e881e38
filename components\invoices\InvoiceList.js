function InvoiceList({ onInvoiceClick }) {
    try {
        const [invoices, setInvoices] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [customersMap, setCustomersMap] = React.useState({});

        React.useEffect(() => {
            fetchInvoices();
            fetchCustomers();
        }, []);

        const fetchInvoices = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/invoice'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setInvoices(data.items || []);
                } else {
                    throw new Error('Failed to fetch invoices');
                }
            } catch (error) {
                console.error('Error fetching invoices:', error);
                setInvoices([]);
            } finally {
                setLoading(false);
            }
        };

        const fetchCustomers = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/customer'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const customerMap = {};
                    (data.items || []).forEach(customer => {
                        customerMap[customer.objectId] = customer.objectData;
                    });
                    setCustomersMap(customerMap);
                } else {
                    throw new Error('Failed to fetch customers');
                }
            } catch (error) {
                console.error('Error fetching customers:', error);
                setCustomersMap({});
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters && filters.status ? filters.status : '');
        };

        const filteredInvoices = React.useMemo(() => {
            return invoices.filter(invoice => {
                const customerName = customersMap[invoice.objectData.customer] && customersMap[invoice.objectData.customer].name ? customersMap[invoice.objectData.customer].name : '';
                const invoiceNumber = invoice.objectData.invoiceNumber || '';
                
                const matchesSearch = !searchQuery || 
                    customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase());
                
                const matchesStatus = !selectedStatus || invoice.objectData.status === selectedStatus;

                return matchesSearch && matchesStatus;
            });
        }, [invoices, searchQuery, selectedStatus, customersMap]);

        const invoiceStatusFilters = [
            { id: 'status', label: 'Status', type: 'select', options: [
                { label: 'Draft', value: 'draft' },
                { label: 'Sent', value: 'sent' },
                { label: 'Paid', value: 'paid' },
                { label: 'Overdue', value: 'overdue' }
            ]}
        ];

        const columns = React.useMemo(() => [
            {
                key: 'invoiceNumber',
                label: 'Invoice #',
                render: (row) => row.objectData.invoiceNumber || `#${row.objectId.substring(0, 8)}`
            },
            {
                key: 'customer',
                label: 'Customer',
                render: (row) => customersMap[row.objectData.customer] && customersMap[row.objectData.customer].name ? customersMap[row.objectData.customer].name : 'Unknown Customer'
            },
            {
                key: 'total',
                label: 'Total',
                render: (row) => formatCurrency(row.objectData.total)
            },
            {
                key: 'dueDate',
                label: 'Due Date',
                render: (row) => formatDate(row.objectData.dueDate)
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`invoice-status ${row.objectData.status}`}>
                        {row.objectData.status ? row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1) : 'Unknown'}
                    </span>
                )
            }
        ], [customersMap]);

        return (
            <div data-name="invoice-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search invoices..."
                        filters={invoiceStatusFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredInvoices}
                    loading={loading}
                    onRowClick={onInvoiceClick}
                    emptyMessage="No invoices found"
                />
            </div>
        );
    } catch (error) {
        console.error('InvoiceList component error:', error);
        reportError(error);
        return null;
    }
}
