function Quotations() {
    try {
        const [showForm, setShowForm] = React.useState(false);
        const [showViewer, setShowViewer] = React.useState(false);
        const [selectedQuotation, setSelectedQuotation] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [companyInfo, setCompanyInfo] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [refreshKey, setRefreshKey] = React.useState(0);

        React.useEffect(() => {
            loadInitialData();
        }, []);

        const loadInitialData = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/settings'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.items && data.items.length > 0) {
                        setCompanyInfo(data.items[0].objectData);
                    }
                } else {
                    console.error('Failed to fetch settings');
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load company information'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleCreateQuotation = () => {
            setSelectedQuotation(null);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleEditQuotation = (quotation) => {
            setSelectedQuotation(quotation);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleViewQuotation = (quotation) => {
            setSelectedQuotation(quotation);
            setShowViewer(true);
            setShowForm(false);
        };

        const handleQuotationClick = (quotation) => {
            handleViewQuotation(quotation);
        };

        const handleFormSubmit = async () => {
            setShowForm(false);
            setSelectedQuotation(null);
            setNotification({
                type: 'success',
                message: selectedQuotation 
                    ? 'Quotation updated successfully' 
                    : 'Quotation created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the quotation list instead of reloading the page
            setRefreshKey(prev => prev + 1);
        };

        const handleFormCancel = () => {
            setShowForm(false);
            setSelectedQuotation(null);
        };

        const handleViewerClose = () => {
            setShowViewer(false);
            setSelectedQuotation(null);
            // Don't reload the page, just refresh the quotation list
            setRefreshKey(prev => prev + 1);
        };

        const handleQuotationDelete = () => {
            setShowViewer(false);
            setSelectedQuotation(null);
            setNotification({
                type: 'success',
                message: 'Quotation deleted successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the quotation list
            setRefreshKey(prev => prev + 1);
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="quotations-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Quotations</h1>
                    <Button
                        onClick={handleCreateQuotation}
                        icon="fas fa-plus"
                    >
                        Create Quotation
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedQuotation ? 'Edit Quotation' : 'New Quotation'}
                        </h2>
                        <QuotationForm
                            quotation={selectedQuotation}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : showViewer && selectedQuotation && companyInfo ? (
                    <DocumentViewer
                        type="quotation"
                        data={selectedQuotation}
                        companyInfo={companyInfo}
                        onEdit={() => handleEditQuotation(selectedQuotation)}
                        onDelete={handleQuotationDelete}
                        onClose={handleViewerClose}
                    />
                ) : (
                    <QuotationList onQuotationClick={handleQuotationClick} key={refreshKey} />
                )}
            </div>
        );
    } catch (error) {
        console.error('Quotations page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
