<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Login Verification</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; }
        button:hover { background: #0056b3; }
        .test-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .test-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; }
        .test-card h3 { margin-top: 0; color: #495057; }
        .step-indicator { display: inline-block; width: 30px; height: 30px; border-radius: 50%; background: #6c757d; color: white; text-align: center; line-height: 30px; margin-right: 10px; }
        .step-indicator.success { background: #28a745; }
        .step-indicator.error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Final Login Verification</h1>
        <p>Complete verification that the login → app.html flow works without "Invalid token" errors.</p>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This test will clear your current session and perform a fresh login test.
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3><span class="step-indicator" id="step1">1</span>Clear Session</h3>
                <button onclick="clearSession()">Clear Session</button>
                <div id="clearStatus"></div>
            </div>

            <div class="test-card">
                <h3><span class="step-indicator" id="step2">2</span>Test Login</h3>
                <button onclick="testLogin()">Test Login</button>
                <div id="loginStatus"></div>
            </div>

            <div class="test-card">
                <h3><span class="step-indicator" id="step3">3</span>Verify Token</h3>
                <button onclick="verifyToken()">Verify Token</button>
                <div id="verifyStatus"></div>
            </div>

            <div class="test-card">
                <h3><span class="step-indicator" id="step4">4</span>Test AuthContext</h3>
                <button onclick="testAuthContext()">Test AuthContext</button>
                <div id="authStatus"></div>
            </div>
        </div>

        <div class="test-card">
            <h3>🚀 Final Test Results</h3>
            <div id="finalResults"></div>
            <button onclick="runAllTests()" style="background: #28a745;">Run All Tests</button>
        </div>

        <div class="info">
            <h3>📋 Manual Verification Steps</h3>
            <ol>
                <li>After all automated tests pass, go to <a href="/biz/login.html" target="_blank">login.html</a></li>
                <li>Login with: <code><EMAIL></code> / <code>admin123</code></li>
                <li>Should redirect to app.html successfully</li>
                <li>Dashboard should load without "Invalid token" errors</li>
                <li>Check browser console - should be no authentication errors</li>
            </ol>
        </div>
    </div>

    <script>
        let testResults = {
            clear: false,
            login: false,
            verify: false,
            auth: false
        };

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateStepIndicator(stepId, success) {
            const indicator = document.getElementById(stepId);
            indicator.className = `step-indicator ${success ? 'success' : 'error'}`;
        }

        function updateFinalResults() {
            const allPassed = Object.values(testResults).every(result => result);
            const passedCount = Object.values(testResults).filter(result => result).length;
            const totalCount = Object.keys(testResults).length;

            let message = `
                <h4>Test Results: ${passedCount}/${totalCount} passed</h4>
                <ul>
                    <li>Clear Session: ${testResults.clear ? '✅' : '❌'}</li>
                    <li>Login Test: ${testResults.login ? '✅' : '❌'}</li>
                    <li>Token Verification: ${testResults.verify ? '✅' : '❌'}</li>
                    <li>AuthContext Test: ${testResults.auth ? '✅' : '❌'}</li>
                </ul>
            `;

            if (allPassed) {
                message += `
                    <div class="status success">
                        <strong>🎉 All tests passed!</strong><br>
                        The login → app.html flow should work correctly.<br>
                        No more "Invalid token" errors expected.
                    </div>
                `;
            } else {
                message += `
                    <div class="status error">
                        <strong>❌ Some tests failed.</strong><br>
                        Please check the individual test results above.
                    </div>
                `;
            }

            showStatus('finalResults', message, allPassed ? 'success' : 'error');
        }

        async function clearSession() {
            try {
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('rememberMe');
                sessionStorage.clear();
                
                showStatus('clearStatus', '✅ Session cleared successfully', 'success');
                testResults.clear = true;
                updateStepIndicator('step1', true);
            } catch (error) {
                showStatus('clearStatus', `❌ Error clearing session: ${error.message}`, 'error');
                testResults.clear = false;
                updateStepIndicator('step1', false);
            }
            updateFinalResults();
        }

        async function testLogin() {
            showStatus('loginStatus', '🔄 Testing login...', 'info');
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123',
                        rememberMe: false
                    })
                });

                const data = await response.json();
                
                if (data.success && data.tokens && data.tokens.access_token) {
                    localStorage.setItem('authToken', data.tokens.access_token);
                    
                    showStatus('loginStatus', `
                        ✅ Login successful<br>
                        User: ${data.user.name}<br>
                        Token stored: ✅
                    `, 'success');
                    testResults.login = true;
                    updateStepIndicator('step2', true);
                } else {
                    showStatus('loginStatus', `❌ Login failed: ${data.error || 'Unknown error'}`, 'error');
                    testResults.login = false;
                    updateStepIndicator('step2', false);
                }
            } catch (error) {
                showStatus('loginStatus', `❌ Login error: ${error.message}`, 'error');
                testResults.login = false;
                updateStepIndicator('step2', false);
            }
            updateFinalResults();
        }

        async function verifyToken() {
            showStatus('verifyStatus', '🔍 Verifying token...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('verifyStatus', '❌ No token found. Run login test first.', 'error');
                testResults.verify = false;
                updateStepIndicator('step3', false);
                updateFinalResults();
                return;
            }

            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                const data = await response.json();
                
                if (data.success && data.user) {
                    showStatus('verifyStatus', `
                        ✅ Token verification successful<br>
                        User: ${data.user.name}<br>
                        Valid token: ✅
                    `, 'success');
                    testResults.verify = true;
                    updateStepIndicator('step3', true);
                } else {
                    showStatus('verifyStatus', `❌ Token verification failed: ${data.error}`, 'error');
                    testResults.verify = false;
                    updateStepIndicator('step3', false);
                }
            } catch (error) {
                showStatus('verifyStatus', `❌ Verification error: ${error.message}`, 'error');
                testResults.verify = false;
                updateStepIndicator('step3', false);
            }
            updateFinalResults();
        }

        async function testAuthContext() {
            showStatus('authStatus', '🧪 Testing AuthContext compatibility...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('authStatus', '❌ No token found. Run previous tests first.', 'error');
                testResults.auth = false;
                updateStepIndicator('step4', false);
                updateFinalResults();
                return;
            }

            try {
                // Test the exact same call that AuthContext makes
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                if (response.ok) {
                    const responseText = await response.text();
                    let data;
                    
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        showStatus('authStatus', `❌ JSON parse error: ${parseError.message}`, 'error');
                        testResults.auth = false;
                        updateStepIndicator('step4', false);
                        updateFinalResults();
                        return;
                    }

                    if (data.success && data.user) {
                        showStatus('authStatus', `
                            ✅ AuthContext compatibility confirmed<br>
                            User: ${data.user.name}<br>
                            AuthContext will work: ✅
                        `, 'success');
                        testResults.auth = true;
                        updateStepIndicator('step4', true);
                    } else {
                        showStatus('authStatus', `❌ AuthContext would fail: ${data.error}`, 'error');
                        testResults.auth = false;
                        updateStepIndicator('step4', false);
                    }
                } else {
                    showStatus('authStatus', `❌ HTTP error: ${response.status}`, 'error');
                    testResults.auth = false;
                    updateStepIndicator('step4', false);
                }
            } catch (error) {
                showStatus('authStatus', `❌ AuthContext test error: ${error.message}`, 'error');
                testResults.auth = false;
                updateStepIndicator('step4', false);
            }
            updateFinalResults();
        }

        async function runAllTests() {
            showStatus('finalResults', '🔄 Running all tests...', 'info');
            
            await clearSession();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await verifyToken();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthContext();
        }

        // Initialize
        updateFinalResults();
    </script>
</body>
</html>