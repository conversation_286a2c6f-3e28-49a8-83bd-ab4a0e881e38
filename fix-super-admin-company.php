<?php
/**
 * Fix Super Admin Company Assignment
 */

require_once 'api/db-config.php';

echo "🔧 Fixing super admin company assignment...\n\n";

try {
    // Get super admin user
    $sql = "SELECT * FROM users WHERE role = 'super_admin' LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "📋 Super admin user:\n";
        echo "Object ID: " . $user['object_id'] . "\n";
        echo "Current company_id: " . $user['company_id'] . "\n\n";
        
        // Check if there's a company with the same object_id as the user
        $companySql = "SELECT * FROM companies WHERE object_id = ?";
        $stmt = $conn->prepare($companySql);
        $stmt->bind_param("s", $user['object_id']);
        $stmt->execute();
        $companyResult = $stmt->get_result();
        
        if ($companyResult->num_rows > 0) {
            $company = $companyResult->fetch_assoc();
            echo "✅ Found matching company:\n";
            echo "Company ID: " . $company['object_id'] . "\n";
            echo "Company Name: " . $company['name'] . "\n\n";
            
            // Update user to use this company_id
            $updateSql = "UPDATE users SET company_id = ? WHERE object_id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("ss", $company['object_id'], $user['object_id']);
            
            if ($updateStmt->execute()) {
                echo "✅ Updated super admin company_id to: " . $company['object_id'] . "\n";
            } else {
                echo "❌ Failed to update user: " . $conn->error . "\n";
            }
        } else {
            echo "❌ No matching company found for user " . $user['object_id'] . "\n";
            
            // Create a company for the super admin
            echo "Creating company for super admin...\n";
            $createSql = "INSERT INTO companies (object_id, name, email, owner_id, status, created_at, updated_at) 
                         VALUES (?, ?, ?, ?, 'active', NOW(), NOW())";
            $createStmt = $conn->prepare($createSql);
            
            $companyId = $user['object_id'];
            $companyName = 'Super Admin Company';
            $companyEmail = $user['email'];
            $ownerId = $user['object_id'];
            
            $createStmt->bind_param("ssss", $companyId, $companyName, $companyEmail, $ownerId);
            
            if ($createStmt->execute()) {
                echo "✅ Created company: $companyId\n";
                
                // Update user to use this company_id
                $updateSql = "UPDATE users SET company_id = ? WHERE object_id = ?";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->bind_param("ss", $companyId, $user['object_id']);
                
                if ($updateStmt->execute()) {
                    echo "✅ Updated super admin company_id to: $companyId\n";
                } else {
                    echo "❌ Failed to update user: " . $conn->error . "\n";
                }
            } else {
                echo "❌ Failed to create company: " . $conn->error . "\n";
            }
        }
        
        // Verify the final state
        echo "\n🔍 Final verification:\n";
        $verifySql = "SELECT u.object_id as user_id, u.company_id, c.name as company_name 
                     FROM users u 
                     LEFT JOIN companies c ON u.company_id = c.object_id 
                     WHERE u.role = 'super_admin'";
        $result = $conn->query($verifySql);
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            echo "User ID: " . $row['user_id'] . "\n";
            echo "Company ID: " . $row['company_id'] . "\n";
            echo "Company Name: " . ($row['company_name'] ?? 'NOT FOUND') . "\n";
        }
        
    } else {
        echo "❌ No super admin user found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
