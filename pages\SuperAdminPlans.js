function SuperAdminPlans() {
    const authContext = React.useContext(window.AuthContext || AuthContext);
    const [plans, setPlans] = React.useState([]);
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [notification, setNotification] = React.useState(null);
    const [showPlanModal, setShowPlanModal] = React.useState(false);
    const [editingPlan, setEditingPlan] = React.useState(null);

    React.useEffect(() => {
        loadPlansData();
    }, []);

    const loadPlansData = async () => {
        try {
            setLoading(true);
            const [plansResponse, businessTypesResponse] = await Promise.all([
                fetch('/api/super-admin/plans.php', {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                }),
                fetch('/api/super-admin/business-types.php', {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                })
            ]);

            const plansData = await plansResponse.json();
            const businessTypesData = await businessTypesResponse.json();

            if (plansData.success) {
                setPlans(plansData.plans || []);
            }
            if (businessTypesData.success) {
                setBusinessTypes(businessTypesData.business_types || []);
            }
        } catch (error) {
            console.error('Error loading plans data:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load plans data'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleCreatePlan = () => {
        setEditingPlan(null);
        setShowPlanModal(true);
    };

    const handleEditPlan = (plan) => {
        setEditingPlan(plan);
        setShowPlanModal(true);
    };

    const handleSavePlan = async (planData) => {
        try {
            const url = editingPlan 
                ? `/api/super-admin/plans.php?id=${editingPlan.id}`
                : '/api/super-admin/plans.php';
            
            const method = editingPlan ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(planData)
            });

            const data = await response.json();
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: editingPlan ? 'Plan updated successfully' : 'Plan created successfully'
                });
                setShowPlanModal(false);
                setEditingPlan(null);
                loadPlansData();
            } else {
                setNotification({
                    type: 'error',
                    message: data.message || 'Failed to save plan'
                });
            }
        } catch (error) {
            console.error('Error saving plan:', error);
            setNotification({
                type: 'error',
                message: 'Failed to save plan'
            });
        }
    };

    const handleDeletePlan = async (planId) => {
        if (!confirm('Are you sure you want to delete this plan?')) return;

        try {
            const response = await fetch(`/api/super-admin/plans.php?id=${planId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: 'Plan deleted successfully'
                });
                loadPlansData();
            } else {
                setNotification({
                    type: 'error',
                    message: data.message || 'Failed to delete plan'
                });
            }
        } catch (error) {
            console.error('Error deleting plan:', error);
            setNotification({
                type: 'error',
                message: 'Failed to delete plan'
            });
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading plans...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div className="flex items-center">
                            <button
                                onClick={() => window.location.hash = '#super-admin'}
                                className="mr-4 text-gray-600 hover:text-gray-900"
                            >
                                <i className="fas fa-arrow-left"></i>
                            </button>
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">Plans Management</h1>
                                <p className="text-sm text-gray-600">Create and manage subscription plans</p>
                            </div>
                        </div>
                        <button
                            onClick={handleCreatePlan}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
                        >
                            <i className="fas fa-plus mr-2"></i>
                            Create New Plan
                        </button>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    {plans.map((plan) => (
                        <div key={plan.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                            <div className="flex justify-between items-start mb-4">
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                                    <p className="text-sm text-gray-600">{plan.short_description}</p>
                                </div>
                                <div className="flex space-x-2">
                                    {plan.is_popular && (
                                        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                            Popular
                                        </span>
                                    )}
                                    <span className={`text-xs px-2 py-1 rounded-full ${
                                        plan.status === 'active' 
                                            ? 'bg-green-100 text-green-800' 
                                            : 'bg-gray-100 text-gray-800'
                                    }`}>
                                        {plan.status}
                                    </span>
                                </div>
                            </div>

                            <div className="mb-4">
                                <div className="text-2xl font-bold text-gray-900">
                                    {formatCurrency(plan.monthly_price)}
                                    <span className="text-sm font-normal text-gray-600">/month</span>
                                </div>
                                <div className="text-sm text-gray-600">
                                    {formatCurrency(plan.yearly_price)}/year
                                </div>
                            </div>

                            <div className="mb-4">
                                <h5 className="text-sm font-medium text-gray-900 mb-2">Features:</h5>
                                <ul className="text-sm text-gray-600 space-y-1">
                                    {plan.features && plan.features.slice(0, 3).map((feature, index) => (
                                        <li key={index} className="flex items-center">
                                            <i className="fas fa-check text-green-500 mr-2"></i>
                                            {feature}
                                        </li>
                                    ))}
                                    {plan.features && plan.features.length > 3 && (
                                        <li className="text-gray-500">
                                            +{plan.features.length - 3} more features
                                        </li>
                                    )}
                                </ul>
                            </div>

                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleEditPlan(plan)}
                                    className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700"
                                >
                                    <i className="fas fa-edit mr-1"></i>
                                    Edit
                                </button>
                                <button
                                    onClick={() => handleDeletePlan(plan.id)}
                                    className="flex-1 bg-red-600 text-white px-3 py-2 rounded-md text-sm hover:bg-red-700"
                                >
                                    <i className="fas fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {plans.length === 0 && (
                    <div className="text-center py-12">
                        <div className="mx-auto h-12 w-12 text-gray-400">
                            <i className="fas fa-credit-card text-4xl"></i>
                        </div>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No plans found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            Get started by creating your first subscription plan.
                        </p>
                        <div className="mt-6">
                            <button
                                onClick={handleCreatePlan}
                                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            >
                                <i className="fas fa-plus mr-2"></i>
                                Create Plan
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {/* Plan Modal */}
            {showPlanModal && (
                <PlanModal
                    isOpen={showPlanModal}
                    onClose={() => {
                        setShowPlanModal(false);
                        setEditingPlan(null);
                    }}
                    plan={editingPlan}
                    onSave={handleSavePlan}
                    businessTypes={businessTypes}
                />
            )}

            {/* Notification */}
            {notification && (
                <Notification
                    type={notification.type}
                    message={notification.message}
                    onClose={() => setNotification(null)}
                />
            )}
        </div>
    );
}

// Make component globally available
window.SuperAdminPlans = SuperAdminPlans;
