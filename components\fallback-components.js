/**
 * Fallback Components for Bizma SaaS Application
 * These components provide basic functionality when external components fail to load
 */

// Global LoadingSpinner fallback
if (typeof window.LoadingSpinner === 'undefined') {
    window.LoadingSpinner = function LoadingSpinner({ size = 'md', text = 'Loading...' }) {
        const sizeClass = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-12 w-12' : 'h-8 w-8';
        return React.createElement('div', {
            className: 'flex flex-col items-center justify-center p-4'
        }, [
            React.createElement('div', {
                key: 'spinner',
                className: `${sizeClass} border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin`
            }),
            text && React.createElement('p', {
                key: 'text',
                className: 'mt-2 text-sm text-gray-600'
            }, text)
        ]);
    };
}

// Global ErrorMessage fallback
if (typeof window.ErrorMessage === 'undefined') {
    window.ErrorMessage = function ErrorMessage({ error, onRetry }) {
        const message = typeof error === 'string' ? error : (error?.message || 'An error occurred');
        return React.createElement('div', {
            className: 'bg-red-50 border border-red-200 rounded-md p-4 my-4'
        }, [
            React.createElement('div', {
                key: 'content',
                className: 'flex items-center'
            }, [
                React.createElement('i', {
                    key: 'icon',
                    className: 'fas fa-exclamation-triangle text-red-500 mr-2'
                }),
                React.createElement('p', {
                    key: 'message',
                    className: 'text-red-700'
                }, message)
            ]),
            onRetry && React.createElement('button', {
                key: 'retry',
                onClick: onRetry,
                className: 'mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors'
            }, 'Try Again')
        ]);
    };
}

// Global TrialBanner fallback
if (typeof window.TrialBanner === 'undefined') {
    window.TrialBanner = function TrialBanner({ subscription }) {
        if (!subscription || subscription.planId !== 'trial') return null;
        
        return React.createElement('div', {
            className: 'bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-4 rounded-r-md shadow-sm'
        }, React.createElement('div', {
            className: 'flex items-center'
        }, [
            React.createElement('div', {
                key: 'icon',
                className: 'py-1'
            }, React.createElement('i', {
                className: 'fas fa-info-circle text-blue-500 mr-2 text-lg'
            })),
            React.createElement('div', {
                key: 'content'
            }, [
                React.createElement('p', {
                    key: 'title',
                    className: 'font-bold'
                }, 'Free Trial Active'),
                React.createElement('p', {
                    key: 'description',
                    className: 'text-sm'
                }, "You're currently on a free trial. Upgrade to unlock all features.")
            ])
        ]));
    };
}

// Global NotificationContainer fallback
if (typeof window.NotificationContainer === 'undefined') {
    window.NotificationContainer = function NotificationContainer() {
        const [notifications, setNotifications] = React.useState([]);
        
        React.useEffect(() => {
            // Simple notification system
            const handleNotification = (event) => {
                const notification = {
                    id: Date.now(),
                    type: event.detail.type || 'info',
                    message: event.detail.message,
                    duration: event.detail.duration || 5000
                };
                
                setNotifications(prev => [...prev, notification]);
                
                // Auto-remove after duration
                setTimeout(() => {
                    setNotifications(prev => prev.filter(n => n.id !== notification.id));
                }, notification.duration);
            };
            
            window.addEventListener('show-notification', handleNotification);
            return () => window.removeEventListener('show-notification', handleNotification);
        }, []);
        
        if (!notifications.length) return null;
        
        return React.createElement('div', {
            className: 'fixed top-4 right-4 z-50 w-full max-w-sm space-y-4'
        }, notifications.map(notification => 
            React.createElement('div', {
                key: notification.id,
                className: `p-4 rounded-md shadow-lg ${
                    notification.type === 'error' ? 'bg-red-100 text-red-700 border border-red-200' :
                    notification.type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' :
                    notification.type === 'warning' ? 'bg-yellow-100 text-yellow-700 border border-yellow-200' :
                    'bg-blue-100 text-blue-700 border border-blue-200'
                }`
            }, [
                React.createElement('div', {
                    key: 'content',
                    className: 'flex items-center justify-between'
                }, [
                    React.createElement('p', {
                        key: 'message',
                        className: 'text-sm font-medium'
                    }, notification.message),
                    React.createElement('button', {
                        key: 'close',
                        onClick: () => setNotifications(prev => prev.filter(n => n.id !== notification.id)),
                        className: 'ml-2 text-lg hover:opacity-70'
                    }, '×')
                ])
            ])
        ));
    };
}

// Global ExtendTrialModal fallback
if (typeof window.ExtendTrialModal === 'undefined') {
    window.ExtendTrialModal = function ExtendTrialModal({ isOpen, onClose, onExtend }) {
        if (!isOpen) return null;
        
        return React.createElement('div', {
            className: 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
        }, React.createElement('div', {
            className: 'bg-white rounded-lg p-6 max-w-md w-full mx-4'
        }, [
            React.createElement('h2', {
                key: 'title',
                className: 'text-xl font-bold mb-4'
            }, 'Extend Trial'),
            React.createElement('p', {
                key: 'message',
                className: 'text-gray-600 mb-6'
            }, 'Would you like to extend your trial period?'),
            React.createElement('div', {
                key: 'actions',
                className: 'flex space-x-3'
            }, [
                React.createElement('button', {
                    key: 'cancel',
                    onClick: onClose,
                    className: 'flex-1 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50'
                }, 'Cancel'),
                React.createElement('button', {
                    key: 'extend',
                    onClick: onExtend,
                    className: 'flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700'
                }, 'Extend Trial')
            ])
        ]));
    };
}

// Global notification helper
if (typeof window.showNotification === 'undefined') {
    window.showNotification = function(message, type = 'info', duration = 5000) {
        window.dispatchEvent(new CustomEvent('show-notification', {
            detail: { message, type, duration }
        }));
    };
}

console.log('Fallback components loaded successfully');
