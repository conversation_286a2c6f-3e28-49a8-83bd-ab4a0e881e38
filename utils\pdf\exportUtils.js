async function exportToPDF(element, filename, options = {}) {
    try {
        const html2pdf = window.html2pdf;
        if (!html2pdf) {
            console.error('html2pdf library not loaded');
            return false;
        }

        const defaultOptions = {
            margin: 0,
            filename: filename || 'document.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { 
                scale: 2,
                useCORS: true,
                letterRendering: true,
                logging: false
            },
            jsPDF: { 
                unit: 'mm', 
                format: options.format || 'a4', 
                orientation: options.orientation || 'portrait' 
            },
            pagebreak: { mode: 'avoid-all' }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        if (options.html2canvas) {
            mergedOptions.html2canvas = { ...defaultOptions.html2canvas, ...options.html2canvas };
        }
        
        if (options.jsPDF) {
            mergedOptions.jsPDF = { ...defaultOptions.jsPDF, ...options.jsPDF };
        }

        const clonedElement = element.cloneNode(true);
        const styleElement = document.createElement('style');
        styleElement.textContent = getPDFStyles();
        
        clonedElement.appendChild(styleElement);
        
        const container = document.createElement('div');
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.top = '-9999px';
        container.appendChild(clonedElement);
        document.body.appendChild(container);

        await html2pdf()
            .from(clonedElement)
            .set(mergedOptions)
            .save();
            
        setTimeout(() => {
            document.body.removeChild(container);
        }, 100);
        
        return true;
    } catch (error) {
        console.error('Error generating PDF:', error);
        return false;
    }
}

function getPDFStyles() {
    return `
        .template-container {
            box-shadow: none !important;
            padding: 15mm !important;
            max-width: 100% !important;
            width: 210mm !important;
            min-height: 297mm !important;
            margin: 0 !important;
            font-size: 10pt !important;
        }
        .template-container::before {
            display: none !important;
        }
        .company-logo {
            max-height: 50px !important;
        }
        .signature-image {
            max-height: 40px !important;
        }
        .items-table th, .items-table td {
            padding: 4px 8px !important;
            font-size: 9pt !important;
        }
        .info-section {
            padding: 8px !important;
            margin-bottom: 10px !important;
        }
        .signature-section {
            margin-top: 15px !important;
        }
        .template-footer {
            margin-top: 15px !important;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        table th {
            background-color: #f3f4f6 !important;
            color: #374151;
            font-weight: 600;
            text-align: left;
            padding: 0.5rem 0.75rem;
        }
        
        table td {
            border-bottom: 1px solid #e5e7eb;
            padding: 0.5rem 0.75rem;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-paid, .status-accepted, .status-signed {
            background-color: #d1fae5 !important;
            color: #065f46 !important;
        }
        
        .status-overdue, .status-rejected {
            background-color: #fee2e2 !important;
            color: #991b1b !important;
        }
        
        .status-draft {
            background-color: #f3f4f6 !important;
            color: #4b5563 !important;
        }
        
        .status-sent, .status-pending {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }
        
        .watermark {
            opacity: 0.07 !important;
            font-size: 4rem !important;
        }
    `;
}

async function exportElementAsImage(element, options = {}) {
    try {
        if (!element) {
            throw new Error('No element provided');
        }
        
        const defaultOptions = {
            type: 'image/png',
            quality: 0.95,
            backgroundColor: '#ffffff',
            scale: 2,
            fileName: 'document.png'
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        const canvas = await html2canvas(element, {
            scale: mergedOptions.scale,
            backgroundColor: mergedOptions.backgroundColor,
            useCORS: true,
            allowTaint: true,
            logging: false
        });

        const dataUrl = canvas.toDataURL(mergedOptions.type, mergedOptions.quality);
        
        if (options.download) {
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = mergedOptions.fileName;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        return dataUrl;
    } catch (error) {
        console.error('Error exporting element as image:', error);
        throw error;
    }
}
