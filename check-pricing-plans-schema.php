<?php
require_once 'api/db-config.php';

echo "<h2>Pricing Plans Table Schema</h2>\n";

$sql = "DESCRIBE pricing_plans";
$result = $conn->query($sql);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>Error: " . $conn->error . "</p>\n";
}

echo "<h2>Current Pricing Plans Data</h2>\n";

$sql = "SELECT * FROM pricing_plans";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr>";
    
    // Get column names
    $fields = $result->fetch_fields();
    foreach ($fields as $field) {
        echo "<th>" . htmlspecialchars($field->name) . "</th>";
    }
    echo "</tr>\n";
    
    // Reset result pointer
    $result->data_seek(0);
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>No data found or error: " . $conn->error . "</p>\n";
}

$conn->close();
?>