/**
 * Enhanced Business Type Templates
 * Provides dynamic templates based on business type with customization options
 */

// Enhanced Business Type Template Manager
window.EnhancedBusinessTemplates = {
    
    // Template configurations for different business types
    templates: {
        'Retail Business': {
            name: 'Retail Business',
            description: 'Perfect for retail stores, shops, and e-commerce businesses',
            defaultItems: [
                { name: 'Product Sales', category: 'Products', price: 0, unit: 'piece' },
                { name: 'Gift Wrapping', category: 'Services', price: 50, unit: 'service' },
                { name: 'Home Delivery', category: 'Services', price: 100, unit: 'delivery' },
                { name: 'Installation Service', category: 'Services', price: 500, unit: 'service' }
            ],
            invoiceTemplate: {
                header: 'SALES INVOICE',
                terms: 'Payment due within 30 days. Returns accepted within 7 days with receipt.',
                footer: 'Thank you for shopping with us!'
            },
            quotationTemplate: {
                header: 'PRICE QUOTATION',
                terms: 'Prices valid for 30 days. Subject to stock availability.',
                footer: 'We look forward to serving you!'
            },
            leadFields: ['Product Interest', 'Budget Range', 'Purchase Timeline', 'Preferred Contact Method'],
            customFields: {
                customers: ['Preferred Payment Method', 'Loyalty Program Member', 'Purchase History'],
                products: ['SKU', 'Brand', 'Category', 'Stock Level', 'Reorder Point']
            }
        },
        
        'Healthcare Services': {
            name: 'Healthcare Services',
            description: 'Tailored for clinics, hospitals, and healthcare providers',
            defaultItems: [
                { name: 'Consultation Fee', category: 'Consultation', price: 500, unit: 'visit' },
                { name: 'Diagnostic Test', category: 'Diagnostics', price: 1000, unit: 'test' },
                { name: 'Treatment Package', category: 'Treatment', price: 5000, unit: 'package' },
                { name: 'Follow-up Visit', category: 'Consultation', price: 300, unit: 'visit' }
            ],
            invoiceTemplate: {
                header: 'MEDICAL BILL',
                terms: 'Payment due immediately. Insurance claims to be processed separately.',
                footer: 'Wishing you good health!'
            },
            quotationTemplate: {
                header: 'TREATMENT ESTIMATE',
                terms: 'Estimate valid for 15 days. Actual costs may vary based on treatment.',
                footer: 'Your health is our priority!'
            },
            leadFields: ['Medical Concern', 'Insurance Provider', 'Preferred Doctor', 'Appointment Preference'],
            customFields: {
                customers: ['Insurance Details', 'Medical History', 'Emergency Contact', 'Allergies'],
                services: ['Duration', 'Prerequisites', 'Follow-up Required', 'Specialist Required']
            }
        },
        
        'Consulting Services': {
            name: 'Consulting Services',
            description: 'Ideal for consultants, agencies, and professional services',
            defaultItems: [
                { name: 'Strategy Consultation', category: 'Consulting', price: 5000, unit: 'hour' },
                { name: 'Project Management', category: 'Management', price: 8000, unit: 'hour' },
                { name: 'Training Session', category: 'Training', price: 3000, unit: 'session' },
                { name: 'Report Preparation', category: 'Documentation', price: 2000, unit: 'report' }
            ],
            invoiceTemplate: {
                header: 'PROFESSIONAL SERVICES INVOICE',
                terms: 'Payment due within 15 days. Late payments subject to 2% monthly interest.',
                footer: 'Thank you for choosing our services!'
            },
            quotationTemplate: {
                header: 'SERVICE PROPOSAL',
                terms: 'Proposal valid for 45 days. Rates subject to scope changes.',
                footer: 'We look forward to partnering with you!'
            },
            leadFields: ['Service Required', 'Project Timeline', 'Budget Range', 'Decision Maker'],
            customFields: {
                customers: ['Industry', 'Company Size', 'Previous Projects', 'Preferred Communication'],
                services: ['Deliverables', 'Timeline', 'Resources Required', 'Success Metrics']
            }
        },
        
        'Manufacturing': {
            name: 'Manufacturing',
            description: 'Built for manufacturers, suppliers, and production companies',
            defaultItems: [
                { name: 'Custom Manufacturing', category: 'Production', price: 10000, unit: 'unit' },
                { name: 'Quality Testing', category: 'QA', price: 500, unit: 'test' },
                { name: 'Packaging Service', category: 'Services', price: 200, unit: 'package' },
                { name: 'Shipping & Handling', category: 'Logistics', price: 1000, unit: 'shipment' }
            ],
            invoiceTemplate: {
                header: 'MANUFACTURING INVOICE',
                terms: 'Payment due within 45 days. Quality guarantee for 1 year.',
                footer: 'Quality is our commitment!'
            },
            quotationTemplate: {
                header: 'MANUFACTURING QUOTE',
                terms: 'Quote valid for 60 days. Minimum order quantities apply.',
                footer: 'Let us manufacture your success!'
            },
            leadFields: ['Product Specifications', 'Quantity Required', 'Delivery Timeline', 'Quality Standards'],
            customFields: {
                customers: ['Industry Type', 'Volume Requirements', 'Quality Certifications', 'Delivery Preferences'],
                products: ['Specifications', 'Materials', 'Production Time', 'Quality Standards', 'Certifications']
            }
        },
        
        'Education Services': {
            name: 'Education Services',
            description: 'Designed for schools, training centers, and educational institutions',
            defaultItems: [
                { name: 'Course Fee', category: 'Education', price: 15000, unit: 'course' },
                { name: 'Study Materials', category: 'Materials', price: 2000, unit: 'set' },
                { name: 'Examination Fee', category: 'Assessment', price: 1000, unit: 'exam' },
                { name: 'Certificate Fee', category: 'Certification', price: 500, unit: 'certificate' }
            ],
            invoiceTemplate: {
                header: 'EDUCATION FEE INVOICE',
                terms: 'Fees due before course commencement. Refund policy as per terms.',
                footer: 'Empowering minds, building futures!'
            },
            quotationTemplate: {
                header: 'COURSE FEE STRUCTURE',
                terms: 'Fees valid for current academic session. Early bird discounts available.',
                footer: 'Invest in your future with us!'
            },
            leadFields: ['Course Interest', 'Education Background', 'Career Goals', 'Preferred Schedule'],
            customFields: {
                customers: ['Education Level', 'Work Experience', 'Career Goals', 'Learning Preferences'],
                courses: ['Duration', 'Prerequisites', 'Certification', 'Career Outcomes']
            }
        },
        
        'Jewellery Business': {
            name: 'Jewellery Business',
            description: 'Specialized for jewellery stores, designers, and manufacturers',
            defaultItems: [
                { name: 'Gold Jewellery', category: 'Gold', price: 50000, unit: 'piece' },
                { name: 'Diamond Jewellery', category: 'Diamond', price: 100000, unit: 'piece' },
                { name: 'Silver Jewellery', category: 'Silver', price: 5000, unit: 'piece' },
                { name: 'Custom Design', category: 'Services', price: 10000, unit: 'design' },
                { name: 'Repair Service', category: 'Services', price: 1000, unit: 'service' }
            ],
            invoiceTemplate: {
                header: 'JEWELLERY INVOICE',
                terms: 'Payment due at delivery. Buyback guarantee available. Hallmark certified.',
                footer: 'Crafting memories, one piece at a time!'
            },
            quotationTemplate: {
                header: 'JEWELLERY QUOTATION',
                terms: 'Prices subject to gold/silver rate changes. Custom orders require advance payment.',
                footer: 'Your trust is our treasure!'
            },
            leadFields: ['Occasion', 'Metal Preference', 'Budget Range', 'Design Style', 'Gemstone Preference'],
            customFields: {
                customers: ['Occasion History', 'Metal Allergies', 'Size Preferences', 'Style Preferences'],
                products: ['Metal Type', 'Purity', 'Weight', 'Gemstones', 'Certification', 'Hallmark']
            }
        }
    },
    
    /**
     * Get template for business type
     */
    getTemplate(businessType) {
        return this.templates[businessType] || this.templates['Consulting Services'];
    },
    
    /**
     * Get all available templates
     */
    getAllTemplates() {
        return Object.values(this.templates);
    },
    
    /**
     * Apply template to company
     */
    async applyTemplate(businessType, companyId) {
        try {
            const template = this.getTemplate(businessType);
            const results = {
                items: [],
                settings: null,
                success: true,
                errors: []
            };
            
            // Create default items
            for (const item of template.defaultItems) {
                try {
                    const response = await fetch(window.getApiUrl('/item'), {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            ...item,
                            company_id: companyId
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        results.items.push(data);
                    } else {
                        results.errors.push(`Failed to create item: ${item.name}`);
                    }
                } catch (error) {
                    results.errors.push(`Error creating item ${item.name}: ${error.message}`);
                }
            }
            
            // Apply template settings
            try {
                const settingsResponse = await fetch(window.getApiUrl('/settings/template'), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        business_type: businessType,
                        invoice_template: template.invoiceTemplate,
                        quotation_template: template.quotationTemplate,
                        custom_fields: template.customFields,
                        lead_fields: template.leadFields
                    })
                });
                
                if (settingsResponse.ok) {
                    results.settings = await settingsResponse.json();
                } else {
                    results.errors.push('Failed to apply template settings');
                }
            } catch (error) {
                results.errors.push(`Error applying settings: ${error.message}`);
            }
            
            return results;
            
        } catch (error) {
            console.error('Error applying template:', error);
            return {
                success: false,
                error: error.message,
                items: [],
                settings: null,
                errors: [error.message]
            };
        }
    },
    
    /**
     * Get template preview
     */
    getTemplatePreview(businessType) {
        const template = this.getTemplate(businessType);
        
        return {
            name: template.name,
            description: template.description,
            itemsCount: template.defaultItems.length,
            features: [
                `${template.defaultItems.length} pre-configured items`,
                'Customized invoice template',
                'Customized quotation template',
                `${template.leadFields.length} lead tracking fields`,
                'Industry-specific custom fields'
            ],
            sampleItems: template.defaultItems.slice(0, 3),
            invoicePreview: template.invoiceTemplate,
            quotationPreview: template.quotationTemplate
        };
    },
    
    /**
     * Customize template
     */
    customizeTemplate(businessType, customizations) {
        const template = { ...this.getTemplate(businessType) };
        
        if (customizations.items) {
            template.defaultItems = [...template.defaultItems, ...customizations.items];
        }
        
        if (customizations.invoiceTemplate) {
            template.invoiceTemplate = { ...template.invoiceTemplate, ...customizations.invoiceTemplate };
        }
        
        if (customizations.quotationTemplate) {
            template.quotationTemplate = { ...template.quotationTemplate, ...customizations.quotationTemplate };
        }
        
        if (customizations.leadFields) {
            template.leadFields = [...template.leadFields, ...customizations.leadFields];
        }
        
        if (customizations.customFields) {
            template.customFields = { ...template.customFields, ...customizations.customFields };
        }
        
        return template;
    }
};

// Business Template Selector Component
window.BusinessTemplateSelector = function BusinessTemplateSelector({ 
    selectedBusinessType, 
    onBusinessTypeChange, 
    onApplyTemplate,
    showPreview = true 
}) {
    const [templates] = React.useState(window.EnhancedBusinessTemplates.getAllTemplates());
    const [preview, setPreview] = React.useState(null);
    const [applying, setApplying] = React.useState(false);
    
    React.useEffect(() => {
        if (selectedBusinessType && showPreview) {
            const templatePreview = window.EnhancedBusinessTemplates.getTemplatePreview(selectedBusinessType);
            setPreview(templatePreview);
        }
    }, [selectedBusinessType, showPreview]);
    
    const handleApplyTemplate = async () => {
        if (!selectedBusinessType) return;
        
        setApplying(true);
        try {
            const authContext = React.useContext(window.AuthContext);
            const companyId = authContext?.user?.company_id;
            
            if (!companyId) {
                throw new Error('Company ID not found');
            }
            
            const result = await window.EnhancedBusinessTemplates.applyTemplate(selectedBusinessType, companyId);
            
            if (onApplyTemplate) {
                onApplyTemplate(result);
            }
        } catch (error) {
            console.error('Error applying template:', error);
            if (onApplyTemplate) {
                onApplyTemplate({ success: false, error: error.message });
            }
        } finally {
            setApplying(false);
        }
    };
    
    return (
        <div className="business-template-selector">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {templates.map((template) => (
                    <div
                        key={template.name}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            selectedBusinessType === template.name
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => onBusinessTypeChange && onBusinessTypeChange(template.name)}
                    >
                        <h3 className="font-semibold text-lg mb-2">{template.name}</h3>
                        <p className="text-gray-600 text-sm mb-3">{template.description}</p>
                        <div className="text-xs text-gray-500">
                            {template.defaultItems.length} items included
                        </div>
                    </div>
                ))}
            </div>
            
            {preview && showPreview && (
                <div className="bg-white border rounded-lg p-6 mb-6">
                    <h3 className="text-xl font-semibold mb-4">Template Preview: {preview.name}</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 className="font-medium mb-2">Features Included:</h4>
                            <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                                {preview.features.map((feature, index) => (
                                    <li key={index}>{feature}</li>
                                ))}
                            </ul>
                        </div>
                        
                        <div>
                            <h4 className="font-medium mb-2">Sample Items:</h4>
                            <div className="space-y-2">
                                {preview.sampleItems.map((item, index) => (
                                    <div key={index} className="text-sm bg-gray-50 p-2 rounded">
                                        <span className="font-medium">{item.name}</span>
                                        <span className="text-gray-500 ml-2">₹{item.price}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                    
                    {onApplyTemplate && (
                        <div className="mt-6 pt-4 border-t">
                            <button
                                onClick={handleApplyTemplate}
                                disabled={applying}
                                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                            >
                                {applying ? 'Applying Template...' : 'Apply This Template'}
                            </button>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};