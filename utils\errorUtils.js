// Error reporting utility functions

// Global error reporting function
function reportError(error) {
    try {
        // Log to console
        console.error('Application Error:', error);

        // Show user-friendly error message using toast if available
        if (window.toast) {
            window.toast.error('An error occurred. Our team has been notified.');
        }

        // In a production environment, you would send this to your error tracking service
        // For now, we'll just ensure the error is properly logged
        const errorDetails = {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };

        // Log detailed error information
        console.error('Error Details:', errorDetails);
    } catch (e) {
        // Fallback error handling
        console.error('Error in error reporting:', e);
    }
}

// Make reportError available globally
window.reportError = reportError;