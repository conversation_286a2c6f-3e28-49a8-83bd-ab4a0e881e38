<?php
// Disable displaying errors directly to output
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Ensure we always return JSON
header('Content-Type: application/json');

// Custom error handler to capture all errors
function errorHandler($errno, $errstr, $errfile, $errline) {
    $error = [
        'success' => false,
        'message' => 'PHP Error: ' . $errstr,
        'error_details' => [
            'type' => $errno,
            'file' => basename($errfile),
            'line' => $errline
        ]
    ];
    
    http_response_code(500);
    echo json_encode($error);
    exit;
}

// Set custom error handler
set_error_handler('errorHandler');

try {
    require_once __DIR__ . '/../config/config.php';
    
    // Load configuration
    Config::load();
    
    // Get database configuration
    $db = Config::getDatabase();
    
    // Create connection
    $conn = @new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Check required tables
    $requiredTables = ['users', 'companies', 'subscriptions', 'pricing_plans'];
    $tableStatus = [];
    
    foreach ($requiredTables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $exists = $result->num_rows > 0;
        
        $tableStatus[$table] = [
            'exists' => $exists
        ];
        
        if ($exists) {
            // Get table structure
            $columns = [];
            $columnsResult = $conn->query("DESCRIBE $table");
            while ($column = $columnsResult->fetch_assoc()) {
                $columns[] = $column;
            }
            $tableStatus[$table]['columns'] = $columns;
            
            // Get row count
            $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
            $countRow = $countResult->fetch_assoc();
            $tableStatus[$table]['row_count'] = $countRow['count'];
        }
    }
    
    // Close connection
    $conn->close();
    
    // Return results
    echo json_encode([
        'success' => true,
        'database' => [
            'host' => $db['host'],
            'name' => $db['database'],
            'connected' => true
        ],
        'tables' => $tableStatus
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>