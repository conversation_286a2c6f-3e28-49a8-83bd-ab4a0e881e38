// LoadingSpinner Component
function LoadingSpinner({ size = 'md', text = 'Loading...', center = true, fullScreen = false, overlay = false }) {
        // Size variants with border thickness
        const sizeClasses = {
            xs: 'h-3 w-3 border-2',
            sm: 'h-5 w-5 border-2',
            md: 'h-8 w-8 border-2',
            lg: 'h-12 w-12 border-3',
            xl: 'h-16 w-16 border-4'
        };
        
        // Text size based on spinner size
        const textSizes = {
            xs: 'text-xs',
            sm: 'text-xs',
            md: 'text-sm',
            lg: 'text-base',
            xl: 'text-lg'
        };
        
        // Get the appropriate size classes
        const spinnerSize = sizeClasses[size] || sizeClasses.md;
        const textSize = textSizes[size] || textSizes.md;
        
        // Base spinner element
        const spinner = document.createElement('div');
        spinner.className = 'flex flex-col items-center';

        const spinnerDiv = document.createElement('div');
        spinnerDiv.className = `animate-spin rounded-full border-b-2 border-blue-600 ${spinnerSize}`;
        spinner.appendChild(spinnerDiv);

        if (text) {
            const textP = document.createElement('p');
            textP.className = `mt-2 ${textSize} text-gray-600 text-center`;
            textP.textContent = text;
            spinner.appendChild(textP);
        }
        
        // If fullScreen, render a centered spinner that covers the entire viewport
        if (fullScreen) {
            const fullScreenDiv = document.createElement('div');
            fullScreenDiv.className = 'fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50';
            fullScreenDiv.appendChild(spinner);
            return fullScreenDiv;
        }

        // If overlay, render a spinner that covers its parent container
        if (overlay) {
            const overlayDiv = document.createElement('div');
            overlayDiv.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10';
            overlayDiv.appendChild(spinner);
            return overlayDiv;
        }

        // If center is true, center the spinner in its container
        if (center) {
            const centerDiv = document.createElement('div');
            centerDiv.className = 'flex justify-center items-center p-4';
            centerDiv.appendChild(spinner);
            return centerDiv;
        }
        
        // Default: just return the spinner
        return spinner;
}

// Make LoadingSpinner globally available
window.LoadingSpinner = LoadingSpinner;