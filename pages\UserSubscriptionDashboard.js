function UserSubscriptionDashboard() {
    try {
        const authContext = React.useContext(AuthContext);
        const [currentSubscription, setCurrentSubscription] = React.useState(null);
        const [trialStatus, setTrialStatus] = React.useState(null);
        const [usageStats, setUsageStats] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [showPlansModal, setShowPlansModal] = React.useState(false);
        const [notification, setNotification] = React.useState(null);

        React.useEffect(() => {
            if (authContext && authContext.isAuthenticated && authContext.token) {
                fetchSubscriptionData();
            }
        }, [authContext]);

        const fetchSubscriptionData = async () => {
            try {
                setLoading(true);
                
                // Fetch current subscription
                const subResponse = await fetch(window.getApiUrl('/subscription-management/current'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (subResponse.ok) {
                    const subData = await subResponse.json();
                    if (subData.success) {
                        setCurrentSubscription(subData.data);
                    }
                }

                // Fetch trial status
                const trialResponse = await fetch(window.getApiUrl('/subscription-management/trial-status'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (trialResponse.ok) {
                    const trialData = await trialResponse.json();
                    if (trialData.success) {
                        setTrialStatus(trialData.data);
                    }
                }

                // Fetch usage stats
                const usageResponse = await fetch(window.getApiUrl('/subscription-management/usage'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (usageResponse.ok) {
                    const usageData = await usageResponse.json();
                    if (usageData.success) {
                        setUsageStats(usageData.data);
                    }
                }

            } catch (error) {
                console.error('Error fetching subscription data:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load subscription data'
                });
            } finally {
                setLoading(false);
            }
        };

        const getSubscriptionStatusColor = (status) => {
            switch (status) {
                case 'active': return 'bg-green-100 text-green-800';
                case 'trial': return 'bg-blue-100 text-blue-800';
                case 'expired': return 'bg-red-100 text-red-800';
                case 'cancelled': return 'bg-gray-100 text-gray-800';
                default: return 'bg-yellow-100 text-yellow-800';
            }
        };

        const formatDate = (dateString) => {
            if (!dateString) return 'N/A';
            return new Date(dateString).toLocaleDateString();
        };

        const calculateTrialDaysLeft = () => {
            if (!trialStatus || !trialStatus.trial_end_date) return 0;
            const endDate = new Date(trialStatus.trial_end_date);
            const today = new Date();
            const diffTime = endDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return Math.max(0, diffDays);
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            );
        }

        return (
            <div data-name="user-subscription-dashboard">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Page Header */}
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900">My Subscription</h1>
                        <p className="mt-2 text-gray-600">Manage your subscription, view usage, and billing information</p>
                    </div>

                    {/* Notification */}
                    {notification && (
                        <div className={`mb-6 p-4 rounded-md ${
                            notification.type === 'error' ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'
                        }`}>
                            <div className="flex">
                                <div className="ml-3">
                                    <p className="text-sm font-medium">{notification.message}</p>
                                </div>
                                <div className="ml-auto pl-3">
                                    <button
                                        onClick={() => setNotification(null)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <span className="sr-only">Dismiss</span>
                                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="space-y-8">
                        {/* Current Subscription Status */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold text-gray-900">Current Subscription</h2>
                                <button
                                    onClick={() => setShowPlansModal(true)}
                                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                                >
                                    View Plans
                                </button>
                            </div>
                            
                            {currentSubscription ? (
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Plan</h3>
                                        <p className="mt-1 text-lg font-semibold text-gray-900 capitalize">
                                            {currentSubscription.plan_id || 'Unknown'}
                                        </p>
                                    </div>
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Status</h3>
                                        <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSubscriptionStatusColor(currentSubscription.status)}`}>
                                            {currentSubscription.status || 'Unknown'}
                                        </span>
                                    </div>
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Billing</h3>
                                        <p className="mt-1 text-lg font-semibold text-gray-900">
                                            ₹{currentSubscription.amount || 0}/{currentSubscription.billing_cycle || 'month'}
                                        </p>
                                    </div>
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Next Billing</h3>
                                        <p className="mt-1 text-sm text-gray-900">
                                            {formatDate(currentSubscription.next_billing_date)}
                                        </p>
                                    </div>
                                </div>
                            ) : trialStatus && trialStatus.status === 'trial' ? (
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0">
                                                <svg className="h-8 w-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <div className="ml-4">
                                                <h3 className="text-lg font-medium text-blue-800">Free Trial Active</h3>
                                                <p className="mt-1 text-sm text-blue-700">
                                                    {calculateTrialDaysLeft()} days remaining in your free trial
                                                </p>
                                                <p className="text-xs text-blue-600">
                                                    Trial ends: {formatDate(trialStatus.trial_end_date)}
                                                </p>
                                            </div>
                                        </div>
                                        <button
                                            onClick={() => setShowPlansModal(true)}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                                        >
                                            Upgrade Now
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0">
                                                <svg className="h-8 w-8 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <div className="ml-4">
                                                <h3 className="text-lg font-medium text-yellow-800">No Active Subscription</h3>
                                                <p className="mt-1 text-sm text-yellow-700">
                                                    Start your free trial or choose a plan to get started
                                                </p>
                                            </div>
                                        </div>
                                        <button
                                            onClick={() => setShowPlansModal(true)}
                                            className="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition-colors"
                                        >
                                            Get Started
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Usage Statistics */}
                        {usageStats && (
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h2 className="text-xl font-semibold text-gray-900 mb-6">Usage Statistics</h2>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Leads</h3>
                                        <div className="mt-2">
                                            <div className="flex items-center justify-between mb-1">
                                                <span className="text-lg font-semibold text-gray-900">
                                                    {usageStats.leads_used || 0}/{usageStats.leads_limit || 'Unlimited'}
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    {usageStats.leads_limit ? `${Math.round((usageStats.leads_used / usageStats.leads_limit) * 100)}%` : '0%'}
                                                </span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-blue-600 h-2 rounded-full"
                                                    style={{ 
                                                        width: usageStats.leads_limit ? 
                                                            `${Math.min((usageStats.leads_used / usageStats.leads_limit) * 100, 100)}%` : 
                                                            '10%' 
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Team Members</h3>
                                        <div className="mt-2">
                                            <div className="flex items-center justify-between mb-1">
                                                <span className="text-lg font-semibold text-gray-900">
                                                    {usageStats.users_used || 0}/{usageStats.users_limit || 'Unlimited'}
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    {usageStats.users_limit ? `${Math.round((usageStats.users_used / usageStats.users_limit) * 100)}%` : '0%'}
                                                </span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-green-600 h-2 rounded-full"
                                                    style={{ 
                                                        width: usageStats.users_limit ? 
                                                            `${Math.min((usageStats.users_used / usageStats.users_limit) * 100, 100)}%` : 
                                                            '10%' 
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Storage</h3>
                                        <div className="mt-2">
                                            <div className="flex items-center justify-between mb-1">
                                                <span className="text-lg font-semibold text-gray-900">
                                                    {usageStats.storage_used || 0}GB/{usageStats.storage_limit || 'Unlimited'}GB
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    {usageStats.storage_limit ? `${Math.round((usageStats.storage_used / usageStats.storage_limit) * 100)}%` : '0%'}
                                                </span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2">
                                                <div
                                                    className="bg-purple-600 h-2 rounded-full"
                                                    style={{ 
                                                        width: usageStats.storage_limit ? 
                                                            `${Math.min((usageStats.storage_used / usageStats.storage_limit) * 100, 100)}%` : 
                                                            '10%' 
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Billing History */}
                        {currentSubscription && (
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h2 className="text-xl font-semibold text-gray-900 mb-6">Billing Information</h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Payment Method</h3>
                                        <p className="mt-1 text-gray-900">
                                            {currentSubscription.payment_method || 'Not set'}
                                        </p>
                                    </div>
                                    <div>
                                        <h3 className="text-sm font-medium text-gray-500">Next Billing Date</h3>
                                        <p className="mt-1 text-gray-900">
                                            {formatDate(currentSubscription.next_billing_date)}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Plans Modal */}
                    {showPlansModal && (
                        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg font-bold text-gray-900">Choose Your Plan</h3>
                                    <button
                                        onClick={() => setShowPlansModal(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                <div className="mt-4">
                                    <p className="text-gray-600 mb-6">Select the perfect plan for your business needs</p>
                                    {/* This would include the Subscriptions component for plan selection */}
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">Plan selection component will be loaded here</p>
                                        <button
                                            onClick={() => setShowPlansModal(false)}
                                            className="mt-4 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                                        >
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('UserSubscriptionDashboard error:', error);
        return (
            <div className="text-center py-8">
                <p className="text-red-600">Error loading subscription dashboard</p>
            </div>
        );
    }
}
