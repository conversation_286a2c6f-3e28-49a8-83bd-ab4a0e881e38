<?php
require_once 'api/db-config.php';

echo "=== Fixing Database Collations ===\n\n";

// Fix collation issues by converting all tables to utf8mb4_unicode_ci
$tables = ['pricing_plans', 'subscriptions', 'companies', 'users', 'usage_tracking', 'payment_transactions'];

foreach ($tables as $table) {
    echo "Fixing collation for $table...\n";
    
    // Convert table to utf8mb4_unicode_ci
    $sql = "ALTER TABLE $table CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    
    if ($conn->query($sql)) {
        echo "   ✓ $table collation updated\n";
    } else {
        echo "   ⚠ $table collation update failed: " . $conn->error . "\n";
    }
}

// Test the join query that was failing
echo "\nTesting subscription join query...\n";
$testResult = $conn->query("
    SELECT s.status, c.name as company_name, pp.name as plan_name
    FROM subscriptions s
    JOIN companies c ON s.company_id = c.object_id
    JOIN pricing_plans pp ON s.plan_id = pp.id
    LIMIT 1
");

if ($testResult) {
    echo "   ✓ Join query working correctly\n";
    if ($testResult->num_rows > 0) {
        $row = $testResult->fetch_assoc();
        echo "   Sample: {$row['company_name']} -> {$row['plan_name']} ({$row['status']})\n";
    }
} else {
    echo "   ✗ Join query still failing: " . $conn->error . "\n";
}

echo "\n=== Collation Fix Complete ===\n";

$conn->close();
?>