<?php
/**
 * Business Types API
 * Handles fetching business types for registration
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Fetch business types from database
        $sql = "SELECT id, name, description, icon, default_modules, default_categories, default_features, default_templates, sort_order 
                FROM business_types 
                WHERE is_active = TRUE 
                ORDER BY sort_order ASC, name ASC";
        
        $result = $conn->query($sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . $conn->error);
        }
        
        $businessTypes = [];
        while ($row = $result->fetch_assoc()) {
            // Parse JSON fields
            $row['default_modules'] = json_decode($row['default_modules'] ?? '[]', true);
            $row['default_categories'] = json_decode($row['default_categories'] ?? '[]', true);
            $row['default_features'] = json_decode($row['default_features'] ?? '[]', true);
            $row['default_templates'] = json_decode($row['default_templates'] ?? '[]', true);
            
            // Add features for frontend display
            $row['features'] = array_slice($row['default_features'], 0, 4); // Show first 4 features
            
            // Add color based on business type for UI
            $colors = [
                'jewellery' => 'purple',
                'retail' => 'blue',
                'education' => 'green',
                'healthcare' => 'red',
                'consulting' => 'indigo',
                'manufacturing' => 'gray'
            ];
            $row['color'] = $colors[$row['id']] ?? 'blue';
            
            $businessTypes[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $businessTypes,
            'count' => count($businessTypes)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Business Types API Error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch business types: ' . $e->getMessage()
    ]);
}
?>
