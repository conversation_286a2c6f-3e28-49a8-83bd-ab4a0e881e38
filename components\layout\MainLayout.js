// Make MainLayout component globally available
window.MainLayout = function MainLayout({ children, user, onLogout, currentPage, setCurrentPage }) {
    try {
        // State management
        const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
        const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
        const [isDesktop, setIsDesktop] = React.useState(false);



        // Handle sidebar toggle from header or sidebar
        React.useEffect(() => {
            const handleSidebarToggle = () => {
                console.log('handleSidebarToggle called');
                setSidebarCollapsed(prev => {
                    console.log('Sidebar toggle: changing from', prev, 'to', !prev);
                    return !prev;
                });
            };

            const handleSidebarToggleRequest = () => {
                console.log('handleSidebarToggleRequest called');
                setSidebarCollapsed(prev => {
                    console.log('Sidebar toggle request: changing from', prev, 'to', !prev);
                    return !prev;
                });
            };

            // Listen for sidebar toggle events
            window.addEventListener('sidebar-toggle', handleSidebarToggle);
            window.addEventListener('sidebar-toggle-request', handleSidebarToggleRequest);

            return () => {
                window.removeEventListener('sidebar-toggle', handleSidebarToggle);
                window.removeEventListener('sidebar-toggle-request', handleSidebarToggleRequest);
            };
        }, []);

        // Handle responsive behavior and keyboard shortcuts
        React.useEffect(() => {
            const handleResize = () => {
                const isDesktopSize = window.innerWidth >= 768;
                setIsDesktop(isDesktopSize);

                // Auto-close mobile menu on larger screens
                if (isDesktopSize) {
                    setIsMobileMenuOpen(false);
                }
                
                // Smart responsive sidebar behavior
                if (window.innerWidth < 1024) {
                    setSidebarCollapsed(true);
                } else if (window.innerWidth >= 1280) {
                    setSidebarCollapsed(false);
                }
            };

            // Keyboard shortcuts
            const handleKeyDown = (e) => {
                // Ctrl+B or Cmd+B to toggle sidebar
                if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                    e.preventDefault();
                    if (window.innerWidth >= 768) {
                        setSidebarCollapsed(prev => !prev);
                    }
                }
                // Escape to close mobile menu
                if (e.key === 'Escape' && isMobileMenuOpen) {
                    setIsMobileMenuOpen(false);
                }
            };

            // Initial check
            handleResize();

            // Event listeners
            window.addEventListener('resize', handleResize);
            document.addEventListener('keydown', handleKeyDown);

            // Set up a debounced resize handler to prevent performance issues
            let resizeTimer;
            const debouncedResize = () => {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(handleResize, 100);
            };
            
            window.addEventListener('resize', debouncedResize);

            return () => {
                window.removeEventListener('resize', handleResize);
                window.removeEventListener('resize', debouncedResize);
                document.removeEventListener('keydown', handleKeyDown);
                clearTimeout(resizeTimer);
            };
        }, [isMobileMenuOpen]);
        
        // Toggle mobile menu
        const toggleMobileMenu = () => {
            setIsMobileMenuOpen(!isMobileMenuOpen);
        };
        
        return (
            <div data-name="main-layout" className="flex min-h-screen bg-gray-50">
                {/* Mobile menu overlay */}
                {isMobileMenuOpen && (
                    <div
                        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
                        onClick={() => setIsMobileMenuOpen(false)}
                    ></div>
                )}

                {/* Desktop Sidebar - Always visible on desktop */}
                <div className="hidden md:block">
                    <Sidebar
                        currentPage={currentPage}
                        setCurrentPage={setCurrentPage}
                        user={user}
                        onMobileClose={null}
                        collapsed={sidebarCollapsed}
                        onToggle={() => setSidebarCollapsed(prev => !prev)}
                    />
                </div>

                {/* Mobile Sidebar - Only shown when menu is open on mobile devices */}
                {isMobileMenuOpen && window.innerWidth < 768 && (
                    <div className="md:hidden">
                        <Sidebar
                            currentPage={currentPage}
                            setCurrentPage={setCurrentPage}
                            user={user}
                            onMobileClose={() => setIsMobileMenuOpen(false)}
                            collapsed={false}
                            onToggle={null}
                        />
                    </div>
                )}

                {/* Main content area - responsive to sidebar state */}
                <div
                    data-name="main-content"
                    className="flex-1 flex flex-col min-h-screen transition-all duration-300 ease-in-out"
                    style={{
                        marginLeft: isDesktop ? (sidebarCollapsed ? '80px' : '256px') : '0',
                        '--sidebar-state': sidebarCollapsed ? 'collapsed' : 'expanded'
                    }}
                >
                    <Header 
                        user={user} 
                        onLogout={onLogout}
                        onMobileMenuToggle={toggleMobileMenu}
                        isMobileMenuOpen={isMobileMenuOpen}
                    />
                    <main
                        data-name="page-content"
                        className="flex-1 py-6 px-4 md:px-6 lg:px-8"
                    >
                        {children}
                    </main>

                    {/* Footer */}
                    <footer className="py-4 px-6 text-center text-sm text-gray-500 border-t border-gray-200 bg-white">
                        <p>© {new Date().getFullYear()} Bizma. All rights reserved.</p>
                    </footer>
                </div>
            </div>
        );
    } catch (error) {
        console.error('MainLayout component error:', error);
        reportError(error);
        return null;
    }
}
