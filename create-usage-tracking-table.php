<?php
require_once 'api/db-config.php';

echo "Creating usage tracking table...\n";

$sql = "
CREATE TABLE IF NOT EXISTS usage_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE DEFAULT NULL,
    company_id VARCHAR(50) NOT NULL,
    month_year VARCHAR(7) NOT NULL,
    leads_created INT DEFAULT 0,
    customers_created INT DEFAULT 0,
    invoices_sent INT DEFAULT 0,
    quotations_sent INT DEFAULT 0,
    contracts_created INT DEFAULT 0,
    storage_used BIGINT DEFAULT 0,
    api_calls INT DEFAULT 0,
    email_sent INT DEFAULT 0,
    reports_generated INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_month (company_id, month_year),
    INDEX idx_month_year (month_year),
    FOREIGN KEY (company_id) REFERENCES companies(object_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

if ($conn->query($sql)) {
    echo "✓ Usage tracking table created successfully\n";
    
    // Initialize usage tracking for existing companies
    $initSql = "
        INSERT IGNORE INTO usage_tracking (company_id, month_year, created_at)
        SELECT object_id, DATE_FORMAT(NOW(), '%Y-%m'), NOW()
        FROM companies
    ";
    
    if ($conn->query($initSql)) {
        echo "✓ Usage tracking initialized for existing companies\n";
    } else {
        echo "✗ Failed to initialize usage tracking: " . $conn->error . "\n";
    }
    
} else {
    echo "✗ Failed to create usage tracking table: " . $conn->error . "\n";
}

$conn->close();
?>