<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Login Flow</h1>\n";

// Test 1: Database connection
echo "<h2>1. Testing Database Connection</h2>\n";
try {
    require_once 'api/db-config.php';
    echo "✅ Database connection successful<br>\n";
    echo "Database: " . $conn->get_server_info() . "<br>\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>\n";
    exit;
}

// Test 2: Check if enhanced auth handler file exists
echo "<h2>2. Testing Enhanced Auth Handler File</h2>\n";
$authHandlerPath = __DIR__ . '/api/enhanced-auth-handler.php';
if (file_exists($authHandlerPath)) {
    echo "✅ Enhanced auth handler file exists<br>\n";
} else {
    echo "❌ Enhanced auth handler file not found at: $authHandlerPath<br>\n";
}

// Test 3: Check user
echo "<h2>3. Testing User Lookup</h2>\n";
$email = '<EMAIL>';
$stmt = $conn->prepare("SELECT object_id, name, email, password_hash, status FROM users WHERE email = ? AND status = 'active'");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "✅ User found<br>\n";
    echo "Object ID: " . $user['object_id'] . "<br>\n";
    echo "Name: " . $user['name'] . "<br>\n";
    echo "Status: " . $user['status'] . "<br>\n";
    echo "Has password_hash: " . (!empty($user['password_hash']) ? 'Yes' : 'No') . "<br>\n";
    
    // Test password verification
    $password = 'BhaviGani@56';
    if (!empty($user['password_hash'])) {
        if (password_verify($password, $user['password_hash'])) {
            echo "✅ Password verification successful<br>\n";
        } else {
            echo "❌ Password verification failed<br>\n";
        }
    } else {
        echo "❌ No password hash found<br>\n";
    }
} else {
    echo "❌ User not found or inactive<br>\n";
}

// Test 4: Test API endpoint directly
echo "<h2>4. Testing API Endpoint</h2>\n";

// Simulate the API call
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['action'] = 'login';

// Capture the output
ob_start();

try {
    // Simulate POST data
    $postData = json_encode([
        'email' => '<EMAIL>',
        'password' => 'BhaviGani@56',
        'rememberMe' => false
    ]);
    
    // Mock the php://input
    $tempFile = tmpfile();
    fwrite($tempFile, $postData);
    rewind($tempFile);
    
    echo "Simulating API call...<br>\n";
    echo "POST data: " . htmlspecialchars($postData) . "<br>\n";
    
    // Include the enhanced auth handler
    include 'api/enhanced-auth-handler.php';
    
} catch (Exception $e) {
    echo "❌ API test failed: " . $e->getMessage() . "<br>\n";
}

$output = ob_get_clean();
echo "<h3>API Output:</h3>\n";
echo "<pre>" . htmlspecialchars($output) . "</pre>\n";

// Test 5: Test via curl simulation
echo "<h2>5. Testing via HTTP Request</h2>\n";

$url = 'http://localhost:8000/biz/api/api.php/auth/login';
$data = json_encode([
    'email' => '<EMAIL>',
    'password' => 'BhaviGani@56',
    'rememberMe' => false
]);

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => $data
    ]
];

$context = stream_context_create($options);
$response = file_get_contents($url, false, $context);

if ($response !== false) {
    echo "✅ HTTP request successful<br>\n";
    echo "<h3>Response:</h3>\n";
    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        if ($responseData['success']) {
            echo "✅ Login successful via HTTP<br>\n";
        } else {
            echo "❌ Login failed: " . ($responseData['error'] ?? 'Unknown error') . "<br>\n";
        }
    } else {
        echo "❌ Invalid JSON response<br>\n";
    }
} else {
    echo "❌ HTTP request failed<br>\n";
    echo "Error: " . error_get_last()['message'] . "<br>\n";
}

$conn->close();
?>
