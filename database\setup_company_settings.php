<?php
/**
 * Setup company_settings table for email configuration
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🔧 Setting up company_settings table...\n";

try {
    // Read and execute the migration
    $sql = file_get_contents(__DIR__ . '/migrations/create_company_settings_table.sql');
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ company_settings table created successfully\n";
    } else {
        echo "❌ Error creating company_settings table: " . $conn->error . "\n";
    }
    
    // Check if table exists and show structure
    $result = $conn->query("DESCRIBE company_settings");
    if ($result) {
        echo "\n📋 company_settings table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo "   - {$row['Field']}: {$row['Type']}\n";
        }
    }
    
    echo "\n🎉 Company settings table setup completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

$conn->close();
?>
