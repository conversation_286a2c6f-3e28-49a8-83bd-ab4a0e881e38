function RealEstateItems() {
    try {
        const realEstateItems = [
            {
                name: "Property Listing - Basic",
                description: "Basic property listing with photos, description, and contact details. Includes listing on website and basic marketing.",
                price: 5000,
                category: "Property Services",
                subcategory: "Property Listing",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Property Listing - Premium",
                description: "Premium property listing with professional photography, virtual tour, detailed description, and enhanced marketing across multiple platforms.",
                price: 15000,
                category: "Property Services",
                subcategory: "Property Listing",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Property Management - Monthly",
                description: "Complete property management including tenant screening, rent collection, maintenance coordination, and monthly reporting.",
                price: 8000,
                category: "Property Services",
                subcategory: "Property Management",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Property Valuation",
                description: "Professional property valuation report including market analysis, comparable properties, and detailed assessment.",
                price: 3000,
                category: "Consultation Services",
                subcategory: "Valuation",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Legal Documentation",
                description: "Complete legal documentation for property transactions including agreement drafting, verification, and registration assistance.",
                price: 10000,
                category: "Legal Services",
                subcategory: "Documentation",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Home Loan Assistance",
                description: "Complete home loan processing assistance including documentation, bank liaison, and approval facilitation.",
                price: 25000,
                category: "Financial Services",
                subcategory: "Loan Processing",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Property Insurance",
                description: "Comprehensive property insurance coverage including fire, theft, natural disasters, and liability protection.",
                price: 12000,
                category: "Insurance Services",
                subcategory: "Property Insurance",
                isRecurring: true,
                recurringPeriod: "yearly",
                itemType: "service"
            },
            {
                name: "Interior Design Consultation",
                description: "Professional interior design consultation including space planning, design concepts, and material recommendations.",
                price: 20000,
                category: "Design Services",
                subcategory: "Interior Design",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Property Maintenance - Basic",
                description: "Basic property maintenance including cleaning, minor repairs, and regular upkeep services.",
                price: 5000,
                category: "Maintenance Services",
                subcategory: "Basic Maintenance",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Property Maintenance - Comprehensive",
                description: "Comprehensive property maintenance including all repairs, renovations, landscaping, and emergency services.",
                price: 15000,
                category: "Maintenance Services",
                subcategory: "Comprehensive Maintenance",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Market Research Report",
                description: "Detailed market research report including price trends, demand analysis, and investment opportunities in specific areas.",
                price: 8000,
                category: "Research Services",
                subcategory: "Market Analysis",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Property Investment Consultation",
                description: "Expert consultation on property investment opportunities including ROI analysis, risk assessment, and portfolio recommendations.",
                price: 15000,
                category: "Consultation Services",
                subcategory: "Investment Advisory",
                isRecurring: false,
                itemType: "service"
            }
        ];

        const realEstateCategories = [
            {
                name: "Property Services",
                description: "Core property-related services",
                subcategories: ["Property Listing", "Property Management", "Property Sales"]
            },
            {
                name: "Legal Services",
                description: "Legal and documentation services",
                subcategories: ["Documentation", "Registration", "Compliance"]
            },
            {
                name: "Financial Services",
                description: "Financial and loan services",
                subcategories: ["Loan Processing", "Investment Planning", "Tax Advisory"]
            },
            {
                name: "Insurance Services",
                description: "Insurance and protection services",
                subcategories: ["Property Insurance", "Liability Insurance", "Title Insurance"]
            },
            {
                name: "Design Services",
                description: "Design and architecture services",
                subcategories: ["Interior Design", "Architecture", "Landscaping"]
            },
            {
                name: "Maintenance Services",
                description: "Property maintenance and upkeep",
                subcategories: ["Basic Maintenance", "Comprehensive Maintenance", "Emergency Services"]
            },
            {
                name: "Consultation Services",
                description: "Expert consultation and advisory",
                subcategories: ["Valuation", "Investment Advisory", "Market Analysis"]
            },
            {
                name: "Research Services",
                description: "Market research and analysis",
                subcategories: ["Market Analysis", "Feasibility Studies", "Due Diligence"]
            }
        ];

        const handleImportItems = async () => {
            try {
                // Import categories first
                for (const category of realEstateCategories) {
                    await trickleCreateObject('category', {
                        name: category.name,
                        description: category.description,
                        createdAt: new Date().toISOString()
                    });

                    // Import subcategories
                    for (const subcategoryName of category.subcategories) {
                        await trickleCreateObject('subcategory', {
                            name: subcategoryName,
                            parentCategory: category.name,
                            createdAt: new Date().toISOString()
                        });
                    }
                }

                // Import items
                for (const item of realEstateItems) {
                    await trickleCreateObject('item', {
                        ...item,
                        createdAt: new Date().toISOString()
                    });
                }

                alert('Real Estate items and categories imported successfully!');
                window.location.reload();
            } catch (error) {
                console.error('Error importing real estate items:', error);
                alert('Error importing items. Please try again.');
            }
        };

        return (
            <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h2 className="text-xl font-semibold text-gray-900">Real Estate Business Template</h2>
                        <p className="text-gray-600 mt-1">Pre-configured items and categories for real estate businesses</p>
                    </div>
                    <button
                        onClick={handleImportItems}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Import Template
                    </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Categories ({realEstateCategories.length})</h3>
                        <div className="space-y-3">
                            {realEstateCategories.map((category, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg p-4">
                                    <h4 className="font-medium text-gray-900">{category.name}</h4>
                                    <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                                    <div className="mt-2">
                                        <span className="text-xs text-gray-500">Subcategories: </span>
                                        <span className="text-xs text-blue-600">{category.subcategories.join(', ')}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Sample Items ({realEstateItems.length})</h3>
                        <div className="space-y-3 max-h-96 overflow-y-auto">
                            {realEstateItems.map((item, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg p-4">
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                            <h4 className="font-medium text-gray-900">{item.name}</h4>
                                            <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                                            <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                                                <span>₹{item.price.toLocaleString()}</span>
                                                <span>{item.category} → {item.subcategory}</span>
                                                {item.isRecurring && (
                                                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                        {item.recurringPeriod}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-start">
                        <i className="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div>
                            <h4 className="text-sm font-medium text-blue-900">Template Information</h4>
                            <p className="text-sm text-blue-700 mt-1">
                                This template includes {realEstateCategories.length} categories and {realEstateItems.length} pre-configured items 
                                specifically designed for real estate businesses. You can customize these items after importing or add your own.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('RealEstateItems component error:', error);
        reportError(error);
        return null;
    }
}

// Make component globally available
window.RealEstateItems = RealEstateItems;