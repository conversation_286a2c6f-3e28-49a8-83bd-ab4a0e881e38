<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Bizma</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone@7/babel.min.js"></script>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function SimpleHeader() {
            return (
                <header className="bg-white shadow-sm">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center py-4">
                            <div className="flex items-center">
                                <i className="fas fa-chart-line text-blue-600 text-2xl mr-3"></i>
                                <h1 className="text-2xl font-bold text-gray-900">Bizma</h1>
                            </div>
                            <nav className="hidden md:flex space-x-8">
                                <a href="#features" className="text-gray-600 hover:text-blue-600">Features</a>
                                <a href="#pricing" className="text-gray-600 hover:text-blue-600">Pricing</a>
                                <a href="login.html" className="text-gray-600 hover:text-blue-600">Sign In</a>
                                <a href="register.html" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    Get Started
                                </a>
                            </nav>
                        </div>
                    </div>
                </header>
            );
        }

        function SimpleHero() {
            return (
                <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <h1 className="text-4xl md:text-6xl font-bold mb-6">
                            Grow Your Business with Smart Management
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 text-blue-100">
                            The all-in-one platform for customer management, invoicing, and business growth
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="register.html" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-200">
                                <i className="fas fa-rocket mr-2"></i>
                                Start Your Free Trial
                            </a>
                            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition duration-200">
                                Watch Demo
                            </button>
                        </div>
                        <p className="mt-4 text-blue-200">
                            Join thousands of businesses already using Bizma
                        </p>
                    </div>
                </section>
            );
        }

        function SimpleFeatures() {
            const features = [
                {
                    icon: 'fas fa-users',
                    title: 'Customer Management',
                    description: 'Organize and manage your customer relationships with powerful CRM tools.'
                },
                {
                    icon: 'fas fa-file-invoice-dollar',
                    title: 'Smart Invoicing',
                    description: 'Create professional invoices and quotations with automated calculations.'
                },
                {
                    icon: 'fas fa-chart-line',
                    title: 'Business Analytics',
                    description: 'Get insights into your business performance with detailed reports.'
                },
                {
                    icon: 'fas fa-mobile-alt',
                    title: 'Mobile Ready',
                    description: 'Access your business data anywhere, anytime with our responsive design.'
                }
            ];

            return (
                <section id="features" className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Why choose Bizma?
                            </h2>
                            <p className="text-xl text-gray-600">
                                Built by entrepreneurs, for entrepreneurs. We understand the challenges of running a business.
                            </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {features.map((feature, index) => (
                                <div key={index} className="text-center p-6 rounded-lg hover:shadow-lg transition duration-200">
                                    <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i className={`${feature.icon} text-blue-600 text-2xl`}></i>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                                    <p className="text-gray-600">{feature.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>
            );
        }

        function SimplePricing() {
            return (
                <section id="pricing" className="py-20 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Simple, Transparent Pricing
                            </h2>
                            <p className="text-xl text-gray-600">
                                Start with a 14-day free trial. No credit card required.
                            </p>
                        </div>
                        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
                            <div className="bg-blue-600 text-white text-center py-4">
                                <h3 className="text-2xl font-bold">Business Plan</h3>
                                <p className="text-blue-100">Everything you need to run your business</p>
                            </div>
                            <div className="p-8">
                                <div className="text-center mb-6">
                                    <span className="text-4xl font-bold text-gray-900">₹500</span>
                                    <span className="text-gray-600">/month</span>
                                </div>
                                <ul className="space-y-3 mb-8">
                                    <li className="flex items-center">
                                        <i className="fas fa-check text-green-500 mr-3"></i>
                                        Customer Management
                                    </li>
                                    <li className="flex items-center">
                                        <i className="fas fa-check text-green-500 mr-3"></i>
                                        Invoice Generation
                                    </li>
                                    <li className="flex items-center">
                                        <i className="fas fa-check text-green-500 mr-3"></i>
                                        Business Analytics
                                    </li>
                                    <li className="flex items-center">
                                        <i className="fas fa-check text-green-500 mr-3"></i>
                                        24/7 Support
                                    </li>
                                </ul>
                                <a href="register.html" className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition duration-200 block text-center">
                                    Start Free Trial
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
            );
        }

        function SimpleFooter() {
            return (
                <footer className="bg-gray-900 text-white py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="flex items-center justify-center mb-4">
                                <i className="fas fa-chart-line text-blue-400 text-2xl mr-3"></i>
                                <h3 className="text-2xl font-bold">Bizma</h3>
                            </div>
                            <p className="text-gray-400 mb-6">
                                The all-in-one business management platform
                            </p>
                            <div className="flex justify-center space-x-6">
                                <a href="privacy.html" className="text-gray-400 hover:text-white">Privacy</a>
                                <a href="terms.html" className="text-gray-400 hover:text-white">Terms</a>
                                <a href="contact.html" className="text-gray-400 hover:text-white">Contact</a>
                            </div>
                            <p className="text-gray-500 mt-6">
                                © 2024 Bizma. All rights reserved.
                            </p>
                        </div>
                    </div>
                </footer>
            );
        }

        function App() {
            return (
                <div className="min-h-screen">
                    <SimpleHeader />
                    <SimpleHero />
                    <SimpleFeatures />
                    <SimplePricing />
                    <SimpleFooter />
                </div>
            );
        }

        // Render the app
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
