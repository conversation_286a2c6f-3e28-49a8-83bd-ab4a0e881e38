<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subscription API Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .url-display { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Subscription API Fix - localhost/biz</h1>
        
        <div class="test-section info">
            <h3>Environment Check</h3>
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Expected Base:</strong> http://localhost/biz</p>
            <p><strong>Auth Token:</strong> <span id="auth-status"></span></p>
        </div>
        
        <div class="test-section">
            <h2>URL Generation Test</h2>
            <div id="url-test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>API Tests</h2>
            <button onclick="testSubscriptionEndpoint()">Test Subscription API</button>
            <button onclick="testTrickleFunction()">Test trickleListObjects</button>
            <div id="api-results"></div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="/biz/config.js"></script>
    <script src="/biz/utils/api-utils.js"></script>

    <script>
        // Initialize page
        window.addEventListener('load', function() {
            // Show current environment
            document.getElementById('current-url').textContent = window.location.href;
            
            const token = localStorage.getItem('authToken');
            document.getElementById('auth-status').textContent = token ? 
                `Found (${token.substring(0, 20)}...)` : 'Not found - Please login first';
            
            // Test URL generation
            testUrlGeneration();
        });
        
        function testUrlGeneration() {
            const testCases = [
                '/subscription',
                '/subscription?limit=10', 
                '/subscription-management/current',
                'subscription'
            ];
            
            let html = '<h3>URL Generation Results:</h3>';
            
            testCases.forEach(endpoint => {
                const generatedUrl = window.getApiUrl(endpoint);
                html += `
                    <div class="url-display">
                        <strong>Input:</strong> "${endpoint}"<br>
                        <strong>Generated:</strong> ${generatedUrl}
                    </div>
                `;
            });
            
            document.getElementById('url-test-results').innerHTML = html;
        }
        
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return fetch(url, {
                ...options,
                headers
            });
        }
        
        async function testSubscriptionEndpoint() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>Testing subscription endpoint...</p>';
            
            try {
                // Test the new URL generation
                const url = window.getApiUrl('/subscription?limit=10');
                console.log('Testing subscription URL:', url);
                
                const response = await makeAuthenticatedRequest(url);
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = { raw: text };
                }
                
                resultsDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultsDiv.innerHTML = `
                    <h3>Subscription Endpoint Test</h3>
                    <div class="url-display"><strong>URL:</strong> ${url}</div>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <h4>Response:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    ${response.ok ? '<p><strong>✅ API Call Successful!</strong></p>' : '<p><strong>❌ API Call Failed</strong></p>'}
                `;
            } catch (error) {
                resultsDiv.className = 'test-section error';
                resultsDiv.innerHTML = `
                    <h3>Subscription Endpoint Test</h3>
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        async function testTrickleFunction() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>Testing trickleListObjects function...</p>';
            
            try {
                console.log('Calling trickleListObjects("subscription", 10)...');
                const data = await trickleListObjects('subscription', 10);
                console.log('trickleListObjects result:', data);
                
                resultsDiv.className = 'test-section success';
                resultsDiv.innerHTML = `
                    <h3>trickleListObjects Test</h3>
                    <p><strong>Function Call:</strong> trickleListObjects('subscription', 10)</p>
                    <h4>Result:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    <p><strong>✅ Function Call Successful!</strong></p>
                `;
            } catch (error) {
                console.error('trickleListObjects error:', error);
                resultsDiv.className = 'test-section error';
                resultsDiv.innerHTML = `
                    <h3>trickleListObjects Test</h3>
                    <p><strong>Function Call:</strong> trickleListObjects('subscription', 10)</p>
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                    <p><strong>❌ Function Call Failed</strong></p>
                `;
            }
        }
    </script>
</body>
</html>
