<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Maintenance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: #f8f8f8;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        p {
            color: #666;
            margin-bottom: 30px;
        }
        .maintenance-icon {
            font-size: 80px;
            color: #3b82f6;
            margin-bottom: 20px;
        }
        .estimated-time {
            background: #f0f7ff;
            padding: 15px;
            border-radius: 5px;
            margin: 30px 0;
            font-weight: bold;
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="maintenance-icon">🛠️</div>
        <h1>We're Currently Under Maintenance</h1>
        <p>Sorry for the inconvenience. We're performing scheduled maintenance to improve your experience.</p>
        
        <div class="estimated-time">
            Estimated completion time: <span id="completion-time">Soon</span>
        </div>
        
        <p>Thank you for your patience.</p>
    </div>
    
    <script>
        // You can set the estimated completion time dynamically
        // document.getElementById('completion-time').textContent = 'July 5, 2023 at 12:00 PM UTC';
    </script>
</body>
</html>