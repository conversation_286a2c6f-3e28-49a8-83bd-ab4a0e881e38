{"name": "date-fns", "version": "3.6.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "description": "Modern JavaScript date utility library", "repository": "https://github.com/date-fns/date-fns", "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}, "sideEffects": false, "browser": "./index", "main": "./index.js", "module": "./index.mjs", "jsdelivr": "./cdn.min.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./index.d.mts", "default": "./index.mjs"}}, "./constants": {"require": {"types": "./constants.d.ts", "default": "./constants.js"}, "import": {"types": "./constants.d.mts", "default": "./constants.mjs"}}, "./locale": {"require": {"types": "./locale.d.ts", "default": "./locale.js"}, "import": {"types": "./locale.d.mts", "default": "./locale.mjs"}}, "./fp": {"require": {"types": "./fp.d.ts", "default": "./fp.js"}, "import": {"types": "./fp.d.mts", "default": "./fp.mjs"}}, "./add": {"require": {"types": "./add.d.ts", "default": "./add.js"}, "import": {"types": "./add.d.mts", "default": "./add.mjs"}}, "./addBusinessDays": {"require": {"types": "./addBusinessDays.d.ts", "default": "./addBusinessDays.js"}, "import": {"types": "./addBusinessDays.d.mts", "default": "./addBusinessDays.mjs"}}, "./addDays": {"require": {"types": "./addDays.d.ts", "default": "./addDays.js"}, "import": {"types": "./addDays.d.mts", "default": "./addDays.mjs"}}, "./addHours": {"require": {"types": "./addHours.d.ts", "default": "./addHours.js"}, "import": {"types": "./addHours.d.mts", "default": "./addHours.mjs"}}, "./addISOWeekYears": {"require": {"types": "./addISOWeekYears.d.ts", "default": "./addISOWeekYears.js"}, "import": {"types": "./addISOWeekYears.d.mts", "default": "./addISOWeekYears.mjs"}}, "./addMilliseconds": {"require": {"types": "./addMilliseconds.d.ts", "default": "./addMilliseconds.js"}, "import": {"types": "./addMilliseconds.d.mts", "default": "./addMilliseconds.mjs"}}, "./addMinutes": {"require": {"types": "./addMinutes.d.ts", "default": "./addMinutes.js"}, "import": {"types": "./addMinutes.d.mts", "default": "./addMinutes.mjs"}}, "./addMonths": {"require": {"types": "./addMonths.d.ts", "default": "./addMonths.js"}, "import": {"types": "./addMonths.d.mts", "default": "./addMonths.mjs"}}, "./addQuarters": {"require": {"types": "./addQuarters.d.ts", "default": "./addQuarters.js"}, "import": {"types": "./addQuarters.d.mts", "default": "./addQuarters.mjs"}}, "./addSeconds": {"require": {"types": "./addSeconds.d.ts", "default": "./addSeconds.js"}, "import": {"types": "./addSeconds.d.mts", "default": "./addSeconds.mjs"}}, "./addWeeks": {"require": {"types": "./addWeeks.d.ts", "default": "./addWeeks.js"}, "import": {"types": "./addWeeks.d.mts", "default": "./addWeeks.mjs"}}, "./addYears": {"require": {"types": "./addYears.d.ts", "default": "./addYears.js"}, "import": {"types": "./addYears.d.mts", "default": "./addYears.mjs"}}, "./areIntervalsOverlapping": {"require": {"types": "./areIntervalsOverlapping.d.ts", "default": "./areIntervalsOverlapping.js"}, "import": {"types": "./areIntervalsOverlapping.d.mts", "default": "./areIntervalsOverlapping.mjs"}}, "./clamp": {"require": {"types": "./clamp.d.ts", "default": "./clamp.js"}, "import": {"types": "./clamp.d.mts", "default": "./clamp.mjs"}}, "./closestIndexTo": {"require": {"types": "./closestIndexTo.d.ts", "default": "./closestIndexTo.js"}, "import": {"types": "./closestIndexTo.d.mts", "default": "./closestIndexTo.mjs"}}, "./closestTo": {"require": {"types": "./closestTo.d.ts", "default": "./closestTo.js"}, "import": {"types": "./closestTo.d.mts", "default": "./closestTo.mjs"}}, "./compareAsc": {"require": {"types": "./compareAsc.d.ts", "default": "./compareAsc.js"}, "import": {"types": "./compareAsc.d.mts", "default": "./compareAsc.mjs"}}, "./compareDesc": {"require": {"types": "./compareDesc.d.ts", "default": "./compareDesc.js"}, "import": {"types": "./compareDesc.d.mts", "default": "./compareDesc.mjs"}}, "./constructFrom": {"require": {"types": "./constructFrom.d.ts", "default": "./constructFrom.js"}, "import": {"types": "./constructFrom.d.mts", "default": "./constructFrom.mjs"}}, "./constructNow": {"require": {"types": "./constructNow.d.ts", "default": "./constructNow.js"}, "import": {"types": "./constructNow.d.mts", "default": "./constructNow.mjs"}}, "./daysToWeeks": {"require": {"types": "./daysToWeeks.d.ts", "default": "./daysToWeeks.js"}, "import": {"types": "./daysToWeeks.d.mts", "default": "./daysToWeeks.mjs"}}, "./differenceInBusinessDays": {"require": {"types": "./differenceInBusinessDays.d.ts", "default": "./differenceInBusinessDays.js"}, "import": {"types": "./differenceInBusinessDays.d.mts", "default": "./differenceInBusinessDays.mjs"}}, "./differenceInCalendarDays": {"require": {"types": "./differenceInCalendarDays.d.ts", "default": "./differenceInCalendarDays.js"}, "import": {"types": "./differenceInCalendarDays.d.mts", "default": "./differenceInCalendarDays.mjs"}}, "./differenceInCalendarISOWeekYears": {"require": {"types": "./differenceInCalendarISOWeekYears.d.ts", "default": "./differenceInCalendarISOWeekYears.js"}, "import": {"types": "./differenceInCalendarISOWeekYears.d.mts", "default": "./differenceInCalendarISOWeekYears.mjs"}}, "./differenceInCalendarISOWeeks": {"require": {"types": "./differenceInCalendarISOWeeks.d.ts", "default": "./differenceInCalendarISOWeeks.js"}, "import": {"types": "./differenceInCalendarISOWeeks.d.mts", "default": "./differenceInCalendarISOWeeks.mjs"}}, "./differenceInCalendarMonths": {"require": {"types": "./differenceInCalendarMonths.d.ts", "default": "./differenceInCalendarMonths.js"}, "import": {"types": "./differenceInCalendarMonths.d.mts", "default": "./differenceInCalendarMonths.mjs"}}, "./differenceInCalendarQuarters": {"require": {"types": "./differenceInCalendarQuarters.d.ts", "default": "./differenceInCalendarQuarters.js"}, "import": {"types": "./differenceInCalendarQuarters.d.mts", "default": "./differenceInCalendarQuarters.mjs"}}, "./differenceInCalendarWeeks": {"require": {"types": "./differenceInCalendarWeeks.d.ts", "default": "./differenceInCalendarWeeks.js"}, "import": {"types": "./differenceInCalendarWeeks.d.mts", "default": "./differenceInCalendarWeeks.mjs"}}, "./differenceInCalendarYears": {"require": {"types": "./differenceInCalendarYears.d.ts", "default": "./differenceInCalendarYears.js"}, "import": {"types": "./differenceInCalendarYears.d.mts", "default": "./differenceInCalendarYears.mjs"}}, "./differenceInDays": {"require": {"types": "./differenceInDays.d.ts", "default": "./differenceInDays.js"}, "import": {"types": "./differenceInDays.d.mts", "default": "./differenceInDays.mjs"}}, "./differenceInHours": {"require": {"types": "./differenceInHours.d.ts", "default": "./differenceInHours.js"}, "import": {"types": "./differenceInHours.d.mts", "default": "./differenceInHours.mjs"}}, "./differenceInISOWeekYears": {"require": {"types": "./differenceInISOWeekYears.d.ts", "default": "./differenceInISOWeekYears.js"}, "import": {"types": "./differenceInISOWeekYears.d.mts", "default": "./differenceInISOWeekYears.mjs"}}, "./differenceInMilliseconds": {"require": {"types": "./differenceInMilliseconds.d.ts", "default": "./differenceInMilliseconds.js"}, "import": {"types": "./differenceInMilliseconds.d.mts", "default": "./differenceInMilliseconds.mjs"}}, "./differenceInMinutes": {"require": {"types": "./differenceInMinutes.d.ts", "default": "./differenceInMinutes.js"}, "import": {"types": "./differenceInMinutes.d.mts", "default": "./differenceInMinutes.mjs"}}, "./differenceInMonths": {"require": {"types": "./differenceInMonths.d.ts", "default": "./differenceInMonths.js"}, "import": {"types": "./differenceInMonths.d.mts", "default": "./differenceInMonths.mjs"}}, "./differenceInQuarters": {"require": {"types": "./differenceInQuarters.d.ts", "default": "./differenceInQuarters.js"}, "import": {"types": "./differenceInQuarters.d.mts", "default": "./differenceInQuarters.mjs"}}, "./differenceInSeconds": {"require": {"types": "./differenceInSeconds.d.ts", "default": "./differenceInSeconds.js"}, "import": {"types": "./differenceInSeconds.d.mts", "default": "./differenceInSeconds.mjs"}}, "./differenceInWeeks": {"require": {"types": "./differenceInWeeks.d.ts", "default": "./differenceInWeeks.js"}, "import": {"types": "./differenceInWeeks.d.mts", "default": "./differenceInWeeks.mjs"}}, "./differenceInYears": {"require": {"types": "./differenceInYears.d.ts", "default": "./differenceInYears.js"}, "import": {"types": "./differenceInYears.d.mts", "default": "./differenceInYears.mjs"}}, "./eachDayOfInterval": {"require": {"types": "./eachDayOfInterval.d.ts", "default": "./eachDayOfInterval.js"}, "import": {"types": "./eachDayOfInterval.d.mts", "default": "./eachDayOfInterval.mjs"}}, "./eachHourOfInterval": {"require": {"types": "./eachHourOfInterval.d.ts", "default": "./eachHourOfInterval.js"}, "import": {"types": "./eachHourOfInterval.d.mts", "default": "./eachHourOfInterval.mjs"}}, "./eachMinuteOfInterval": {"require": {"types": "./eachMinuteOfInterval.d.ts", "default": "./eachMinuteOfInterval.js"}, "import": {"types": "./eachMinuteOfInterval.d.mts", "default": "./eachMinuteOfInterval.mjs"}}, "./eachMonthOfInterval": {"require": {"types": "./eachMonthOfInterval.d.ts", "default": "./eachMonthOfInterval.js"}, "import": {"types": "./eachMonthOfInterval.d.mts", "default": "./eachMonthOfInterval.mjs"}}, "./eachQuarterOfInterval": {"require": {"types": "./eachQuarterOfInterval.d.ts", "default": "./eachQuarterOfInterval.js"}, "import": {"types": "./eachQuarterOfInterval.d.mts", "default": "./eachQuarterOfInterval.mjs"}}, "./eachWeekOfInterval": {"require": {"types": "./eachWeekOfInterval.d.ts", "default": "./eachWeekOfInterval.js"}, "import": {"types": "./eachWeekOfInterval.d.mts", "default": "./eachWeekOfInterval.mjs"}}, "./eachWeekendOfInterval": {"require": {"types": "./eachWeekendOfInterval.d.ts", "default": "./eachWeekendOfInterval.js"}, "import": {"types": "./eachWeekendOfInterval.d.mts", "default": "./eachWeekendOfInterval.mjs"}}, "./eachWeekendOfMonth": {"require": {"types": "./eachWeekendOfMonth.d.ts", "default": "./eachWeekendOfMonth.js"}, "import": {"types": "./eachWeekendOfMonth.d.mts", "default": "./eachWeekendOfMonth.mjs"}}, "./eachWeekendOfYear": {"require": {"types": "./eachWeekendOfYear.d.ts", "default": "./eachWeekendOfYear.js"}, "import": {"types": "./eachWeekendOfYear.d.mts", "default": "./eachWeekendOfYear.mjs"}}, "./eachYearOfInterval": {"require": {"types": "./eachYearOfInterval.d.ts", "default": "./eachYearOfInterval.js"}, "import": {"types": "./eachYearOfInterval.d.mts", "default": "./eachYearOfInterval.mjs"}}, "./endOfDay": {"require": {"types": "./endOfDay.d.ts", "default": "./endOfDay.js"}, "import": {"types": "./endOfDay.d.mts", "default": "./endOfDay.mjs"}}, "./endOfDecade": {"require": {"types": "./endOfDecade.d.ts", "default": "./endOfDecade.js"}, "import": {"types": "./endOfDecade.d.mts", "default": "./endOfDecade.mjs"}}, "./endOfHour": {"require": {"types": "./endOfHour.d.ts", "default": "./endOfHour.js"}, "import": {"types": "./endOfHour.d.mts", "default": "./endOfHour.mjs"}}, "./endOfISOWeek": {"require": {"types": "./endOfISOWeek.d.ts", "default": "./endOfISOWeek.js"}, "import": {"types": "./endOfISOWeek.d.mts", "default": "./endOfISOWeek.mjs"}}, "./endOfISOWeekYear": {"require": {"types": "./endOfISOWeekYear.d.ts", "default": "./endOfISOWeekYear.js"}, "import": {"types": "./endOfISOWeekYear.d.mts", "default": "./endOfISOWeekYear.mjs"}}, "./endOfMinute": {"require": {"types": "./endOfMinute.d.ts", "default": "./endOfMinute.js"}, "import": {"types": "./endOfMinute.d.mts", "default": "./endOfMinute.mjs"}}, "./endOfMonth": {"require": {"types": "./endOfMonth.d.ts", "default": "./endOfMonth.js"}, "import": {"types": "./endOfMonth.d.mts", "default": "./endOfMonth.mjs"}}, "./endOfQuarter": {"require": {"types": "./endOfQuarter.d.ts", "default": "./endOfQuarter.js"}, "import": {"types": "./endOfQuarter.d.mts", "default": "./endOfQuarter.mjs"}}, "./endOfSecond": {"require": {"types": "./endOfSecond.d.ts", "default": "./endOfSecond.js"}, "import": {"types": "./endOfSecond.d.mts", "default": "./endOfSecond.mjs"}}, "./endOfToday": {"require": {"types": "./endOfToday.d.ts", "default": "./endOfToday.js"}, "import": {"types": "./endOfToday.d.mts", "default": "./endOfToday.mjs"}}, "./endOfTomorrow": {"require": {"types": "./endOfTomorrow.d.ts", "default": "./endOfTomorrow.js"}, "import": {"types": "./endOfTomorrow.d.mts", "default": "./endOfTomorrow.mjs"}}, "./endOfWeek": {"require": {"types": "./endOfWeek.d.ts", "default": "./endOfWeek.js"}, "import": {"types": "./endOfWeek.d.mts", "default": "./endOfWeek.mjs"}}, "./endOfYear": {"require": {"types": "./endOfYear.d.ts", "default": "./endOfYear.js"}, "import": {"types": "./endOfYear.d.mts", "default": "./endOfYear.mjs"}}, "./endOfYesterday": {"require": {"types": "./endOfYesterday.d.ts", "default": "./endOfYesterday.js"}, "import": {"types": "./endOfYesterday.d.mts", "default": "./endOfYesterday.mjs"}}, "./format": {"require": {"types": "./format.d.ts", "default": "./format.js"}, "import": {"types": "./format.d.mts", "default": "./format.mjs"}}, "./formatDistance": {"require": {"types": "./formatDistance.d.ts", "default": "./formatDistance.js"}, "import": {"types": "./formatDistance.d.mts", "default": "./formatDistance.mjs"}}, "./formatDistanceStrict": {"require": {"types": "./formatDistanceStrict.d.ts", "default": "./formatDistanceStrict.js"}, "import": {"types": "./formatDistanceStrict.d.mts", "default": "./formatDistanceStrict.mjs"}}, "./formatDistanceToNow": {"require": {"types": "./formatDistanceToNow.d.ts", "default": "./formatDistanceToNow.js"}, "import": {"types": "./formatDistanceToNow.d.mts", "default": "./formatDistanceToNow.mjs"}}, "./formatDistanceToNowStrict": {"require": {"types": "./formatDistanceToNowStrict.d.ts", "default": "./formatDistanceToNowStrict.js"}, "import": {"types": "./formatDistanceToNowStrict.d.mts", "default": "./formatDistanceToNowStrict.mjs"}}, "./formatDuration": {"require": {"types": "./formatDuration.d.ts", "default": "./formatDuration.js"}, "import": {"types": "./formatDuration.d.mts", "default": "./formatDuration.mjs"}}, "./formatISO": {"require": {"types": "./formatISO.d.ts", "default": "./formatISO.js"}, "import": {"types": "./formatISO.d.mts", "default": "./formatISO.mjs"}}, "./formatISO9075": {"require": {"types": "./formatISO9075.d.ts", "default": "./formatISO9075.js"}, "import": {"types": "./formatISO9075.d.mts", "default": "./formatISO9075.mjs"}}, "./formatISODuration": {"require": {"types": "./formatISODuration.d.ts", "default": "./formatISODuration.js"}, "import": {"types": "./formatISODuration.d.mts", "default": "./formatISODuration.mjs"}}, "./formatRFC3339": {"require": {"types": "./formatRFC3339.d.ts", "default": "./formatRFC3339.js"}, "import": {"types": "./formatRFC3339.d.mts", "default": "./formatRFC3339.mjs"}}, "./formatRFC7231": {"require": {"types": "./formatRFC7231.d.ts", "default": "./formatRFC7231.js"}, "import": {"types": "./formatRFC7231.d.mts", "default": "./formatRFC7231.mjs"}}, "./formatRelative": {"require": {"types": "./formatRelative.d.ts", "default": "./formatRelative.js"}, "import": {"types": "./formatRelative.d.mts", "default": "./formatRelative.mjs"}}, "./fromUnixTime": {"require": {"types": "./fromUnixTime.d.ts", "default": "./fromUnixTime.js"}, "import": {"types": "./fromUnixTime.d.mts", "default": "./fromUnixTime.mjs"}}, "./getDate": {"require": {"types": "./getDate.d.ts", "default": "./getDate.js"}, "import": {"types": "./getDate.d.mts", "default": "./getDate.mjs"}}, "./getDay": {"require": {"types": "./getDay.d.ts", "default": "./getDay.js"}, "import": {"types": "./getDay.d.mts", "default": "./getDay.mjs"}}, "./getDayOfYear": {"require": {"types": "./getDayOfYear.d.ts", "default": "./getDayOfYear.js"}, "import": {"types": "./getDayOfYear.d.mts", "default": "./getDayOfYear.mjs"}}, "./getDaysInMonth": {"require": {"types": "./getDaysInMonth.d.ts", "default": "./getDaysInMonth.js"}, "import": {"types": "./getDaysInMonth.d.mts", "default": "./getDaysInMonth.mjs"}}, "./getDaysInYear": {"require": {"types": "./getDaysInYear.d.ts", "default": "./getDaysInYear.js"}, "import": {"types": "./getDaysInYear.d.mts", "default": "./getDaysInYear.mjs"}}, "./getDecade": {"require": {"types": "./getDecade.d.ts", "default": "./getDecade.js"}, "import": {"types": "./getDecade.d.mts", "default": "./getDecade.mjs"}}, "./getDefaultOptions": {"require": {"types": "./getDefaultOptions.d.ts", "default": "./getDefaultOptions.js"}, "import": {"types": "./getDefaultOptions.d.mts", "default": "./getDefaultOptions.mjs"}}, "./getHours": {"require": {"types": "./getHours.d.ts", "default": "./getHours.js"}, "import": {"types": "./getHours.d.mts", "default": "./getHours.mjs"}}, "./getISODay": {"require": {"types": "./getISODay.d.ts", "default": "./getISODay.js"}, "import": {"types": "./getISODay.d.mts", "default": "./getISODay.mjs"}}, "./getISOWeek": {"require": {"types": "./getISOWeek.d.ts", "default": "./getISOWeek.js"}, "import": {"types": "./getISOWeek.d.mts", "default": "./getISOWeek.mjs"}}, "./getISOWeekYear": {"require": {"types": "./getISOWeekYear.d.ts", "default": "./getISOWeekYear.js"}, "import": {"types": "./getISOWeekYear.d.mts", "default": "./getISOWeekYear.mjs"}}, "./getISOWeeksInYear": {"require": {"types": "./getISOWeeksInYear.d.ts", "default": "./getISOWeeksInYear.js"}, "import": {"types": "./getISOWeeksInYear.d.mts", "default": "./getISOWeeksInYear.mjs"}}, "./getMilliseconds": {"require": {"types": "./getMilliseconds.d.ts", "default": "./getMilliseconds.js"}, "import": {"types": "./getMilliseconds.d.mts", "default": "./getMilliseconds.mjs"}}, "./getMinutes": {"require": {"types": "./getMinutes.d.ts", "default": "./getMinutes.js"}, "import": {"types": "./getMinutes.d.mts", "default": "./getMinutes.mjs"}}, "./getMonth": {"require": {"types": "./getMonth.d.ts", "default": "./getMonth.js"}, "import": {"types": "./getMonth.d.mts", "default": "./getMonth.mjs"}}, "./getOverlappingDaysInIntervals": {"require": {"types": "./getOverlappingDaysInIntervals.d.ts", "default": "./getOverlappingDaysInIntervals.js"}, "import": {"types": "./getOverlappingDaysInIntervals.d.mts", "default": "./getOverlappingDaysInIntervals.mjs"}}, "./getQuarter": {"require": {"types": "./getQuarter.d.ts", "default": "./getQuarter.js"}, "import": {"types": "./getQuarter.d.mts", "default": "./getQuarter.mjs"}}, "./getSeconds": {"require": {"types": "./getSeconds.d.ts", "default": "./getSeconds.js"}, "import": {"types": "./getSeconds.d.mts", "default": "./getSeconds.mjs"}}, "./getTime": {"require": {"types": "./getTime.d.ts", "default": "./getTime.js"}, "import": {"types": "./getTime.d.mts", "default": "./getTime.mjs"}}, "./getUnixTime": {"require": {"types": "./getUnixTime.d.ts", "default": "./getUnixTime.js"}, "import": {"types": "./getUnixTime.d.mts", "default": "./getUnixTime.mjs"}}, "./getWeek": {"require": {"types": "./getWeek.d.ts", "default": "./getWeek.js"}, "import": {"types": "./getWeek.d.mts", "default": "./getWeek.mjs"}}, "./getWeekOfMonth": {"require": {"types": "./getWeekOfMonth.d.ts", "default": "./getWeekOfMonth.js"}, "import": {"types": "./getWeekOfMonth.d.mts", "default": "./getWeekOfMonth.mjs"}}, "./getWeekYear": {"require": {"types": "./getWeekYear.d.ts", "default": "./getWeekYear.js"}, "import": {"types": "./getWeekYear.d.mts", "default": "./getWeekYear.mjs"}}, "./getWeeksInMonth": {"require": {"types": "./getWeeksInMonth.d.ts", "default": "./getWeeksInMonth.js"}, "import": {"types": "./getWeeksInMonth.d.mts", "default": "./getWeeksInMonth.mjs"}}, "./getYear": {"require": {"types": "./getYear.d.ts", "default": "./getYear.js"}, "import": {"types": "./getYear.d.mts", "default": "./getYear.mjs"}}, "./hoursToMilliseconds": {"require": {"types": "./hoursToMilliseconds.d.ts", "default": "./hoursToMilliseconds.js"}, "import": {"types": "./hoursToMilliseconds.d.mts", "default": "./hoursToMilliseconds.mjs"}}, "./hoursToMinutes": {"require": {"types": "./hoursToMinutes.d.ts", "default": "./hoursToMinutes.js"}, "import": {"types": "./hoursToMinutes.d.mts", "default": "./hoursToMinutes.mjs"}}, "./hoursToSeconds": {"require": {"types": "./hoursToSeconds.d.ts", "default": "./hoursToSeconds.js"}, "import": {"types": "./hoursToSeconds.d.mts", "default": "./hoursToSeconds.mjs"}}, "./interval": {"require": {"types": "./interval.d.ts", "default": "./interval.js"}, "import": {"types": "./interval.d.mts", "default": "./interval.mjs"}}, "./intervalToDuration": {"require": {"types": "./intervalToDuration.d.ts", "default": "./intervalToDuration.js"}, "import": {"types": "./intervalToDuration.d.mts", "default": "./intervalToDuration.mjs"}}, "./intlFormat": {"require": {"types": "./intlFormat.d.ts", "default": "./intlFormat.js"}, "import": {"types": "./intlFormat.d.mts", "default": "./intlFormat.mjs"}}, "./intlFormatDistance": {"require": {"types": "./intlFormatDistance.d.ts", "default": "./intlFormatDistance.js"}, "import": {"types": "./intlFormatDistance.d.mts", "default": "./intlFormatDistance.mjs"}}, "./isAfter": {"require": {"types": "./isAfter.d.ts", "default": "./isAfter.js"}, "import": {"types": "./isAfter.d.mts", "default": "./isAfter.mjs"}}, "./isBefore": {"require": {"types": "./isBefore.d.ts", "default": "./isBefore.js"}, "import": {"types": "./isBefore.d.mts", "default": "./isBefore.mjs"}}, "./isDate": {"require": {"types": "./isDate.d.ts", "default": "./isDate.js"}, "import": {"types": "./isDate.d.mts", "default": "./isDate.mjs"}}, "./isEqual": {"require": {"types": "./isEqual.d.ts", "default": "./isEqual.js"}, "import": {"types": "./isEqual.d.mts", "default": "./isEqual.mjs"}}, "./isExists": {"require": {"types": "./isExists.d.ts", "default": "./isExists.js"}, "import": {"types": "./isExists.d.mts", "default": "./isExists.mjs"}}, "./isFirstDayOfMonth": {"require": {"types": "./isFirstDayOfMonth.d.ts", "default": "./isFirstDayOfMonth.js"}, "import": {"types": "./isFirstDayOfMonth.d.mts", "default": "./isFirstDayOfMonth.mjs"}}, "./isFriday": {"require": {"types": "./isFriday.d.ts", "default": "./isFriday.js"}, "import": {"types": "./isFriday.d.mts", "default": "./isFriday.mjs"}}, "./isFuture": {"require": {"types": "./isFuture.d.ts", "default": "./isFuture.js"}, "import": {"types": "./isFuture.d.mts", "default": "./isFuture.mjs"}}, "./isLastDayOfMonth": {"require": {"types": "./isLastDayOfMonth.d.ts", "default": "./isLastDayOfMonth.js"}, "import": {"types": "./isLastDayOfMonth.d.mts", "default": "./isLastDayOfMonth.mjs"}}, "./isLeapYear": {"require": {"types": "./isLeapYear.d.ts", "default": "./isLeapYear.js"}, "import": {"types": "./isLeapYear.d.mts", "default": "./isLeapYear.mjs"}}, "./isMatch": {"require": {"types": "./isMatch.d.ts", "default": "./isMatch.js"}, "import": {"types": "./isMatch.d.mts", "default": "./isMatch.mjs"}}, "./isMonday": {"require": {"types": "./isMonday.d.ts", "default": "./isMonday.js"}, "import": {"types": "./isMonday.d.mts", "default": "./isMonday.mjs"}}, "./isPast": {"require": {"types": "./isPast.d.ts", "default": "./isPast.js"}, "import": {"types": "./isPast.d.mts", "default": "./isPast.mjs"}}, "./isSameDay": {"require": {"types": "./isSameDay.d.ts", "default": "./isSameDay.js"}, "import": {"types": "./isSameDay.d.mts", "default": "./isSameDay.mjs"}}, "./isSameHour": {"require": {"types": "./isSameHour.d.ts", "default": "./isSameHour.js"}, "import": {"types": "./isSameHour.d.mts", "default": "./isSameHour.mjs"}}, "./isSameISOWeek": {"require": {"types": "./isSameISOWeek.d.ts", "default": "./isSameISOWeek.js"}, "import": {"types": "./isSameISOWeek.d.mts", "default": "./isSameISOWeek.mjs"}}, "./isSameISOWeekYear": {"require": {"types": "./isSameISOWeekYear.d.ts", "default": "./isSameISOWeekYear.js"}, "import": {"types": "./isSameISOWeekYear.d.mts", "default": "./isSameISOWeekYear.mjs"}}, "./isSameMinute": {"require": {"types": "./isSameMinute.d.ts", "default": "./isSameMinute.js"}, "import": {"types": "./isSameMinute.d.mts", "default": "./isSameMinute.mjs"}}, "./isSameMonth": {"require": {"types": "./isSameMonth.d.ts", "default": "./isSameMonth.js"}, "import": {"types": "./isSameMonth.d.mts", "default": "./isSameMonth.mjs"}}, "./isSameQuarter": {"require": {"types": "./isSameQuarter.d.ts", "default": "./isSameQuarter.js"}, "import": {"types": "./isSameQuarter.d.mts", "default": "./isSameQuarter.mjs"}}, "./isSameSecond": {"require": {"types": "./isSameSecond.d.ts", "default": "./isSameSecond.js"}, "import": {"types": "./isSameSecond.d.mts", "default": "./isSameSecond.mjs"}}, "./isSameWeek": {"require": {"types": "./isSameWeek.d.ts", "default": "./isSameWeek.js"}, "import": {"types": "./isSameWeek.d.mts", "default": "./isSameWeek.mjs"}}, "./isSameYear": {"require": {"types": "./isSameYear.d.ts", "default": "./isSameYear.js"}, "import": {"types": "./isSameYear.d.mts", "default": "./isSameYear.mjs"}}, "./isSaturday": {"require": {"types": "./isSaturday.d.ts", "default": "./isSaturday.js"}, "import": {"types": "./isSaturday.d.mts", "default": "./isSaturday.mjs"}}, "./isSunday": {"require": {"types": "./isSunday.d.ts", "default": "./isSunday.js"}, "import": {"types": "./isSunday.d.mts", "default": "./isSunday.mjs"}}, "./isThisHour": {"require": {"types": "./isThisHour.d.ts", "default": "./isThisHour.js"}, "import": {"types": "./isThisHour.d.mts", "default": "./isThisHour.mjs"}}, "./isThisISOWeek": {"require": {"types": "./isThisISOWeek.d.ts", "default": "./isThisISOWeek.js"}, "import": {"types": "./isThisISOWeek.d.mts", "default": "./isThisISOWeek.mjs"}}, "./isThisMinute": {"require": {"types": "./isThisMinute.d.ts", "default": "./isThisMinute.js"}, "import": {"types": "./isThisMinute.d.mts", "default": "./isThisMinute.mjs"}}, "./isThisMonth": {"require": {"types": "./isThisMonth.d.ts", "default": "./isThisMonth.js"}, "import": {"types": "./isThisMonth.d.mts", "default": "./isThisMonth.mjs"}}, "./isThisQuarter": {"require": {"types": "./isThisQuarter.d.ts", "default": "./isThisQuarter.js"}, "import": {"types": "./isThisQuarter.d.mts", "default": "./isThisQuarter.mjs"}}, "./isThisSecond": {"require": {"types": "./isThisSecond.d.ts", "default": "./isThisSecond.js"}, "import": {"types": "./isThisSecond.d.mts", "default": "./isThisSecond.mjs"}}, "./isThisWeek": {"require": {"types": "./isThisWeek.d.ts", "default": "./isThisWeek.js"}, "import": {"types": "./isThisWeek.d.mts", "default": "./isThisWeek.mjs"}}, "./isThisYear": {"require": {"types": "./isThisYear.d.ts", "default": "./isThisYear.js"}, "import": {"types": "./isThisYear.d.mts", "default": "./isThisYear.mjs"}}, "./isThursday": {"require": {"types": "./isThursday.d.ts", "default": "./isThursday.js"}, "import": {"types": "./isThursday.d.mts", "default": "./isThursday.mjs"}}, "./isToday": {"require": {"types": "./isToday.d.ts", "default": "./isToday.js"}, "import": {"types": "./isToday.d.mts", "default": "./isToday.mjs"}}, "./isTomorrow": {"require": {"types": "./isTomorrow.d.ts", "default": "./isTomorrow.js"}, "import": {"types": "./isTomorrow.d.mts", "default": "./isTomorrow.mjs"}}, "./isTuesday": {"require": {"types": "./isTuesday.d.ts", "default": "./isTuesday.js"}, "import": {"types": "./isTuesday.d.mts", "default": "./isTuesday.mjs"}}, "./isValid": {"require": {"types": "./isValid.d.ts", "default": "./isValid.js"}, "import": {"types": "./isValid.d.mts", "default": "./isValid.mjs"}}, "./isWednesday": {"require": {"types": "./isWednesday.d.ts", "default": "./isWednesday.js"}, "import": {"types": "./isWednesday.d.mts", "default": "./isWednesday.mjs"}}, "./isWeekend": {"require": {"types": "./isWeekend.d.ts", "default": "./isWeekend.js"}, "import": {"types": "./isWeekend.d.mts", "default": "./isWeekend.mjs"}}, "./isWithinInterval": {"require": {"types": "./isWithinInterval.d.ts", "default": "./isWithinInterval.js"}, "import": {"types": "./isWithinInterval.d.mts", "default": "./isWithinInterval.mjs"}}, "./isYesterday": {"require": {"types": "./isYesterday.d.ts", "default": "./isYesterday.js"}, "import": {"types": "./isYesterday.d.mts", "default": "./isYesterday.mjs"}}, "./lastDayOfDecade": {"require": {"types": "./lastDayOfDecade.d.ts", "default": "./lastDayOfDecade.js"}, "import": {"types": "./lastDayOfDecade.d.mts", "default": "./lastDayOfDecade.mjs"}}, "./lastDayOfISOWeek": {"require": {"types": "./lastDayOfISOWeek.d.ts", "default": "./lastDayOfISOWeek.js"}, "import": {"types": "./lastDayOfISOWeek.d.mts", "default": "./lastDayOfISOWeek.mjs"}}, "./lastDayOfISOWeekYear": {"require": {"types": "./lastDayOfISOWeekYear.d.ts", "default": "./lastDayOfISOWeekYear.js"}, "import": {"types": "./lastDayOfISOWeekYear.d.mts", "default": "./lastDayOfISOWeekYear.mjs"}}, "./lastDayOfMonth": {"require": {"types": "./lastDayOfMonth.d.ts", "default": "./lastDayOfMonth.js"}, "import": {"types": "./lastDayOfMonth.d.mts", "default": "./lastDayOfMonth.mjs"}}, "./lastDayOfQuarter": {"require": {"types": "./lastDayOfQuarter.d.ts", "default": "./lastDayOfQuarter.js"}, "import": {"types": "./lastDayOfQuarter.d.mts", "default": "./lastDayOfQuarter.mjs"}}, "./lastDayOfWeek": {"require": {"types": "./lastDayOfWeek.d.ts", "default": "./lastDayOfWeek.js"}, "import": {"types": "./lastDayOfWeek.d.mts", "default": "./lastDayOfWeek.mjs"}}, "./lastDayOfYear": {"require": {"types": "./lastDayOfYear.d.ts", "default": "./lastDayOfYear.js"}, "import": {"types": "./lastDayOfYear.d.mts", "default": "./lastDayOfYear.mjs"}}, "./lightFormat": {"require": {"types": "./lightFormat.d.ts", "default": "./lightFormat.js"}, "import": {"types": "./lightFormat.d.mts", "default": "./lightFormat.mjs"}}, "./max": {"require": {"types": "./max.d.ts", "default": "./max.js"}, "import": {"types": "./max.d.mts", "default": "./max.mjs"}}, "./milliseconds": {"require": {"types": "./milliseconds.d.ts", "default": "./milliseconds.js"}, "import": {"types": "./milliseconds.d.mts", "default": "./milliseconds.mjs"}}, "./millisecondsToHours": {"require": {"types": "./millisecondsToHours.d.ts", "default": "./millisecondsToHours.js"}, "import": {"types": "./millisecondsToHours.d.mts", "default": "./millisecondsToHours.mjs"}}, "./millisecondsToMinutes": {"require": {"types": "./millisecondsToMinutes.d.ts", "default": "./millisecondsToMinutes.js"}, "import": {"types": "./millisecondsToMinutes.d.mts", "default": "./millisecondsToMinutes.mjs"}}, "./millisecondsToSeconds": {"require": {"types": "./millisecondsToSeconds.d.ts", "default": "./millisecondsToSeconds.js"}, "import": {"types": "./millisecondsToSeconds.d.mts", "default": "./millisecondsToSeconds.mjs"}}, "./min": {"require": {"types": "./min.d.ts", "default": "./min.js"}, "import": {"types": "./min.d.mts", "default": "./min.mjs"}}, "./minutesToHours": {"require": {"types": "./minutesToHours.d.ts", "default": "./minutesToHours.js"}, "import": {"types": "./minutesToHours.d.mts", "default": "./minutesToHours.mjs"}}, "./minutesToMilliseconds": {"require": {"types": "./minutesToMilliseconds.d.ts", "default": "./minutesToMilliseconds.js"}, "import": {"types": "./minutesToMilliseconds.d.mts", "default": "./minutesToMilliseconds.mjs"}}, "./minutesToSeconds": {"require": {"types": "./minutesToSeconds.d.ts", "default": "./minutesToSeconds.js"}, "import": {"types": "./minutesToSeconds.d.mts", "default": "./minutesToSeconds.mjs"}}, "./monthsToQuarters": {"require": {"types": "./monthsToQuarters.d.ts", "default": "./monthsToQuarters.js"}, "import": {"types": "./monthsToQuarters.d.mts", "default": "./monthsToQuarters.mjs"}}, "./monthsToYears": {"require": {"types": "./monthsToYears.d.ts", "default": "./monthsToYears.js"}, "import": {"types": "./monthsToYears.d.mts", "default": "./monthsToYears.mjs"}}, "./nextDay": {"require": {"types": "./nextDay.d.ts", "default": "./nextDay.js"}, "import": {"types": "./nextDay.d.mts", "default": "./nextDay.mjs"}}, "./nextFriday": {"require": {"types": "./nextFriday.d.ts", "default": "./nextFriday.js"}, "import": {"types": "./nextFriday.d.mts", "default": "./nextFriday.mjs"}}, "./nextMonday": {"require": {"types": "./nextMonday.d.ts", "default": "./nextMonday.js"}, "import": {"types": "./nextMonday.d.mts", "default": "./nextMonday.mjs"}}, "./nextSaturday": {"require": {"types": "./nextSaturday.d.ts", "default": "./nextSaturday.js"}, "import": {"types": "./nextSaturday.d.mts", "default": "./nextSaturday.mjs"}}, "./nextSunday": {"require": {"types": "./nextSunday.d.ts", "default": "./nextSunday.js"}, "import": {"types": "./nextSunday.d.mts", "default": "./nextSunday.mjs"}}, "./nextThursday": {"require": {"types": "./nextThursday.d.ts", "default": "./nextThursday.js"}, "import": {"types": "./nextThursday.d.mts", "default": "./nextThursday.mjs"}}, "./nextTuesday": {"require": {"types": "./nextTuesday.d.ts", "default": "./nextTuesday.js"}, "import": {"types": "./nextTuesday.d.mts", "default": "./nextTuesday.mjs"}}, "./nextWednesday": {"require": {"types": "./nextWednesday.d.ts", "default": "./nextWednesday.js"}, "import": {"types": "./nextWednesday.d.mts", "default": "./nextWednesday.mjs"}}, "./parse": {"require": {"types": "./parse.d.ts", "default": "./parse.js"}, "import": {"types": "./parse.d.mts", "default": "./parse.mjs"}}, "./parseISO": {"require": {"types": "./parseISO.d.ts", "default": "./parseISO.js"}, "import": {"types": "./parseISO.d.mts", "default": "./parseISO.mjs"}}, "./parseJSON": {"require": {"types": "./parseJSON.d.ts", "default": "./parseJSON.js"}, "import": {"types": "./parseJSON.d.mts", "default": "./parseJSON.mjs"}}, "./previousDay": {"require": {"types": "./previousDay.d.ts", "default": "./previousDay.js"}, "import": {"types": "./previousDay.d.mts", "default": "./previousDay.mjs"}}, "./previousFriday": {"require": {"types": "./previousFriday.d.ts", "default": "./previousFriday.js"}, "import": {"types": "./previousFriday.d.mts", "default": "./previousFriday.mjs"}}, "./previousMonday": {"require": {"types": "./previousMonday.d.ts", "default": "./previousMonday.js"}, "import": {"types": "./previousMonday.d.mts", "default": "./previousMonday.mjs"}}, "./previousSaturday": {"require": {"types": "./previousSaturday.d.ts", "default": "./previousSaturday.js"}, "import": {"types": "./previousSaturday.d.mts", "default": "./previousSaturday.mjs"}}, "./previousSunday": {"require": {"types": "./previousSunday.d.ts", "default": "./previousSunday.js"}, "import": {"types": "./previousSunday.d.mts", "default": "./previousSunday.mjs"}}, "./previousThursday": {"require": {"types": "./previousThursday.d.ts", "default": "./previousThursday.js"}, "import": {"types": "./previousThursday.d.mts", "default": "./previousThursday.mjs"}}, "./previousTuesday": {"require": {"types": "./previousTuesday.d.ts", "default": "./previousTuesday.js"}, "import": {"types": "./previousTuesday.d.mts", "default": "./previousTuesday.mjs"}}, "./previousWednesday": {"require": {"types": "./previousWednesday.d.ts", "default": "./previousWednesday.js"}, "import": {"types": "./previousWednesday.d.mts", "default": "./previousWednesday.mjs"}}, "./quartersToMonths": {"require": {"types": "./quartersToMonths.d.ts", "default": "./quartersToMonths.js"}, "import": {"types": "./quartersToMonths.d.mts", "default": "./quartersToMonths.mjs"}}, "./quartersToYears": {"require": {"types": "./quartersToYears.d.ts", "default": "./quartersToYears.js"}, "import": {"types": "./quartersToYears.d.mts", "default": "./quartersToYears.mjs"}}, "./roundToNearestHours": {"require": {"types": "./roundToNearestHours.d.ts", "default": "./roundToNearestHours.js"}, "import": {"types": "./roundToNearestHours.d.mts", "default": "./roundToNearestHours.mjs"}}, "./roundToNearestMinutes": {"require": {"types": "./roundToNearestMinutes.d.ts", "default": "./roundToNearestMinutes.js"}, "import": {"types": "./roundToNearestMinutes.d.mts", "default": "./roundToNearestMinutes.mjs"}}, "./secondsToHours": {"require": {"types": "./secondsToHours.d.ts", "default": "./secondsToHours.js"}, "import": {"types": "./secondsToHours.d.mts", "default": "./secondsToHours.mjs"}}, "./secondsToMilliseconds": {"require": {"types": "./secondsToMilliseconds.d.ts", "default": "./secondsToMilliseconds.js"}, "import": {"types": "./secondsToMilliseconds.d.mts", "default": "./secondsToMilliseconds.mjs"}}, "./secondsToMinutes": {"require": {"types": "./secondsToMinutes.d.ts", "default": "./secondsToMinutes.js"}, "import": {"types": "./secondsToMinutes.d.mts", "default": "./secondsToMinutes.mjs"}}, "./set": {"require": {"types": "./set.d.ts", "default": "./set.js"}, "import": {"types": "./set.d.mts", "default": "./set.mjs"}}, "./setDate": {"require": {"types": "./setDate.d.ts", "default": "./setDate.js"}, "import": {"types": "./setDate.d.mts", "default": "./setDate.mjs"}}, "./setDay": {"require": {"types": "./setDay.d.ts", "default": "./setDay.js"}, "import": {"types": "./setDay.d.mts", "default": "./setDay.mjs"}}, "./setDayOfYear": {"require": {"types": "./setDayOfYear.d.ts", "default": "./setDayOfYear.js"}, "import": {"types": "./setDayOfYear.d.mts", "default": "./setDayOfYear.mjs"}}, "./setDefaultOptions": {"require": {"types": "./setDefaultOptions.d.ts", "default": "./setDefaultOptions.js"}, "import": {"types": "./setDefaultOptions.d.mts", "default": "./setDefaultOptions.mjs"}}, "./setHours": {"require": {"types": "./setHours.d.ts", "default": "./setHours.js"}, "import": {"types": "./setHours.d.mts", "default": "./setHours.mjs"}}, "./setISODay": {"require": {"types": "./setISODay.d.ts", "default": "./setISODay.js"}, "import": {"types": "./setISODay.d.mts", "default": "./setISODay.mjs"}}, "./setISOWeek": {"require": {"types": "./setISOWeek.d.ts", "default": "./setISOWeek.js"}, "import": {"types": "./setISOWeek.d.mts", "default": "./setISOWeek.mjs"}}, "./setISOWeekYear": {"require": {"types": "./setISOWeekYear.d.ts", "default": "./setISOWeekYear.js"}, "import": {"types": "./setISOWeekYear.d.mts", "default": "./setISOWeekYear.mjs"}}, "./setMilliseconds": {"require": {"types": "./setMilliseconds.d.ts", "default": "./setMilliseconds.js"}, "import": {"types": "./setMilliseconds.d.mts", "default": "./setMilliseconds.mjs"}}, "./setMinutes": {"require": {"types": "./setMinutes.d.ts", "default": "./setMinutes.js"}, "import": {"types": "./setMinutes.d.mts", "default": "./setMinutes.mjs"}}, "./setMonth": {"require": {"types": "./setMonth.d.ts", "default": "./setMonth.js"}, "import": {"types": "./setMonth.d.mts", "default": "./setMonth.mjs"}}, "./setQuarter": {"require": {"types": "./setQuarter.d.ts", "default": "./setQuarter.js"}, "import": {"types": "./setQuarter.d.mts", "default": "./setQuarter.mjs"}}, "./setSeconds": {"require": {"types": "./setSeconds.d.ts", "default": "./setSeconds.js"}, "import": {"types": "./setSeconds.d.mts", "default": "./setSeconds.mjs"}}, "./setWeek": {"require": {"types": "./setWeek.d.ts", "default": "./setWeek.js"}, "import": {"types": "./setWeek.d.mts", "default": "./setWeek.mjs"}}, "./setWeekYear": {"require": {"types": "./setWeekYear.d.ts", "default": "./setWeekYear.js"}, "import": {"types": "./setWeekYear.d.mts", "default": "./setWeekYear.mjs"}}, "./setYear": {"require": {"types": "./setYear.d.ts", "default": "./setYear.js"}, "import": {"types": "./setYear.d.mts", "default": "./setYear.mjs"}}, "./startOfDay": {"require": {"types": "./startOfDay.d.ts", "default": "./startOfDay.js"}, "import": {"types": "./startOfDay.d.mts", "default": "./startOfDay.mjs"}}, "./startOfDecade": {"require": {"types": "./startOfDecade.d.ts", "default": "./startOfDecade.js"}, "import": {"types": "./startOfDecade.d.mts", "default": "./startOfDecade.mjs"}}, "./startOfHour": {"require": {"types": "./startOfHour.d.ts", "default": "./startOfHour.js"}, "import": {"types": "./startOfHour.d.mts", "default": "./startOfHour.mjs"}}, "./startOfISOWeek": {"require": {"types": "./startOfISOWeek.d.ts", "default": "./startOfISOWeek.js"}, "import": {"types": "./startOfISOWeek.d.mts", "default": "./startOfISOWeek.mjs"}}, "./startOfISOWeekYear": {"require": {"types": "./startOfISOWeekYear.d.ts", "default": "./startOfISOWeekYear.js"}, "import": {"types": "./startOfISOWeekYear.d.mts", "default": "./startOfISOWeekYear.mjs"}}, "./startOfMinute": {"require": {"types": "./startOfMinute.d.ts", "default": "./startOfMinute.js"}, "import": {"types": "./startOfMinute.d.mts", "default": "./startOfMinute.mjs"}}, "./startOfMonth": {"require": {"types": "./startOfMonth.d.ts", "default": "./startOfMonth.js"}, "import": {"types": "./startOfMonth.d.mts", "default": "./startOfMonth.mjs"}}, "./startOfQuarter": {"require": {"types": "./startOfQuarter.d.ts", "default": "./startOfQuarter.js"}, "import": {"types": "./startOfQuarter.d.mts", "default": "./startOfQuarter.mjs"}}, "./startOfSecond": {"require": {"types": "./startOfSecond.d.ts", "default": "./startOfSecond.js"}, "import": {"types": "./startOfSecond.d.mts", "default": "./startOfSecond.mjs"}}, "./startOfToday": {"require": {"types": "./startOfToday.d.ts", "default": "./startOfToday.js"}, "import": {"types": "./startOfToday.d.mts", "default": "./startOfToday.mjs"}}, "./startOfTomorrow": {"require": {"types": "./startOfTomorrow.d.ts", "default": "./startOfTomorrow.js"}, "import": {"types": "./startOfTomorrow.d.mts", "default": "./startOfTomorrow.mjs"}}, "./startOfWeek": {"require": {"types": "./startOfWeek.d.ts", "default": "./startOfWeek.js"}, "import": {"types": "./startOfWeek.d.mts", "default": "./startOfWeek.mjs"}}, "./startOfWeekYear": {"require": {"types": "./startOfWeekYear.d.ts", "default": "./startOfWeekYear.js"}, "import": {"types": "./startOfWeekYear.d.mts", "default": "./startOfWeekYear.mjs"}}, "./startOfYear": {"require": {"types": "./startOfYear.d.ts", "default": "./startOfYear.js"}, "import": {"types": "./startOfYear.d.mts", "default": "./startOfYear.mjs"}}, "./startOfYesterday": {"require": {"types": "./startOfYesterday.d.ts", "default": "./startOfYesterday.js"}, "import": {"types": "./startOfYesterday.d.mts", "default": "./startOfYesterday.mjs"}}, "./sub": {"require": {"types": "./sub.d.ts", "default": "./sub.js"}, "import": {"types": "./sub.d.mts", "default": "./sub.mjs"}}, "./subBusinessDays": {"require": {"types": "./subBusinessDays.d.ts", "default": "./subBusinessDays.js"}, "import": {"types": "./subBusinessDays.d.mts", "default": "./subBusinessDays.mjs"}}, "./subDays": {"require": {"types": "./subDays.d.ts", "default": "./subDays.js"}, "import": {"types": "./subDays.d.mts", "default": "./subDays.mjs"}}, "./subHours": {"require": {"types": "./subHours.d.ts", "default": "./subHours.js"}, "import": {"types": "./subHours.d.mts", "default": "./subHours.mjs"}}, "./subISOWeekYears": {"require": {"types": "./subISOWeekYears.d.ts", "default": "./subISOWeekYears.js"}, "import": {"types": "./subISOWeekYears.d.mts", "default": "./subISOWeekYears.mjs"}}, "./subMilliseconds": {"require": {"types": "./subMilliseconds.d.ts", "default": "./subMilliseconds.js"}, "import": {"types": "./subMilliseconds.d.mts", "default": "./subMilliseconds.mjs"}}, "./subMinutes": {"require": {"types": "./subMinutes.d.ts", "default": "./subMinutes.js"}, "import": {"types": "./subMinutes.d.mts", "default": "./subMinutes.mjs"}}, "./subMonths": {"require": {"types": "./subMonths.d.ts", "default": "./subMonths.js"}, "import": {"types": "./subMonths.d.mts", "default": "./subMonths.mjs"}}, "./subQuarters": {"require": {"types": "./subQuarters.d.ts", "default": "./subQuarters.js"}, "import": {"types": "./subQuarters.d.mts", "default": "./subQuarters.mjs"}}, "./subSeconds": {"require": {"types": "./subSeconds.d.ts", "default": "./subSeconds.js"}, "import": {"types": "./subSeconds.d.mts", "default": "./subSeconds.mjs"}}, "./subWeeks": {"require": {"types": "./subWeeks.d.ts", "default": "./subWeeks.js"}, "import": {"types": "./subWeeks.d.mts", "default": "./subWeeks.mjs"}}, "./subYears": {"require": {"types": "./subYears.d.ts", "default": "./subYears.js"}, "import": {"types": "./subYears.d.mts", "default": "./subYears.mjs"}}, "./toDate": {"require": {"types": "./toDate.d.ts", "default": "./toDate.js"}, "import": {"types": "./toDate.d.mts", "default": "./toDate.mjs"}}, "./transpose": {"require": {"types": "./transpose.d.ts", "default": "./transpose.js"}, "import": {"types": "./transpose.d.mts", "default": "./transpose.mjs"}}, "./weeksToDays": {"require": {"types": "./weeksToDays.d.ts", "default": "./weeksToDays.js"}, "import": {"types": "./weeksToDays.d.mts", "default": "./weeksToDays.mjs"}}, "./yearsToDays": {"require": {"types": "./yearsToDays.d.ts", "default": "./yearsToDays.js"}, "import": {"types": "./yearsToDays.d.mts", "default": "./yearsToDays.mjs"}}, "./yearsToMonths": {"require": {"types": "./yearsToMonths.d.ts", "default": "./yearsToMonths.js"}, "import": {"types": "./yearsToMonths.d.mts", "default": "./yearsToMonths.mjs"}}, "./yearsToQuarters": {"require": {"types": "./yearsToQuarters.d.ts", "default": "./yearsToQuarters.js"}, "import": {"types": "./yearsToQuarters.d.mts", "default": "./yearsToQuarters.mjs"}}, "./fp/add": {"require": {"types": "./fp/add.d.ts", "default": "./fp/add.js"}, "import": {"types": "./fp/add.d.mts", "default": "./fp/add.mjs"}}, "./fp/addBusinessDays": {"require": {"types": "./fp/addBusinessDays.d.ts", "default": "./fp/addBusinessDays.js"}, "import": {"types": "./fp/addBusinessDays.d.mts", "default": "./fp/addBusinessDays.mjs"}}, "./fp/addDays": {"require": {"types": "./fp/addDays.d.ts", "default": "./fp/addDays.js"}, "import": {"types": "./fp/addDays.d.mts", "default": "./fp/addDays.mjs"}}, "./fp/addHours": {"require": {"types": "./fp/addHours.d.ts", "default": "./fp/addHours.js"}, "import": {"types": "./fp/addHours.d.mts", "default": "./fp/addHours.mjs"}}, "./fp/addISOWeekYears": {"require": {"types": "./fp/addISOWeekYears.d.ts", "default": "./fp/addISOWeekYears.js"}, "import": {"types": "./fp/addISOWeekYears.d.mts", "default": "./fp/addISOWeekYears.mjs"}}, "./fp/addMilliseconds": {"require": {"types": "./fp/addMilliseconds.d.ts", "default": "./fp/addMilliseconds.js"}, "import": {"types": "./fp/addMilliseconds.d.mts", "default": "./fp/addMilliseconds.mjs"}}, "./fp/addMinutes": {"require": {"types": "./fp/addMinutes.d.ts", "default": "./fp/addMinutes.js"}, "import": {"types": "./fp/addMinutes.d.mts", "default": "./fp/addMinutes.mjs"}}, "./fp/addMonths": {"require": {"types": "./fp/addMonths.d.ts", "default": "./fp/addMonths.js"}, "import": {"types": "./fp/addMonths.d.mts", "default": "./fp/addMonths.mjs"}}, "./fp/addQuarters": {"require": {"types": "./fp/addQuarters.d.ts", "default": "./fp/addQuarters.js"}, "import": {"types": "./fp/addQuarters.d.mts", "default": "./fp/addQuarters.mjs"}}, "./fp/addSeconds": {"require": {"types": "./fp/addSeconds.d.ts", "default": "./fp/addSeconds.js"}, "import": {"types": "./fp/addSeconds.d.mts", "default": "./fp/addSeconds.mjs"}}, "./fp/addWeeks": {"require": {"types": "./fp/addWeeks.d.ts", "default": "./fp/addWeeks.js"}, "import": {"types": "./fp/addWeeks.d.mts", "default": "./fp/addWeeks.mjs"}}, "./fp/addYears": {"require": {"types": "./fp/addYears.d.ts", "default": "./fp/addYears.js"}, "import": {"types": "./fp/addYears.d.mts", "default": "./fp/addYears.mjs"}}, "./fp/areIntervalsOverlapping": {"require": {"types": "./fp/areIntervalsOverlapping.d.ts", "default": "./fp/areIntervalsOverlapping.js"}, "import": {"types": "./fp/areIntervalsOverlapping.d.mts", "default": "./fp/areIntervalsOverlapping.mjs"}}, "./fp/areIntervalsOverlappingWithOptions": {"require": {"types": "./fp/areIntervalsOverlappingWithOptions.d.ts", "default": "./fp/areIntervalsOverlappingWithOptions.js"}, "import": {"types": "./fp/areIntervalsOverlappingWithOptions.d.mts", "default": "./fp/areIntervalsOverlappingWithOptions.mjs"}}, "./fp/clamp": {"require": {"types": "./fp/clamp.d.ts", "default": "./fp/clamp.js"}, "import": {"types": "./fp/clamp.d.mts", "default": "./fp/clamp.mjs"}}, "./fp/closestIndexTo": {"require": {"types": "./fp/closestIndexTo.d.ts", "default": "./fp/closestIndexTo.js"}, "import": {"types": "./fp/closestIndexTo.d.mts", "default": "./fp/closestIndexTo.mjs"}}, "./fp/closestTo": {"require": {"types": "./fp/closestTo.d.ts", "default": "./fp/closestTo.js"}, "import": {"types": "./fp/closestTo.d.mts", "default": "./fp/closestTo.mjs"}}, "./fp/compareAsc": {"require": {"types": "./fp/compareAsc.d.ts", "default": "./fp/compareAsc.js"}, "import": {"types": "./fp/compareAsc.d.mts", "default": "./fp/compareAsc.mjs"}}, "./fp/compareDesc": {"require": {"types": "./fp/compareDesc.d.ts", "default": "./fp/compareDesc.js"}, "import": {"types": "./fp/compareDesc.d.mts", "default": "./fp/compareDesc.mjs"}}, "./fp/constructFrom": {"require": {"types": "./fp/constructFrom.d.ts", "default": "./fp/constructFrom.js"}, "import": {"types": "./fp/constructFrom.d.mts", "default": "./fp/constructFrom.mjs"}}, "./fp/daysToWeeks": {"require": {"types": "./fp/daysToWeeks.d.ts", "default": "./fp/daysToWeeks.js"}, "import": {"types": "./fp/daysToWeeks.d.mts", "default": "./fp/daysToWeeks.mjs"}}, "./fp/differenceInBusinessDays": {"require": {"types": "./fp/differenceInBusinessDays.d.ts", "default": "./fp/differenceInBusinessDays.js"}, "import": {"types": "./fp/differenceInBusinessDays.d.mts", "default": "./fp/differenceInBusinessDays.mjs"}}, "./fp/differenceInCalendarDays": {"require": {"types": "./fp/differenceInCalendarDays.d.ts", "default": "./fp/differenceInCalendarDays.js"}, "import": {"types": "./fp/differenceInCalendarDays.d.mts", "default": "./fp/differenceInCalendarDays.mjs"}}, "./fp/differenceInCalendarISOWeekYears": {"require": {"types": "./fp/differenceInCalendarISOWeekYears.d.ts", "default": "./fp/differenceInCalendarISOWeekYears.js"}, "import": {"types": "./fp/differenceInCalendarISOWeekYears.d.mts", "default": "./fp/differenceInCalendarISOWeekYears.mjs"}}, "./fp/differenceInCalendarISOWeeks": {"require": {"types": "./fp/differenceInCalendarISOWeeks.d.ts", "default": "./fp/differenceInCalendarISOWeeks.js"}, "import": {"types": "./fp/differenceInCalendarISOWeeks.d.mts", "default": "./fp/differenceInCalendarISOWeeks.mjs"}}, "./fp/differenceInCalendarMonths": {"require": {"types": "./fp/differenceInCalendarMonths.d.ts", "default": "./fp/differenceInCalendarMonths.js"}, "import": {"types": "./fp/differenceInCalendarMonths.d.mts", "default": "./fp/differenceInCalendarMonths.mjs"}}, "./fp/differenceInCalendarQuarters": {"require": {"types": "./fp/differenceInCalendarQuarters.d.ts", "default": "./fp/differenceInCalendarQuarters.js"}, "import": {"types": "./fp/differenceInCalendarQuarters.d.mts", "default": "./fp/differenceInCalendarQuarters.mjs"}}, "./fp/differenceInCalendarWeeks": {"require": {"types": "./fp/differenceInCalendarWeeks.d.ts", "default": "./fp/differenceInCalendarWeeks.js"}, "import": {"types": "./fp/differenceInCalendarWeeks.d.mts", "default": "./fp/differenceInCalendarWeeks.mjs"}}, "./fp/differenceInCalendarWeeksWithOptions": {"require": {"types": "./fp/differenceInCalendarWeeksWithOptions.d.ts", "default": "./fp/differenceInCalendarWeeksWithOptions.js"}, "import": {"types": "./fp/differenceInCalendarWeeksWithOptions.d.mts", "default": "./fp/differenceInCalendarWeeksWithOptions.mjs"}}, "./fp/differenceInCalendarYears": {"require": {"types": "./fp/differenceInCalendarYears.d.ts", "default": "./fp/differenceInCalendarYears.js"}, "import": {"types": "./fp/differenceInCalendarYears.d.mts", "default": "./fp/differenceInCalendarYears.mjs"}}, "./fp/differenceInDays": {"require": {"types": "./fp/differenceInDays.d.ts", "default": "./fp/differenceInDays.js"}, "import": {"types": "./fp/differenceInDays.d.mts", "default": "./fp/differenceInDays.mjs"}}, "./fp/differenceInHours": {"require": {"types": "./fp/differenceInHours.d.ts", "default": "./fp/differenceInHours.js"}, "import": {"types": "./fp/differenceInHours.d.mts", "default": "./fp/differenceInHours.mjs"}}, "./fp/differenceInHoursWithOptions": {"require": {"types": "./fp/differenceInHoursWithOptions.d.ts", "default": "./fp/differenceInHoursWithOptions.js"}, "import": {"types": "./fp/differenceInHoursWithOptions.d.mts", "default": "./fp/differenceInHoursWithOptions.mjs"}}, "./fp/differenceInISOWeekYears": {"require": {"types": "./fp/differenceInISOWeekYears.d.ts", "default": "./fp/differenceInISOWeekYears.js"}, "import": {"types": "./fp/differenceInISOWeekYears.d.mts", "default": "./fp/differenceInISOWeekYears.mjs"}}, "./fp/differenceInMilliseconds": {"require": {"types": "./fp/differenceInMilliseconds.d.ts", "default": "./fp/differenceInMilliseconds.js"}, "import": {"types": "./fp/differenceInMilliseconds.d.mts", "default": "./fp/differenceInMilliseconds.mjs"}}, "./fp/differenceInMinutes": {"require": {"types": "./fp/differenceInMinutes.d.ts", "default": "./fp/differenceInMinutes.js"}, "import": {"types": "./fp/differenceInMinutes.d.mts", "default": "./fp/differenceInMinutes.mjs"}}, "./fp/differenceInMinutesWithOptions": {"require": {"types": "./fp/differenceInMinutesWithOptions.d.ts", "default": "./fp/differenceInMinutesWithOptions.js"}, "import": {"types": "./fp/differenceInMinutesWithOptions.d.mts", "default": "./fp/differenceInMinutesWithOptions.mjs"}}, "./fp/differenceInMonths": {"require": {"types": "./fp/differenceInMonths.d.ts", "default": "./fp/differenceInMonths.js"}, "import": {"types": "./fp/differenceInMonths.d.mts", "default": "./fp/differenceInMonths.mjs"}}, "./fp/differenceInQuarters": {"require": {"types": "./fp/differenceInQuarters.d.ts", "default": "./fp/differenceInQuarters.js"}, "import": {"types": "./fp/differenceInQuarters.d.mts", "default": "./fp/differenceInQuarters.mjs"}}, "./fp/differenceInQuartersWithOptions": {"require": {"types": "./fp/differenceInQuartersWithOptions.d.ts", "default": "./fp/differenceInQuartersWithOptions.js"}, "import": {"types": "./fp/differenceInQuartersWithOptions.d.mts", "default": "./fp/differenceInQuartersWithOptions.mjs"}}, "./fp/differenceInSeconds": {"require": {"types": "./fp/differenceInSeconds.d.ts", "default": "./fp/differenceInSeconds.js"}, "import": {"types": "./fp/differenceInSeconds.d.mts", "default": "./fp/differenceInSeconds.mjs"}}, "./fp/differenceInSecondsWithOptions": {"require": {"types": "./fp/differenceInSecondsWithOptions.d.ts", "default": "./fp/differenceInSecondsWithOptions.js"}, "import": {"types": "./fp/differenceInSecondsWithOptions.d.mts", "default": "./fp/differenceInSecondsWithOptions.mjs"}}, "./fp/differenceInWeeks": {"require": {"types": "./fp/differenceInWeeks.d.ts", "default": "./fp/differenceInWeeks.js"}, "import": {"types": "./fp/differenceInWeeks.d.mts", "default": "./fp/differenceInWeeks.mjs"}}, "./fp/differenceInWeeksWithOptions": {"require": {"types": "./fp/differenceInWeeksWithOptions.d.ts", "default": "./fp/differenceInWeeksWithOptions.js"}, "import": {"types": "./fp/differenceInWeeksWithOptions.d.mts", "default": "./fp/differenceInWeeksWithOptions.mjs"}}, "./fp/differenceInYears": {"require": {"types": "./fp/differenceInYears.d.ts", "default": "./fp/differenceInYears.js"}, "import": {"types": "./fp/differenceInYears.d.mts", "default": "./fp/differenceInYears.mjs"}}, "./fp/eachDayOfInterval": {"require": {"types": "./fp/eachDayOfInterval.d.ts", "default": "./fp/eachDayOfInterval.js"}, "import": {"types": "./fp/eachDayOfInterval.d.mts", "default": "./fp/eachDayOfInterval.mjs"}}, "./fp/eachDayOfIntervalWithOptions": {"require": {"types": "./fp/eachDayOfIntervalWithOptions.d.ts", "default": "./fp/eachDayOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachDayOfIntervalWithOptions.d.mts", "default": "./fp/eachDayOfIntervalWithOptions.mjs"}}, "./fp/eachHourOfInterval": {"require": {"types": "./fp/eachHourOfInterval.d.ts", "default": "./fp/eachHourOfInterval.js"}, "import": {"types": "./fp/eachHourOfInterval.d.mts", "default": "./fp/eachHourOfInterval.mjs"}}, "./fp/eachHourOfIntervalWithOptions": {"require": {"types": "./fp/eachHourOfIntervalWithOptions.d.ts", "default": "./fp/eachHourOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachHourOfIntervalWithOptions.d.mts", "default": "./fp/eachHourOfIntervalWithOptions.mjs"}}, "./fp/eachMinuteOfInterval": {"require": {"types": "./fp/eachMinuteOfInterval.d.ts", "default": "./fp/eachMinuteOfInterval.js"}, "import": {"types": "./fp/eachMinuteOfInterval.d.mts", "default": "./fp/eachMinuteOfInterval.mjs"}}, "./fp/eachMinuteOfIntervalWithOptions": {"require": {"types": "./fp/eachMinuteOfIntervalWithOptions.d.ts", "default": "./fp/eachMinuteOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachMinuteOfIntervalWithOptions.d.mts", "default": "./fp/eachMinuteOfIntervalWithOptions.mjs"}}, "./fp/eachMonthOfInterval": {"require": {"types": "./fp/eachMonthOfInterval.d.ts", "default": "./fp/eachMonthOfInterval.js"}, "import": {"types": "./fp/eachMonthOfInterval.d.mts", "default": "./fp/eachMonthOfInterval.mjs"}}, "./fp/eachMonthOfIntervalWithOptions": {"require": {"types": "./fp/eachMonthOfIntervalWithOptions.d.ts", "default": "./fp/eachMonthOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachMonthOfIntervalWithOptions.d.mts", "default": "./fp/eachMonthOfIntervalWithOptions.mjs"}}, "./fp/eachQuarterOfInterval": {"require": {"types": "./fp/eachQuarterOfInterval.d.ts", "default": "./fp/eachQuarterOfInterval.js"}, "import": {"types": "./fp/eachQuarterOfInterval.d.mts", "default": "./fp/eachQuarterOfInterval.mjs"}}, "./fp/eachQuarterOfIntervalWithOptions": {"require": {"types": "./fp/eachQuarterOfIntervalWithOptions.d.ts", "default": "./fp/eachQuarterOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachQuarterOfIntervalWithOptions.d.mts", "default": "./fp/eachQuarterOfIntervalWithOptions.mjs"}}, "./fp/eachWeekOfInterval": {"require": {"types": "./fp/eachWeekOfInterval.d.ts", "default": "./fp/eachWeekOfInterval.js"}, "import": {"types": "./fp/eachWeekOfInterval.d.mts", "default": "./fp/eachWeekOfInterval.mjs"}}, "./fp/eachWeekOfIntervalWithOptions": {"require": {"types": "./fp/eachWeekOfIntervalWithOptions.d.ts", "default": "./fp/eachWeekOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachWeekOfIntervalWithOptions.d.mts", "default": "./fp/eachWeekOfIntervalWithOptions.mjs"}}, "./fp/eachWeekendOfInterval": {"require": {"types": "./fp/eachWeekendOfInterval.d.ts", "default": "./fp/eachWeekendOfInterval.js"}, "import": {"types": "./fp/eachWeekendOfInterval.d.mts", "default": "./fp/eachWeekendOfInterval.mjs"}}, "./fp/eachWeekendOfMonth": {"require": {"types": "./fp/eachWeekendOfMonth.d.ts", "default": "./fp/eachWeekendOfMonth.js"}, "import": {"types": "./fp/eachWeekendOfMonth.d.mts", "default": "./fp/eachWeekendOfMonth.mjs"}}, "./fp/eachWeekendOfYear": {"require": {"types": "./fp/eachWeekendOfYear.d.ts", "default": "./fp/eachWeekendOfYear.js"}, "import": {"types": "./fp/eachWeekendOfYear.d.mts", "default": "./fp/eachWeekendOfYear.mjs"}}, "./fp/eachYearOfInterval": {"require": {"types": "./fp/eachYearOfInterval.d.ts", "default": "./fp/eachYearOfInterval.js"}, "import": {"types": "./fp/eachYearOfInterval.d.mts", "default": "./fp/eachYearOfInterval.mjs"}}, "./fp/eachYearOfIntervalWithOptions": {"require": {"types": "./fp/eachYearOfIntervalWithOptions.d.ts", "default": "./fp/eachYearOfIntervalWithOptions.js"}, "import": {"types": "./fp/eachYearOfIntervalWithOptions.d.mts", "default": "./fp/eachYearOfIntervalWithOptions.mjs"}}, "./fp/endOfDay": {"require": {"types": "./fp/endOfDay.d.ts", "default": "./fp/endOfDay.js"}, "import": {"types": "./fp/endOfDay.d.mts", "default": "./fp/endOfDay.mjs"}}, "./fp/endOfDecade": {"require": {"types": "./fp/endOfDecade.d.ts", "default": "./fp/endOfDecade.js"}, "import": {"types": "./fp/endOfDecade.d.mts", "default": "./fp/endOfDecade.mjs"}}, "./fp/endOfHour": {"require": {"types": "./fp/endOfHour.d.ts", "default": "./fp/endOfHour.js"}, "import": {"types": "./fp/endOfHour.d.mts", "default": "./fp/endOfHour.mjs"}}, "./fp/endOfISOWeek": {"require": {"types": "./fp/endOfISOWeek.d.ts", "default": "./fp/endOfISOWeek.js"}, "import": {"types": "./fp/endOfISOWeek.d.mts", "default": "./fp/endOfISOWeek.mjs"}}, "./fp/endOfISOWeekYear": {"require": {"types": "./fp/endOfISOWeekYear.d.ts", "default": "./fp/endOfISOWeekYear.js"}, "import": {"types": "./fp/endOfISOWeekYear.d.mts", "default": "./fp/endOfISOWeekYear.mjs"}}, "./fp/endOfMinute": {"require": {"types": "./fp/endOfMinute.d.ts", "default": "./fp/endOfMinute.js"}, "import": {"types": "./fp/endOfMinute.d.mts", "default": "./fp/endOfMinute.mjs"}}, "./fp/endOfMonth": {"require": {"types": "./fp/endOfMonth.d.ts", "default": "./fp/endOfMonth.js"}, "import": {"types": "./fp/endOfMonth.d.mts", "default": "./fp/endOfMonth.mjs"}}, "./fp/endOfQuarter": {"require": {"types": "./fp/endOfQuarter.d.ts", "default": "./fp/endOfQuarter.js"}, "import": {"types": "./fp/endOfQuarter.d.mts", "default": "./fp/endOfQuarter.mjs"}}, "./fp/endOfSecond": {"require": {"types": "./fp/endOfSecond.d.ts", "default": "./fp/endOfSecond.js"}, "import": {"types": "./fp/endOfSecond.d.mts", "default": "./fp/endOfSecond.mjs"}}, "./fp/endOfWeek": {"require": {"types": "./fp/endOfWeek.d.ts", "default": "./fp/endOfWeek.js"}, "import": {"types": "./fp/endOfWeek.d.mts", "default": "./fp/endOfWeek.mjs"}}, "./fp/endOfWeekWithOptions": {"require": {"types": "./fp/endOfWeekWithOptions.d.ts", "default": "./fp/endOfWeekWithOptions.js"}, "import": {"types": "./fp/endOfWeekWithOptions.d.mts", "default": "./fp/endOfWeekWithOptions.mjs"}}, "./fp/endOfYear": {"require": {"types": "./fp/endOfYear.d.ts", "default": "./fp/endOfYear.js"}, "import": {"types": "./fp/endOfYear.d.mts", "default": "./fp/endOfYear.mjs"}}, "./fp/format": {"require": {"types": "./fp/format.d.ts", "default": "./fp/format.js"}, "import": {"types": "./fp/format.d.mts", "default": "./fp/format.mjs"}}, "./fp/formatDistance": {"require": {"types": "./fp/formatDistance.d.ts", "default": "./fp/formatDistance.js"}, "import": {"types": "./fp/formatDistance.d.mts", "default": "./fp/formatDistance.mjs"}}, "./fp/formatDistanceStrict": {"require": {"types": "./fp/formatDistanceStrict.d.ts", "default": "./fp/formatDistanceStrict.js"}, "import": {"types": "./fp/formatDistanceStrict.d.mts", "default": "./fp/formatDistanceStrict.mjs"}}, "./fp/formatDistanceStrictWithOptions": {"require": {"types": "./fp/formatDistanceStrictWithOptions.d.ts", "default": "./fp/formatDistanceStrictWithOptions.js"}, "import": {"types": "./fp/formatDistanceStrictWithOptions.d.mts", "default": "./fp/formatDistanceStrictWithOptions.mjs"}}, "./fp/formatDistanceWithOptions": {"require": {"types": "./fp/formatDistanceWithOptions.d.ts", "default": "./fp/formatDistanceWithOptions.js"}, "import": {"types": "./fp/formatDistanceWithOptions.d.mts", "default": "./fp/formatDistanceWithOptions.mjs"}}, "./fp/formatDuration": {"require": {"types": "./fp/formatDuration.d.ts", "default": "./fp/formatDuration.js"}, "import": {"types": "./fp/formatDuration.d.mts", "default": "./fp/formatDuration.mjs"}}, "./fp/formatDurationWithOptions": {"require": {"types": "./fp/formatDurationWithOptions.d.ts", "default": "./fp/formatDurationWithOptions.js"}, "import": {"types": "./fp/formatDurationWithOptions.d.mts", "default": "./fp/formatDurationWithOptions.mjs"}}, "./fp/formatISO": {"require": {"types": "./fp/formatISO.d.ts", "default": "./fp/formatISO.js"}, "import": {"types": "./fp/formatISO.d.mts", "default": "./fp/formatISO.mjs"}}, "./fp/formatISO9075": {"require": {"types": "./fp/formatISO9075.d.ts", "default": "./fp/formatISO9075.js"}, "import": {"types": "./fp/formatISO9075.d.mts", "default": "./fp/formatISO9075.mjs"}}, "./fp/formatISO9075WithOptions": {"require": {"types": "./fp/formatISO9075WithOptions.d.ts", "default": "./fp/formatISO9075WithOptions.js"}, "import": {"types": "./fp/formatISO9075WithOptions.d.mts", "default": "./fp/formatISO9075WithOptions.mjs"}}, "./fp/formatISODuration": {"require": {"types": "./fp/formatISODuration.d.ts", "default": "./fp/formatISODuration.js"}, "import": {"types": "./fp/formatISODuration.d.mts", "default": "./fp/formatISODuration.mjs"}}, "./fp/formatISOWithOptions": {"require": {"types": "./fp/formatISOWithOptions.d.ts", "default": "./fp/formatISOWithOptions.js"}, "import": {"types": "./fp/formatISOWithOptions.d.mts", "default": "./fp/formatISOWithOptions.mjs"}}, "./fp/formatRFC3339": {"require": {"types": "./fp/formatRFC3339.d.ts", "default": "./fp/formatRFC3339.js"}, "import": {"types": "./fp/formatRFC3339.d.mts", "default": "./fp/formatRFC3339.mjs"}}, "./fp/formatRFC3339WithOptions": {"require": {"types": "./fp/formatRFC3339WithOptions.d.ts", "default": "./fp/formatRFC3339WithOptions.js"}, "import": {"types": "./fp/formatRFC3339WithOptions.d.mts", "default": "./fp/formatRFC3339WithOptions.mjs"}}, "./fp/formatRFC7231": {"require": {"types": "./fp/formatRFC7231.d.ts", "default": "./fp/formatRFC7231.js"}, "import": {"types": "./fp/formatRFC7231.d.mts", "default": "./fp/formatRFC7231.mjs"}}, "./fp/formatRelative": {"require": {"types": "./fp/formatRelative.d.ts", "default": "./fp/formatRelative.js"}, "import": {"types": "./fp/formatRelative.d.mts", "default": "./fp/formatRelative.mjs"}}, "./fp/formatRelativeWithOptions": {"require": {"types": "./fp/formatRelativeWithOptions.d.ts", "default": "./fp/formatRelativeWithOptions.js"}, "import": {"types": "./fp/formatRelativeWithOptions.d.mts", "default": "./fp/formatRelativeWithOptions.mjs"}}, "./fp/formatWithOptions": {"require": {"types": "./fp/formatWithOptions.d.ts", "default": "./fp/formatWithOptions.js"}, "import": {"types": "./fp/formatWithOptions.d.mts", "default": "./fp/formatWithOptions.mjs"}}, "./fp/fromUnixTime": {"require": {"types": "./fp/fromUnixTime.d.ts", "default": "./fp/fromUnixTime.js"}, "import": {"types": "./fp/fromUnixTime.d.mts", "default": "./fp/fromUnixTime.mjs"}}, "./fp/getDate": {"require": {"types": "./fp/getDate.d.ts", "default": "./fp/getDate.js"}, "import": {"types": "./fp/getDate.d.mts", "default": "./fp/getDate.mjs"}}, "./fp/getDay": {"require": {"types": "./fp/getDay.d.ts", "default": "./fp/getDay.js"}, "import": {"types": "./fp/getDay.d.mts", "default": "./fp/getDay.mjs"}}, "./fp/getDayOfYear": {"require": {"types": "./fp/getDayOfYear.d.ts", "default": "./fp/getDayOfYear.js"}, "import": {"types": "./fp/getDayOfYear.d.mts", "default": "./fp/getDayOfYear.mjs"}}, "./fp/getDaysInMonth": {"require": {"types": "./fp/getDaysInMonth.d.ts", "default": "./fp/getDaysInMonth.js"}, "import": {"types": "./fp/getDaysInMonth.d.mts", "default": "./fp/getDaysInMonth.mjs"}}, "./fp/getDaysInYear": {"require": {"types": "./fp/getDaysInYear.d.ts", "default": "./fp/getDaysInYear.js"}, "import": {"types": "./fp/getDaysInYear.d.mts", "default": "./fp/getDaysInYear.mjs"}}, "./fp/getDecade": {"require": {"types": "./fp/getDecade.d.ts", "default": "./fp/getDecade.js"}, "import": {"types": "./fp/getDecade.d.mts", "default": "./fp/getDecade.mjs"}}, "./fp/getHours": {"require": {"types": "./fp/getHours.d.ts", "default": "./fp/getHours.js"}, "import": {"types": "./fp/getHours.d.mts", "default": "./fp/getHours.mjs"}}, "./fp/getISODay": {"require": {"types": "./fp/getISODay.d.ts", "default": "./fp/getISODay.js"}, "import": {"types": "./fp/getISODay.d.mts", "default": "./fp/getISODay.mjs"}}, "./fp/getISOWeek": {"require": {"types": "./fp/getISOWeek.d.ts", "default": "./fp/getISOWeek.js"}, "import": {"types": "./fp/getISOWeek.d.mts", "default": "./fp/getISOWeek.mjs"}}, "./fp/getISOWeekYear": {"require": {"types": "./fp/getISOWeekYear.d.ts", "default": "./fp/getISOWeekYear.js"}, "import": {"types": "./fp/getISOWeekYear.d.mts", "default": "./fp/getISOWeekYear.mjs"}}, "./fp/getISOWeeksInYear": {"require": {"types": "./fp/getISOWeeksInYear.d.ts", "default": "./fp/getISOWeeksInYear.js"}, "import": {"types": "./fp/getISOWeeksInYear.d.mts", "default": "./fp/getISOWeeksInYear.mjs"}}, "./fp/getMilliseconds": {"require": {"types": "./fp/getMilliseconds.d.ts", "default": "./fp/getMilliseconds.js"}, "import": {"types": "./fp/getMilliseconds.d.mts", "default": "./fp/getMilliseconds.mjs"}}, "./fp/getMinutes": {"require": {"types": "./fp/getMinutes.d.ts", "default": "./fp/getMinutes.js"}, "import": {"types": "./fp/getMinutes.d.mts", "default": "./fp/getMinutes.mjs"}}, "./fp/getMonth": {"require": {"types": "./fp/getMonth.d.ts", "default": "./fp/getMonth.js"}, "import": {"types": "./fp/getMonth.d.mts", "default": "./fp/getMonth.mjs"}}, "./fp/getOverlappingDaysInIntervals": {"require": {"types": "./fp/getOverlappingDaysInIntervals.d.ts", "default": "./fp/getOverlappingDaysInIntervals.js"}, "import": {"types": "./fp/getOverlappingDaysInIntervals.d.mts", "default": "./fp/getOverlappingDaysInIntervals.mjs"}}, "./fp/getQuarter": {"require": {"types": "./fp/getQuarter.d.ts", "default": "./fp/getQuarter.js"}, "import": {"types": "./fp/getQuarter.d.mts", "default": "./fp/getQuarter.mjs"}}, "./fp/getSeconds": {"require": {"types": "./fp/getSeconds.d.ts", "default": "./fp/getSeconds.js"}, "import": {"types": "./fp/getSeconds.d.mts", "default": "./fp/getSeconds.mjs"}}, "./fp/getTime": {"require": {"types": "./fp/getTime.d.ts", "default": "./fp/getTime.js"}, "import": {"types": "./fp/getTime.d.mts", "default": "./fp/getTime.mjs"}}, "./fp/getUnixTime": {"require": {"types": "./fp/getUnixTime.d.ts", "default": "./fp/getUnixTime.js"}, "import": {"types": "./fp/getUnixTime.d.mts", "default": "./fp/getUnixTime.mjs"}}, "./fp/getWeek": {"require": {"types": "./fp/getWeek.d.ts", "default": "./fp/getWeek.js"}, "import": {"types": "./fp/getWeek.d.mts", "default": "./fp/getWeek.mjs"}}, "./fp/getWeekOfMonth": {"require": {"types": "./fp/getWeekOfMonth.d.ts", "default": "./fp/getWeekOfMonth.js"}, "import": {"types": "./fp/getWeekOfMonth.d.mts", "default": "./fp/getWeekOfMonth.mjs"}}, "./fp/getWeekOfMonthWithOptions": {"require": {"types": "./fp/getWeekOfMonthWithOptions.d.ts", "default": "./fp/getWeekOfMonthWithOptions.js"}, "import": {"types": "./fp/getWeekOfMonthWithOptions.d.mts", "default": "./fp/getWeekOfMonthWithOptions.mjs"}}, "./fp/getWeekWithOptions": {"require": {"types": "./fp/getWeekWithOptions.d.ts", "default": "./fp/getWeekWithOptions.js"}, "import": {"types": "./fp/getWeekWithOptions.d.mts", "default": "./fp/getWeekWithOptions.mjs"}}, "./fp/getWeekYear": {"require": {"types": "./fp/getWeekYear.d.ts", "default": "./fp/getWeekYear.js"}, "import": {"types": "./fp/getWeekYear.d.mts", "default": "./fp/getWeekYear.mjs"}}, "./fp/getWeekYearWithOptions": {"require": {"types": "./fp/getWeekYearWithOptions.d.ts", "default": "./fp/getWeekYearWithOptions.js"}, "import": {"types": "./fp/getWeekYearWithOptions.d.mts", "default": "./fp/getWeekYearWithOptions.mjs"}}, "./fp/getWeeksInMonth": {"require": {"types": "./fp/getWeeksInMonth.d.ts", "default": "./fp/getWeeksInMonth.js"}, "import": {"types": "./fp/getWeeksInMonth.d.mts", "default": "./fp/getWeeksInMonth.mjs"}}, "./fp/getWeeksInMonthWithOptions": {"require": {"types": "./fp/getWeeksInMonthWithOptions.d.ts", "default": "./fp/getWeeksInMonthWithOptions.js"}, "import": {"types": "./fp/getWeeksInMonthWithOptions.d.mts", "default": "./fp/getWeeksInMonthWithOptions.mjs"}}, "./fp/getYear": {"require": {"types": "./fp/getYear.d.ts", "default": "./fp/getYear.js"}, "import": {"types": "./fp/getYear.d.mts", "default": "./fp/getYear.mjs"}}, "./fp/hoursToMilliseconds": {"require": {"types": "./fp/hoursToMilliseconds.d.ts", "default": "./fp/hoursToMilliseconds.js"}, "import": {"types": "./fp/hoursToMilliseconds.d.mts", "default": "./fp/hoursToMilliseconds.mjs"}}, "./fp/hoursToMinutes": {"require": {"types": "./fp/hoursToMinutes.d.ts", "default": "./fp/hoursToMinutes.js"}, "import": {"types": "./fp/hoursToMinutes.d.mts", "default": "./fp/hoursToMinutes.mjs"}}, "./fp/hoursToSeconds": {"require": {"types": "./fp/hoursToSeconds.d.ts", "default": "./fp/hoursToSeconds.js"}, "import": {"types": "./fp/hoursToSeconds.d.mts", "default": "./fp/hoursToSeconds.mjs"}}, "./fp/interval": {"require": {"types": "./fp/interval.d.ts", "default": "./fp/interval.js"}, "import": {"types": "./fp/interval.d.mts", "default": "./fp/interval.mjs"}}, "./fp/intervalToDuration": {"require": {"types": "./fp/intervalToDuration.d.ts", "default": "./fp/intervalToDuration.js"}, "import": {"types": "./fp/intervalToDuration.d.mts", "default": "./fp/intervalToDuration.mjs"}}, "./fp/intervalWithOptions": {"require": {"types": "./fp/intervalWithOptions.d.ts", "default": "./fp/intervalWithOptions.js"}, "import": {"types": "./fp/intervalWithOptions.d.mts", "default": "./fp/intervalWithOptions.mjs"}}, "./fp/intlFormat": {"require": {"types": "./fp/intlFormat.d.ts", "default": "./fp/intlFormat.js"}, "import": {"types": "./fp/intlFormat.d.mts", "default": "./fp/intlFormat.mjs"}}, "./fp/intlFormatDistance": {"require": {"types": "./fp/intlFormatDistance.d.ts", "default": "./fp/intlFormatDistance.js"}, "import": {"types": "./fp/intlFormatDistance.d.mts", "default": "./fp/intlFormatDistance.mjs"}}, "./fp/intlFormatDistanceWithOptions": {"require": {"types": "./fp/intlFormatDistanceWithOptions.d.ts", "default": "./fp/intlFormatDistanceWithOptions.js"}, "import": {"types": "./fp/intlFormatDistanceWithOptions.d.mts", "default": "./fp/intlFormatDistanceWithOptions.mjs"}}, "./fp/isAfter": {"require": {"types": "./fp/isAfter.d.ts", "default": "./fp/isAfter.js"}, "import": {"types": "./fp/isAfter.d.mts", "default": "./fp/isAfter.mjs"}}, "./fp/isBefore": {"require": {"types": "./fp/isBefore.d.ts", "default": "./fp/isBefore.js"}, "import": {"types": "./fp/isBefore.d.mts", "default": "./fp/isBefore.mjs"}}, "./fp/isDate": {"require": {"types": "./fp/isDate.d.ts", "default": "./fp/isDate.js"}, "import": {"types": "./fp/isDate.d.mts", "default": "./fp/isDate.mjs"}}, "./fp/isEqual": {"require": {"types": "./fp/isEqual.d.ts", "default": "./fp/isEqual.js"}, "import": {"types": "./fp/isEqual.d.mts", "default": "./fp/isEqual.mjs"}}, "./fp/isExists": {"require": {"types": "./fp/isExists.d.ts", "default": "./fp/isExists.js"}, "import": {"types": "./fp/isExists.d.mts", "default": "./fp/isExists.mjs"}}, "./fp/isFirstDayOfMonth": {"require": {"types": "./fp/isFirstDayOfMonth.d.ts", "default": "./fp/isFirstDayOfMonth.js"}, "import": {"types": "./fp/isFirstDayOfMonth.d.mts", "default": "./fp/isFirstDayOfMonth.mjs"}}, "./fp/isFriday": {"require": {"types": "./fp/isFriday.d.ts", "default": "./fp/isFriday.js"}, "import": {"types": "./fp/isFriday.d.mts", "default": "./fp/isFriday.mjs"}}, "./fp/isLastDayOfMonth": {"require": {"types": "./fp/isLastDayOfMonth.d.ts", "default": "./fp/isLastDayOfMonth.js"}, "import": {"types": "./fp/isLastDayOfMonth.d.mts", "default": "./fp/isLastDayOfMonth.mjs"}}, "./fp/isLeapYear": {"require": {"types": "./fp/isLeapYear.d.ts", "default": "./fp/isLeapYear.js"}, "import": {"types": "./fp/isLeapYear.d.mts", "default": "./fp/isLeapYear.mjs"}}, "./fp/isMatch": {"require": {"types": "./fp/isMatch.d.ts", "default": "./fp/isMatch.js"}, "import": {"types": "./fp/isMatch.d.mts", "default": "./fp/isMatch.mjs"}}, "./fp/isMatchWithOptions": {"require": {"types": "./fp/isMatchWithOptions.d.ts", "default": "./fp/isMatchWithOptions.js"}, "import": {"types": "./fp/isMatchWithOptions.d.mts", "default": "./fp/isMatchWithOptions.mjs"}}, "./fp/isMonday": {"require": {"types": "./fp/isMonday.d.ts", "default": "./fp/isMonday.js"}, "import": {"types": "./fp/isMonday.d.mts", "default": "./fp/isMonday.mjs"}}, "./fp/isSameDay": {"require": {"types": "./fp/isSameDay.d.ts", "default": "./fp/isSameDay.js"}, "import": {"types": "./fp/isSameDay.d.mts", "default": "./fp/isSameDay.mjs"}}, "./fp/isSameHour": {"require": {"types": "./fp/isSameHour.d.ts", "default": "./fp/isSameHour.js"}, "import": {"types": "./fp/isSameHour.d.mts", "default": "./fp/isSameHour.mjs"}}, "./fp/isSameISOWeek": {"require": {"types": "./fp/isSameISOWeek.d.ts", "default": "./fp/isSameISOWeek.js"}, "import": {"types": "./fp/isSameISOWeek.d.mts", "default": "./fp/isSameISOWeek.mjs"}}, "./fp/isSameISOWeekYear": {"require": {"types": "./fp/isSameISOWeekYear.d.ts", "default": "./fp/isSameISOWeekYear.js"}, "import": {"types": "./fp/isSameISOWeekYear.d.mts", "default": "./fp/isSameISOWeekYear.mjs"}}, "./fp/isSameMinute": {"require": {"types": "./fp/isSameMinute.d.ts", "default": "./fp/isSameMinute.js"}, "import": {"types": "./fp/isSameMinute.d.mts", "default": "./fp/isSameMinute.mjs"}}, "./fp/isSameMonth": {"require": {"types": "./fp/isSameMonth.d.ts", "default": "./fp/isSameMonth.js"}, "import": {"types": "./fp/isSameMonth.d.mts", "default": "./fp/isSameMonth.mjs"}}, "./fp/isSameQuarter": {"require": {"types": "./fp/isSameQuarter.d.ts", "default": "./fp/isSameQuarter.js"}, "import": {"types": "./fp/isSameQuarter.d.mts", "default": "./fp/isSameQuarter.mjs"}}, "./fp/isSameSecond": {"require": {"types": "./fp/isSameSecond.d.ts", "default": "./fp/isSameSecond.js"}, "import": {"types": "./fp/isSameSecond.d.mts", "default": "./fp/isSameSecond.mjs"}}, "./fp/isSameWeek": {"require": {"types": "./fp/isSameWeek.d.ts", "default": "./fp/isSameWeek.js"}, "import": {"types": "./fp/isSameWeek.d.mts", "default": "./fp/isSameWeek.mjs"}}, "./fp/isSameWeekWithOptions": {"require": {"types": "./fp/isSameWeekWithOptions.d.ts", "default": "./fp/isSameWeekWithOptions.js"}, "import": {"types": "./fp/isSameWeekWithOptions.d.mts", "default": "./fp/isSameWeekWithOptions.mjs"}}, "./fp/isSameYear": {"require": {"types": "./fp/isSameYear.d.ts", "default": "./fp/isSameYear.js"}, "import": {"types": "./fp/isSameYear.d.mts", "default": "./fp/isSameYear.mjs"}}, "./fp/isSaturday": {"require": {"types": "./fp/isSaturday.d.ts", "default": "./fp/isSaturday.js"}, "import": {"types": "./fp/isSaturday.d.mts", "default": "./fp/isSaturday.mjs"}}, "./fp/isSunday": {"require": {"types": "./fp/isSunday.d.ts", "default": "./fp/isSunday.js"}, "import": {"types": "./fp/isSunday.d.mts", "default": "./fp/isSunday.mjs"}}, "./fp/isThursday": {"require": {"types": "./fp/isThursday.d.ts", "default": "./fp/isThursday.js"}, "import": {"types": "./fp/isThursday.d.mts", "default": "./fp/isThursday.mjs"}}, "./fp/isTuesday": {"require": {"types": "./fp/isTuesday.d.ts", "default": "./fp/isTuesday.js"}, "import": {"types": "./fp/isTuesday.d.mts", "default": "./fp/isTuesday.mjs"}}, "./fp/isValid": {"require": {"types": "./fp/isValid.d.ts", "default": "./fp/isValid.js"}, "import": {"types": "./fp/isValid.d.mts", "default": "./fp/isValid.mjs"}}, "./fp/isWednesday": {"require": {"types": "./fp/isWednesday.d.ts", "default": "./fp/isWednesday.js"}, "import": {"types": "./fp/isWednesday.d.mts", "default": "./fp/isWednesday.mjs"}}, "./fp/isWeekend": {"require": {"types": "./fp/isWeekend.d.ts", "default": "./fp/isWeekend.js"}, "import": {"types": "./fp/isWeekend.d.mts", "default": "./fp/isWeekend.mjs"}}, "./fp/isWithinInterval": {"require": {"types": "./fp/isWithinInterval.d.ts", "default": "./fp/isWithinInterval.js"}, "import": {"types": "./fp/isWithinInterval.d.mts", "default": "./fp/isWithinInterval.mjs"}}, "./fp/lastDayOfDecade": {"require": {"types": "./fp/lastDayOfDecade.d.ts", "default": "./fp/lastDayOfDecade.js"}, "import": {"types": "./fp/lastDayOfDecade.d.mts", "default": "./fp/lastDayOfDecade.mjs"}}, "./fp/lastDayOfISOWeek": {"require": {"types": "./fp/lastDayOfISOWeek.d.ts", "default": "./fp/lastDayOfISOWeek.js"}, "import": {"types": "./fp/lastDayOfISOWeek.d.mts", "default": "./fp/lastDayOfISOWeek.mjs"}}, "./fp/lastDayOfISOWeekYear": {"require": {"types": "./fp/lastDayOfISOWeekYear.d.ts", "default": "./fp/lastDayOfISOWeekYear.js"}, "import": {"types": "./fp/lastDayOfISOWeekYear.d.mts", "default": "./fp/lastDayOfISOWeekYear.mjs"}}, "./fp/lastDayOfMonth": {"require": {"types": "./fp/lastDayOfMonth.d.ts", "default": "./fp/lastDayOfMonth.js"}, "import": {"types": "./fp/lastDayOfMonth.d.mts", "default": "./fp/lastDayOfMonth.mjs"}}, "./fp/lastDayOfQuarter": {"require": {"types": "./fp/lastDayOfQuarter.d.ts", "default": "./fp/lastDayOfQuarter.js"}, "import": {"types": "./fp/lastDayOfQuarter.d.mts", "default": "./fp/lastDayOfQuarter.mjs"}}, "./fp/lastDayOfWeek": {"require": {"types": "./fp/lastDayOfWeek.d.ts", "default": "./fp/lastDayOfWeek.js"}, "import": {"types": "./fp/lastDayOfWeek.d.mts", "default": "./fp/lastDayOfWeek.mjs"}}, "./fp/lastDayOfWeekWithOptions": {"require": {"types": "./fp/lastDayOfWeekWithOptions.d.ts", "default": "./fp/lastDayOfWeekWithOptions.js"}, "import": {"types": "./fp/lastDayOfWeekWithOptions.d.mts", "default": "./fp/lastDayOfWeekWithOptions.mjs"}}, "./fp/lastDayOfYear": {"require": {"types": "./fp/lastDayOfYear.d.ts", "default": "./fp/lastDayOfYear.js"}, "import": {"types": "./fp/lastDayOfYear.d.mts", "default": "./fp/lastDayOfYear.mjs"}}, "./fp/lightFormat": {"require": {"types": "./fp/lightFormat.d.ts", "default": "./fp/lightFormat.js"}, "import": {"types": "./fp/lightFormat.d.mts", "default": "./fp/lightFormat.mjs"}}, "./fp/max": {"require": {"types": "./fp/max.d.ts", "default": "./fp/max.js"}, "import": {"types": "./fp/max.d.mts", "default": "./fp/max.mjs"}}, "./fp/milliseconds": {"require": {"types": "./fp/milliseconds.d.ts", "default": "./fp/milliseconds.js"}, "import": {"types": "./fp/milliseconds.d.mts", "default": "./fp/milliseconds.mjs"}}, "./fp/millisecondsToHours": {"require": {"types": "./fp/millisecondsToHours.d.ts", "default": "./fp/millisecondsToHours.js"}, "import": {"types": "./fp/millisecondsToHours.d.mts", "default": "./fp/millisecondsToHours.mjs"}}, "./fp/millisecondsToMinutes": {"require": {"types": "./fp/millisecondsToMinutes.d.ts", "default": "./fp/millisecondsToMinutes.js"}, "import": {"types": "./fp/millisecondsToMinutes.d.mts", "default": "./fp/millisecondsToMinutes.mjs"}}, "./fp/millisecondsToSeconds": {"require": {"types": "./fp/millisecondsToSeconds.d.ts", "default": "./fp/millisecondsToSeconds.js"}, "import": {"types": "./fp/millisecondsToSeconds.d.mts", "default": "./fp/millisecondsToSeconds.mjs"}}, "./fp/min": {"require": {"types": "./fp/min.d.ts", "default": "./fp/min.js"}, "import": {"types": "./fp/min.d.mts", "default": "./fp/min.mjs"}}, "./fp/minutesToHours": {"require": {"types": "./fp/minutesToHours.d.ts", "default": "./fp/minutesToHours.js"}, "import": {"types": "./fp/minutesToHours.d.mts", "default": "./fp/minutesToHours.mjs"}}, "./fp/minutesToMilliseconds": {"require": {"types": "./fp/minutesToMilliseconds.d.ts", "default": "./fp/minutesToMilliseconds.js"}, "import": {"types": "./fp/minutesToMilliseconds.d.mts", "default": "./fp/minutesToMilliseconds.mjs"}}, "./fp/minutesToSeconds": {"require": {"types": "./fp/minutesToSeconds.d.ts", "default": "./fp/minutesToSeconds.js"}, "import": {"types": "./fp/minutesToSeconds.d.mts", "default": "./fp/minutesToSeconds.mjs"}}, "./fp/monthsToQuarters": {"require": {"types": "./fp/monthsToQuarters.d.ts", "default": "./fp/monthsToQuarters.js"}, "import": {"types": "./fp/monthsToQuarters.d.mts", "default": "./fp/monthsToQuarters.mjs"}}, "./fp/monthsToYears": {"require": {"types": "./fp/monthsToYears.d.ts", "default": "./fp/monthsToYears.js"}, "import": {"types": "./fp/monthsToYears.d.mts", "default": "./fp/monthsToYears.mjs"}}, "./fp/nextDay": {"require": {"types": "./fp/nextDay.d.ts", "default": "./fp/nextDay.js"}, "import": {"types": "./fp/nextDay.d.mts", "default": "./fp/nextDay.mjs"}}, "./fp/nextFriday": {"require": {"types": "./fp/nextFriday.d.ts", "default": "./fp/nextFriday.js"}, "import": {"types": "./fp/nextFriday.d.mts", "default": "./fp/nextFriday.mjs"}}, "./fp/nextMonday": {"require": {"types": "./fp/nextMonday.d.ts", "default": "./fp/nextMonday.js"}, "import": {"types": "./fp/nextMonday.d.mts", "default": "./fp/nextMonday.mjs"}}, "./fp/nextSaturday": {"require": {"types": "./fp/nextSaturday.d.ts", "default": "./fp/nextSaturday.js"}, "import": {"types": "./fp/nextSaturday.d.mts", "default": "./fp/nextSaturday.mjs"}}, "./fp/nextSunday": {"require": {"types": "./fp/nextSunday.d.ts", "default": "./fp/nextSunday.js"}, "import": {"types": "./fp/nextSunday.d.mts", "default": "./fp/nextSunday.mjs"}}, "./fp/nextThursday": {"require": {"types": "./fp/nextThursday.d.ts", "default": "./fp/nextThursday.js"}, "import": {"types": "./fp/nextThursday.d.mts", "default": "./fp/nextThursday.mjs"}}, "./fp/nextTuesday": {"require": {"types": "./fp/nextTuesday.d.ts", "default": "./fp/nextTuesday.js"}, "import": {"types": "./fp/nextTuesday.d.mts", "default": "./fp/nextTuesday.mjs"}}, "./fp/nextWednesday": {"require": {"types": "./fp/nextWednesday.d.ts", "default": "./fp/nextWednesday.js"}, "import": {"types": "./fp/nextWednesday.d.mts", "default": "./fp/nextWednesday.mjs"}}, "./fp/parse": {"require": {"types": "./fp/parse.d.ts", "default": "./fp/parse.js"}, "import": {"types": "./fp/parse.d.mts", "default": "./fp/parse.mjs"}}, "./fp/parseISO": {"require": {"types": "./fp/parseISO.d.ts", "default": "./fp/parseISO.js"}, "import": {"types": "./fp/parseISO.d.mts", "default": "./fp/parseISO.mjs"}}, "./fp/parseISOWithOptions": {"require": {"types": "./fp/parseISOWithOptions.d.ts", "default": "./fp/parseISOWithOptions.js"}, "import": {"types": "./fp/parseISOWithOptions.d.mts", "default": "./fp/parseISOWithOptions.mjs"}}, "./fp/parseJSON": {"require": {"types": "./fp/parseJSON.d.ts", "default": "./fp/parseJSON.js"}, "import": {"types": "./fp/parseJSON.d.mts", "default": "./fp/parseJSON.mjs"}}, "./fp/parseWithOptions": {"require": {"types": "./fp/parseWithOptions.d.ts", "default": "./fp/parseWithOptions.js"}, "import": {"types": "./fp/parseWithOptions.d.mts", "default": "./fp/parseWithOptions.mjs"}}, "./fp/previousDay": {"require": {"types": "./fp/previousDay.d.ts", "default": "./fp/previousDay.js"}, "import": {"types": "./fp/previousDay.d.mts", "default": "./fp/previousDay.mjs"}}, "./fp/previousFriday": {"require": {"types": "./fp/previousFriday.d.ts", "default": "./fp/previousFriday.js"}, "import": {"types": "./fp/previousFriday.d.mts", "default": "./fp/previousFriday.mjs"}}, "./fp/previousMonday": {"require": {"types": "./fp/previousMonday.d.ts", "default": "./fp/previousMonday.js"}, "import": {"types": "./fp/previousMonday.d.mts", "default": "./fp/previousMonday.mjs"}}, "./fp/previousSaturday": {"require": {"types": "./fp/previousSaturday.d.ts", "default": "./fp/previousSaturday.js"}, "import": {"types": "./fp/previousSaturday.d.mts", "default": "./fp/previousSaturday.mjs"}}, "./fp/previousSunday": {"require": {"types": "./fp/previousSunday.d.ts", "default": "./fp/previousSunday.js"}, "import": {"types": "./fp/previousSunday.d.mts", "default": "./fp/previousSunday.mjs"}}, "./fp/previousThursday": {"require": {"types": "./fp/previousThursday.d.ts", "default": "./fp/previousThursday.js"}, "import": {"types": "./fp/previousThursday.d.mts", "default": "./fp/previousThursday.mjs"}}, "./fp/previousTuesday": {"require": {"types": "./fp/previousTuesday.d.ts", "default": "./fp/previousTuesday.js"}, "import": {"types": "./fp/previousTuesday.d.mts", "default": "./fp/previousTuesday.mjs"}}, "./fp/previousWednesday": {"require": {"types": "./fp/previousWednesday.d.ts", "default": "./fp/previousWednesday.js"}, "import": {"types": "./fp/previousWednesday.d.mts", "default": "./fp/previousWednesday.mjs"}}, "./fp/quartersToMonths": {"require": {"types": "./fp/quartersToMonths.d.ts", "default": "./fp/quartersToMonths.js"}, "import": {"types": "./fp/quartersToMonths.d.mts", "default": "./fp/quartersToMonths.mjs"}}, "./fp/quartersToYears": {"require": {"types": "./fp/quartersToYears.d.ts", "default": "./fp/quartersToYears.js"}, "import": {"types": "./fp/quartersToYears.d.mts", "default": "./fp/quartersToYears.mjs"}}, "./fp/roundToNearestHours": {"require": {"types": "./fp/roundToNearestHours.d.ts", "default": "./fp/roundToNearestHours.js"}, "import": {"types": "./fp/roundToNearestHours.d.mts", "default": "./fp/roundToNearestHours.mjs"}}, "./fp/roundToNearestHoursWithOptions": {"require": {"types": "./fp/roundToNearestHoursWithOptions.d.ts", "default": "./fp/roundToNearestHoursWithOptions.js"}, "import": {"types": "./fp/roundToNearestHoursWithOptions.d.mts", "default": "./fp/roundToNearestHoursWithOptions.mjs"}}, "./fp/roundToNearestMinutes": {"require": {"types": "./fp/roundToNearestMinutes.d.ts", "default": "./fp/roundToNearestMinutes.js"}, "import": {"types": "./fp/roundToNearestMinutes.d.mts", "default": "./fp/roundToNearestMinutes.mjs"}}, "./fp/roundToNearestMinutesWithOptions": {"require": {"types": "./fp/roundToNearestMinutesWithOptions.d.ts", "default": "./fp/roundToNearestMinutesWithOptions.js"}, "import": {"types": "./fp/roundToNearestMinutesWithOptions.d.mts", "default": "./fp/roundToNearestMinutesWithOptions.mjs"}}, "./fp/secondsToHours": {"require": {"types": "./fp/secondsToHours.d.ts", "default": "./fp/secondsToHours.js"}, "import": {"types": "./fp/secondsToHours.d.mts", "default": "./fp/secondsToHours.mjs"}}, "./fp/secondsToMilliseconds": {"require": {"types": "./fp/secondsToMilliseconds.d.ts", "default": "./fp/secondsToMilliseconds.js"}, "import": {"types": "./fp/secondsToMilliseconds.d.mts", "default": "./fp/secondsToMilliseconds.mjs"}}, "./fp/secondsToMinutes": {"require": {"types": "./fp/secondsToMinutes.d.ts", "default": "./fp/secondsToMinutes.js"}, "import": {"types": "./fp/secondsToMinutes.d.mts", "default": "./fp/secondsToMinutes.mjs"}}, "./fp/set": {"require": {"types": "./fp/set.d.ts", "default": "./fp/set.js"}, "import": {"types": "./fp/set.d.mts", "default": "./fp/set.mjs"}}, "./fp/setDate": {"require": {"types": "./fp/setDate.d.ts", "default": "./fp/setDate.js"}, "import": {"types": "./fp/setDate.d.mts", "default": "./fp/setDate.mjs"}}, "./fp/setDay": {"require": {"types": "./fp/setDay.d.ts", "default": "./fp/setDay.js"}, "import": {"types": "./fp/setDay.d.mts", "default": "./fp/setDay.mjs"}}, "./fp/setDayOfYear": {"require": {"types": "./fp/setDayOfYear.d.ts", "default": "./fp/setDayOfYear.js"}, "import": {"types": "./fp/setDayOfYear.d.mts", "default": "./fp/setDayOfYear.mjs"}}, "./fp/setDayWithOptions": {"require": {"types": "./fp/setDayWithOptions.d.ts", "default": "./fp/setDayWithOptions.js"}, "import": {"types": "./fp/setDayWithOptions.d.mts", "default": "./fp/setDayWithOptions.mjs"}}, "./fp/setHours": {"require": {"types": "./fp/setHours.d.ts", "default": "./fp/setHours.js"}, "import": {"types": "./fp/setHours.d.mts", "default": "./fp/setHours.mjs"}}, "./fp/setISODay": {"require": {"types": "./fp/setISODay.d.ts", "default": "./fp/setISODay.js"}, "import": {"types": "./fp/setISODay.d.mts", "default": "./fp/setISODay.mjs"}}, "./fp/setISOWeek": {"require": {"types": "./fp/setISOWeek.d.ts", "default": "./fp/setISOWeek.js"}, "import": {"types": "./fp/setISOWeek.d.mts", "default": "./fp/setISOWeek.mjs"}}, "./fp/setISOWeekYear": {"require": {"types": "./fp/setISOWeekYear.d.ts", "default": "./fp/setISOWeekYear.js"}, "import": {"types": "./fp/setISOWeekYear.d.mts", "default": "./fp/setISOWeekYear.mjs"}}, "./fp/setMilliseconds": {"require": {"types": "./fp/setMilliseconds.d.ts", "default": "./fp/setMilliseconds.js"}, "import": {"types": "./fp/setMilliseconds.d.mts", "default": "./fp/setMilliseconds.mjs"}}, "./fp/setMinutes": {"require": {"types": "./fp/setMinutes.d.ts", "default": "./fp/setMinutes.js"}, "import": {"types": "./fp/setMinutes.d.mts", "default": "./fp/setMinutes.mjs"}}, "./fp/setMonth": {"require": {"types": "./fp/setMonth.d.ts", "default": "./fp/setMonth.js"}, "import": {"types": "./fp/setMonth.d.mts", "default": "./fp/setMonth.mjs"}}, "./fp/setQuarter": {"require": {"types": "./fp/setQuarter.d.ts", "default": "./fp/setQuarter.js"}, "import": {"types": "./fp/setQuarter.d.mts", "default": "./fp/setQuarter.mjs"}}, "./fp/setSeconds": {"require": {"types": "./fp/setSeconds.d.ts", "default": "./fp/setSeconds.js"}, "import": {"types": "./fp/setSeconds.d.mts", "default": "./fp/setSeconds.mjs"}}, "./fp/setWeek": {"require": {"types": "./fp/setWeek.d.ts", "default": "./fp/setWeek.js"}, "import": {"types": "./fp/setWeek.d.mts", "default": "./fp/setWeek.mjs"}}, "./fp/setWeekWithOptions": {"require": {"types": "./fp/setWeekWithOptions.d.ts", "default": "./fp/setWeekWithOptions.js"}, "import": {"types": "./fp/setWeekWithOptions.d.mts", "default": "./fp/setWeekWithOptions.mjs"}}, "./fp/setWeekYear": {"require": {"types": "./fp/setWeekYear.d.ts", "default": "./fp/setWeekYear.js"}, "import": {"types": "./fp/setWeekYear.d.mts", "default": "./fp/setWeekYear.mjs"}}, "./fp/setWeekYearWithOptions": {"require": {"types": "./fp/setWeekYearWithOptions.d.ts", "default": "./fp/setWeekYearWithOptions.js"}, "import": {"types": "./fp/setWeekYearWithOptions.d.mts", "default": "./fp/setWeekYearWithOptions.mjs"}}, "./fp/setYear": {"require": {"types": "./fp/setYear.d.ts", "default": "./fp/setYear.js"}, "import": {"types": "./fp/setYear.d.mts", "default": "./fp/setYear.mjs"}}, "./fp/startOfDay": {"require": {"types": "./fp/startOfDay.d.ts", "default": "./fp/startOfDay.js"}, "import": {"types": "./fp/startOfDay.d.mts", "default": "./fp/startOfDay.mjs"}}, "./fp/startOfDecade": {"require": {"types": "./fp/startOfDecade.d.ts", "default": "./fp/startOfDecade.js"}, "import": {"types": "./fp/startOfDecade.d.mts", "default": "./fp/startOfDecade.mjs"}}, "./fp/startOfHour": {"require": {"types": "./fp/startOfHour.d.ts", "default": "./fp/startOfHour.js"}, "import": {"types": "./fp/startOfHour.d.mts", "default": "./fp/startOfHour.mjs"}}, "./fp/startOfISOWeek": {"require": {"types": "./fp/startOfISOWeek.d.ts", "default": "./fp/startOfISOWeek.js"}, "import": {"types": "./fp/startOfISOWeek.d.mts", "default": "./fp/startOfISOWeek.mjs"}}, "./fp/startOfISOWeekYear": {"require": {"types": "./fp/startOfISOWeekYear.d.ts", "default": "./fp/startOfISOWeekYear.js"}, "import": {"types": "./fp/startOfISOWeekYear.d.mts", "default": "./fp/startOfISOWeekYear.mjs"}}, "./fp/startOfMinute": {"require": {"types": "./fp/startOfMinute.d.ts", "default": "./fp/startOfMinute.js"}, "import": {"types": "./fp/startOfMinute.d.mts", "default": "./fp/startOfMinute.mjs"}}, "./fp/startOfMonth": {"require": {"types": "./fp/startOfMonth.d.ts", "default": "./fp/startOfMonth.js"}, "import": {"types": "./fp/startOfMonth.d.mts", "default": "./fp/startOfMonth.mjs"}}, "./fp/startOfQuarter": {"require": {"types": "./fp/startOfQuarter.d.ts", "default": "./fp/startOfQuarter.js"}, "import": {"types": "./fp/startOfQuarter.d.mts", "default": "./fp/startOfQuarter.mjs"}}, "./fp/startOfSecond": {"require": {"types": "./fp/startOfSecond.d.ts", "default": "./fp/startOfSecond.js"}, "import": {"types": "./fp/startOfSecond.d.mts", "default": "./fp/startOfSecond.mjs"}}, "./fp/startOfWeek": {"require": {"types": "./fp/startOfWeek.d.ts", "default": "./fp/startOfWeek.js"}, "import": {"types": "./fp/startOfWeek.d.mts", "default": "./fp/startOfWeek.mjs"}}, "./fp/startOfWeekWithOptions": {"require": {"types": "./fp/startOfWeekWithOptions.d.ts", "default": "./fp/startOfWeekWithOptions.js"}, "import": {"types": "./fp/startOfWeekWithOptions.d.mts", "default": "./fp/startOfWeekWithOptions.mjs"}}, "./fp/startOfWeekYear": {"require": {"types": "./fp/startOfWeekYear.d.ts", "default": "./fp/startOfWeekYear.js"}, "import": {"types": "./fp/startOfWeekYear.d.mts", "default": "./fp/startOfWeekYear.mjs"}}, "./fp/startOfWeekYearWithOptions": {"require": {"types": "./fp/startOfWeekYearWithOptions.d.ts", "default": "./fp/startOfWeekYearWithOptions.js"}, "import": {"types": "./fp/startOfWeekYearWithOptions.d.mts", "default": "./fp/startOfWeekYearWithOptions.mjs"}}, "./fp/startOfYear": {"require": {"types": "./fp/startOfYear.d.ts", "default": "./fp/startOfYear.js"}, "import": {"types": "./fp/startOfYear.d.mts", "default": "./fp/startOfYear.mjs"}}, "./fp/sub": {"require": {"types": "./fp/sub.d.ts", "default": "./fp/sub.js"}, "import": {"types": "./fp/sub.d.mts", "default": "./fp/sub.mjs"}}, "./fp/subBusinessDays": {"require": {"types": "./fp/subBusinessDays.d.ts", "default": "./fp/subBusinessDays.js"}, "import": {"types": "./fp/subBusinessDays.d.mts", "default": "./fp/subBusinessDays.mjs"}}, "./fp/subDays": {"require": {"types": "./fp/subDays.d.ts", "default": "./fp/subDays.js"}, "import": {"types": "./fp/subDays.d.mts", "default": "./fp/subDays.mjs"}}, "./fp/subHours": {"require": {"types": "./fp/subHours.d.ts", "default": "./fp/subHours.js"}, "import": {"types": "./fp/subHours.d.mts", "default": "./fp/subHours.mjs"}}, "./fp/subISOWeekYears": {"require": {"types": "./fp/subISOWeekYears.d.ts", "default": "./fp/subISOWeekYears.js"}, "import": {"types": "./fp/subISOWeekYears.d.mts", "default": "./fp/subISOWeekYears.mjs"}}, "./fp/subMilliseconds": {"require": {"types": "./fp/subMilliseconds.d.ts", "default": "./fp/subMilliseconds.js"}, "import": {"types": "./fp/subMilliseconds.d.mts", "default": "./fp/subMilliseconds.mjs"}}, "./fp/subMinutes": {"require": {"types": "./fp/subMinutes.d.ts", "default": "./fp/subMinutes.js"}, "import": {"types": "./fp/subMinutes.d.mts", "default": "./fp/subMinutes.mjs"}}, "./fp/subMonths": {"require": {"types": "./fp/subMonths.d.ts", "default": "./fp/subMonths.js"}, "import": {"types": "./fp/subMonths.d.mts", "default": "./fp/subMonths.mjs"}}, "./fp/subQuarters": {"require": {"types": "./fp/subQuarters.d.ts", "default": "./fp/subQuarters.js"}, "import": {"types": "./fp/subQuarters.d.mts", "default": "./fp/subQuarters.mjs"}}, "./fp/subSeconds": {"require": {"types": "./fp/subSeconds.d.ts", "default": "./fp/subSeconds.js"}, "import": {"types": "./fp/subSeconds.d.mts", "default": "./fp/subSeconds.mjs"}}, "./fp/subWeeks": {"require": {"types": "./fp/subWeeks.d.ts", "default": "./fp/subWeeks.js"}, "import": {"types": "./fp/subWeeks.d.mts", "default": "./fp/subWeeks.mjs"}}, "./fp/subYears": {"require": {"types": "./fp/subYears.d.ts", "default": "./fp/subYears.js"}, "import": {"types": "./fp/subYears.d.mts", "default": "./fp/subYears.mjs"}}, "./fp/toDate": {"require": {"types": "./fp/toDate.d.ts", "default": "./fp/toDate.js"}, "import": {"types": "./fp/toDate.d.mts", "default": "./fp/toDate.mjs"}}, "./fp/transpose": {"require": {"types": "./fp/transpose.d.ts", "default": "./fp/transpose.js"}, "import": {"types": "./fp/transpose.d.mts", "default": "./fp/transpose.mjs"}}, "./fp/weeksToDays": {"require": {"types": "./fp/weeksToDays.d.ts", "default": "./fp/weeksToDays.js"}, "import": {"types": "./fp/weeksToDays.d.mts", "default": "./fp/weeksToDays.mjs"}}, "./fp/yearsToDays": {"require": {"types": "./fp/yearsToDays.d.ts", "default": "./fp/yearsToDays.js"}, "import": {"types": "./fp/yearsToDays.d.mts", "default": "./fp/yearsToDays.mjs"}}, "./fp/yearsToMonths": {"require": {"types": "./fp/yearsToMonths.d.ts", "default": "./fp/yearsToMonths.js"}, "import": {"types": "./fp/yearsToMonths.d.mts", "default": "./fp/yearsToMonths.mjs"}}, "./fp/yearsToQuarters": {"require": {"types": "./fp/yearsToQuarters.d.ts", "default": "./fp/yearsToQuarters.js"}, "import": {"types": "./fp/yearsToQuarters.d.mts", "default": "./fp/yearsToQuarters.mjs"}}, "./locale/af": {"require": {"types": "./locale/af.d.ts", "default": "./locale/af.js"}, "import": {"types": "./locale/af.d.mts", "default": "./locale/af.mjs"}}, "./locale/ar": {"require": {"types": "./locale/ar.d.ts", "default": "./locale/ar.js"}, "import": {"types": "./locale/ar.d.mts", "default": "./locale/ar.mjs"}}, "./locale/ar-DZ": {"require": {"types": "./locale/ar-DZ.d.ts", "default": "./locale/ar-DZ.js"}, "import": {"types": "./locale/ar-DZ.d.mts", "default": "./locale/ar-DZ.mjs"}}, "./locale/ar-EG": {"require": {"types": "./locale/ar-EG.d.ts", "default": "./locale/ar-EG.js"}, "import": {"types": "./locale/ar-EG.d.mts", "default": "./locale/ar-EG.mjs"}}, "./locale/ar-MA": {"require": {"types": "./locale/ar-MA.d.ts", "default": "./locale/ar-MA.js"}, "import": {"types": "./locale/ar-MA.d.mts", "default": "./locale/ar-MA.mjs"}}, "./locale/ar-SA": {"require": {"types": "./locale/ar-SA.d.ts", "default": "./locale/ar-SA.js"}, "import": {"types": "./locale/ar-SA.d.mts", "default": "./locale/ar-SA.mjs"}}, "./locale/ar-TN": {"require": {"types": "./locale/ar-TN.d.ts", "default": "./locale/ar-TN.js"}, "import": {"types": "./locale/ar-TN.d.mts", "default": "./locale/ar-TN.mjs"}}, "./locale/az": {"require": {"types": "./locale/az.d.ts", "default": "./locale/az.js"}, "import": {"types": "./locale/az.d.mts", "default": "./locale/az.mjs"}}, "./locale/be": {"require": {"types": "./locale/be.d.ts", "default": "./locale/be.js"}, "import": {"types": "./locale/be.d.mts", "default": "./locale/be.mjs"}}, "./locale/be-tarask": {"require": {"types": "./locale/be-tarask.d.ts", "default": "./locale/be-tarask.js"}, "import": {"types": "./locale/be-tarask.d.mts", "default": "./locale/be-tarask.mjs"}}, "./locale/bg": {"require": {"types": "./locale/bg.d.ts", "default": "./locale/bg.js"}, "import": {"types": "./locale/bg.d.mts", "default": "./locale/bg.mjs"}}, "./locale/bn": {"require": {"types": "./locale/bn.d.ts", "default": "./locale/bn.js"}, "import": {"types": "./locale/bn.d.mts", "default": "./locale/bn.mjs"}}, "./locale/bs": {"require": {"types": "./locale/bs.d.ts", "default": "./locale/bs.js"}, "import": {"types": "./locale/bs.d.mts", "default": "./locale/bs.mjs"}}, "./locale/ca": {"require": {"types": "./locale/ca.d.ts", "default": "./locale/ca.js"}, "import": {"types": "./locale/ca.d.mts", "default": "./locale/ca.mjs"}}, "./locale/ckb": {"require": {"types": "./locale/ckb.d.ts", "default": "./locale/ckb.js"}, "import": {"types": "./locale/ckb.d.mts", "default": "./locale/ckb.mjs"}}, "./locale/cs": {"require": {"types": "./locale/cs.d.ts", "default": "./locale/cs.js"}, "import": {"types": "./locale/cs.d.mts", "default": "./locale/cs.mjs"}}, "./locale/cy": {"require": {"types": "./locale/cy.d.ts", "default": "./locale/cy.js"}, "import": {"types": "./locale/cy.d.mts", "default": "./locale/cy.mjs"}}, "./locale/da": {"require": {"types": "./locale/da.d.ts", "default": "./locale/da.js"}, "import": {"types": "./locale/da.d.mts", "default": "./locale/da.mjs"}}, "./locale/de": {"require": {"types": "./locale/de.d.ts", "default": "./locale/de.js"}, "import": {"types": "./locale/de.d.mts", "default": "./locale/de.mjs"}}, "./locale/de-AT": {"require": {"types": "./locale/de-AT.d.ts", "default": "./locale/de-AT.js"}, "import": {"types": "./locale/de-AT.d.mts", "default": "./locale/de-AT.mjs"}}, "./locale/el": {"require": {"types": "./locale/el.d.ts", "default": "./locale/el.js"}, "import": {"types": "./locale/el.d.mts", "default": "./locale/el.mjs"}}, "./locale/en-AU": {"require": {"types": "./locale/en-AU.d.ts", "default": "./locale/en-AU.js"}, "import": {"types": "./locale/en-AU.d.mts", "default": "./locale/en-AU.mjs"}}, "./locale/en-CA": {"require": {"types": "./locale/en-CA.d.ts", "default": "./locale/en-CA.js"}, "import": {"types": "./locale/en-CA.d.mts", "default": "./locale/en-CA.mjs"}}, "./locale/en-GB": {"require": {"types": "./locale/en-GB.d.ts", "default": "./locale/en-GB.js"}, "import": {"types": "./locale/en-GB.d.mts", "default": "./locale/en-GB.mjs"}}, "./locale/en-IE": {"require": {"types": "./locale/en-IE.d.ts", "default": "./locale/en-IE.js"}, "import": {"types": "./locale/en-IE.d.mts", "default": "./locale/en-IE.mjs"}}, "./locale/en-IN": {"require": {"types": "./locale/en-IN.d.ts", "default": "./locale/en-IN.js"}, "import": {"types": "./locale/en-IN.d.mts", "default": "./locale/en-IN.mjs"}}, "./locale/en-NZ": {"require": {"types": "./locale/en-NZ.d.ts", "default": "./locale/en-NZ.js"}, "import": {"types": "./locale/en-NZ.d.mts", "default": "./locale/en-NZ.mjs"}}, "./locale/en-US": {"require": {"types": "./locale/en-US.d.ts", "default": "./locale/en-US.js"}, "import": {"types": "./locale/en-US.d.mts", "default": "./locale/en-US.mjs"}}, "./locale/en-ZA": {"require": {"types": "./locale/en-ZA.d.ts", "default": "./locale/en-ZA.js"}, "import": {"types": "./locale/en-ZA.d.mts", "default": "./locale/en-ZA.mjs"}}, "./locale/eo": {"require": {"types": "./locale/eo.d.ts", "default": "./locale/eo.js"}, "import": {"types": "./locale/eo.d.mts", "default": "./locale/eo.mjs"}}, "./locale/es": {"require": {"types": "./locale/es.d.ts", "default": "./locale/es.js"}, "import": {"types": "./locale/es.d.mts", "default": "./locale/es.mjs"}}, "./locale/et": {"require": {"types": "./locale/et.d.ts", "default": "./locale/et.js"}, "import": {"types": "./locale/et.d.mts", "default": "./locale/et.mjs"}}, "./locale/eu": {"require": {"types": "./locale/eu.d.ts", "default": "./locale/eu.js"}, "import": {"types": "./locale/eu.d.mts", "default": "./locale/eu.mjs"}}, "./locale/fa-IR": {"require": {"types": "./locale/fa-IR.d.ts", "default": "./locale/fa-IR.js"}, "import": {"types": "./locale/fa-IR.d.mts", "default": "./locale/fa-IR.mjs"}}, "./locale/fi": {"require": {"types": "./locale/fi.d.ts", "default": "./locale/fi.js"}, "import": {"types": "./locale/fi.d.mts", "default": "./locale/fi.mjs"}}, "./locale/fr": {"require": {"types": "./locale/fr.d.ts", "default": "./locale/fr.js"}, "import": {"types": "./locale/fr.d.mts", "default": "./locale/fr.mjs"}}, "./locale/fr-CA": {"require": {"types": "./locale/fr-CA.d.ts", "default": "./locale/fr-CA.js"}, "import": {"types": "./locale/fr-CA.d.mts", "default": "./locale/fr-CA.mjs"}}, "./locale/fr-CH": {"require": {"types": "./locale/fr-CH.d.ts", "default": "./locale/fr-CH.js"}, "import": {"types": "./locale/fr-CH.d.mts", "default": "./locale/fr-CH.mjs"}}, "./locale/fy": {"require": {"types": "./locale/fy.d.ts", "default": "./locale/fy.js"}, "import": {"types": "./locale/fy.d.mts", "default": "./locale/fy.mjs"}}, "./locale/gd": {"require": {"types": "./locale/gd.d.ts", "default": "./locale/gd.js"}, "import": {"types": "./locale/gd.d.mts", "default": "./locale/gd.mjs"}}, "./locale/gl": {"require": {"types": "./locale/gl.d.ts", "default": "./locale/gl.js"}, "import": {"types": "./locale/gl.d.mts", "default": "./locale/gl.mjs"}}, "./locale/gu": {"require": {"types": "./locale/gu.d.ts", "default": "./locale/gu.js"}, "import": {"types": "./locale/gu.d.mts", "default": "./locale/gu.mjs"}}, "./locale/he": {"require": {"types": "./locale/he.d.ts", "default": "./locale/he.js"}, "import": {"types": "./locale/he.d.mts", "default": "./locale/he.mjs"}}, "./locale/hi": {"require": {"types": "./locale/hi.d.ts", "default": "./locale/hi.js"}, "import": {"types": "./locale/hi.d.mts", "default": "./locale/hi.mjs"}}, "./locale/hr": {"require": {"types": "./locale/hr.d.ts", "default": "./locale/hr.js"}, "import": {"types": "./locale/hr.d.mts", "default": "./locale/hr.mjs"}}, "./locale/ht": {"require": {"types": "./locale/ht.d.ts", "default": "./locale/ht.js"}, "import": {"types": "./locale/ht.d.mts", "default": "./locale/ht.mjs"}}, "./locale/hu": {"require": {"types": "./locale/hu.d.ts", "default": "./locale/hu.js"}, "import": {"types": "./locale/hu.d.mts", "default": "./locale/hu.mjs"}}, "./locale/hy": {"require": {"types": "./locale/hy.d.ts", "default": "./locale/hy.js"}, "import": {"types": "./locale/hy.d.mts", "default": "./locale/hy.mjs"}}, "./locale/id": {"require": {"types": "./locale/id.d.ts", "default": "./locale/id.js"}, "import": {"types": "./locale/id.d.mts", "default": "./locale/id.mjs"}}, "./locale/is": {"require": {"types": "./locale/is.d.ts", "default": "./locale/is.js"}, "import": {"types": "./locale/is.d.mts", "default": "./locale/is.mjs"}}, "./locale/it": {"require": {"types": "./locale/it.d.ts", "default": "./locale/it.js"}, "import": {"types": "./locale/it.d.mts", "default": "./locale/it.mjs"}}, "./locale/it-CH": {"require": {"types": "./locale/it-CH.d.ts", "default": "./locale/it-CH.js"}, "import": {"types": "./locale/it-CH.d.mts", "default": "./locale/it-CH.mjs"}}, "./locale/ja": {"require": {"types": "./locale/ja.d.ts", "default": "./locale/ja.js"}, "import": {"types": "./locale/ja.d.mts", "default": "./locale/ja.mjs"}}, "./locale/ja-Hira": {"require": {"types": "./locale/ja-Hira.d.ts", "default": "./locale/ja-Hira.js"}, "import": {"types": "./locale/ja-Hira.d.mts", "default": "./locale/ja-Hira.mjs"}}, "./locale/ka": {"require": {"types": "./locale/ka.d.ts", "default": "./locale/ka.js"}, "import": {"types": "./locale/ka.d.mts", "default": "./locale/ka.mjs"}}, "./locale/kk": {"require": {"types": "./locale/kk.d.ts", "default": "./locale/kk.js"}, "import": {"types": "./locale/kk.d.mts", "default": "./locale/kk.mjs"}}, "./locale/km": {"require": {"types": "./locale/km.d.ts", "default": "./locale/km.js"}, "import": {"types": "./locale/km.d.mts", "default": "./locale/km.mjs"}}, "./locale/kn": {"require": {"types": "./locale/kn.d.ts", "default": "./locale/kn.js"}, "import": {"types": "./locale/kn.d.mts", "default": "./locale/kn.mjs"}}, "./locale/ko": {"require": {"types": "./locale/ko.d.ts", "default": "./locale/ko.js"}, "import": {"types": "./locale/ko.d.mts", "default": "./locale/ko.mjs"}}, "./locale/lb": {"require": {"types": "./locale/lb.d.ts", "default": "./locale/lb.js"}, "import": {"types": "./locale/lb.d.mts", "default": "./locale/lb.mjs"}}, "./locale/lt": {"require": {"types": "./locale/lt.d.ts", "default": "./locale/lt.js"}, "import": {"types": "./locale/lt.d.mts", "default": "./locale/lt.mjs"}}, "./locale/lv": {"require": {"types": "./locale/lv.d.ts", "default": "./locale/lv.js"}, "import": {"types": "./locale/lv.d.mts", "default": "./locale/lv.mjs"}}, "./locale/mk": {"require": {"types": "./locale/mk.d.ts", "default": "./locale/mk.js"}, "import": {"types": "./locale/mk.d.mts", "default": "./locale/mk.mjs"}}, "./locale/mn": {"require": {"types": "./locale/mn.d.ts", "default": "./locale/mn.js"}, "import": {"types": "./locale/mn.d.mts", "default": "./locale/mn.mjs"}}, "./locale/ms": {"require": {"types": "./locale/ms.d.ts", "default": "./locale/ms.js"}, "import": {"types": "./locale/ms.d.mts", "default": "./locale/ms.mjs"}}, "./locale/mt": {"require": {"types": "./locale/mt.d.ts", "default": "./locale/mt.js"}, "import": {"types": "./locale/mt.d.mts", "default": "./locale/mt.mjs"}}, "./locale/nb": {"require": {"types": "./locale/nb.d.ts", "default": "./locale/nb.js"}, "import": {"types": "./locale/nb.d.mts", "default": "./locale/nb.mjs"}}, "./locale/nl": {"require": {"types": "./locale/nl.d.ts", "default": "./locale/nl.js"}, "import": {"types": "./locale/nl.d.mts", "default": "./locale/nl.mjs"}}, "./locale/nl-BE": {"require": {"types": "./locale/nl-BE.d.ts", "default": "./locale/nl-BE.js"}, "import": {"types": "./locale/nl-BE.d.mts", "default": "./locale/nl-BE.mjs"}}, "./locale/nn": {"require": {"types": "./locale/nn.d.ts", "default": "./locale/nn.js"}, "import": {"types": "./locale/nn.d.mts", "default": "./locale/nn.mjs"}}, "./locale/oc": {"require": {"types": "./locale/oc.d.ts", "default": "./locale/oc.js"}, "import": {"types": "./locale/oc.d.mts", "default": "./locale/oc.mjs"}}, "./locale/pl": {"require": {"types": "./locale/pl.d.ts", "default": "./locale/pl.js"}, "import": {"types": "./locale/pl.d.mts", "default": "./locale/pl.mjs"}}, "./locale/pt": {"require": {"types": "./locale/pt.d.ts", "default": "./locale/pt.js"}, "import": {"types": "./locale/pt.d.mts", "default": "./locale/pt.mjs"}}, "./locale/pt-BR": {"require": {"types": "./locale/pt-BR.d.ts", "default": "./locale/pt-BR.js"}, "import": {"types": "./locale/pt-BR.d.mts", "default": "./locale/pt-BR.mjs"}}, "./locale/ro": {"require": {"types": "./locale/ro.d.ts", "default": "./locale/ro.js"}, "import": {"types": "./locale/ro.d.mts", "default": "./locale/ro.mjs"}}, "./locale/ru": {"require": {"types": "./locale/ru.d.ts", "default": "./locale/ru.js"}, "import": {"types": "./locale/ru.d.mts", "default": "./locale/ru.mjs"}}, "./locale/se": {"require": {"types": "./locale/se.d.ts", "default": "./locale/se.js"}, "import": {"types": "./locale/se.d.mts", "default": "./locale/se.mjs"}}, "./locale/sk": {"require": {"types": "./locale/sk.d.ts", "default": "./locale/sk.js"}, "import": {"types": "./locale/sk.d.mts", "default": "./locale/sk.mjs"}}, "./locale/sl": {"require": {"types": "./locale/sl.d.ts", "default": "./locale/sl.js"}, "import": {"types": "./locale/sl.d.mts", "default": "./locale/sl.mjs"}}, "./locale/sq": {"require": {"types": "./locale/sq.d.ts", "default": "./locale/sq.js"}, "import": {"types": "./locale/sq.d.mts", "default": "./locale/sq.mjs"}}, "./locale/sr": {"require": {"types": "./locale/sr.d.ts", "default": "./locale/sr.js"}, "import": {"types": "./locale/sr.d.mts", "default": "./locale/sr.mjs"}}, "./locale/sr-Latn": {"require": {"types": "./locale/sr-Latn.d.ts", "default": "./locale/sr-Latn.js"}, "import": {"types": "./locale/sr-Latn.d.mts", "default": "./locale/sr-Latn.mjs"}}, "./locale/sv": {"require": {"types": "./locale/sv.d.ts", "default": "./locale/sv.js"}, "import": {"types": "./locale/sv.d.mts", "default": "./locale/sv.mjs"}}, "./locale/ta": {"require": {"types": "./locale/ta.d.ts", "default": "./locale/ta.js"}, "import": {"types": "./locale/ta.d.mts", "default": "./locale/ta.mjs"}}, "./locale/te": {"require": {"types": "./locale/te.d.ts", "default": "./locale/te.js"}, "import": {"types": "./locale/te.d.mts", "default": "./locale/te.mjs"}}, "./locale/th": {"require": {"types": "./locale/th.d.ts", "default": "./locale/th.js"}, "import": {"types": "./locale/th.d.mts", "default": "./locale/th.mjs"}}, "./locale/tr": {"require": {"types": "./locale/tr.d.ts", "default": "./locale/tr.js"}, "import": {"types": "./locale/tr.d.mts", "default": "./locale/tr.mjs"}}, "./locale/ug": {"require": {"types": "./locale/ug.d.ts", "default": "./locale/ug.js"}, "import": {"types": "./locale/ug.d.mts", "default": "./locale/ug.mjs"}}, "./locale/uk": {"require": {"types": "./locale/uk.d.ts", "default": "./locale/uk.js"}, "import": {"types": "./locale/uk.d.mts", "default": "./locale/uk.mjs"}}, "./locale/uz": {"require": {"types": "./locale/uz.d.ts", "default": "./locale/uz.js"}, "import": {"types": "./locale/uz.d.mts", "default": "./locale/uz.mjs"}}, "./locale/uz-Cyrl": {"require": {"types": "./locale/uz-Cyrl.d.ts", "default": "./locale/uz-Cyrl.js"}, "import": {"types": "./locale/uz-Cyrl.d.mts", "default": "./locale/uz-Cyrl.mjs"}}, "./locale/vi": {"require": {"types": "./locale/vi.d.ts", "default": "./locale/vi.js"}, "import": {"types": "./locale/vi.d.mts", "default": "./locale/vi.mjs"}}, "./locale/zh-CN": {"require": {"types": "./locale/zh-CN.d.ts", "default": "./locale/zh-CN.js"}, "import": {"types": "./locale/zh-CN.d.mts", "default": "./locale/zh-CN.mjs"}}, "./locale/zh-HK": {"require": {"types": "./locale/zh-HK.d.ts", "default": "./locale/zh-HK.js"}, "import": {"types": "./locale/zh-HK.d.mts", "default": "./locale/zh-HK.mjs"}}, "./locale/zh-TW": {"require": {"types": "./locale/zh-TW.d.ts", "default": "./locale/zh-TW.js"}, "import": {"types": "./locale/zh-TW.d.mts", "default": "./locale/zh-TW.mjs"}}}, "scripts": {"test": "vitest run", "lint": "eslint .", "types": "tsc --noEmit", "locale-snapshots": "env TZ=utc tsx ./scripts/build/localeSnapshots/index.ts", "stats": "cloc . --exclude-dir=node_modules,tmp,.git,lib"}, "devDependencies": {"@babel/cli": "^7.22.10", "@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/preset-typescript": "^7.22.5", "@date-fns/docs": "^0.29.0", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@size-limit/esbuild": "^11.0.1", "@size-limit/file": "^11.0.1", "@types/bun": "^1.0.3", "@types/lodash": "^4.14.202", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitest/browser": "^1.3.1", "@vitest/coverage-v8": "^1.3.1", "babel-plugin-add-import-extension": "^1.6.0", "bun": "^1.0.25", "cloc": "^2.2.0", "coveralls": "^3.1.1", "eslint": "^8.55.0", "firebase": "^3.7.1", "fp-ts": "^2.16.2", "js-fns": "^2.5.1", "jscodeshift": "^0.15.2", "lodash": "^4.17.21", "playwright": "^1.40.1", "prettier": "^3.1.0", "simple-git": "^2.35.2", "sinon": "^7.4.1", "size-limit": "^11.0.1", "tsx": "^4.6.1", "typedoc": "^0.25.4", "typedoc-plugin-missing-exports": "^2.1.0", "typescript": "^5.3.2", "vitest": "^1.3.1"}}