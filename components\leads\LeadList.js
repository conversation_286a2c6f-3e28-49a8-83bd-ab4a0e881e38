function LeadList({ onLeadClick, onDeleteLead, selectedLeads = [], onLeadSelect }) {
    try {
        const [leads, setLeads] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [selectedPriority, setSelectedPriority] = React.useState('');
        const [selectedSource, setSelectedSource] = React.useState('');
        const [currentPage, setCurrentPage] = React.useState(1);
        const [pageSize, setPageSize] = React.useState(20);
        const [totalCount, setTotalCount] = React.useState(0);
        const [sortField, setSortField] = React.useState('createdAt');
        const [sortDirection, setSortDirection] = React.useState('desc');

        React.useEffect(() => {
            fetchLeads();
        }, [currentPage, pageSize, sortField, sortDirection]);

        // Debounced search effect
        React.useEffect(() => {
            const timeoutId = setTimeout(() => {
                if (currentPage !== 1) {
                    setCurrentPage(1);
                } else {
                    fetchLeads();
                }
            }, 300);

            return () => clearTimeout(timeoutId);
        }, [searchQuery, selectedStatus, selectedPriority, selectedSource]);

        const fetchLeads = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');

                // Build query parameters
                const params = new URLSearchParams({
                    page: currentPage.toString(),
                    limit: pageSize.toString(),
                    sort: sortField,
                    order: sortDirection
                });

                if (searchQuery) params.append('search', searchQuery);
                if (selectedStatus) params.append('status', selectedStatus);
                if (selectedPriority) params.append('priority', selectedPriority);
                if (selectedSource) params.append('source', selectedSource);

                const response = await fetch(window.getApiUrl(`/lead?${params.toString()}`), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setLeads(data.items || []);
                    setTotalCount(data.total || data.items?.length || 0);
                } else {
                    throw new Error('Failed to fetch leads');
                }
            } catch (error) {
                console.error('Error fetching leads:', error);
                setLeads([]);
                setTotalCount(0);
            } finally {
                setLoading(false);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters && filters.status ? filters.status : '');
            setSelectedPriority(filters && filters.priority ? filters.priority : '');
            setSelectedSource(filters && filters.source ? filters.source : '');
            setCurrentPage(1);
        };

        const handleSort = (field) => {
            if (sortField === field) {
                setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
            } else {
                setSortField(field);
                setSortDirection('asc');
            }
        };

        const handleSelectAll = (checked) => {
            if (checked) {
                const allLeadIds = leads.map(lead => lead.objectId);
                onLeadSelect?.(allLeadIds);
            } else {
                onLeadSelect?.([]);
            }
        };

        const handleSelectLead = (leadId, checked) => {
            if (checked) {
                onLeadSelect?.([...selectedLeads, leadId]);
            } else {
                onLeadSelect?.(selectedLeads.filter(id => id !== leadId));
            }
        };

        const isAllSelected = leads.length > 0 && leads.every(lead => selectedLeads.includes(lead.objectId));
        const isPartiallySelected = selectedLeads.length > 0 && !isAllSelected;

        const filteredLeads = React.useMemo(() => {
            return leads.filter(lead => {
                const matchesSearch = !searchQuery ||
                    lead.objectData.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (lead.objectData.email && lead.objectData.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (lead.objectData.company && lead.objectData.company.toLowerCase().includes(searchQuery.toLowerCase()));
                
                const matchesStatus = !selectedStatus || lead.objectData.status === selectedStatus;
                const matchesPriority = !selectedPriority || lead.objectData.priority === selectedPriority;
                const matchesSource = !selectedSource || lead.objectData.source === selectedSource;

                return matchesSearch && matchesStatus && matchesPriority && matchesSource;
            });
        }, [leads, searchQuery, selectedStatus, selectedPriority, selectedSource]);

        const leadFilters = [
            {
                id: 'status',
                label: 'Status',
                type: 'select',
                options: [
                    { label: 'New', value: 'new' },
                    { label: 'Contacted', value: 'contacted' },
                    { label: 'Qualified', value: 'qualified' },
                    { label: 'Proposal', value: 'proposal' },
                    { label: 'Negotiation', value: 'negotiation' },
                    { label: 'Won', value: 'won' },
                    { label: 'Lost', value: 'lost' }
                ]
            },
            {
                id: 'priority',
                label: 'Priority',
                type: 'select',
                options: [
                    { label: 'High', value: 'high' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Low', value: 'low' }
                ]
            },
            {
                id: 'source',
                label: 'Source',
                type: 'select',
                options: [
                    { label: 'Website', value: 'website' },
                    { label: 'Referral', value: 'referral' },
                    { label: 'Social Media', value: 'social' },
                    { label: 'Email Campaign', value: 'email' },
                    { label: 'Event', value: 'event' },
                    { label: 'Other', value: 'other' }
                ]
            }
        ];

        const columns = [
            {
                key: 'select',
                label: (
                    <input
                        type="checkbox"
                        checked={isAllSelected}
                        ref={checkbox => {
                            if (checkbox) checkbox.indeterminate = isPartiallySelected;
                        }}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                ),
                render: (row) => (
                    <input
                        type="checkbox"
                        checked={selectedLeads.includes(row.objectId)}
                        onChange={(e) => handleSelectLead(row.objectId, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        onClick={(e) => e.stopPropagation()}
                    />
                ),
                width: '50px'
            },
            {
                key: 'name',
                label: (
                    <button
                        onClick={() => handleSort('name')}
                        className="flex items-center space-x-1 hover:text-blue-600"
                    >
                        <span>Name</span>
                        {sortField === 'name' && (
                            <i className={`fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'} text-xs`}></i>
                        )}
                    </button>
                ),
                render: (row) => (
                    <div>
                        <div className="font-medium text-gray-900">{row.objectData.name}</div>
                        {row.objectData.email && (
                            <div className="text-sm text-gray-500">{row.objectData.email}</div>
                        )}
                    </div>
                )
            },
            { 
                key: 'company', 
                label: 'Company',
                render: (row) => row.objectData.company || '-'
            },
            { 
                key: 'value', 
                label: 'Value',
                render: (row) => formatCurrency(row.objectData.value)
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.objectData.status === 'won' ? 'bg-green-100 text-green-800' :
                        row.objectData.status === 'lost' ? 'bg-red-100 text-red-800' :
                        row.objectData.status === 'qualified' ? 'bg-blue-100 text-blue-800' :
                        row.objectData.status === 'proposal' ? 'bg-purple-100 text-purple-800' :
                        row.objectData.status === 'negotiation' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                    }`}>
                        {row.objectData.status ? row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1) : 'Unknown'}
                    </span>
                )
            },
            {
                key: 'priority',
                label: 'Priority',
                render: (row) => (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.objectData.priority === 'high' ? 'bg-red-100 text-red-800' :
                        row.objectData.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                    }`}>
                        {row.objectData.priority ? row.objectData.priority.charAt(0).toUpperCase() + row.objectData.priority.slice(1) : 'Unknown'}
                    </span>
                )
            },
            {
                key: 'followUpDate',
                label: 'Follow-up Date',
                render: (row) => row.objectData.followUpDate ? formatDate(row.objectData.followUpDate) : '-'
            },
            {
                key: 'source',
                label: 'Source',
                render: (row) => (
                    <span className="inline-flex items-center">
                        <i className={`mr-1 fas ${
                            row.objectData.source === 'website' ? 'fa-globe' :
                            row.objectData.source === 'referral' ? 'fa-user-friends' :
                            row.objectData.source === 'social' ? 'fa-share-alt' :
                            row.objectData.source === 'email' ? 'fa-envelope' :
                            row.objectData.source === 'event' ? 'fa-calendar' :
                            'fa-question-circle'
                        }`}></i>
                        {row.objectData.source ? row.objectData.source.charAt(0).toUpperCase() + row.objectData.source.slice(1) : 'Unknown'}
                    </span>
                )
            }
        ];

        return (
            <div data-name="lead-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search leads..."
                        filters={leadFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredLeads}
                    loading={loading}
                    onRowClick={onLeadClick}
                    emptyMessage="No leads found"
                />

                {/* Pagination */}
                {totalCount > pageSize && (
                    <div className="flex items-center justify-between mt-6 px-4 py-3 bg-white border-t border-gray-200">
                        <div className="flex items-center text-sm text-gray-700">
                            <span>
                                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} results
                            </span>
                            <select
                                value={pageSize}
                                onChange={(e) => {
                                    setPageSize(Number(e.target.value));
                                    setCurrentPage(1);
                                }}
                                className="ml-4 border border-gray-300 rounded px-2 py-1 text-sm"
                            >
                                <option value={10}>10 per page</option>
                                <option value={20}>20 per page</option>
                                <option value={50}>50 per page</option>
                                <option value={100}>100 per page</option>
                            </select>
                        </div>

                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                disabled={currentPage === 1}
                                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                Previous
                            </button>

                            {/* Page numbers */}
                            {Array.from({ length: Math.min(5, Math.ceil(totalCount / pageSize)) }, (_, i) => {
                                const pageNum = Math.max(1, currentPage - 2) + i;
                                if (pageNum > Math.ceil(totalCount / pageSize)) return null;

                                return (
                                    <button
                                        key={pageNum}
                                        onClick={() => setCurrentPage(pageNum)}
                                        className={`px-3 py-1 border rounded text-sm ${
                                            pageNum === currentPage
                                                ? 'bg-blue-600 text-white border-blue-600'
                                                : 'border-gray-300 hover:bg-gray-50'
                                        }`}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            })}

                            <button
                                onClick={() => setCurrentPage(Math.min(Math.ceil(totalCount / pageSize), currentPage + 1))}
                                disabled={currentPage >= Math.ceil(totalCount / pageSize)}
                                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('LeadList component error:', error);
        reportError(error);
        return null;
    }
}
