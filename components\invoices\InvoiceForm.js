function InvoiceForm({ invoice, onSubmit, onCancel }) {
    try {
        // Define the function before using it in useState
        const generateInvoiceNumber = () => {
            const prefix = 'INV';
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `${prefix}-${timestamp}-${random}`;
        };
        
        const [formData, setFormData] = React.useState({
            customer: invoice && invoice.objectData && invoice.objectData.customer ? invoice.objectData.customer : '',
            items: invoice && invoice.objectData && invoice.objectData.items ? invoice.objectData.items : [{ description: '', quantity: 1, price: 0, itemId: '' }],
            notes: invoice && invoice.objectData && invoice.objectData.notes ? invoice.objectData.notes : '',
            terms: invoice && invoice.objectData && invoice.objectData.terms ? invoice.objectData.terms : '',
            dueDate: invoice && invoice.objectData && invoice.objectData.dueDate ? invoice.objectData.dueDate : '',
            status: invoice && invoice.objectData && invoice.objectData.status ? invoice.objectData.status : 'draft',
            paymentMethod: invoice && invoice.objectData && invoice.objectData.paymentMethod ? invoice.objectData.paymentMethod : 'bank_transfer',
            invoiceNumber: invoice && invoice.objectData && invoice.objectData.invoiceNumber ? invoice.objectData.invoiceNumber : generateInvoiceNumber(),
            amountPaid: invoice && invoice.objectData && invoice.objectData.amountPaid ? invoice.objectData.amountPaid : 0,
            paidAt: invoice && invoice.objectData && invoice.objectData.paidAt ? invoice.objectData.paidAt : null
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [customers, setCustomers] = React.useState([]);
        const [itemsList, setItemsList] = React.useState([]);
        const [selectedCustomerData, setSelectedCustomerData] = React.useState(null);
        const [settings, setSettings] = React.useState({ taxRate: 0 }); // Default tax rate if settings not found

        React.useEffect(() => {
            fetchCustomers();
            fetchItems();
            fetchSettings();
        }, []);

        React.useEffect(() => {
            if (formData.customer) {
                fetchCustomerDetails(formData.customer);
            }
        }, [formData.customer]);

        const fetchCustomers = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/customer'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setCustomers(data.items || []);
                } else {
                    throw new Error('Failed to fetch customers');
                }
            } catch (error) {
                console.error('Error fetching customers:', error);
                setCustomers([]);
            }
        };

        const fetchItems = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/item'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    // Only show active items
                    const activeItems = (data.items || []).filter(item => item.objectData.isActive !== false);
                    setItemsList(activeItems);
                } else {
                    throw new Error('Failed to fetch items');
                }
            } catch (error) {
                console.error('Error fetching items:', error);
                setItemsList([]);
            }
        };

        const fetchSettings = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/settings'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.items && data.items.length > 0) {
                        setSettings(data.items[0].objectData);
                    }
                } else {
                    throw new Error('Failed to fetch settings');
                }
            } catch (error) {
                console.error('Error fetching settings:', error);
                setSettings({});
            }
        };

        const fetchCustomerDetails = async (customerId) => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl(`/customer/${customerId}`), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setSelectedCustomerData(data.objectData);
                } else {
                    throw new Error('Failed to fetch customer details');
                }
            } catch (error) {
                console.error('Error fetching customer details:', error);
                setSelectedCustomerData(null);
            }
        };

        const handleAddItem = () => {
            setFormData(prev => ({
                ...prev,
                items: [...prev.items, { description: '', quantity: 1, price: 0, itemId: '' }]
            }));
        };

        const handleRemoveItem = (index) => {
            setFormData(prev => ({
                ...prev,
                items: prev.items.filter((_, i) => i !== index)
            }));
        };

        const handleItemChange = (index, field, value) => {
            setFormData(prev => ({
                ...prev,
                items: prev.items.map((item, i) => 
                    i === index ? { ...item, [field]: value } : item
                )
            }));
        };

        const handleItemSelection = (index, itemId) => {
            if (!itemId) {
                handleItemChange(index, 'itemId', '');
                handleItemChange(index, 'description', '');
                handleItemChange(index, 'price', 0);
                return;
            }

            const selectedItem = itemsList.find(item => item.objectId === itemId);
            if (selectedItem) {
                handleItemChange(index, 'itemId', itemId);
                handleItemChange(index, 'description', selectedItem.objectData.name);
                handleItemChange(index, 'price', selectedItem.objectData.price);
            }
        };

        const calculateSubtotal = () => {
            return formData.items.reduce((total, item) => 
                total + (item.quantity * item.price), 0
            );
        };

        const calculateTax = (subtotal) => {
            const taxRate = settings.taxRate !== undefined ? settings.taxRate : 0; // Use settings tax rate or default to 0%
            return subtotal * (taxRate / 100);
        };

        const calculateTotal = () => {
            const subtotal = calculateSubtotal();
            const tax = calculateTax(subtotal);
            return subtotal + tax;
        };

        const calculateBalance = () => {
            return calculateTotal() - formData.amountPaid;
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.customer) {
                newErrors.customer = 'Customer is required';
            }
            if (formData.items.length === 0) {
                newErrors.items = 'At least one item is required';
            }
            if (!formData.dueDate) {
                newErrors.dueDate = 'Due date is required';
            }

            // Validate each item has a description and price
            formData.items.forEach((item, index) => {
                if (!item.description) {
                    newErrors[`item_${index}_description`] = 'Description is required';
                }
                if (item.price <= 0) {
                    newErrors[`item_${index}_price`] = 'Price must be greater than zero';
                }
            });

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                const invoiceData = {
                    ...formData,
                    subtotal: calculateSubtotal(),
                    tax: calculateTax(calculateSubtotal()),
                    total: calculateTotal(),
                    balance: calculateBalance(),
                    taxRate: settings.taxRate !== undefined ? settings.taxRate : 0,
                    updatedAt: new Date().toISOString()
                };

                const token = localStorage.getItem('authToken');
                let response;

                if (invoice && invoice.objectId) {
                    // Update existing invoice
                    response = await fetch(window.getApiUrl(`/invoice/${invoice.objectId}`), {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(invoiceData)
                    });
                } else {
                    // Create new invoice
                    response = await fetch(window.getApiUrl('/invoice'), {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(invoiceData)
                    });
                }

                if (response.ok) {
                    onSubmit();
                } else {
                    // Get detailed error message from response
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.message || errorData.error || `Failed to save invoice (${response.status})`;
                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('Error saving invoice:', error);
                setErrors({ submit: error.message || 'Failed to save invoice' });
            } finally {
                setLoading(false);
            }
        };

        return (
            <form data-name="invoice-form" onSubmit={handleSubmit} className="invoice-form">
                <div className="grid grid-cols-1 gap-6">
                    <div className="flex justify-between items-center">
                        <div className="text-xl font-bold text-gray-700">
                            Invoice #{formData.invoiceNumber}
                        </div>
                        <div>
                            <Button
                                type="button"
                                variant="secondary"
                                icon="fas fa-random"
                                onClick={() => setFormData(prev => ({
                                    ...prev,
                                    invoiceNumber: generateInvoiceNumber()
                                }))}
                                className="text-sm"
                            >
                                Regenerate Number
                            </Button>
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Customer
                        </label>
                        <select
                            value={formData.customer}
                            onChange={(e) => setFormData(prev => ({ ...prev, customer: e.target.value }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="">Select Customer</option>
                            {customers.map(customer => (
                                <option key={customer.objectId} value={customer.objectId}>
                                    {customer.objectData.name}
                                </option>
                            ))}
                        </select>
                        {errors.customer && (
                            <p className="mt-1 text-sm text-red-600">{errors.customer}</p>
                        )}
                    </div>

                    {selectedCustomerData && (
                        <div className="bg-gray-50 p-4 rounded-md">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">Customer Information</h3>
                            <div className="text-sm">
                                <p><span className="font-medium">Name:</span> {selectedCustomerData.name}</p>
                                {selectedCustomerData.company && <p><span className="font-medium">Company:</span> {selectedCustomerData.company}</p>}
                                {selectedCustomerData.email && <p><span className="font-medium">Email:</span> {selectedCustomerData.email}</p>}
                                {selectedCustomerData.phone && <p><span className="font-medium">Phone:</span> {selectedCustomerData.phone}</p>}
                            </div>
                        </div>
                    )}

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Items
                        </label>
                        <div className="invoice-items">
                            {formData.items.map((item, index) => (
                                <div key={index} className="invoice-item">
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Item</label>
                                        <select
                                            value={item.itemId || ''}
                                            onChange={(e) => handleItemSelection(index, e.target.value)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        >
                                            <option value="">Select Item</option>
                                            {itemsList.map(catalogItem => (
                                                <option key={catalogItem.objectId} value={catalogItem.objectId}>
                                                    {catalogItem.objectData.name} - {formatCurrency(catalogItem.objectData.price)}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Description</label>
                                        <input
                                            type="text"
                                            placeholder="Description"
                                            value={item.description}
                                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                        {errors[`item_${index}_description`] && (
                                            <p className="mt-1 text-xs text-red-600">{errors[`item_${index}_description`]}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Quantity</label>
                                        <input
                                            type="number"
                                            placeholder="Quantity"
                                            value={item.quantity}
                                            onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 0)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Price</label>
                                        <input
                                            type="number"
                                            placeholder="Price"
                                            value={item.price}
                                            onChange={(e) => handleItemChange(index, 'price', parseFloat(e.target.value) || 0)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                        {errors[`item_${index}_price`] && (
                                            <p className="mt-1 text-xs text-red-600">{errors[`item_${index}_price`]}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Total</label>
                                        <div className="text-right py-2 px-2 bg-gray-50 rounded-md">
                                            {formatCurrency(item.quantity * item.price)}
                                        </div>
                                    </div>
                                    <div className="flex items-end">
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveItem(index)}
                                            className="text-red-600 hover:text-red-800"
                                        >
                                            <i className="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <button
                            type="button"
                            onClick={handleAddItem}
                            className="mt-2 text-blue-600 hover:text-blue-800"
                        >
                            <i className="fas fa-plus mr-1"></i>
                            Add Item
                        </button>
                        {errors.items && (
                            <p className="mt-1 text-sm text-red-600">{errors.items}</p>
                        )}
                    </div>

                    <div className="invoice-totals">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="text-right">Subtotal:</div>
                            <div className="text-right font-medium">
                                {formatCurrency(calculateSubtotal())}
                            </div>
                            <div className="text-right">Tax ({settings.taxRate !== undefined ? settings.taxRate : 0}%):</div>
                            <div className="text-right font-medium">
                                {formatCurrency(calculateTax(calculateSubtotal()))}
                            </div>
                            <div className="text-right font-bold">Total:</div>
                            <div className="text-right text-xl font-bold">
                                {formatCurrency(calculateTotal())}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Due Date
                            </label>
                            <input
                                type="date"
                                value={formData.dueDate}
                                onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                            {errors.dueDate && (
                                <p className="mt-1 text-sm text-red-600">{errors.dueDate}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Status
                            </label>
                            <select
                                value={formData.status}
                                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="draft">Draft</option>
                                <option value="sent">Sent</option>
                                <option value="paid">Paid</option>
                                <option value="overdue">Overdue</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Payment Method
                            </label>
                            <select
                                value={formData.paymentMethod}
                                onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="credit_card">Credit Card</option>
                                <option value="cash">Cash</option>
                                <option value="upi">UPI</option>
                                <option value="cheque">Cheque</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    {formData.status === 'paid' || formData.amountPaid > 0 ? (
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Amount Paid
                            </label>
                            <div className="mt-1 relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">₹</span>
                                </div>
                                <input
                                    type="number"
                                    name="amountPaid"
                                    value={formData.amountPaid}
                                    onChange={(e) => setFormData(prev => ({ 
                                        ...prev, 
                                        amountPaid: parseFloat(e.target.value) || 0,
                                        status: parseFloat(e.target.value) >= calculateTotal() ? 'paid' : prev.status === 'paid' ? 'sent' : prev.status
                                    }))}
                                    min="0"
                                    step="0.01"
                                    className="block w-full pl-7 pr-12 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <button
                                        type="button"
                                        onClick={() => setFormData(prev => ({ 
                                            ...prev, 
                                            amountPaid: calculateTotal(),
                                            status: 'paid',
                                            paidAt: new Date().toISOString()
                                        }))}
                                        className="text-sm text-blue-600 hover:text-blue-800"
                                    >
                                        Mark as fully paid
                                    </button>
                                </div>
                            </div>
                            <p className="mt-2 text-sm text-gray-500">
                                Balance due: {formatCurrency(calculateBalance())}
                            </p>
                        </div>
                    ) : null}

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Notes
                        </label>
                        <textarea
                            value={formData.notes}
                            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Additional notes to the customer"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Terms and Conditions
                        </label>
                        <textarea
                            value={formData.terms}
                            onChange={(e) => setFormData(prev => ({ ...prev, terms: e.target.value }))}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Payment terms and conditions"
                        />
                    </div>
                </div>

                {errors.submit && (
                    <div className="mt-4 text-red-600 text-sm">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {invoice && invoice.objectId ? 'Update Invoice' : 'Create Invoice'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('InvoiceForm component error:', error);
        reportError(error);
        return null;
    }
}
