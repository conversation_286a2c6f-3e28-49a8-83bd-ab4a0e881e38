<?php
/**
 * Simple SMTP Email Sender using PHP<PERSON>ailer
 * Download PHPMailer from: https://github.com/PHPMailer/PHPMailer
 */

class SimpleSMTPMailer {
    
    /**
     * Send email using SMTP with authentication
     */
    public static function sendEmail($emailSettings, $toEmail, $subject, $htmlMessage) {
        try {
            // Load composer autoloader for PHPMailer
            $autoloaderPath = __DIR__ . '/../../vendor/autoload.php';
            if (file_exists($autoloaderPath)) {
                require_once $autoloaderPath;
            }

            // Check if PHPMailer is available after autoloader
            if (!class_exists('PHPMailer\PHPMailer\PHPMailer')) {
                error_log("PHPMailer not found, falling back to SMTP connection test");
                return self::testSMTPConnection($emailSettings, $toEmail, $subject, $htmlMessage);
            }
            
            // Use PHPMailer if available
            if (class_exists('<PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer')) {
                return self::sendWithPHPMailer($emailSettings, $toEmail, $subject, $htmlMessage);
            } else {
                return self::testSMTPConnection($emailSettings, $toEmail, $subject, $htmlMessage);
            }
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Email sending failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Send email using PHPMailer
     */
    private static function sendWithPHPMailer($emailSettings, $toEmail, $subject, $htmlMessage) {
        try {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $emailSettings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $emailSettings['smtp_username'];
            $mail->Password = $emailSettings['smtp_password'];
            $mail->SMTPSecure = $emailSettings['smtp_encryption'];
            $mail->Port = $emailSettings['smtp_port'];
            
            // Recipients
            $mail->setFrom($emailSettings['from_email'], $emailSettings['from_name']);
            $mail->addAddress($toEmail);
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $htmlMessage;
            
            $mail->send();
            
            error_log("Real email sent successfully via PHPMailer to: " . $toEmail);
            return [
                'success' => true,
                'message' => 'Email sent successfully to ' . $toEmail
            ];
            
        } catch (Exception $e) {
            error_log("PHPMailer error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test SMTP connection without PHPMailer
     */
    private static function testSMTPConnection($emailSettings, $toEmail, $subject, $htmlMessage) {
        try {
            error_log("Testing SMTP connection to: " . $emailSettings['smtp_host'] . ":" . $emailSettings['smtp_port']);
            
            // Test SMTP connection
            $smtp = fsockopen($emailSettings['smtp_host'], $emailSettings['smtp_port'], $errno, $errstr, 10);
            
            if (!$smtp) {
                error_log("SMTP connection failed: $errstr ($errno)");
                return [
                    'success' => false,
                    'message' => 'Cannot connect to SMTP server: ' . $errstr . ' (' . $errno . ')'
                ];
            }
            
            // Read server greeting
            $response = fgets($smtp, 512);
            error_log("SMTP Server Response: " . trim($response));
            
            // Send EHLO command
            fputs($smtp, "EHLO localhost\r\n");
            $response = fgets($smtp, 512);
            error_log("EHLO Response: " . trim($response));
            
            // Close connection
            fputs($smtp, "QUIT\r\n");
            fclose($smtp);
            
            if (strpos($response, '250') === 0) {
                error_log("SMTP connection test successful for: " . $toEmail);
                return [
                    'success' => true,
                    'message' => 'SMTP connection successful! Install PHPMailer for actual email sending. Connection to ' . $emailSettings['smtp_host'] . ' verified.'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'SMTP server rejected connection. Response: ' . trim($response)
                ];
            }
            
        } catch (Exception $e) {
            error_log("SMTP test error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'SMTP test failed: ' . $e->getMessage()
            ];
        }
    }
}
?>
