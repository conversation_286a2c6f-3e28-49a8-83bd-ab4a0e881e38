<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Check users table structure
    $result = $conn->query("DESCRIBE users");
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row;
    }
    
    // Check superadmin user data
    $stmt = $conn->prepare("SELECT id, object_id, name, email, password, password_hash, role, status FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $userResult = $stmt->get_result();
    $userData = $userResult->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'table_structure' => $columns,
        'superadmin_data' => $userData ? [
            'id' => $userData['id'],
            'object_id' => $userData['object_id'],
            'name' => $userData['name'],
            'email' => $userData['email'],
            'has_password' => !empty($userData['password']),
            'has_password_hash' => !empty($userData['password_hash']),
            'password_length' => strlen($userData['password'] ?? ''),
            'password_hash_length' => strlen($userData['password_hash'] ?? ''),
            'role' => $userData['role'],
            'status' => $userData['status']
        ] : null
    ]);
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
