<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Login Test</h1>
    <p>Use this page to test the login API directly.</p>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="admin123">
    </div>
    
    <div class="form-group">
        <label>
            <input type="checkbox" id="remember-me" checked>
            Remember me
        </label>
    </div>
    
    <button id="login-btn">Login</button>
    
    <div class="result" id="result">Results will appear here...</div>
    
    <script>
        document.getElementById('login-btn').addEventListener('click', async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember-me').checked;
            const resultDiv = document.getElementById('result');
            
            resultDiv.textContent = 'Sending login request...';
            
            try {
                const loginData = {
                    action: 'login',
                    email: email,
                    password: password,
                    remember_me: rememberMe
                };
                
                const response = await fetch('/biz/api/api.php/auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                resultDiv.textContent = 'Response:\n' + JSON.stringify(data, null, 2);
                
                if (data.success) {
                    resultDiv.style.backgroundColor = '#e6ffe6';
                    resultDiv.style.borderColor = '#4CAF50';
                } else {
                    resultDiv.style.backgroundColor = '#ffebeb';
                    resultDiv.style.borderColor = '#f44336';
                }
            } catch (error) {
                resultDiv.textContent = 'Error:\n' + error.message;
                resultDiv.style.backgroundColor = '#ffebeb';
                resultDiv.style.borderColor = '#f44336';
            }
        });
    </script>
</body>
</html>