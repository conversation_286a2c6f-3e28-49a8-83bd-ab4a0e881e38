function isEmailValid(email) {
    try {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    } catch (error) {
        console.error('isEmailValid error:', error);
        return false;
    }
}

function isPhoneValid(phone) {
    try {
        // More flexible phone validation that supports international formats
        const re = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
        return re.test(String(phone).trim());
    } catch (error) {
        console.error('isPhoneValid error:', error);
        return false;
    }
}

function isPasswordStrong(password) {
    try {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        return (
            password.length >= minLength &&
            hasUpperCase &&
            hasLowerCase &&
            hasNumbers &&
            hasSpecialChar
        );
    } catch (error) {
        console.error('isPasswordStrong error:', error);
        return false;
    }
}

function validateRequired(value) {
    try {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    } catch (error) {
        console.error('validateRequired error:', error);
        return false;
    }
}

function validateLength(value, min, max) {
    try {
        const length = value.toString().length;
        return length >= min && length <= max;
    } catch (error) {
        console.error('validateLength error:', error);
        return false;
    }
}

function validateNumber(value, min, max) {
    try {
        const num = Number(value);
        return !isNaN(num) && num >= min && num <= max;
    } catch (error) {
        console.error('validateNumber error:', error);
        return false;
    }
}

function validateUrl(url) {
    try {
        new URL(url);
        return true;
    } catch (e) {
        return false;
    }
}

function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

function validateField(type, value, options = {}) {
    try {
        const { required = false, minLength, maxLength, strict = true } = options;
        
        // Check if required
        if (required && !validateRequired(value)) {
            return { valid: false, message: 'This field is required' };
        }
        
        // Skip validation if empty and not required
        if (!value || value.toString().trim() === '') {
            return { valid: true, message: '' };
        }
        
        // Check min length
        if (minLength && !validateLength(value, minLength, Number.MAX_SAFE_INTEGER)) {
            return { valid: false, message: `Must be at least ${minLength} characters` };
        }
        
        // Check max length
        if (maxLength && !validateLength(value, 0, maxLength)) {
            return { valid: false, message: `Must be no more than ${maxLength} characters` };
        }
        
        // Type-specific validation
        switch (type) {
            case 'email':
                return {
                    valid: isEmailValid(value),
                    message: isEmailValid(value) ? '' : 'Please enter a valid email address'
                };
            case 'password':
                const isValid = strict ? isPasswordStrong(value) : validateLength(value, 6, Number.MAX_SAFE_INTEGER);
                return {
                    valid: isValid,
                    message: isValid ? '' : strict 
                        ? 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'
                        : 'Password must be at least 6 characters'
                };
            case 'phone':
                return {
                    valid: isPhoneValid(value),
                    message: isPhoneValid(value) ? '' : 'Please enter a valid phone number'
                };
            case 'url':
                return {
                    valid: validateUrl(value),
                    message: validateUrl(value) ? '' : 'Please enter a valid URL'
                };
            default:
                return { valid: true, message: '' };
        }
    } catch (error) {
        console.error('validateField error:', error);
        return { valid: false, message: 'Validation error occurred' };
    }
}

// Make validation functions globally available
window.ValidationUtils = {
    isEmailValid,
    isPhoneValid,
    isPasswordStrong,
    validateRequired,
    validateLength,
    validateNumber,
    validateUrl,
    sanitizeInput,
    validateField
};
