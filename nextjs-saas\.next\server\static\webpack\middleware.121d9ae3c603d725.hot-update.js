"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/middleware */ \"(middleware)/./node_modules/next-auth/middleware.js\");\n/* harmony import */ var next_auth_middleware__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_middleware__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_auth_middleware__WEBPACK_IMPORTED_MODULE_0__.withAuth)(function middleware(req) {\n// Add any additional middleware logic here\n}, {\n    callbacks: {\n        authorized: ({ token })=>!!token\n    }\n}));\nconst config = {\n    matcher: [\n        \"/admin/:path*\"\n    ] // Temporarily only protect admin routes\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbWlkZGxld2FyZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBRS9DLGlFQUFlQSw4REFBUUEsQ0FDckIsU0FBU0MsV0FBV0MsR0FBRztBQUNyQiwyQ0FBMkM7QUFDN0MsR0FDQTtJQUNFQyxXQUFXO1FBQ1RDLFlBQVksQ0FBQyxFQUFFQyxLQUFLLEVBQUUsR0FBSyxDQUFDLENBQUNBO0lBQy9CO0FBQ0YsSUFDRDtBQUVNLE1BQU1DLFNBQVM7SUFDcEJDLFNBQVM7UUFBQztLQUFnQixDQUFDLHdDQUF3QztBQUNyRSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL21pZGRsZXdhcmUudHM/NDIyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB3aXRoQXV0aCB9IGZyb20gJ25leHQtYXV0aC9taWRkbGV3YXJlJ1xuXG5leHBvcnQgZGVmYXVsdCB3aXRoQXV0aChcbiAgZnVuY3Rpb24gbWlkZGxld2FyZShyZXEpIHtcbiAgICAvLyBBZGQgYW55IGFkZGl0aW9uYWwgbWlkZGxld2FyZSBsb2dpYyBoZXJlXG4gIH0sXG4gIHtcbiAgICBjYWxsYmFja3M6IHtcbiAgICAgIGF1dGhvcml6ZWQ6ICh7IHRva2VuIH0pID0+ICEhdG9rZW4sXG4gICAgfSxcbiAgfVxuKVxuXG5leHBvcnQgY29uc3QgY29uZmlnID0ge1xuICBtYXRjaGVyOiBbJy9hZG1pbi86cGF0aConXSAvLyBUZW1wb3JhcmlseSBvbmx5IHByb3RlY3QgYWRtaW4gcm91dGVzXG59XG4iXSwibmFtZXMiOlsid2l0aEF1dGgiLCJtaWRkbGV3YXJlIiwicmVxIiwiY2FsbGJhY2tzIiwiYXV0aG9yaXplZCIsInRva2VuIiwiY29uZmlnIiwibWF0Y2hlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});