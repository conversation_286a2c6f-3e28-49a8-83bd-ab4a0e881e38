<?php
/**
 * Comprehensive Project Audit Script
 * Tests all major flows and identifies issues
 */

require_once 'api/db-config.php';

echo "<h1>🔍 Comprehensive Project Audit</h1>\n";

// 1. Database Structure Check
echo "<h2>1. Database Structure Check</h2>\n";

$tables = ['users', 'companies', 'subscriptions', 'pricing_plans', 'business_types', 'leads', 'activities', 'tasks', 'notes'];

foreach ($tables as $table) {
    $sql = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>\n";
        
        // Check record count
        $countSql = "SELECT COUNT(*) as count FROM $table";
        $countResult = $conn->query($countSql);
        if ($countResult) {
            $count = $countResult->fetch_assoc()['count'];
            echo "<p style='margin-left: 20px;'>📊 Records: $count</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Table '$table' missing</p>\n";
    }
}

// 2. API Endpoints Check
echo "<h2>2. API Endpoints Check</h2>\n";

$endpoints = [
    '/auth/verify' => 'Authentication verification',
    '/subscription-management/current' => 'Current subscription',
    '/subscription-management/usage' => 'Usage statistics',
    '/super-admin/dashboard' => 'Super admin dashboard',
    '/business-types' => 'Business types',
    '/pricing-plans' => 'Pricing plans'
];

foreach ($endpoints as $endpoint => $description) {
    $url = "http://localhost/biz/api/api.php$endpoint";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Content-Type: application/json\r\n",
            'timeout' => 5
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p style='color: green;'>✅ $description ($endpoint)</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ $description ($endpoint) - Invalid JSON response</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ $description ($endpoint) - No response</p>\n";
    }
}

// 3. Business Types Check
echo "<h2>3. Business Types Implementation</h2>\n";

$sql = "SELECT * FROM business_types ORDER BY name";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<p style='color: green;'>✅ Business types configured</p>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Name</th><th>Description</th><th>Status</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['description']) . "</td>";
        echo "<td>" . htmlspecialchars($row['status']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p style='color: red;'>❌ No business types found</p>\n";
}

// 4. Pricing Plans Check
echo "<h2>4. Pricing Plans Implementation</h2>\n";

$sql = "SELECT * FROM pricing_plans ORDER BY price";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<p style='color: green;'>✅ Pricing plans configured</p>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Name</th><th>Price</th><th>Features</th><th>Status</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>₹" . number_format($row['price']) . "</td>";
        echo "<td>" . htmlspecialchars(substr($row['features'], 0, 100)) . "...</td>";
        echo "<td>" . htmlspecialchars($row['status']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p style='color: red;'>❌ No pricing plans found</p>\n";
}

// 5. User Roles Check
echo "<h2>5. User Roles & Permissions</h2>\n";

$sql = "SELECT role, COUNT(*) as count FROM users GROUP BY role";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Role</th><th>User Count</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['role']) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p style='color: red;'>❌ No users found</p>\n";
}

// 6. Subscription Status Check
echo "<h2>6. Subscription System Status</h2>\n";

$sql = "SELECT status, COUNT(*) as count FROM subscriptions GROUP BY status";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Status</th><th>Count</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['status']) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p style='color: orange;'>⚠️ No subscriptions found</p>\n";
}

// 7. File Structure Check
echo "<h2>7. Critical Files Check</h2>\n";

$criticalFiles = [
    'app.html' => 'Main application file',
    'app.js' => 'Main application script',
    'config.js' => 'Configuration file',
    'components/auth/AuthContext.js' => 'Authentication context',
    'components/auth/LoginForm.js' => 'Login form component',
    'pages/Dashboard.js' => 'User dashboard',
    'pages/SuperAdminDashboard.js' => 'Super admin dashboard',
    'api/api.php' => 'Main API endpoint',
    'api/simple-auth.php' => 'Authentication API'
];

foreach ($criticalFiles as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p style='color: green;'>✅ $description ($file) - " . number_format($size) . " bytes</p>\n";
    } else {
        echo "<p style='color: red;'>❌ $description ($file) - Missing</p>\n";
    }
}

echo "<h2>8. Recommendations</h2>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc; margin: 10px 0;'>\n";
echo "<h3>🎯 Priority Improvements Needed:</h3>\n";
echo "<ul>\n";
echo "<li><strong>Authentication Flow:</strong> Implement robust token validation and refresh</li>\n";
echo "<li><strong>Subscription Management:</strong> Add trial expiration handling and upgrade flows</li>\n";
echo "<li><strong>Business Templates:</strong> Enhance template customization based on business type</li>\n";
echo "<li><strong>Error Handling:</strong> Implement comprehensive error boundaries and user feedback</li>\n";
echo "<li><strong>Performance:</strong> Add caching and optimize API calls</li>\n";
echo "<li><strong>Security:</strong> Enhance input validation and CSRF protection</li>\n";
echo "</ul>\n";
echo "</div>\n";

$conn->close();
?>