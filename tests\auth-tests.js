// Authentication and Registration Tests
document.addEventListener('DOMContentLoaded', function() {
    if (typeof TestRunner !== 'undefined') {
        const testRunner = window.testRunner || new TestRunner();
        window.testRunner = testRunner;

        const authTests = [
            {
                name: 'Registration API endpoint should be accessible',
                test: async function() {
                    const response = await testRunner.makeApiCall('/register.php', {
                        method: 'OPTIONS'
                    });
                    testRunner.expect(response.status).toBe(200);
                }
            },
            {
                name: 'Registration should require valid email format',
                test: async function() {
                    const response = await testRunner.makeApiCall('/register.php', {
                        method: 'POST',
                        body: JSON.stringify({
                            name: 'Test User',
                            email: 'invalid-email',
                            password: 'password123',
                            company_name: 'Test Company'
                        })
                    });
                    testRunner.expect(response.ok).toBeFalsy();
                }
            },
            {
                name: 'Registration should require all mandatory fields',
                test: async function() {
                    const response = await testRunner.makeApiCall('/register.php', {
                        method: 'POST',
                        body: JSON.stringify({
                            email: '<EMAIL>'
                        })
                    });
                    testRunner.expect(response.ok).toBeFalsy();
                    testRunner.expect(response.data.message).toContain('required');
                }
            },
            {
                name: 'Login API endpoint should be accessible',
                test: async function() {
                    const response = await testRunner.makeApiCall('/login.php', {
                        method: 'OPTIONS'
                    });
                    testRunner.expect(response.status).toBe(200);
                }
            },
            {
                name: 'Login should reject invalid credentials',
                test: async function() {
                    const response = await testRunner.makeApiCall('/login.php', {
                        method: 'POST',
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'wrongpassword'
                        })
                    });
                    testRunner.expect(response.ok).toBeFalsy();
                }
            },
            {
                name: 'Password reset API should be accessible',
                test: async function() {
                    const response = await testRunner.makeApiCall('/reset-password.php', {
                        method: 'OPTIONS'
                    });
                    testRunner.expect(response.status).toBe(200);
                }
            },
            {
                name: 'Business type selection should be available during registration',
                test: async function() {
                    const response = await testRunner.makeApiCall('/business-types.php');
                    testRunner.expect(response.ok).toBeTruthy();
                    testRunner.expect(response.data.success).toBeTruthy();
                    testRunner.expect(response.data.data).toBeInstanceOf(Array);
                    testRunner.expect(response.data.data.length).toBeGreaterThan(0);
                }
            },
            {
                name: 'Registration form validation should work on frontend',
                test: async function() {
                    // Test if registration form exists and has proper validation
                    const testForm = document.createElement('form');
                    const emailInput = document.createElement('input');
                    emailInput.type = 'email';
                    emailInput.required = true;
                    emailInput.value = 'invalid-email';
                    testForm.appendChild(emailInput);
                    
                    testRunner.expect(emailInput.checkValidity()).toBeFalsy();
                    
                    emailInput.value = '<EMAIL>';
                    testRunner.expect(emailInput.checkValidity()).toBeTruthy();
                }
            },
            {
                name: 'Session management should work correctly',
                test: async function() {
                    // Test session storage and retrieval
                    const testToken = 'test-jwt-token-123';
                    localStorage.setItem('token', testToken);
                    
                    const storedToken = localStorage.getItem('token');
                    testRunner.expect(storedToken).toBe(testToken);
                    
                    localStorage.removeItem('token');
                    testRunner.expect(localStorage.getItem('token')).toBe(null);
                }
            },
            {
                name: 'User profile API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/user-profile.php');
                    testRunner.expect(response.status).toBe(401);
                }
            }
        ];

        testRunner.addTestSuite('Authentication', authTests);
    }
});
