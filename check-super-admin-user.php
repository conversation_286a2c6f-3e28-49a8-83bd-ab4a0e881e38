<?php
require_once 'api/db-config.php';

echo "🔍 Checking Super Admin User...\n\n";

try {
    $sql = "SELECT * FROM users WHERE role = 'super_admin'";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo "Super Admin User:\n";
            echo "Name: " . $row['name'] . "\n";
            echo "Email: " . $row['email'] . "\n";
            echo "Company ID: " . ($row['company_id'] ?? 'NULL') . "\n";
            echo "Object ID: " . $row['object_id'] . "\n";
            echo "Role: " . $row['role'] . "\n";
            echo "Status: " . $row['status'] . "\n\n";
            
            // If no company_id, create one
            if (empty($row['company_id'])) {
                echo "⚠️ Super admin has no company_id. Creating one...\n";
                $companyId = 'super_admin_company';
                
                // Update user with company_id
                $updateSql = "UPDATE users SET company_id = ? WHERE object_id = ?";
                $stmt = $conn->prepare($updateSql);
                $stmt->bind_param("ss", $companyId, $row['object_id']);
                
                if ($stmt->execute()) {
                    echo "✅ Updated super admin with company_id: $companyId\n";
                } else {
                    echo "❌ Failed to update company_id: " . $conn->error . "\n";
                }
            }
        }
    } else {
        echo "❌ No super admin users found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
