<?php
/**
 * Create system_settings table manually
 */

require_once __DIR__ . '/../api/db-config.php';

echo "⚙️ Creating system_settings table...\n\n";

try {
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value LONGTEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json', 'html') DEFAULT 'string',
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_setting_key (setting_key),
        INDEX idx_category (category),
        INDEX idx_is_public (is_public)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ system_settings table created successfully\n";
        
        // Verify table structure
        $result = $conn->query("DESCRIBE system_settings");
        echo "\n📋 Table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']}: {$row['Type']}\n";
        }
        
    } else {
        echo "❌ Error creating system_settings table: " . $conn->error . "\n";
    }
    
} catch (Exception $e) {
    echo "💥 Table creation failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Table creation completed.\n";
?>
