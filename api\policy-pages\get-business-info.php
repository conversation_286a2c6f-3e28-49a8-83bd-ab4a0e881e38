<?php
require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $conn = Database::getConnection();
    
    // Get business information from system settings
    $businessInfo = [
        'company_name' => 'Bizma',
        'company_address' => '',
        'company_phone' => '',
        'company_email' => '<EMAIL>',
        'company_website' => 'https://bizma.com',
        'support_email' => '<EMAIL>',
        'privacy_email' => '<EMAIL>',
        'legal_email' => '<EMAIL>',
        'last_updated' => date('Y-m-d')
    ];
    
    // Try to get business info from system settings
    $stmt = $conn->prepare("
        SELECT setting_key, setting_value 
        FROM system_settings 
        WHERE setting_key IN ('company_name', 'company_address', 'company_phone', 'company_email', 'company_website', 'support_email', 'privacy_email', 'legal_email')
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $businessInfo[$row['setting_key']] = $row['setting_value'];
    }
    
    // If no settings found, try to get from a default company record
    if (empty($businessInfo['company_name']) || $businessInfo['company_name'] === 'Bizma') {
        $companyStmt = $conn->prepare("
            SELECT name, address, phone, email, website 
            FROM companies 
            WHERE name LIKE '%bizma%' OR name LIKE '%admin%' 
            ORDER BY created_at ASC 
            LIMIT 1
        ");
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if ($companyRow = $companyResult->fetch_assoc()) {
            $businessInfo['company_name'] = $companyRow['name'] ?: 'Bizma';
            $businessInfo['company_address'] = $companyRow['address'] ?: '';
            $businessInfo['company_phone'] = $companyRow['phone'] ?: '';
            $businessInfo['company_email'] = $companyRow['email'] ?: '<EMAIL>';
            $businessInfo['company_website'] = $companyRow['website'] ?: 'https://bizma.com';
        }
    }
    
    // Ensure we have fallback values
    if (empty($businessInfo['support_email'])) {
        $businessInfo['support_email'] = $businessInfo['company_email'];
    }
    if (empty($businessInfo['privacy_email'])) {
        $businessInfo['privacy_email'] = $businessInfo['company_email'];
    }
    if (empty($businessInfo['legal_email'])) {
        $businessInfo['legal_email'] = $businessInfo['company_email'];
    }
    
    echo json_encode(['success' => true, 'data' => $businessInfo]);
    
} catch (Exception $e) {
    error_log("Business Info API Error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Failed to load business information',
        'data' => [
            'company_name' => 'Bizma',
            'company_email' => '<EMAIL>',
            'support_email' => '<EMAIL>',
            'privacy_email' => '<EMAIL>',
            'legal_email' => '<EMAIL>',
            'company_website' => 'https://bizma.com',
            'last_updated' => date('Y-m-d')
        ]
    ]);
}
?>
