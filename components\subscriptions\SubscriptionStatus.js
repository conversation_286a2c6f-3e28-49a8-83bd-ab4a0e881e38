// Subscription Status Component for User Dashboard
function SubscriptionStatus({ authContext, subscription, onUpgrade, onManage }) {
    const [loading, setLoading] = React.useState(false);
    const [usageStats, setUsageStats] = React.useState(null);

    React.useEffect(() => {
        if (subscription) {
            fetchUsageStats();
        }
    }, [subscription]);

    const fetchUsageStats = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/subscription-management/usage'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setUsageStats(data.data);
                }
            }
        } catch (error) {
            console.error('Error fetching usage stats:', error);
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };

    const getStatusColor = (status) => {
        const colors = {
            trial: 'bg-blue-50 border-blue-200 text-blue-800',
            active: 'bg-green-50 border-green-200 text-green-800',
            expired: 'bg-red-50 border-red-200 text-red-800',
            cancelled: 'bg-gray-50 border-gray-200 text-gray-800'
        };
        return colors[status] || colors.active;
    };

    const getStatusIcon = (status) => {
        const icons = {
            trial: 'fas fa-clock',
            active: 'fas fa-check-circle',
            expired: 'fas fa-exclamation-triangle',
            cancelled: 'fas fa-times-circle'
        };
        return icons[status] || icons.active;
    };

    const getStatusLabel = (status) => {
        const labels = {
            trial: 'Free Trial',
            active: 'Active Subscription',
            expired: 'Expired',
            cancelled: 'Cancelled'
        };
        return labels[status] || status;
    };

    if (!subscription) {
        return (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="text-center">
                    <i className="fas fa-credit-card text-gray-300 text-3xl mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                    <p className="text-gray-600 mb-4">Start your free trial to access all features</p>
                    <button
                        onClick={() => window.location.href = '/biz/register.html'}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Start Free Trial
                    </button>
                </div>
            </div>
        );
    }

    const isTrialExpiring = subscription.is_trial && subscription.trial_days_remaining <= 3;
    const isExpired = subscription.trial_expired || subscription.status === 'expired';

    return (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-6">
                <div>
                    <div className="flex items-center mb-2">
                        <i className={`${getStatusIcon(subscription.status)} mr-2`}></i>
                        <h3 className="text-lg font-semibold text-gray-900">
                            {subscription.plan_name || 'Current Plan'}
                        </h3>
                        <span className={`ml-3 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(subscription.status)}`}>
                            {getStatusLabel(subscription.status)}
                        </span>
                    </div>
                    
                    {subscription.is_trial && (
                        <p className="text-sm text-gray-600">
                            {subscription.trial_expired ? 
                                'Your trial has expired' : 
                                `${subscription.trial_days_remaining} days remaining in trial`
                            }
                        </p>
                    )}
                    
                    {subscription.status === 'active' && (
                        <p className="text-sm text-gray-600">
                            Next billing: {new Date(subscription.next_billing_date).toLocaleDateString()}
                        </p>
                    )}
                </div>

                <div className="text-right">
                    {subscription.status === 'active' && (
                        <div className="text-2xl font-bold text-gray-900">
                            {formatCurrency(subscription.price)}
                            <span className="text-sm font-normal text-gray-600">
                                /{subscription.billing_cycle === 'yearly' ? 'year' : 'month'}
                            </span>
                        </div>
                    )}
                </div>
            </div>

            {/* Usage Statistics */}
            {usageStats && (
                <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Usage Overview</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Object.entries(usageStats).map(([key, usage]) => (
                            <div key={key} className="text-center">
                                <div className="text-lg font-semibold text-gray-900">
                                    {usage.current}
                                    {!usage.unlimited && (
                                        <span className="text-sm text-gray-500">/{usage.limit}</span>
                                    )}
                                </div>
                                <div className="text-xs text-gray-600 capitalize">
                                    {key.replace('_', ' ')}
                                </div>
                                {!usage.unlimited && (
                                    <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                                        <div 
                                            className={`h-1 rounded-full ${
                                                usage.percentage > 80 ? 'bg-red-500' : 
                                                usage.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                                            }`}
                                            style={{ width: `${Math.min(usage.percentage, 100)}%` }}
                                        ></div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
                {(subscription.is_trial || isTrialExpiring || isExpired) && (
                    <button
                        onClick={onUpgrade}
                        className={`flex-1 px-4 py-2 rounded-md font-medium transition-colors ${
                            isExpired ? 
                            'bg-red-600 text-white hover:bg-red-700' :
                            'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                    >
                        <i className="fas fa-arrow-up mr-2"></i>
                        {isExpired ? 'Reactivate' : 'Upgrade Plan'}
                    </button>
                )}
                
                {subscription.status === 'active' && (
                    <button
                        onClick={onManage}
                        className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 font-medium transition-colors"
                    >
                        <i className="fas fa-cog mr-2"></i>
                        Manage Subscription
                    </button>
                )}
                
                <button
                    onClick={fetchUsageStats}
                    disabled={loading}
                    className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
                >
                    <i className={`fas ${loading ? 'fa-spinner fa-spin' : 'fa-sync-alt'} mr-2`}></i>
                    Refresh
                </button>
            </div>

            {/* Features List */}
            {subscription.features && subscription.features.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Plan Features</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {subscription.features.map((feature, index) => (
                            <div key={index} className="flex items-center text-sm text-gray-600">
                                <i className="fas fa-check text-green-500 mr-2"></i>
                                {feature}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}

// Subscription Management Modal
function SubscriptionManagementModal({ isOpen, onClose, subscription, authContext }) {
    const [loading, setLoading] = React.useState(false);
    const [activeTab, setActiveTab] = React.useState('overview');

    const handleCancelSubscription = async () => {
        if (!confirm('Are you sure you want to cancel your subscription?')) {
            return;
        }

        setLoading(true);
        try {
            const response = await fetch(window.getApiUrl('/subscription-management/cancel'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    if (window.showNotification) {
                        window.showNotification('Subscription cancelled successfully', 'success');
                    }
                    onClose();
                    window.location.reload();
                }
            }
        } catch (error) {
            console.error('Error cancelling subscription:', error);
            if (window.showNotification) {
                window.showNotification('Failed to cancel subscription', 'error');
            }
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            Manage Subscription
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    {/* Tab Navigation */}
                    <div className="border-b border-gray-200 mb-6">
                        <nav className="-mb-px flex space-x-8">
                            {[
                                { id: 'overview', name: 'Overview', icon: 'fas fa-chart-line' },
                                { id: 'billing', name: 'Billing', icon: 'fas fa-credit-card' },
                                { id: 'settings', name: 'Settings', icon: 'fas fa-cog' }
                            ].map((tab) => (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        activeTab === tab.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    <i className={`${tab.icon} mr-2`}></i>
                                    {tab.name}
                                </button>
                            ))}
                        </nav>
                    </div>

                    {/* Tab Content */}
                    {activeTab === 'overview' && (
                        <div className="space-y-4">
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-medium text-gray-900 mb-2">Current Plan</h4>
                                <p className="text-gray-600">{subscription?.plan_name}</p>
                                <p className="text-sm text-gray-500">
                                    {subscription?.billing_cycle === 'yearly' ? 'Billed annually' : 'Billed monthly'}
                                </p>
                            </div>
                            
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-medium text-gray-900 mb-2">Next Billing</h4>
                                <p className="text-gray-600">
                                    {subscription?.next_billing_date ? 
                                        new Date(subscription.next_billing_date).toLocaleDateString() : 
                                        'N/A'
                                    }
                                </p>
                            </div>
                        </div>
                    )}

                    {activeTab === 'billing' && (
                        <div className="space-y-4">
                            <div className="text-center py-8">
                                <i className="fas fa-credit-card text-gray-300 text-3xl mb-4"></i>
                                <h4 className="text-lg font-medium text-gray-900 mb-2">Billing History</h4>
                                <p className="text-gray-600">Billing history and payment methods would be displayed here</p>
                            </div>
                        </div>
                    )}

                    {activeTab === 'settings' && (
                        <div className="space-y-4">
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                <h4 className="font-medium text-red-900 mb-2">Danger Zone</h4>
                                <p className="text-sm text-red-700 mb-4">
                                    Cancelling your subscription will downgrade your account at the end of the current billing period.
                                </p>
                                <button
                                    onClick={handleCancelSubscription}
                                    disabled={loading}
                                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50"
                                >
                                    {loading ? (
                                        <>
                                            <i className="fas fa-spinner fa-spin mr-2"></i>
                                            Cancelling...
                                        </>
                                    ) : (
                                        'Cancel Subscription'
                                    )}
                                </button>
                            </div>
                        </div>
                    )}

                    <div className="flex justify-end mt-6">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make components globally available
window.SubscriptionStatus = SubscriptionStatus;
window.SubscriptionManagementModal = SubscriptionManagementModal;
