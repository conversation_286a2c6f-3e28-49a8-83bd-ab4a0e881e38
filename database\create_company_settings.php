<?php
/**
 * Create company_settings table
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🔧 Creating company_settings table...\n\n";

try {
    $sql = "CREATE TABLE IF NOT EXISTS company_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        object_id VARCHAR(50) UNIQUE NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_object_id (object_id),
        INDEX idx_company_id (company_id),
        INDEX idx_setting_key (setting_key),
        UNIQUE KEY unique_company_setting (company_id, setting_key)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ company_settings table created successfully\n";
    } else {
        echo "❌ Error creating company_settings table: " . $conn->error . "\n";
    }
    
    // Also create system_settings and policy_pages tables
    echo "⚙️ Creating system_settings table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value LONGTEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json', 'html') DEFAULT 'string',
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_setting_key (setting_key),
        INDEX idx_category (category),
        INDEX idx_is_public (is_public)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ system_settings table created successfully\n";
    } else {
        echo "❌ Error creating system_settings table: " . $conn->error . "\n";
    }
    
    echo "📄 Creating policy_pages table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS policy_pages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        object_id VARCHAR(50) UNIQUE NOT NULL,
        page_type ENUM('terms', 'privacy', 'refund', 'cookie', 'disclaimer') NOT NULL,
        title VARCHAR(255) NOT NULL,
        content LONGTEXT NOT NULL,
        template_variables JSON,
        is_active BOOLEAN DEFAULT TRUE,
        version VARCHAR(20) DEFAULT '1.0',
        effective_date DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_object_id (object_id),
        INDEX idx_page_type (page_type),
        INDEX idx_is_active (is_active)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ policy_pages table created successfully\n";
    } else {
        echo "❌ Error creating policy_pages table: " . $conn->error . "\n";
    }
    
    echo "🔍 Creating audit_logs table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS audit_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        object_id VARCHAR(50) UNIQUE NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        action VARCHAR(100) NOT NULL,
        entity_type VARCHAR(50),
        entity_id VARCHAR(50),
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_object_id (object_id),
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_entity_type (entity_type),
        INDEX idx_entity_id (entity_id),
        INDEX idx_created_at (created_at)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ audit_logs table created successfully\n";
    } else {
        echo "❌ Error creating audit_logs table: " . $conn->error . "\n";
    }
    
    echo "\n🎉 All additional tables created successfully!\n";
    
} catch (Exception $e) {
    echo "💥 Table creation failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Table creation completed.\n";
?>
