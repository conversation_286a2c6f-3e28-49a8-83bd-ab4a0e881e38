<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple SubscriptionStatus Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div id="root"></div>

    <script type="text/babel" src="components/SimpleSubscriptionStatus.js"></script>

    <script type="text/babel">
        function TestApp() {
            const mockAuth = {
                isAuthenticated: true,
                token: 'test-token',
                user: { id: 1, name: 'Test User' }
            };

            const mockSubscription = {
                plan_name: 'Free Trial',
                status: 'trial',
                is_trial: true,
                trial_end_date: '2025-08-15',
                amount: 0,
                billing_cycle: 'monthly'
            };

            return (
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-2xl font-bold mb-6">Simple SubscriptionStatus Test</h1>
                    
                    <div className="space-y-6">
                        <div>
                            <h2 className="text-lg font-semibold mb-3">With Trial Subscription:</h2>
                            <SimpleSubscriptionStatus
                                authContext={mockAuth}
                                subscription={mockSubscription}
                                onUpgrade={() => alert('Upgrade clicked')}
                                onManage={() => alert('Manage clicked')}
                            />
                        </div>

                        <div>
                            <h2 className="text-lg font-semibold mb-3">No Subscription:</h2>
                            <SimpleSubscriptionStatus
                                authContext={mockAuth}
                                subscription={null}
                                onUpgrade={() => alert('Start trial clicked')}
                                onManage={() => alert('Manage clicked')}
                            />
                        </div>

                        <div>
                            <h2 className="text-lg font-semibold mb-3">No Auth:</h2>
                            <SimpleSubscriptionStatus
                                authContext={null}
                                subscription={mockSubscription}
                                onUpgrade={() => alert('Upgrade clicked')}
                                onManage={() => alert('Manage clicked')}
                            />
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>