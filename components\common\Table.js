function Table({
    columns,
    data,
    onRowClick,
    loading = false,
    emptyMessage = 'No data available',
    sortable = true,
    selectable = false,
    selectedRows = [],
    onRowSelect,
    pagination = false,
    itemsPerPage = 10,
    currentPage = 1,
    onPageChange,
    className = ''
}) {
    try {
        const [sortColumn, setSortColumn] = React.useState(null);
        const [sortDirection, setSortDirection] = React.useState('asc');
        const [localSelectedRows, setLocalSelectedRows] = React.useState(selectedRows || []);

        // Only update localSelectedRows if selectable is true and selectedRows actually changed
        React.useEffect(() => {
            if (selectable) {
                setLocalSelectedRows(selectedRows || []);
            }
        }, [selectedRows, selectable]);

        const handleSort = (columnKey) => {
            if (!sortable) return;
            
            if (sortColumn === columnKey) {
                setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
            } else {
                setSortColumn(columnKey);
                setSortDirection('asc');
            }
        };

        const handleRowClick = (item, index) => {
            if (selectable) {
                const isSelected = localSelectedRows.includes(item.objectId);
                const newSelectedRows = isSelected
                    ? localSelectedRows.filter(id => id !== item.objectId)
                    : [...localSelectedRows, item.objectId];
                
                setLocalSelectedRows(newSelectedRows);
                
                if (onRowSelect) {
                    onRowSelect(newSelectedRows);
                }
            } else if (onRowClick) {
                onRowClick(item, index);
            }
        };

        const handleSelectAll = (e) => {
            if (e.target.checked) {
                const allIds = data.map(item => item.objectId);
                setLocalSelectedRows(allIds);
                if (onRowSelect) {
                    onRowSelect(allIds);
                }
            } else {
                setLocalSelectedRows([]);
                if (onRowSelect) {
                    onRowSelect([]);
                }
            }
        };

        const isRowSelected = (itemId) => {
            return localSelectedRows.includes(itemId);
        };

        const sortedData = React.useMemo(() => {
            if (!sortColumn) return data;
            
            return [...data].sort((a, b) => {
                let aValue, bValue;
                
                const column = columns.find(col => col.key === sortColumn);
                if (column && column.sortValue) {
                    aValue = column.sortValue(a);
                    bValue = column.sortValue(b);
                } else {
                    aValue = a.objectData[sortColumn];
                    bValue = b.objectData[sortColumn];
                }
                
                if (aValue === undefined || aValue === null) aValue = '';
                if (bValue === undefined || bValue === null) bValue = '';
                
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return sortDirection === 'asc' 
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                } else {
                    return sortDirection === 'asc' 
                        ? aValue - bValue
                        : bValue - aValue;
                }
            });
        }, [data, sortColumn, sortDirection, columns]);
        
        const paginatedData = React.useMemo(() => {
            if (!pagination) return sortedData;
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            return sortedData.slice(startIndex, startIndex + itemsPerPage);
        }, [sortedData, pagination, currentPage, itemsPerPage]);
        
        const pageCount = React.useMemo(() => {
            if (!pagination) return 0;
            return Math.ceil(sortedData.length / itemsPerPage);
        }, [sortedData, pagination, itemsPerPage]);

        if (loading) {
            return (
                <div className="flex justify-center items-center py-8">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        if (data.length === 0) {
            return (
                <div className="bg-white rounded-lg border border-gray-200 p-6 text-center text-gray-500">
                    {emptyMessage}
                </div>
            );
        }

        const renderPagination = () => {
            if (!pagination || pageCount <= 1) return null;

            return (
                <div className="flex justify-between items-center mt-4 px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                    <div className="hidden sm:block">
                        <p className="text-sm text-gray-700">
                            Showing {" "}
                            <span className="font-medium">
                                {(currentPage - 1) * itemsPerPage + 1}
                            </span>
                            {" "}to{" "}
                            <span className="font-medium">
                                {Math.min(currentPage * itemsPerPage, sortedData.length)}
                            </span>
                            {" "}of{" "}
                            <span className="font-medium">{sortedData.length}</span>
                            {" "}results
                        </p>
                    </div>
                    <div className="flex-1 flex justify-end">
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button
                                onClick={() => onPageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                                    currentPage === 1
                                        ? 'text-gray-300 cursor-not-allowed'
                                        : 'text-gray-500 hover:bg-gray-50'
                                }`}
                            >
                                <span className="sr-only">Previous</span>
                                <i className="fas fa-chevron-left"></i>
                            </button>
                            
                            {[...Array(pageCount)].map((_, i) => {
                                const pageNum = i + 1;
                                const showPage = pageNum === 1 || 
                                                pageNum === pageCount || 
                                                Math.abs(pageNum - currentPage) <= 1;
                                
                                if (!showPage) {
                                    if (pageNum === 2 || pageNum === pageCount - 1) {
                                        return (
                                            <span
                                                key={`ellipsis-${pageNum}`}
                                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                                            >
                                                ...
                                            </span>
                                        );
                                    }
                                    return null;
                                }
                                
                                return (
                                    <button
                                        key={pageNum}
                                        onClick={() => onPageChange(pageNum)}
                                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium ${
                                            currentPage === pageNum
                                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                : 'bg-white text-gray-500 hover:bg-gray-50'
                                        }`}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            })}
                            
                            <button
                                onClick={() => onPageChange(currentPage + 1)}
                                disabled={currentPage === pageCount}
                                className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                                    currentPage === pageCount
                                        ? 'text-gray-300 cursor-not-allowed'
                                        : 'text-gray-500 hover:bg-gray-50'
                                }`}
                            >
                                <span className="sr-only">Next</span>
                                <i className="fas fa-chevron-right"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            );
        };

        return (
            <div data-name="table-component" className={`overflow-x-auto ${className}`}>
                <table className="min-w-full divide-y divide-gray-200 border-collapse">
                    <thead className="bg-gray-50">
                        <tr>
                            {selectable && (
                                <th className="px-6 py-3 w-10">
                                    <input
                                        type="checkbox"
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        checked={localSelectedRows.length === data.length && data.length > 0}
                                        onChange={handleSelectAll}
                                    />
                                </th>
                            )}
                            {columns.map((column) => (
                                <th
                                    key={column.key}
                                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                                        sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                                    }`}
                                    onClick={() => sortable && handleSort(column.key)}
                                >
                                    <div className="flex items-center">
                                        <span>{column.label}</span>
                                        {sortable && sortColumn === column.key && (
                                            <span className="ml-2">
                                                {sortDirection === 'asc' ? (
                                                    <i className="fas fa-sort-up"></i>
                                                ) : (
                                                    <i className="fas fa-sort-down"></i>
                                                )}
                                            </span>
                                        )}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {paginatedData.map((item, index) => (
                            <tr
                                key={item.objectId || index}
                                className={`${onRowClick || selectable ? 'cursor-pointer hover:bg-gray-50' : ''} ${
                                    isRowSelected(item.objectId) ? 'bg-blue-50' : ''
                                }`}
                                onClick={() => handleRowClick(item, index)}
                            >
                                {selectable && (
                                    <td className="px-6 py-4 whitespace-nowrap w-10">
                                        <input
                                            type="checkbox"
                                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            checked={isRowSelected(item.objectId)}
                                            onChange={(e) => {
                                                e.stopPropagation();
                                                handleRowClick(item, index);
                                            }}
                                        />
                                    </td>
                                )}
                                {columns.map((column) => (
                                    <td
                                        key={`${item.objectId || index}-${column.key}`}
                                        className="px-6 py-4 whitespace-nowrap"
                                    >
                                        {column.render
                                            ? column.render(item, index)
                                            : item.objectData[column.key]}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>

                {renderPagination()}
            </div>
        );
    } catch (error) {
        console.error('Table component error:', error);
        reportError(error);
        return (
            <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded">
                Error rendering table. Please try again.
            </div>
        );
    }
}
