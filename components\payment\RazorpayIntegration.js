// Razorpay Payment Integration Component
function RazorpayPayment({ 
    amount, 
    currency = 'INR', 
    planData, 
    userInfo, 
    onSuccess, 
    onError, 
    onClose,
    buttonText = 'Pay Now',
    buttonClass = 'bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700'
}) {
    const [loading, setLoading] = React.useState(false);
    const [razorpayLoaded, setRazorpayLoaded] = React.useState(false);

    React.useEffect(() => {
        // Load Razorpay script
        if (!window.Razorpay) {
            const script = document.createElement('script');
            script.src = 'https://checkout.razorpay.com/v1/checkout.js';
            script.onload = () => setRazorpayLoaded(true);
            script.onerror = () => {
                console.error('Failed to load Razorpay script');
                if (onError) onError('Failed to load payment gateway');
            };
            document.body.appendChild(script);
        } else {
            setRazorpayLoaded(true);
        }
    }, []);

    const createOrder = async () => {
        try {
            const response = await fetch(window.getApiUrl('/payment/create-order'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    amount: amount * 100, // Convert to paise
                    currency: currency,
                    plan_id: planData?.id,
                    billing_cycle: planData?.billingCycle || 'monthly'
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create order');
            }

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message || 'Failed to create order');
            }

            return data.data;
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    };

    const handlePayment = async () => {
        if (!razorpayLoaded) {
            if (onError) onError('Payment gateway not loaded');
            return;
        }

        setLoading(true);
        try {
            const orderData = await createOrder();

            const options = {
                key: orderData.razorpay_key_id,
                amount: orderData.amount,
                currency: orderData.currency,
                name: 'Bizma',
                description: `${planData?.name || 'Subscription'} - ${planData?.billingCycle || 'monthly'} billing`,
                order_id: orderData.order_id,
                prefill: {
                    name: userInfo?.name || '',
                    email: userInfo?.email || '',
                    contact: userInfo?.phone || ''
                },
                theme: {
                    color: '#3B82F6'
                },
                handler: async function(response) {
                    try {
                        // Verify payment on server
                        const verifyResponse = await fetch(window.getApiUrl('/payment/verify'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${localStorage.getItem('token')}`
                            },
                            body: JSON.stringify({
                                razorpay_order_id: response.razorpay_order_id,
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_signature: response.razorpay_signature,
                                order_id: orderData.order_id
                            })
                        });

                        const verifyData = await verifyResponse.json();
                        if (verifyData.success) {
                            if (onSuccess) onSuccess(verifyData.data);
                        } else {
                            if (onError) onError(verifyData.message || 'Payment verification failed');
                        }
                    } catch (error) {
                        console.error('Payment verification error:', error);
                        if (onError) onError('Payment verification failed');
                    }
                },
                modal: {
                    ondismiss: function() {
                        if (onClose) onClose();
                    }
                }
            };

            const rzp = new window.Razorpay(options);
            rzp.open();

        } catch (error) {
            console.error('Payment error:', error);
            if (onError) onError(error.message || 'Payment failed');
        } finally {
            setLoading(false);
        }
    };

    return (
        <button
            onClick={handlePayment}
            disabled={loading || !razorpayLoaded}
            className={`${buttonClass} ${(loading || !razorpayLoaded) ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
            {loading ? (
                <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Processing...
                </>
            ) : !razorpayLoaded ? (
                <>
                    <i className="fas fa-clock mr-2"></i>
                    Loading...
                </>
            ) : (
                <>
                    <i className="fas fa-credit-card mr-2"></i>
                    {buttonText}
                </>
            )}
        </button>
    );
}

// Payment Status Component
function PaymentStatus({ status, message, onRetry, onClose }) {
    const getStatusIcon = () => {
        switch (status) {
            case 'success':
                return 'fas fa-check-circle text-green-500';
            case 'error':
                return 'fas fa-times-circle text-red-500';
            case 'processing':
                return 'fas fa-spinner fa-spin text-blue-500';
            default:
                return 'fas fa-info-circle text-gray-500';
        }
    };

    const getStatusColor = () => {
        switch (status) {
            case 'success':
                return 'bg-green-50 border-green-200';
            case 'error':
                return 'bg-red-50 border-red-200';
            case 'processing':
                return 'bg-blue-50 border-blue-200';
            default:
                return 'bg-gray-50 border-gray-200';
        }
    };

    return (
        <div className={`border rounded-lg p-6 ${getStatusColor()}`}>
            <div className="text-center">
                <i className={`${getStatusIcon()} text-4xl mb-4`}></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {status === 'success' && 'Payment Successful!'}
                    {status === 'error' && 'Payment Failed'}
                    {status === 'processing' && 'Processing Payment...'}
                </h3>
                {message && (
                    <p className="text-gray-600 mb-4">{message}</p>
                )}
                
                <div className="flex justify-center space-x-3">
                    {status === 'error' && onRetry && (
                        <button
                            onClick={onRetry}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                        >
                            Try Again
                        </button>
                    )}
                    {onClose && (
                        <button
                            onClick={onClose}
                            className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                        >
                            Close
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}

// Payment Summary Component
function PaymentSummary({ planData, billingCycle, amount, currency = 'INR' }) {
    const formatAmount = (amt) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0
        }).format(amt);
    };

    return (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Payment Summary</h4>
            
            <div className="space-y-2">
                <div className="flex justify-between">
                    <span className="text-gray-600">Plan:</span>
                    <span className="font-medium">{planData?.name || 'Selected Plan'}</span>
                </div>
                
                <div className="flex justify-between">
                    <span className="text-gray-600">Billing:</span>
                    <span className="font-medium capitalize">{billingCycle || 'monthly'}</span>
                </div>
                
                {billingCycle === 'yearly' && planData?.yearly_savings > 0 && (
                    <div className="flex justify-between text-green-600">
                        <span>Savings:</span>
                        <span className="font-medium">
                            {formatAmount(planData.yearly_savings)}
                        </span>
                    </div>
                )}
                
                <div className="border-t pt-2 mt-2">
                    <div className="flex justify-between text-lg font-bold">
                        <span>Total:</span>
                        <span>{formatAmount(amount)}</span>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Upgrade Payment Modal Component
function UpgradePaymentModal({ 
    isOpen, 
    onClose, 
    planData, 
    billingCycle, 
    userInfo, 
    onSuccess 
}) {
    const [paymentStatus, setPaymentStatus] = React.useState(null);
    const amount = billingCycle === 'yearly' ? planData?.price_yearly : planData?.price_monthly;

    const handlePaymentSuccess = (data) => {
        setPaymentStatus({ status: 'success', message: 'Your subscription has been upgraded successfully!' });
        setTimeout(() => {
            if (onSuccess) onSuccess(data);
            onClose();
        }, 2000);
    };

    const handlePaymentError = (error) => {
        setPaymentStatus({ status: 'error', message: error });
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            Upgrade Subscription
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    {paymentStatus ? (
                        <PaymentStatus
                            status={paymentStatus.status}
                            message={paymentStatus.message}
                            onRetry={() => setPaymentStatus(null)}
                            onClose={onClose}
                        />
                    ) : (
                        <>
                            <PaymentSummary
                                planData={planData}
                                billingCycle={billingCycle}
                                amount={amount}
                            />

                            <div className="text-center">
                                <RazorpayPayment
                                    amount={amount}
                                    planData={{ ...planData, billingCycle }}
                                    userInfo={userInfo}
                                    onSuccess={handlePaymentSuccess}
                                    onError={handlePaymentError}
                                    onClose={onClose}
                                    buttonText={`Pay ${new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', minimumFractionDigits: 0 }).format(amount)}`}
                                    buttonClass="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 font-medium"
                                />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}

// Make components globally available
window.RazorpayPayment = RazorpayPayment;
window.PaymentStatus = PaymentStatus;
window.PaymentSummary = PaymentSummary;
window.UpgradePaymentModal = UpgradePaymentModal;
