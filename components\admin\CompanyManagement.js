// Company/Subscriber Management Component for Super Admin
function CompanyManagement({ authContext, setNotification }) {
    const [companies, setCompanies] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [selectedCompany, setSelectedCompany] = React.useState(null);
    const [showDetails, setShowDetails] = React.useState(false);
    const [filters, setFilters] = React.useState({
        status: 'all',
        plan: 'all',
        search: ''
    });
    const [stats, setStats] = React.useState({
        total: 0,
        active: 0,
        trial: 0,
        expired: 0
    });

    React.useEffect(() => {
        fetchCompanies();
        fetchStats();
    }, [filters]);

    const fetchCompanies = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams(filters);
            const response = await fetch(window.getApiUrl(`/super-admin/companies?${params}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setCompanies(data.data || []);
                }
            }
        } catch (error) {
            console.error('Error fetching companies:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load companies'
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchStats = async () => {
        try {
            const response = await fetch(window.getApiUrl('/super-admin/companies/stats'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setStats(data.data);
                }
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
        }
    };

    const handleStatusChange = async (companyId, newStatus) => {
        try {
            const response = await fetch(window.getApiUrl('/super-admin/companies/status'), {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ company_id: companyId, status: newStatus })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Company status updated to ${newStatus}`
                    });
                    fetchCompanies();
                    fetchStats();
                } else {
                    throw new Error(data.message);
                }
            } else {
                throw new Error('Failed to update status');
            }
        } catch (error) {
            console.error('Error updating status:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to update company status'
            });
        }
    };

    const handleViewDetails = (company) => {
        setSelectedCompany(company);
        setShowDetails(true);
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            active: { color: 'green', text: 'Active' },
            trial: { color: 'blue', text: 'Trial' },
            expired: { color: 'red', text: 'Expired' },
            suspended: { color: 'yellow', text: 'Suspended' },
            cancelled: { color: 'gray', text: 'Cancelled' }
        };

        const config = statusConfig[status] || statusConfig.cancelled;
        return (
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${config.color}-100 text-${config.color}-800`}>
                {config.text}
            </span>
        );
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header with Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <i className="fas fa-building text-white text-sm"></i>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Total Companies</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <i className="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Active</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.active}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <i className="fas fa-clock text-white text-sm"></i>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Trial</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.trial}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                                <i className="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">Expired</p>
                            <p className="text-2xl font-semibold text-gray-900">{stats.expired}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input
                            type="text"
                            value={filters.search}
                            onChange={(e) => setFilters({...filters, search: e.target.value})}
                            placeholder="Company name or email..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select
                            value={filters.status}
                            onChange={(e) => setFilters({...filters, status: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="trial">Trial</option>
                            <option value="expired">Expired</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Plan</label>
                        <select
                            value={filters.plan}
                            onChange={(e) => setFilters({...filters, plan: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="all">All Plans</option>
                            <option value="basic">Basic</option>
                            <option value="professional">Professional</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>
                    <div className="flex items-end">
                        <button
                            onClick={fetchCompanies}
                            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                        >
                            <i className="fas fa-search mr-2"></i>
                            Search
                        </button>
                    </div>
                </div>
            </div>

            {/* Companies Table */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Companies ({companies.length})</h3>
                </div>
                
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {companies.map((company) => (
                                <tr key={company.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div className="text-sm font-medium text-gray-900">{company.name}</div>
                                            <div className="text-sm text-gray-500">{company.owner_email}</div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{company.plan_name || 'No Plan'}</div>
                                        <div className="text-sm text-gray-500">₹{company.plan_price || 0}/month</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        {getStatusBadge(company.subscription_status)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {company.user_count || 0}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {new Date(company.created_at).toLocaleDateString()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button
                                            onClick={() => handleViewDetails(company)}
                                            className="text-blue-600 hover:text-blue-900"
                                            title="View Details"
                                        >
                                            <i className="fas fa-eye"></i>
                                        </button>
                                        <button
                                            onClick={() => handleStatusChange(company.id, 
                                                company.subscription_status === 'active' ? 'suspended' : 'active')}
                                            className={`${company.subscription_status === 'active' 
                                                ? 'text-red-600 hover:text-red-900' 
                                                : 'text-green-600 hover:text-green-900'}`}
                                            title={company.subscription_status === 'active' ? 'Suspend' : 'Activate'}
                                        >
                                            <i className={`fas ${company.subscription_status === 'active' 
                                                ? 'fa-ban' : 'fa-check-circle'}`}></i>
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {companies.length === 0 && (
                    <div className="text-center py-12">
                        <div className="text-gray-400 mb-4">
                            <i className="fas fa-building text-4xl"></i>
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Companies Found</h3>
                        <p className="text-gray-600">No companies match your current filters.</p>
                    </div>
                )}
            </div>

            {/* Company Details Modal */}
            {showDetails && selectedCompany && (
                <CompanyDetailsModal
                    company={selectedCompany}
                    isOpen={showDetails}
                    onClose={() => setShowDetails(false)}
                    authContext={authContext}
                    setNotification={setNotification}
                />
            )}
        </div>
    );
}

// Company Details Modal Component
function CompanyDetailsModal({ company, isOpen, onClose, authContext, setNotification }) {
    const [companyDetails, setCompanyDetails] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [activeTab, setActiveTab] = React.useState('overview');

    React.useEffect(() => {
        if (isOpen && company) {
            fetchCompanyDetails();
        }
    }, [isOpen, company]);

    const fetchCompanyDetails = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl(`/super-admin/companies/details?company_id=${company.object_id}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setCompanyDetails(data.data);
                }
            }
        } catch (error) {
            console.error('Error fetching company details:', error);
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-bold text-gray-900">
                        Company Details: {company.name}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                {loading ? (
                    <div className="flex justify-center items-center py-12">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                ) : (
                    <div>
                        {/* Tabs */}
                        <div className="border-b border-gray-200 mb-6">
                            <nav className="-mb-px flex space-x-8">
                                {['overview', 'subscription', 'usage', 'users'].map((tab) => (
                                    <button
                                        key={tab}
                                        onClick={() => setActiveTab(tab)}
                                        className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                                            activeTab === tab
                                                ? 'border-blue-500 text-blue-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        {tab}
                                    </button>
                                ))}
                            </nav>
                        </div>

                        {/* Tab Content */}
                        {activeTab === 'overview' && companyDetails && (
                            <div className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h4 className="text-lg font-medium text-gray-900 mb-4">Company Information</h4>
                                        <dl className="space-y-2">
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Name</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.name}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Industry</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.industry || 'Not specified'}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Size</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.size || 'Not specified'}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Business Type</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.business_type_name || 'Not specified'}</dd>
                                            </div>
                                        </dl>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-medium text-gray-900 mb-4">Owner Information</h4>
                                        <dl className="space-y-2">
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Name</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.owner_name}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Email</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.owner_email}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Phone</dt>
                                                <dd className="text-sm text-gray-900">{companyDetails.owner_phone || 'Not provided'}</dd>
                                            </div>
                                            <div>
                                                <dt className="text-sm font-medium text-gray-500">Joined</dt>
                                                <dd className="text-sm text-gray-900">{new Date(companyDetails.created_at).toLocaleDateString()}</dd>
                                            </div>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeTab === 'subscription' && companyDetails && (
                            <div className="space-y-4">
                                <h4 className="text-lg font-medium text-gray-900">Subscription Details</h4>
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Current Plan</dt>
                                            <dd className="text-sm text-gray-900">{companyDetails.subscription?.plan_name || 'No active plan'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Status</dt>
                                            <dd className="text-sm text-gray-900">{companyDetails.subscription?.status || 'No subscription'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Billing Cycle</dt>
                                            <dd className="text-sm text-gray-900">{companyDetails.subscription?.billing_cycle || 'N/A'}</dd>
                                        </div>
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Amount</dt>
                                            <dd className="text-sm text-gray-900">₹{companyDetails.subscription?.amount || 0}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        )}

                        {activeTab === 'usage' && companyDetails && (
                            <div className="space-y-4">
                                <h4 className="text-lg font-medium text-gray-900">Usage Statistics</h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="bg-blue-50 rounded-lg p-4">
                                        <div className="text-2xl font-bold text-blue-600">{companyDetails.usage?.users || 0}</div>
                                        <div className="text-sm text-gray-600">Active Users</div>
                                    </div>
                                    <div className="bg-green-50 rounded-lg p-4">
                                        <div className="text-2xl font-bold text-green-600">{companyDetails.usage?.leads || 0}</div>
                                        <div className="text-sm text-gray-600">Total Leads</div>
                                    </div>
                                    <div className="bg-purple-50 rounded-lg p-4">
                                        <div className="text-2xl font-bold text-purple-600">{companyDetails.usage?.customers || 0}</div>
                                        <div className="text-sm text-gray-600">Customers</div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}

// Make components globally available
window.CompanyManagement = CompanyManagement;
window.CompanyDetailsModal = CompanyDetailsModal;
