<?php
// Include database configuration
require_once 'db-config.php';

// Email of the user to update
$adminEmail = '<EMAIL>'; // Change this to the actual admin email

// Update the user role to super_admin
$sql = "UPDATE users SET role = 'super_admin' WHERE email = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $adminEmail);

if ($stmt->execute()) {
    echo "User role updated successfully to super_admin for $adminEmail";
} else {
    echo "Error updating user role: " . $conn->error;
}

// Close the connection
$stmt->close();
$conn->close();
?>