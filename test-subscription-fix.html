<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Subscription Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Subscription API Fix</h1>
        
        <div class="test-section">
            <h2>Test 1: New Query Parameter Method</h2>
            <button onclick="testNewMethod()">Test /api/api.php?endpoint=subscription&limit=10</button>
            <div id="new-method-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Using trickleListObjects Function</h2>
            <button onclick="testTrickleFunction()">Test trickleListObjects('subscription', 10)</button>
            <div id="trickle-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Direct API Call (Old Method)</h2>
            <button onclick="testOldMethod()">Test /api/api.php/subscription?limit=10</button>
            <div id="old-method-result"></div>
        </div>
    </div>

    <!-- Include the updated api-utils.js -->
    <script src="/biz/utils/api-utils.js"></script>

    <script>
        // Check authentication status on load
        window.addEventListener('load', function() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                alert('⚠️ No auth token found. Please login first at /biz/login.html');
            }
        });
        
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return fetch(url, {
                ...options,
                headers
            });
        }
        
        async function testNewMethod() {
            const resultDiv = document.getElementById('new-method-result');
            resultDiv.innerHTML = '<p>Testing new query parameter method...</p>';
            
            try {
                const response = await makeAuthenticatedRequest('/biz/api/api.php?endpoint=subscription&limit=10');
                
                const text = await response.text();
                console.log('New method response:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.error ? '<p><strong>❌ Error: ' + data.error + '</strong></p>' : ''}
                    ${response.ok ? '<p><strong>✅ Request Successful!</strong></p>' : '<p><strong>❌ Request Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testTrickleFunction() {
            const resultDiv = document.getElementById('trickle-result');
            resultDiv.innerHTML = '<p>Testing trickleListObjects function...</p>';
            
            try {
                console.log('Calling trickleListObjects...');
                const data = await trickleListObjects('subscription', 10);
                console.log('Trickle function response:', data);
                
                resultDiv.className = 'test-section success';
                resultDiv.innerHTML = `
                    <h3>✅ Function Call Successful</h3>
                    <h4>Response:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Trickle function error:', error);
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h3>❌ Function Call Failed</h3>
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        async function testOldMethod() {
            const resultDiv = document.getElementById('old-method-result');
            resultDiv.innerHTML = '<p>Testing old path-style method...</p>';
            
            try {
                const response = await makeAuthenticatedRequest('/biz/api/api.php/subscription?limit=10');
                
                const text = await response.text();
                console.log('Old method response:', text);
                
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${response.ok ? '<p><strong>✅ Request Successful!</strong></p>' : '<p><strong>❌ Request Failed (Expected)</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
