<?php
/**
 * Create refresh tokens table for enhanced authentication
 */

require_once 'api/db-config.php';

echo "<h2>Creating Refresh Tokens Table</h2>\n";

$sql = "CREATE TABLE IF NOT EXISTS refresh_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES users(object_id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "<p style='color: green;'>✅ Refresh tokens table created successfully</p>\n";
} else {
    echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>\n";
}

// Clean up expired tokens procedure
$cleanupSql = "CREATE EVENT IF NOT EXISTS cleanup_expired_tokens
ON SCHEDULE EVERY 1 DAY
DO
DELETE FROM refresh_tokens WHERE expires_at < NOW() OR is_active = 0";

if ($conn->query($cleanupSql) === TRUE) {
    echo "<p style='color: green;'>✅ Token cleanup event created</p>\n";
} else {
    echo "<p style='color: orange;'>⚠️ Could not create cleanup event: " . $conn->error . "</p>\n";
}

$conn->close();
?>