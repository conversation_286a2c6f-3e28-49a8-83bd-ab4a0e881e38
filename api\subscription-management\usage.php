<?php
require_once '../config/database.php';
require_once '../utils/SecurityUtils.php';
require_once '../utils/AuthUtils.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication
    $authResult = AuthUtils::verifyToken();
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    $user = $authResult['user'];
    $conn = Database::getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        handleGetUsageStats($conn, $user);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Usage Stats API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetUsageStats($conn, $user) {
    try {
        // Get user's company
        $companyStmt = $conn->prepare("SELECT object_id FROM companies WHERE owner_id = ? OR id = ?");
        $companyStmt->bind_param("ss", $user['object_id'], $user['company_id']);
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if ($companyRow = $companyResult->fetch_assoc()) {
            $companyId = $companyRow['object_id'];
            
            // Get current subscription limits
            $subscriptionStmt = $conn->prepare("
                SELECT p.max_users, p.max_leads, p.max_storage
                FROM subscriptions s
                LEFT JOIN plans p ON s.plan_id = p.id
                WHERE s.company_id = ? AND s.status IN ('active', 'trial')
                ORDER BY s.created_at DESC 
                LIMIT 1
            ");
            $subscriptionStmt->bind_param("s", $companyId);
            $subscriptionStmt->execute();
            $subscriptionResult = $subscriptionStmt->get_result();
            
            $limits = [
                'max_users' => null,
                'max_leads' => null,
                'max_storage' => null
            ];
            
            if ($subscriptionRow = $subscriptionResult->fetch_assoc()) {
                $limits = [
                    'max_users' => $subscriptionRow['max_users'],
                    'max_leads' => $subscriptionRow['max_leads'],
                    'max_storage' => $subscriptionRow['max_storage']
                ];
            }
            
            // Count current usage
            
            // Count users in the company
            $userCountStmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE company_id = ?");
            $userCountStmt->bind_param("s", $user['company_id']);
            $userCountStmt->execute();
            $userCountResult = $userCountStmt->get_result();
            $userCount = $userCountResult->fetch_assoc()['count'];
            
            // Count leads for the company
            $leadCountStmt = $conn->prepare("SELECT COUNT(*) as count FROM leads WHERE company_id = ?");
            $leadCountStmt->bind_param("s", $companyId);
            $leadCountStmt->execute();
            $leadCountResult = $leadCountStmt->get_result();
            $leadCount = $leadCountResult->fetch_assoc()['count'];
            
            // Calculate storage usage (simplified - you might want to implement actual file size calculation)
            $storageUsed = 0; // Default to 0 for now
            
            // Get leads created this month for trend analysis
            $thisMonthStmt = $conn->prepare("
                SELECT COUNT(*) as count 
                FROM leads 
                WHERE company_id = ? AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
            ");
            $thisMonthStmt->bind_param("s", $companyId);
            $thisMonthStmt->execute();
            $thisMonthResult = $thisMonthStmt->get_result();
            $leadsThisMonth = $thisMonthResult->fetch_assoc()['count'];
            
            // Get customers count
            $customerCountStmt = $conn->prepare("SELECT COUNT(*) as count FROM customers WHERE company_id = ?");
            $customerCountStmt->bind_param("s", $companyId);
            $customerCountStmt->execute();
            $customerCountResult = $customerCountStmt->get_result();
            $customerCount = $customerCountResult->fetch_assoc()['count'];
            
            $usageData = [
                // Current usage
                'users_used' => $userCount,
                'leads_used' => $leadCount,
                'customers_count' => $customerCount,
                'storage_used' => $storageUsed,
                
                // Limits from subscription
                'users_limit' => $limits['max_users'],
                'leads_limit' => $limits['max_leads'],
                'storage_limit' => $limits['max_storage'],
                
                // Usage percentages
                'users_percentage' => $limits['max_users'] ? round(($userCount / $limits['max_users']) * 100, 1) : 0,
                'leads_percentage' => $limits['max_leads'] ? round(($leadCount / $limits['max_leads']) * 100, 1) : 0,
                'storage_percentage' => $limits['max_storage'] ? round(($storageUsed / $limits['max_storage']) * 100, 1) : 0,
                
                // Trend data
                'leads_this_month' => $leadsThisMonth,
                
                // Status indicators
                'users_near_limit' => $limits['max_users'] ? ($userCount / $limits['max_users']) > 0.8 : false,
                'leads_near_limit' => $limits['max_leads'] ? ($leadCount / $limits['max_leads']) > 0.8 : false,
                'storage_near_limit' => $limits['max_storage'] ? ($storageUsed / $limits['max_storage']) > 0.8 : false,
                
                // Additional metrics
                'last_updated' => date('Y-m-d H:i:s')
            ];
            
            echo json_encode(['success' => true, 'data' => $usageData]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Company not found']);
        }
    } catch (Exception $e) {
        error_log("Error getting usage stats: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to get usage statistics']);
    }
}
?>
