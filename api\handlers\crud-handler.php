<?php
// Include required handlers with proper paths
require_once __DIR__ . '/items-handler.php';
require_once __DIR__ . '/object-handler.php';
require_once __DIR__ . '/field-handler.php';
require_once __DIR__ . '/validation-handler.php';
require_once __DIR__ . '/subscription-handler.php';

// Function to create an object
function createObjectHandler($objectType) {
    global $conn;

    // Get current user and enforce authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }

    // Get request body
    $requestBody = file_get_contents('php://input');
    $objectData = json_decode($requestBody, true);

    if (!$objectData) {
        error_log("createObject: Invalid request body for $objectType: " . $requestBody);
        http_response_code(400);
        echo json_encode(['error' => 'Invalid request body']);
        return;
    }

    error_log("createObject: Creating $objectType with data: " . print_r($objectData, true));
    
    // Handle subcategory object types (format: item_subcategory:parentId)
    $parts = explode(':', $objectType);
    $baseType = $parts[0];
    $parentId = isset($parts[1]) ? $parts[1] : null;

    // Handle subscription with special handler
    if ($baseType === 'subscription') {
        createSubscription($objectData);
        return;
    }
    
    // Validate and sanitize object data
    $validation = ValidationHandler::processObjectData($baseType, $objectData);
    if (!$validation['valid']) {
        http_response_code(422);
        echo json_encode(ValidationHandler::formatValidationErrors($validation['errors']));
        return;
    }
    
    $objectData = $validation['data'];
    
    // Map object type to database table
    $table = mapObjectTypeToTable($baseType);
    
    if (!$table) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid object type']);
        return;
    }
    
    // Generate object ID
    $objectId = generateId();
    
    // Extract fields based on object type
    $fields = extractFields($baseType, $objectData);

    // Handle special field types (arrays, JSON, etc.)
    if ($baseType === 'lead' && isset($fields['tags']) && is_array($fields['tags'])) {
        $fields['tags'] = json_encode($fields['tags']);
    }

    // Add object ID and parent ID if applicable
    $fields['object_id'] = $objectId;
    if ($baseType === 'item_subcategory' && $parentId) {
        $fields['parent_id'] = $parentId;
    }
    
    // Add user_id for activities, tasks, and notes
    if (in_array($baseType, ['activity', 'task', 'note'])) {
        if (isset($currentUser['object_id'])) {
            $fields['user_id'] = $currentUser['object_id'];
        }
    }

    // Add company field for multi-tenant isolation (if table supports it)
    if (tableHasCompanyId($table)) {
        if ($table === 'contracts') {
            // For contracts, we don't add company_id directly since it uses customer_id
            // The customer_id should be provided in the request data
        } else {
            $companyField = getCompanyFieldName($table);
            
            // Ensure user has company_id
            if (!isset($currentUser['company_id']) || empty($currentUser['company_id'])) {
                // If user doesn't have company_id, create a default one or use user's object_id
                $companyId = isset($currentUser['object_id']) ? $currentUser['object_id'] : 'default_company';
                error_log("Warning: User has no company_id, using: " . $companyId);
            } else {
                $companyId = $currentUser['company_id'];
            }
            
            $fields[$companyField] = $companyId;
            error_log("Setting $companyField to: " . $companyId);
        }
    }
    
    // Build SQL query
    $columns = implode(', ', array_keys($fields));
    $placeholders = implode(', ', array_fill(0, count($fields), '?'));
    
    $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
    
    // Prepare statement
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    // Bind parameters
    $types = str_repeat('s', count($fields));
    $stmt->bind_param($types, ...array_values($fields));
    
    // Execute statement
    error_log("createObject: Executing SQL: $sql with values: " . print_r(array_values($fields), true));
    if ($stmt->execute()) {
        error_log("createObject: Successfully created $baseType with ID: $objectId");

        // Handle related items if needed
        if (isset($objectData['items']) && in_array($baseType, ['invoice', 'quotation', 'contract'])) {
            saveRelatedItems($objectId, $objectData['items'], $baseType);
        }

        // Get the created object
        $createdObject = [
            'objectId' => $objectId,
            'objectType' => $baseType,
            'objectData' => $objectData,
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ];

        http_response_code(201);
        echo json_encode($createdObject);
    } else {
        error_log("createObject: Failed to create $baseType: " . $stmt->error);
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create object: ' . $stmt->error]);
    }
    
    $stmt->close();
}

// Function to update an object
function updateObjectHandler($objectType, $objectId) {
    global $conn;
    
    error_log("updateObject called: objectType=$objectType, objectId=$objectId");
    
    // Get request body
    $requestBody = file_get_contents('php://input');
    error_log("Request body: " . $requestBody);
    
    $objectData = json_decode($requestBody, true);
    error_log("Parsed object data: " . print_r($objectData, true));
    
    if (!$objectData) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid request body']);
        return;
    }
    
    // Handle subcategory object types (format: item_subcategory:parentId)
    $parts = explode(':', $objectType);
    $baseType = $parts[0];
    error_log("updateObject: baseType extracted = $baseType");

    // Handle subscription with special handler
    if ($baseType === 'subscription') {
        updateSubscription($objectId, $objectData);
        return;
    }
    
    // Validate and sanitize object data
    error_log("updateObject: Starting validation for baseType: $baseType");
    $validation = ValidationHandler::processObjectData($baseType, $objectData);
    error_log("updateObject: Validation result: " . print_r($validation, true));
    
    if (!$validation['valid']) {
        error_log("updateObject: Validation failed: " . print_r($validation['errors'], true));
        http_response_code(422);
        echo json_encode(ValidationHandler::formatValidationErrors($validation['errors']));
        return;
    }
    
    $objectData = $validation['data'];
    error_log("updateObject: Validated object data: " . print_r($objectData, true));
    
    // Map object type to database table
    $table = mapObjectTypeToTable($baseType);
    
    if (!$table) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid object type']);
        return;
    }
    
    // Sanitize object ID
    $safeObjectId = sanitizeInput($objectId);
    
    // Check if object exists using prepared statement
    $checkSql = "SELECT * FROM $table WHERE object_id = ?";
    $checkStmt = $conn->prepare($checkSql);
    if (!$checkStmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare check statement: ' . $conn->error]);
        return;
    }
    $checkStmt->bind_param("s", $safeObjectId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => "Object with ID $objectId not found"]);
        $checkStmt->close();
        return;
    }
    $checkStmt->close();
    
    // Extract fields based on object type
    $fields = extractFields($baseType, $objectData);

    // Handle special field types (arrays, JSON, etc.)
    if ($baseType === 'lead' && isset($fields['tags']) && is_array($fields['tags'])) {
        $fields['tags'] = json_encode($fields['tags']);
    }

    // Add updated timestamp
    $fields['updated_at'] = date('Y-m-d H:i:s');
    
    // Build SQL query
    $setClause = [];
    foreach ($fields as $key => $value) {
        $setClause[] = "$key = ?";
    }
    $setClause = implode(', ', $setClause);
    
    $sql = "UPDATE $table SET $setClause WHERE object_id = ?";
    
    // Prepare statement
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    // Bind parameters
    $types = str_repeat('s', count($fields)) . 's';
    $params = array_values($fields);
    $params[] = $safeObjectId;
    $stmt->bind_param($types, ...$params);
    
    // Execute statement
    if ($stmt->execute()) {
        // Handle related items if needed
        if (isset($objectData['items']) && in_array($baseType, ['invoice', 'quotation', 'contract'])) {
            // Delete existing items first
            deleteRelatedItems($safeObjectId, $baseType);
            // Save new items
            saveRelatedItems($safeObjectId, $objectData['items'], $baseType);
        }
        
        // Get the updated object
        $updatedObject = [
            'objectId' => $objectId,
            'objectType' => $baseType,
            'objectData' => $objectData,
            'updatedAt' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($updatedObject);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update object: ' . $stmt->error]);
    }
    
    $stmt->close();
}

// Function to delete an object
function deleteObjectHandler($objectType, $objectId) {
    global $conn;
    
    // Handle subcategory object types (format: item_subcategory:parentId)
    $parts = explode(':', $objectType);
    $baseType = $parts[0];

    // Handle subscription with special handler
    if ($baseType === 'subscription') {
        deleteSubscription($objectId);
        return;
    }
    
    // Map object type to database table
    $table = mapObjectTypeToTable($baseType);
    
    if (!$table) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid object type']);
        return;
    }
    
    // Sanitize object ID
    $safeObjectId = sanitizeInput($objectId);
    
    // Check if object exists
    $checkSql = "SELECT * FROM $table WHERE object_id = '$safeObjectId'";
    $result = $conn->query($checkSql);
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => "Object with ID $objectId not found"]);
        return;
    }
    
    // Delete related items first if needed
    if (in_array($baseType, ['invoice', 'quotation', 'contract'])) {
        deleteRelatedItems($safeObjectId, $baseType);
    }
    
    // Delete the object using prepared statement
    $sql = "DELETE FROM $table WHERE object_id = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare delete statement: ' . $conn->error]);
        return;
    }
    
    $stmt->bind_param("s", $safeObjectId);
    
    if ($stmt->execute()) {
        http_response_code(204); // No content
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete object: ' . $stmt->error]);
    }
    
    $stmt->close();
}

// Function to get a single object
function getObjectHandler($objectType, $objectId) {
    global $conn;

    // Get current user and enforce authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }

    // Handle subcategory object types (format: item_subcategory:parentId)
    $parts = explode(':', $objectType);
    $baseType = $parts[0];

    // Handle subscription with special handler
    if ($baseType === 'subscription') {
        getSubscription($objectId);
        return;
    }

    // Map object type to database table
    $table = mapObjectTypeToTable($baseType);

    if (!$table) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid object type']);
        return;
    }
    
    // Sanitize object ID
    $safeObjectId = sanitizeInput($objectId);
    
    // Query to get object with company isolation using prepared statements
    if (tableHasCompanyId($table)) {
        if ($table === 'contracts') {
            // For contracts, join with customers to filter by company
            $sql = "SELECT c.* FROM $table c
                    JOIN customers cu ON c.customer_id = cu.object_id
                    WHERE c.object_id = ? AND cu.company_id = ?";
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
                return;
            }
            $stmt->bind_param("ss", $safeObjectId, $currentUser['company_id']);
        } else {
            $companyField = getCompanyFieldName($table);
            $sql = "SELECT * FROM $table WHERE object_id = ? AND $companyField = ?";
            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
                return;
            }
            $stmt->bind_param("ss", $safeObjectId, $currentUser['company_id']);
        }
    } else {
        $sql = "SELECT * FROM $table WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
            return;
        }
        $stmt->bind_param("s", $safeObjectId);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        
        // Convert row to object
        $object = rowToObject($row, $baseType);
        
        // Handle related items if needed
        handleRelatedItems($object, $baseType);
        
        echo json_encode($object);
    } else {
        http_response_code(404);
        echo json_encode(['error' => "Object with ID $objectId not found"]);
    }
    
    $stmt->close();
}

// Function to list objects
function listObjectsHandler($objectType) {
    global $conn;

    // Get current user and enforce authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }

    // Get query parameters
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
    $descent = isset($_GET['descent']) ? ($_GET['descent'] === 'true') : true;
    $nextPageToken = isset($_GET['nextPageToken']) ? $_GET['nextPageToken'] : null;
    
    // Handle subcategory object types (format: item_subcategory:parentId)
    $parts = explode(':', $objectType);
    $baseType = $parts[0];
    $parentId = isset($parts[1]) ? $parts[1] : null;

    // Handle subscription with special handler
    if ($baseType === 'subscription') {
        getSubscriptions();
        return;
    }
    
    // Map object type to database table
    $table = mapObjectTypeToTable($baseType);
    
    if (!$table) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid object type']);
        return;
    }
    
    // Build query with company isolation
    $whereConditions = [];

    // Add company isolation for multi-tenant tables
    if (tableHasCompanyId($table)) {
        if ($table === 'contracts') {
            // For contracts, join with customers to filter by company
            $sql = "SELECT c.* FROM $table c
                    JOIN customers cu ON c.customer_id = cu.object_id";
            $whereConditions[] = "cu.company_id = '" . sanitizeInput($currentUser['company_id']) . "'";
        } else {
            $sql = "SELECT * FROM $table";
            $companyField = getCompanyFieldName($table);
            $whereConditions[] = "$companyField = '" . sanitizeInput($currentUser['company_id']) . "'";
        }
    } else {
        $sql = "SELECT * FROM $table";
    }

    // Add parent ID filter for subcategories
    if ($baseType === 'item_subcategory' && $parentId) {
        $safeParentId = sanitizeInput($parentId);
        $whereConditions[] = "parent_id = '$safeParentId'";
    }
    
    // Add lead ID filter for activities, tasks, and notes
    if (in_array($baseType, ['activity', 'task', 'note']) && $parentId) {
        $safeLeadId = sanitizeInput($parentId);
        
        // Check if the lead exists first
        $leadCheckSql = "SELECT object_id FROM leads WHERE object_id = ?";
        if (tableHasCompanyId('leads')) {
            $leadCheckSql .= " AND company_id = ?";
            $leadStmt = $conn->prepare($leadCheckSql);
            $leadStmt->bind_param("ss", $safeLeadId, $currentUser['company_id']);
        } else {
            $leadStmt = $conn->prepare($leadCheckSql);
            $leadStmt->bind_param("s", $safeLeadId);
        }
        
        $leadStmt->execute();
        $leadResult = $leadStmt->get_result();
        
        if ($leadResult->num_rows === 0) {
            // Lead doesn't exist, return error with helpful message
            $leadStmt->close();
            error_log("Activities requested for non-existent lead: $safeLeadId");
            http_response_code(404);
            echo json_encode([
                'error' => 'Lead not found',
                'message' => "Lead with ID '$safeLeadId' does not exist or you don't have access to it.",
                'leadId' => $safeLeadId
            ]);
            return;
        }
        $leadStmt->close();
        
        $whereConditions[] = "lead_id = '$safeLeadId'";
    }

    // Apply WHERE conditions
    if (!empty($whereConditions)) {
        $sql .= " WHERE " . implode(" AND ", $whereConditions);
    }
    
    // Add sorting
    $sql .= " ORDER BY created_at " . ($descent ? "DESC" : "ASC");
    
    // Add pagination
    if ($nextPageToken) {
        $offset = intval($nextPageToken);
        $sql .= " LIMIT $offset, $limit";
        $nextOffset = $offset + $limit;
    } else {
        $sql .= " LIMIT $limit";
        $nextOffset = $limit;
    }
    
    $result = $conn->query($sql);
    
    // Count total objects for pagination
    $countSql = "SELECT COUNT(*) as total FROM $table";
    if ($baseType === 'item_subcategory' && $parentId) {
        $safeParentId = sanitizeInput($parentId);
        $countSql .= " WHERE parent_id = '$safeParentId'";
    }
    $countResult = $conn->query($countSql);
    $totalCount = $countResult->fetch_assoc()['total'];
    
    // Build response
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $object = rowToObject($row, $baseType);
        
        // Handle related items if needed
        handleRelatedItems($object, $baseType);
        
        $items[] = $object;
    }
    
    // Determine if there are more pages
    $hasMorePages = ($nextOffset < $totalCount);
    
    $response = [
        'items' => $items,
        'nextPageToken' => $hasMorePages ? (string)$nextOffset : null
    ];
    
    echo json_encode($response);
}

// Helper function to check if table has company_id column for multi-tenancy
function tableHasCompanyId($table) {
    $multiTenantTables = [
        'customers',
        'invoices',
        'quotations',
        'contracts',
        'payments',
        'subscriptions',
        'users',
        'leads',
        'activities',
        'tasks',
        'notes'
    ];

    return in_array($table, $multiTenantTables);
}

function getCompanyFieldName($table) {
    $companyFields = [
        'customers' => 'company_id',
        'invoices' => 'company_id',
        'quotations' => 'company_id',
        'contracts' => 'customer_id',  // contracts use customer_id instead of company_id
        'payments' => 'company_id',
        'subscriptions' => 'company_id',
        'users' => 'company_id',
        'leads' => 'company_id',
        'activities' => 'company_id',
        'tasks' => 'company_id',
        'notes' => 'company_id'
    ];

    return isset($companyFields[$table]) ? $companyFields[$table] : 'company_id';
}








?>
