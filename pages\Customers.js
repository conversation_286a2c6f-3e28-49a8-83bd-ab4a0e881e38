function Customers() {
    try {
        // State management
        const [showForm, setShowForm] = React.useState(false);
        const [selectedCustomer, setSelectedCustomer] = React.useState(null);
        const [showDetails, setShowDetails] = React.useState(false);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [notification, setNotification] = React.useState(null);
        const [refreshKey, setRefreshKey] = React.useState(0);
        
        // Enhanced state for better UX
        const [searchTerm, setSearchTerm] = React.useState('');
        const [filterType, setFilterType] = React.useState('all');
        const [sortBy, setSortBy] = React.useState('name');
        const [sortOrder, setSortOrder] = React.useState('asc');
        const [selectedCustomers, setSelectedCustomers] = React.useState([]);
        const [showBulkActions, setShowBulkActions] = React.useState(false);
        const [isExporting, setIsExporting] = React.useState(false);
        const [viewMode, setViewMode] = React.useState('grid');
        
        // Get authentication context
        const authContext = React.useContext(window.AuthContext);
        
        // Navigation function using the improved routing system
        const navigate = (page, id = null, action = null, params = {}) => {
            window.dispatchEvent(new CustomEvent('app-navigate', { 
                detail: { 
                    page,
                    id,
                    action,
                    params
                } 
            }));
        };

        // Event handlers
        const handleCreateCustomer = () => {
            setSelectedCustomer(null);
            setShowForm(true);
            setShowDetails(false);
        };

        const handleEditCustomer = (customer) => {
            setSelectedCustomer(customer);
            setShowForm(true);
            setShowDetails(false);
        };

        const handleCustomerClick = (customer) => {
            setSelectedCustomer(customer);
            setShowDetails(true);
            setShowForm(false);
        };

        const handleConfirmDeleteCustomer = () => {
            setShowDeleteConfirm(true);
        };

        const handleDeleteCustomer = async () => {
            if (!selectedCustomer) return;

            try {
                // Get token from auth context or localStorage
                const token = (authContext && authContext.token) || localStorage.getItem('authToken');
                
                if (!token) {
                    throw new Error('Authentication required');
                }
                
                // Use the API utility for better error handling
                const deleteUrl = window.getApiUrl(`/customer/${selectedCustomer.objectId}`);
                
                const response = await fetch(deleteUrl, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || `Failed to delete customer (${response.status})`);
                }
                
                // Success handling
                setNotification({
                    type: 'success',
                    message: 'Customer deleted successfully',
                    duration: 3000
                });
                
                // Reset UI state
                setShowDeleteConfirm(false);
                setSelectedCustomer(null);
                setShowDetails(false);
                setRefreshKey(prev => prev + 1);
                
                // Clear selected customers if in bulk mode
                if (selectedCustomers.includes(selectedCustomer.objectId)) {
                    setSelectedCustomers(selectedCustomers.filter(id => id !== selectedCustomer.objectId));
                }
            } catch (error) {
                console.error('Delete customer error:', error);
                
                // Handle authentication errors
                if (error.message.includes('Authentication') || error.message.includes('401')) {
                    localStorage.removeItem('authToken');
                    // Redirect to login
                    window.dispatchEvent(new CustomEvent('app-navigate', { 
                        detail: { 
                            page: 'login',
                            params: { redirect: 'customers' }
                        } 
                    }));
                    return;
                }
                
                // Show error notification
                setNotification({
                    type: 'error',
                    message: error.message || 'An error occurred while deleting the customer',
                    duration: 5000
                });
            }
        };

        const handleFormSubmit = (customerData) => {
            setNotification({
                type: 'success',
                message: selectedCustomer ? 'Customer updated successfully' : 'Customer created successfully'
            });
            setShowForm(false);
            setSelectedCustomer(null);
            setRefreshKey(prev => prev + 1);
        };

        const handleFormCancel = () => {
            setShowForm(false);
            setSelectedCustomer(null);
        };

        const handleDetailsClose = () => {
            setShowDetails(false);
            setSelectedCustomer(null);
        };

        const handleSearch = (term) => {
            setSearchTerm(term);
        };

        const handleFilter = (type) => {
            setFilterType(type);
        };

        const handleSort = (field) => {
            if (sortBy === field) {
                setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
            } else {
                setSortBy(field);
                setSortOrder('asc');
            }
        };

        const handleCustomerSelect = (customerId, isSelected) => {
            if (isSelected) {
                setSelectedCustomers(prev => [...prev, customerId]);
            } else {
                setSelectedCustomers(prev => prev.filter(id => id !== customerId));
            }
        };

        const handleSelectAll = (customers, isSelected) => {
            if (isSelected) {
                setSelectedCustomers(customers.map(c => c.objectId));
            } else {
                setSelectedCustomers([]);
            }
        };

        const handleBulkDelete = async () => {
            if (selectedCustomers.length === 0) return;

            try {
                const token = localStorage.getItem('authToken');
                const deletePromises = selectedCustomers.map(customerId =>
                    fetch(`${window.APP_CONFIG.API_BASE_URL}/customer/${customerId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    })
                );

                await Promise.all(deletePromises);
                
                setNotification({
                    type: 'success',
                    message: `${selectedCustomers.length} customers deleted successfully`
                });
                setSelectedCustomers([]);
                setShowBulkActions(false);
                setRefreshKey(prev => prev + 1);
            } catch (error) {
                console.error('Bulk delete error:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete some customers'
                });
            }
        };

        const handleExportCustomers = async () => {
            setIsExporting(true);
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch('/biz1/api/api.php/customers/export', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'customers.csv';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    
                    setNotification({
                        type: 'success',
                        message: 'Customers exported successfully'
                    });
                } else {
                    throw new Error('Export failed');
                }
            } catch (error) {
                console.error('Export error:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to export customers'
                });
            } finally {
                setIsExporting(false);
            }
        };

        // Main render
        return (
            <div data-name="customers-page" className="p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
                        <p className="text-gray-600 mt-1">Manage your customer relationships</p>
                    </div>
                    <div className="flex items-center space-x-3">
                        {selectedCustomers.length > 0 && (
                            <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600">
                                    {selectedCustomers.length} selected
                                </span>
                                <Button
                                    variant="secondary"
                                    size="sm"
                                    onClick={() => setShowBulkActions(true)}
                                    icon="fas fa-cog"
                                >
                                    Bulk Actions
                                </Button>
                            </div>
                        )}
                        <Button
                            variant="secondary"
                            onClick={handleExportCustomers}
                            disabled={isExporting}
                            icon="fas fa-download"
                        >
                            {isExporting ? 'Exporting...' : 'Export'}
                        </Button>
                        <Button
                            variant="primary"
                            onClick={handleCreateCustomer}
                            icon="fas fa-plus"
                        >
                            Add Customer
                        </Button>
                    </div>
                </div>

                {/* Notification */}
                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {/* Search and Filter Bar */}
                <div className="bg-white rounded-lg shadow p-4 mb-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <Input
                                type="text"
                                placeholder="Search customers..."
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                                icon="fas fa-search"
                            />
                        </div>
                        <div className="flex gap-2">
                            <select
                                value={filterType}
                                onChange={(e) => handleFilter(e.target.value)}
                                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="all">All Customers</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="vip">VIP</option>
                            </select>
                            <Button
                                variant={viewMode === 'grid' ? 'primary' : 'secondary'}
                                size="sm"
                                onClick={() => setViewMode('grid')}
                                icon="fas fa-th"
                            />
                            <Button
                                variant={viewMode === 'list' ? 'primary' : 'secondary'}
                                size="sm"
                                onClick={() => setViewMode('list')}
                                icon="fas fa-list"
                            />
                        </div>
                    </div>
                </div>

                {/* Bulk Actions Modal */}
                {showBulkActions && (
                    <Modal
                        isOpen={showBulkActions}
                        onClose={() => setShowBulkActions(false)}
                        title="Bulk Actions"
                    >
                        <div className="p-4">
                            <p className="mb-4">
                                Selected {selectedCustomers.length} customers. Choose an action:
                            </p>
                            <div className="flex justify-end space-x-3">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowBulkActions(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleBulkDelete}
                                    icon="fas fa-trash"
                                >
                                    Delete Selected
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}

                {/* Main Content */}
                {showForm ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedCustomer ? 'Edit Customer' : 'New Customer'}
                        </h2>
                        <CustomerForm
                            customer={selectedCustomer}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : showDetails && selectedCustomer ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <CustomerDetails
                            customer={selectedCustomer}
                            onEdit={() => handleEditCustomer(selectedCustomer)}
                            onDelete={handleConfirmDeleteCustomer}
                            onClose={handleDetailsClose}
                        />
                    </div>
                ) : (
                    <CustomerList 
                        key={refreshKey} 
                        onCustomerClick={handleCustomerClick}
                        onCustomerSelect={handleCustomerSelect}
                        onSelectAll={handleSelectAll}
                        selectedCustomers={selectedCustomers}
                        searchTerm={searchTerm}
                        filterType={filterType}
                        sortBy={sortBy}
                        sortOrder={sortOrder}
                        viewMode={viewMode}
                        onSort={handleSort}
                    />
                )}

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <Modal
                        isOpen={showDeleteConfirm}
                        onClose={() => setShowDeleteConfirm(false)}
                        title="Delete Customer"
                    >
                        <div className="p-4">
                            <p className="mb-4">
                                Are you sure you want to delete this customer? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-3">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleDeleteCustomer}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Customers page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return (
            <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Customers</h2>
                    <p className="text-red-600">
                        There was an error loading the customers page. Please refresh and try again.
                    </p>
                </div>
            </div>
        );
    }
}