<?php
/**
 * Check API Error Logs
 */

echo "📋 Checking API Error Logs...\n\n";

// Check PHP error log
$phpErrorLog = 'C:\xampp\php\logs\php_error_log';
if (file_exists($phpErrorLog)) {
    echo "📄 PHP Error Log (last 20 lines):\n";
    echo "=====================================\n";
    $lines = file($phpErrorLog);
    $lastLines = array_slice($lines, -20);
    foreach ($lastLines as $line) {
        echo $line;
    }
    echo "\n";
}

// Check Apache error log
$apacheErrorLog = 'C:\xampp\apache\logs\error.log';
if (file_exists($apacheErrorLog)) {
    echo "📄 Apache Error Log (last 10 lines):\n";
    echo "====================================\n";
    $lines = file($apacheErrorLog);
    $lastLines = array_slice($lines, -10);
    foreach ($lastLines as $line) {
        echo $line;
    }
    echo "\n";
}

// Check custom API logs
$apiLogPath = __DIR__ . '/api/logs/api_errors.log';
if (file_exists($apiLogPath)) {
    echo "📄 API Error Log (last 10 lines):\n";
    echo "==================================\n";
    $lines = file($apiLogPath);
    $lastLines = array_slice($lines, -10);
    foreach ($lastLines as $line) {
        echo $line;
    }
    echo "\n";
}

echo "✅ Log check completed.\n";
?>
