<?php
/**
 * Security Middleware
 * Handles authentication, authorization, rate limiting, and security headers
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../db-config.php';

class SecurityMiddleware {
    private static $rateLimitStore = [];
    
    /**
     * Apply security headers
     */
    public static function applySecurityHeaders() {
        if (Config::isProduction()) {
            // Strict security headers for production
            header('X-Frame-Options: DENY');
            header('X-XSS-Protection: 1; mode=block');
            header('X-Content-Type-Options: nosniff');
            header('Referrer-Policy: strict-origin-when-cross-origin');
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
            header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\'; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data: https:; font-src \'self\'; connect-src \'self\'; frame-ancestors \'none\';');
        } else {
            // Relaxed headers for development
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
            header('X-Content-Type-Options: nosniff');
        }
        
        // Common headers
        header('X-Powered-By: Business Management SaaS');
    }
    
    /**
     * Apply CORS headers
     */
    public static function applyCorsHeaders() {
        $allowedOrigins = explode(',', Config::get('CORS_ALLOWED_ORIGINS', '*'));
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';

        // For development, allow all origins
        if (!Config::isProduction()) {
            header('Access-Control-Allow-Origin: *');
        } else if (in_array('*', $allowedOrigins) || in_array($origin, $allowedOrigins)) {
            header("Access-Control-Allow-Origin: $origin");
        }

        header('Access-Control-Allow-Methods: ' . Config::get('CORS_ALLOWED_METHODS', 'GET,POST,PUT,DELETE,OPTIONS'));
        header('Access-Control-Allow-Headers: ' . Config::get('CORS_ALLOWED_HEADERS', 'Content-Type,Authorization,X-Requested-With'));
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');
    }
    
    /**
     * Rate limiting
     */
    public static function rateLimit($identifier = null, $maxRequests = null, $windowSeconds = null) {
        // Skip rate limiting in CLI mode
        if (php_sapi_name() === 'cli') {
            return;
        }

        $identifier = $identifier ?: self::getClientIdentifier();
        $maxRequests = $maxRequests ?: (int)Config::get('RATE_LIMIT_REQUESTS', 100);
        $windowSeconds = $windowSeconds ?: (int)Config::get('RATE_LIMIT_WINDOW', 3600);
        
        $now = time();
        $windowStart = $now - $windowSeconds;
        
        // Clean old entries
        if (isset(self::$rateLimitStore[$identifier])) {
            self::$rateLimitStore[$identifier] = array_filter(
                self::$rateLimitStore[$identifier],
                function($timestamp) use ($windowStart) {
                    return $timestamp > $windowStart;
                }
            );
        } else {
            self::$rateLimitStore[$identifier] = [];
        }
        
        // Check if limit exceeded
        if (count(self::$rateLimitStore[$identifier]) >= $maxRequests) {
            http_response_code(429);
            header('Retry-After: ' . $windowSeconds);
            echo json_encode([
                'error' => 'Rate limit exceeded',
                'retry_after' => $windowSeconds
            ]);
            exit;
        }
        
        // Add current request
        self::$rateLimitStore[$identifier][] = $now;
        
        // Add rate limit headers
        $remaining = max(0, $maxRequests - count(self::$rateLimitStore[$identifier]));
        header("X-RateLimit-Limit: $maxRequests");
        header("X-RateLimit-Remaining: $remaining");
        header("X-RateLimit-Reset: " . ($now + $windowSeconds));
    }
    
    /**
     * Authenticate user
     */
    public static function authenticate($required = true) {
        $user = getCurrentUser();
        
        if ($required && !$user) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            exit;
        }
        
        return $user;
    }
    
    /**
     * Authorize company access
     */
    public static function authorizeCompany($companyId, $userId = null) {
        if (!$userId) {
            $user = self::authenticate();
            $userId = $user['object_id'];
        }
        
        if (!validateCompanyAccess($companyId, $userId)) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied to this company']);
            exit;
        }
        
        return true;
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateCsrf() {
        // Skip CSRF validation in CLI mode
        if (php_sapi_name() === 'cli') {
            return true;
        }

        // Skip CSRF validation for API endpoints (they use JWT authentication)
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($uri, '/api/') !== false) {
            return true;
        }

        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        if ($method === 'GET' || $method === 'OPTIONS') {
            return true;
        }

        $token = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_POST['_token'] ?? null;
        $sessionToken = $_SESSION['csrf_token'] ?? null;

        if (!$token || !$sessionToken || !hash_equals($sessionToken, $token)) {
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token mismatch']);
            exit;
        }

        return true;
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Generate JWT-like token (simplified implementation)
     */
    public static function generateJWT($payload) {
        // For now, generate a simple token and store it in database
        $token = bin2hex(random_bytes(32));

        // Store token in database with expiry
        $db = Config::getDatabase();
        $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);

        if ($conn->connect_error) {
            throw new Exception('Database connection failed');
        }

        $expires = date('Y-m-d H:i:s', $payload['exp']);
        $user_id = $payload['user_id'];

        $stmt = $conn->prepare("UPDATE users SET auth_token = ?, token_expires = ? WHERE id = ?");
        $stmt->bind_param("ssi", $token, $expires, $user_id);
        $stmt->execute();

        $conn->close();
        return $token;
    }

    /**
     * Verify JWT-like token (simplified implementation)
     */
    public static function verifyJWT($token) {
        $db = Config::getDatabase();
        $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);

        if ($conn->connect_error) {
            throw new Exception('Database connection failed');
        }

        $stmt = $conn->prepare("SELECT * FROM users WHERE auth_token = ? AND token_expires > NOW()");
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $conn->close();
            return [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'company_id' => $user['company_id'],
                'role' => $user['role']
            ];
        }

        $conn->close();
        throw new Exception('Invalid or expired token');
    }

    /**
     * Sanitize input data
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        // Handle null values
        if ($data === null) {
            return '';
        }
        
        // Convert to string if not already
        if (!is_string($data)) {
            $data = (string)$data;
        }

        // Remove null bytes
        $data = str_replace("\0", '', $data);
        
        // Trim whitespace
        $data = trim($data);
        
        // Convert special characters to HTML entities
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        return $data;
    }
    
    /**
     * Validate input against common injection patterns
     */
    public static function validateInput($data, $type = 'general') {
        $patterns = [
            'sql' => '/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i',
            'xss' => '/(<script|javascript:|vbscript:|onload=|onerror=|onclick=)/i',
            'path' => '/(\.\.|\/\.\.|\\\\\.\.)/i',
            'email' => '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            'phone' => '/^[\+]?[1-9][\d]{0,15}$/',
            'general' => '/[<>"\'\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/i'
        ];
        
        if ($type === 'email') {
            return preg_match($patterns['email'], $data);
        }
        
        if ($type === 'phone') {
            return preg_match($patterns['phone'], $data);
        }
        
        // Check for malicious patterns
        foreach (['sql', 'xss', 'path'] as $pattern) {
            if (preg_match($patterns[$pattern], $data)) {
                return false;
            }
        }
        
        // Check general pattern for control characters
        if (preg_match($patterns['general'], $data)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Log security event
     */
    public static function logSecurityEvent($event, $details = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => self::getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details
        ];
        
        $logFile = Config::get('LOG_FILE', './logs/security.log');
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        error_log(json_encode($logData) . "\n", 3, $logFile);
    }
    
    /**
     * Get client IP address
     */
    private static function getClientIp() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Get client identifier for rate limiting
     */
    private static function getClientIdentifier() {
        // Return CLI identifier in CLI mode
        if (php_sapi_name() === 'cli') {
            return 'cli_user';
        }

        $user = getCurrentUser();
        if ($user) {
            return 'user_' . $user['object_id'];
        }

        return 'ip_' . self::getClientIp();
    }
    
    /**
     * Initialize security middleware
     */
    public static function init() {
        // Skip initialization in CLI mode
        if (php_sapi_name() === 'cli') {
            return;
        }

        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Apply security headers
        self::applySecurityHeaders();

        // Apply CORS headers
        self::applyCorsHeaders();

        // Handle preflight requests
        if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }

        // Apply rate limiting
        self::rateLimit();

        // Validate CSRF for state-changing requests
        if (Config::isProduction()) {
            self::validateCsrf();
        }
    }
}

// Auto-initialize security middleware
SecurityMiddleware::init();
?>
