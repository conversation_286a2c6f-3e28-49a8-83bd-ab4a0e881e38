// Simple Test Framework for Bizma
class TestFramework {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            pending: 0,
            total: 0
        };
        this.currentSuite = null;
    }

    describe(suiteName, callback) {
        this.currentSuite = suiteName;
        callback();
        this.currentSuite = null;
    }

    it(testName, testFunction) {
        this.tests.push({
            suite: this.currentSuite,
            name: testName,
            test: testFunction,
            status: 'pending'
        });
    }

    async runTest(testIndex) {
        const test = this.tests[testIndex];
        test.status = 'running';
        
        try {
            await test.test();
            test.status = 'passed';
            test.result = 'Test passed successfully';
            this.results.passed++;
        } catch (error) {
            test.status = 'failed';
            test.result = error.message || 'Test failed';
            test.error = error;
            this.results.failed++;
        }
        
        this.results.total++;
        return test;
    }

    async runAllTests() {
        this.results = { passed: 0, failed: 0, pending: 0, total: 0 };
        
        for (let i = 0; i < this.tests.length; i++) {
            await this.runTest(i);
        }
        
        return this.results;
    }

    expect(actual) {
        return new Assertion(actual);
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

class Assertion {
    constructor(actual) {
        this.actual = actual;
    }

    toBe(expected) {
        if (this.actual !== expected) {
            throw new Error(`Expected ${expected}, but got ${this.actual}`);
        }
        return this;
    }

    toEqual(expected) {
        if (JSON.stringify(this.actual) !== JSON.stringify(expected)) {
            throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(this.actual)}`);
        }
        return this;
    }

    toContain(expected) {
        if (!this.actual.includes(expected)) {
            throw new Error(`Expected ${this.actual} to contain ${expected}`);
        }
        return this;
    }

    toBeTruthy() {
        if (!this.actual) {
            throw new Error(`Expected ${this.actual} to be truthy`);
        }
        return this;
    }

    toBeFalsy() {
        if (this.actual) {
            throw new Error(`Expected ${this.actual} to be falsy`);
        }
        return this;
    }

    toBeGreaterThan(expected) {
        if (this.actual <= expected) {
            throw new Error(`Expected ${this.actual} to be greater than ${expected}`);
        }
        return this;
    }

    toBeLessThan(expected) {
        if (this.actual >= expected) {
            throw new Error(`Expected ${this.actual} to be less than ${expected}`);
        }
        return this;
    }

    toHaveProperty(property) {
        if (!(property in this.actual)) {
            throw new Error(`Expected object to have property ${property}`);
        }
        return this;
    }

    toBeInstanceOf(constructor) {
        if (!(this.actual instanceof constructor)) {
            throw new Error(`Expected ${this.actual} to be instance of ${constructor.name}`);
        }
        return this;
    }
}

// Test Runner UI Controller
class TestRunner {
    constructor() {
        this.framework = new TestFramework();
        this.testSuites = {};
        this.isRunning = false;
    }

    addTestSuite(suiteName, tests) {
        this.testSuites[suiteName] = tests;
    }

    async runAllTests() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.showProgress();
        this.clearResults();
        
        // Load all test suites
        const allTests = [];
        for (const [suiteName, tests] of Object.entries(this.testSuites)) {
            tests.forEach(test => {
                allTests.push({ ...test, suite: suiteName });
            });
        }
        
        let completed = 0;
        const total = allTests.length;
        
        for (const test of allTests) {
            this.updateProgress(completed, total, `Running: ${test.name}`);
            
            const result = await this.runSingleTest(test);
            this.displayTestResult(test.suite, result);
            
            completed++;
            this.updateProgress(completed, total, `Completed: ${test.name}`);
            
            // Small delay to show progress
            await this.framework.sleep(100);
        }
        
        this.updateSummary();
        this.isRunning = false;
        this.hideProgress();
    }

    async runSingleTest(test) {
        const startTime = Date.now();
        
        try {
            await test.test();
            return {
                name: test.name,
                status: 'passed',
                duration: Date.now() - startTime,
                message: 'Test passed successfully'
            };
        } catch (error) {
            return {
                name: test.name,
                status: 'failed',
                duration: Date.now() - startTime,
                message: error.message || 'Test failed',
                error: error
            };
        }
    }

    displayTestResult(suiteName, result) {
        const containerId = this.getSuiteContainerId(suiteName);
        const container = document.getElementById(containerId);
        
        if (!container) return;
        
        const testElement = document.createElement('div');
        testElement.className = `flex items-center justify-between p-2 rounded ${
            result.status === 'passed' ? 'bg-green-50' : 'bg-red-50'
        }`;
        
        const statusIcon = result.status === 'passed' ? 
            '<i class="fas fa-check-circle test-pass"></i>' : 
            '<i class="fas fa-times-circle test-fail"></i>';
        
        testElement.innerHTML = `
            <div class="flex items-center">
                ${statusIcon}
                <span class="ml-2 text-sm font-medium">${result.name}</span>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">${result.duration}ms</span>
                ${result.status === 'failed' ? `
                    <button onclick="this.nextElementSibling.classList.toggle('hidden')" 
                            class="text-xs text-red-600 hover:text-red-800">
                        Details
                    </button>
                    <div class="hidden absolute z-10 bg-red-100 border border-red-300 rounded p-2 text-xs max-w-md">
                        ${result.message}
                    </div>
                ` : ''}
            </div>
        `;
        
        container.appendChild(testElement);
    }

    getSuiteContainerId(suiteName) {
        const mapping = {
            'Authentication': 'authTests',
            'Subscription': 'subscriptionTests',
            'Plan Management': 'planTests',
            'Payment Integration': 'paymentTests',
            'Business Type': 'businessTypeTests',
            'API Integration': 'apiTests'
        };
        return mapping[suiteName] || 'authTests';
    }

    updateSummary() {
        const summary = document.getElementById('testSummary');
        summary.classList.remove('hidden');
        
        const passed = document.querySelectorAll('.test-pass').length;
        const failed = document.querySelectorAll('.test-fail').length;
        const total = passed + failed;
        
        document.getElementById('passedCount').textContent = passed;
        document.getElementById('failedCount').textContent = failed;
        document.getElementById('pendingCount').textContent = 0;
        document.getElementById('totalCount').textContent = total;
    }

    showProgress() {
        document.getElementById('testProgress').classList.remove('hidden');
    }

    hideProgress() {
        document.getElementById('testProgress').classList.add('hidden');
    }

    updateProgress(completed, total, message) {
        const percentage = (completed / total) * 100;
        document.getElementById('progressBar').style.width = `${percentage}%`;
        document.getElementById('progressText').textContent = message;
    }

    clearResults() {
        const containers = ['authTests', 'subscriptionTests', 'planTests', 'paymentTests', 'businessTypeTests', 'apiTests'];
        containers.forEach(id => {
            const container = document.getElementById(id);
            if (container) {
                container.innerHTML = '';
            }
        });
        
        document.getElementById('testSummary').classList.add('hidden');
    }

    // Helper methods for tests
    async makeApiCall(endpoint, options = {}) {
        const baseUrl = window.location.origin + '/biz/api';
        const url = `${baseUrl}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const response = await fetch(url, { ...defaultOptions, ...options });
        return {
            status: response.status,
            ok: response.ok,
            data: await response.json()
        };
    }

    expect(actual) {
        return new Assertion(actual);
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Make framework globally available
window.TestFramework = TestFramework;
window.TestRunner = TestRunner;
window.Assertion = Assertion;
