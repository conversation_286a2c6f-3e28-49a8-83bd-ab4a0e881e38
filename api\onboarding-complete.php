<?php
/**
 * Onboarding Completion API
 * Handles the completion of user onboarding process
 */

require_once 'db-config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid JSON input'
            ]);
            exit;
        }
        
        $userId = $input['user_id'] ?? '';
        $companyId = $input['company_id'] ?? '';
        $businessType = $input['businessType'] ?? '';
        $companyInfo = $input['companyInfo'] ?? [];
        $preferences = $input['preferences'] ?? [];
        $goals = $input['goals'] ?? [];
        
        if (empty($userId) || empty($companyId)) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'User ID and Company ID are required'
            ]);
            exit;
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Update user onboarding status
            $stmt = $conn->prepare("UPDATE users SET onboarding_completed = 1, updated_at = NOW() WHERE object_id = ?");
            $stmt->bind_param("s", $userId);
            $stmt->execute();
            
            // Update company information if provided
            if (!empty($companyInfo)) {
                $updateFields = [];
                $updateValues = [];
                $types = '';
                
                if (!empty($companyInfo['name'])) {
                    $updateFields[] = "name = ?";
                    $updateValues[] = $companyInfo['name'];
                    $types .= 's';
                }
                
                if (!empty($companyInfo['industry'])) {
                    $updateFields[] = "industry = ?";
                    $updateValues[] = $companyInfo['industry'];
                    $types .= 's';
                }
                
                if (!empty($companyInfo['size'])) {
                    $updateFields[] = "company_size = ?";
                    $updateValues[] = $companyInfo['size'];
                    $types .= 's';
                }
                
                if (!empty($companyInfo['location'])) {
                    $updateFields[] = "location = ?";
                    $updateValues[] = $companyInfo['location'];
                    $types .= 's';
                }
                
                if (!empty($businessType)) {
                    $updateFields[] = "business_type = ?";
                    $updateValues[] = $businessType;
                    $types .= 's';
                }
                
                if (!empty($updateFields)) {
                    $updateValues[] = $companyId;
                    $types .= 's';
                    
                    $sql = "UPDATE companies SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE object_id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param($types, ...$updateValues);
                    $stmt->execute();
                }
            }
            
            // Store user preferences
            if (!empty($preferences)) {
                foreach ($preferences as $key => $value) {
                    $stmt = $conn->prepare("
                        INSERT INTO user_preferences (user_id, preference_key, preference_value, created_at) 
                        VALUES (?, ?, ?, NOW()) 
                        ON DUPLICATE KEY UPDATE preference_value = VALUES(preference_value), updated_at = NOW()
                    ");
                    $stmt->bind_param("sss", $userId, $key, $value);
                    $stmt->execute();
                }
            }
            
            // Store user goals
            if (!empty($goals)) {
                // First, remove existing goals
                $stmt = $conn->prepare("DELETE FROM user_goals WHERE user_id = ?");
                $stmt->bind_param("s", $userId);
                $stmt->execute();
                
                // Insert new goals
                foreach ($goals as $goal) {
                    $stmt = $conn->prepare("INSERT INTO user_goals (user_id, goal, created_at) VALUES (?, ?, NOW())");
                    $stmt->bind_param("ss", $userId, $goal);
                    $stmt->execute();
                }
            }
            
            // Create onboarding completion record
            $onboardingData = json_encode([
                'business_type' => $businessType,
                'company_info' => $companyInfo,
                'preferences' => $preferences,
                'goals' => $goals,
                'completed_at' => date('Y-m-d H:i:s')
            ]);
            
            $stmt = $conn->prepare("
                INSERT INTO onboarding_data (user_id, company_id, onboarding_data, completed_at, created_at) 
                VALUES (?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                    onboarding_data = VALUES(onboarding_data), 
                    completed_at = VALUES(completed_at),
                    updated_at = NOW()
            ");
            $stmt->bind_param("sss", $userId, $companyId, $onboardingData);
            $stmt->execute();
            
            $conn->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'Onboarding completed successfully',
                'data' => [
                    'user_id' => $userId,
                    'company_id' => $companyId,
                    'business_type' => $businessType,
                    'onboarding_completed' => true
                ]
            ]);
            
        } catch (Exception $e) {
            $conn->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Onboarding Completion Error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to complete onboarding',
            'details' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

$conn->close();
?>