<?php
// Disable displaying errors directly to output
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Ensure we always return JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Custom error handler to capture all errors
function errorHandler($errno, $errstr, $errfile, $errline) {
    $error = [
        'success' => false,
        'message' => 'PHP Error: ' . $errstr,
        'error_details' => [
            'type' => $errno,
            'file' => basename($errfile),
            'line' => $errline
        ]
    ];
    
    http_response_code(500);
    echo json_encode($error);
    exit;
}

// Set custom error handler
set_error_handler('errorHandler');

try {
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../utils/SecurityUtils.php';
    require_once __DIR__ . '/../utils/EmailUtils.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to load dependencies: ' . $e->getMessage()]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Load configuration
    Config::load();
    
    // Get and parse input data
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input: ' . substr($rawInput, 0, 100) . (strlen($rawInput) > 100 ? '...' : ''));
    }
    
    // Log the input for debugging
    error_log('Registration input: ' . json_encode($input));
    
    // Validate required fields
    $required_fields = ['name', 'email', 'password', 'company_name'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        throw new Exception("Required fields missing: " . implode(', ', $missing_fields));
    }
    
    // Sanitize inputs
    $name = SecurityUtils::sanitizeInput($input['name']);
    $email = SecurityUtils::sanitizeInput($input['email']);
    $password = $input['password'];
    $company_name = SecurityUtils::sanitizeInput($input['company_name']);
    $company_size = SecurityUtils::sanitizeInput($input['company_size'] ?? '');
    $industry = SecurityUtils::sanitizeInput($input['industry'] ?? '');
    $selected_plan = SecurityUtils::sanitizeInput($input['selected_plan'] ?? 'basic');
    $business_type = $input['business_type'] ?? null;
    $business_type_id = $business_type ? SecurityUtils::sanitizeInput($business_type['id']) : null;
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // Validate password strength
    if (!SecurityUtils::validatePasswordStrength($password)) {
        throw new Exception('Password does not meet security requirements (minimum 8 characters with at least one uppercase letter, one lowercase letter, and one number)');
    }
    
    // Connect to database
    $db = Config::getDatabase();
    
    // Log database connection details (without password)
    error_log('Connecting to database: ' . $db['host'] . ', Database: ' . $db['database'] . ', User: ' . $db['username']);
    
    // Create connection with error handling
    $conn = @new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('Email already registered');
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate unique IDs
        $company_object_id = 'comp_' . time() . '_' . rand(100, 999);
        $user_object_id = 'user_' . time() . '_' . rand(100, 999);

        // Hash password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        // Generate email verification token
        $verification_token = bin2hex(random_bytes(32));

        // Create user first (without company_id initially)
        $user_stmt = $conn->prepare("
            INSERT INTO users (object_id, name, email, password_hash, role, email_verified, verification_token, created_at)
            VALUES (?, ?, ?, ?, 'admin', 0, ?, NOW())
        ");
        $user_stmt->bind_param("sssss", $user_object_id, $name, $email, $password_hash, $verification_token);
        $user_stmt->execute();
        $user_id = $conn->insert_id;

        // Create company with owner_id and business type
        $company_stmt = $conn->prepare("
            INSERT INTO companies (object_id, name, size, industry, business_type_id, subscription_plan, owner_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        $company_stmt->bind_param("sssssss", $company_object_id, $company_name, $company_size, $industry, $business_type_id, $selected_plan, $user_object_id);
        $company_stmt->execute();
        $company_id = $conn->insert_id;

        // Update user with company_id
        $update_user_stmt = $conn->prepare("UPDATE users SET company_id = ? WHERE object_id = ?");
        $update_user_stmt->bind_param("ss", $company_object_id, $user_object_id);
        $update_user_stmt->execute();
        
        // Create subscription record
        $subscription_object_id = 'sub_' . time() . '_' . rand(100, 999);
        $trial_end_date = date('Y-m-d H:i:s', strtotime('+14 days'));
        $subscription_stmt = $conn->prepare("
            INSERT INTO subscriptions (object_id, company_id, user_id, plan_id, plan_name, status, start_date, trial_end_date, billing_cycle, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, 'trial', NOW(), ?, ?, NOW(), NOW())
        ");
        $billing_cycle = isset($input['billing_cycle']) ? $input['billing_cycle'] : 'monthly';
        $subscription_stmt->bind_param("sssssss", $subscription_object_id, $company_object_id, $user_object_id, $selected_plan, $selected_plan, $trial_end_date, $billing_cycle);
        $subscription_stmt->execute();

        // Auto-configure business type settings if business type is selected
        if ($business_type_id && $business_type) {
            // Get business type configuration
            $business_type_stmt = $conn->prepare("SELECT default_modules, default_categories, default_features, default_templates FROM business_types WHERE id = ?");
            $business_type_stmt->bind_param("s", $business_type_id);
            $business_type_stmt->execute();
            $business_type_result = $business_type_stmt->get_result();

            if ($business_type_config = $business_type_result->fetch_assoc()) {
                // Store business type configuration in company settings
                $settings_to_create = [
                    'business_modules' => $business_type_config['default_modules'],
                    'business_categories' => $business_type_config['default_categories'],
                    'business_features' => $business_type_config['default_features'],
                    'business_templates' => $business_type_config['default_templates']
                ];

                foreach ($settings_to_create as $key => $value) {
                    $setting_object_id = 'setting_' . time() . '_' . rand(100, 999);
                    $setting_stmt = $conn->prepare("
                        INSERT INTO company_settings (object_id, company_id, setting_key, setting_value, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $setting_stmt->bind_param("ssss", $setting_object_id, $company_object_id, $key, $value);
                    $setting_stmt->execute();
                }
            }
        }

        // Commit transaction
        $conn->commit();
        
        // Send verification email (if email utils are available)
        $emailSent = false;
        $emailError = null;
        
        if (class_exists('EmailUtils')) {
            try {
                $verification_link = "http://" . $_SERVER['HTTP_HOST'] . "/verify-email.php?token=" . $verification_token;
                $emailResult = EmailUtils::sendVerificationEmail($email, $name, $verification_link);
                $emailSent = $emailResult['success'];
                
                if (!$emailSent) {
                    $emailError = $emailResult['message'];
                    error_log("Email verification failed: " . $emailError);
                }
            } catch (Exception $e) {
                // Log email error but don't fail registration
                $emailError = $e->getMessage();
                error_log("Email verification exception: " . $emailError);
            }
        }
        
        // Log successful registration
        error_log("New user registered: {$email} for company: {$company_name}");
        
        // Prepare response
        $response = [
            'success' => true,
            'message' => 'Registration successful!',
            'user' => [
                'id' => $user_id,
                'object_id' => $user_object_id,
                'name' => $name,
                'email' => $email,
                'company_id' => $company_object_id,
                'company_name' => $company_name,
                'role' => 'admin',
                'email_verified' => false
            ]
        ];
        
        // Add email status to response
        if ($emailSent) {
            $response['message'] .= ' Please check your email to verify your account.';
            $response['email_sent'] = true;
        } else {
            $response['message'] .= ' Your account has been created, but we could not send a verification email.';
            $response['email_sent'] = false;
            $response['email_error'] = $emailError;
            
            // Add mail configuration status for debugging
            if (class_exists('EmailUtils')) {
                $response['mail_config'] = EmailUtils::getMailConfigStatus();
            }
        }
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
    
    $conn->close();
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_type' => 'exception'
    ]);
    error_log("Registration error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'error_type' => 'fatal_error'
    ]);
    error_log("Registration fatal error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
}
?>
