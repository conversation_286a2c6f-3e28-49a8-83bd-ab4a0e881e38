-- Create company_settings table for storing flexible key-value settings
CREATE TABLE IF NOT EXISTS company_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_setting_key (setting_key),
    UNIQUE KEY unique_company_setting (company_id, setting_key)
);
