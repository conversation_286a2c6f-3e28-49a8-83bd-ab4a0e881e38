-- Enhanced SaaS Database Schema
-- This migration adds comprehensive support for:
-- 1. Business types and auto-configuration
-- 2. Enhanced plan management with trial/paid flows
-- 3. Payment gateway integration
-- 4. Dynamic policy management
-- 5. Super admin CMS functionality

-- Business Types Table
CREATE TABLE IF NOT EXISTS business_types (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    default_modules JSON,
    default_categories JSON,
    default_features JSON,
    default_templates JSON,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Enhanced Pricing Plans Table
CREATE TABLE IF NOT EXISTS pricing_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    price_monthly DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    price_yearly DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    trial_days INT DEFAULT 14,
    features JSON,
    limits_data JSON, -- Storage, users, leads, etc.
    business_types JSON, -- Which business types this plan supports
    is_trial_available BOOLEAN DEFAULT TRUE,
    is_visible BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Enhanced Subscriptions Table
-- First backup existing data if table exists
CREATE TABLE IF NOT EXISTS subscriptions_backup AS SELECT * FROM subscriptions WHERE 1=0;

-- Drop and recreate with enhanced schema
DROP TABLE IF EXISTS subscriptions;
CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    plan_id VARCHAR(50) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    status ENUM('trial', 'active', 'cancelled', 'expired', 'suspended') DEFAULT 'trial',
    billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(10) DEFAULT 'INR',
    
    -- Trial management
    trial_start_date DATETIME,
    trial_end_date DATETIME,
    trial_extended_days INT DEFAULT 0,
    
    -- Subscription dates
    start_date DATETIME,
    end_date DATETIME,
    next_billing_date DATETIME,
    
    -- Payment information
    payment_method VARCHAR(50),
    payment_gateway VARCHAR(50),
    gateway_subscription_id VARCHAR(255),
    last_payment_date DATETIME,
    last_payment_amount DECIMAL(10,2),
    
    -- Usage tracking
    features JSON,
    limits_data JSON,
    usage_data JSON,
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_object_id (object_id),
    INDEX idx_company_id (company_id),
    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_trial_end_date (trial_end_date),
    INDEX idx_next_billing_date (next_billing_date),
    
    FOREIGN KEY (plan_id) REFERENCES pricing_plans(id) ON UPDATE CASCADE
);

-- Payment Transactions Table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    subscription_id VARCHAR(50) NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    
    -- Transaction details
    transaction_type ENUM('subscription', 'upgrade', 'renewal', 'refund') DEFAULT 'subscription',
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'INR',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    
    -- Payment gateway details
    payment_gateway VARCHAR(50),
    gateway_transaction_id VARCHAR(255),
    gateway_order_id VARCHAR(255),
    gateway_payment_id VARCHAR(255),
    gateway_signature VARCHAR(255),
    gateway_response JSON,
    
    -- Billing details
    billing_period_start DATETIME,
    billing_period_end DATETIME,
    
    -- Metadata
    description TEXT,
    notes TEXT,
    processed_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_object_id (object_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_company_id (company_id),
    INDEX idx_status (status),
    INDEX idx_gateway_transaction_id (gateway_transaction_id),
    
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(object_id) ON DELETE CASCADE
);

-- System Settings Table for CMS
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value LONGTEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'html') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Whether this setting can be accessed by non-admin users
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
);

-- Policy Pages Table
CREATE TABLE IF NOT EXISTS policy_pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    page_type ENUM('terms', 'privacy', 'refund', 'cookie', 'disclaimer') NOT NULL,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT NOT NULL,
    template_variables JSON, -- Variables that get replaced dynamically
    is_active BOOLEAN DEFAULT TRUE,
    version VARCHAR(20) DEFAULT '1.0',
    effective_date DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_object_id (object_id),
    INDEX idx_page_type (page_type),
    INDEX idx_is_active (is_active)
);

-- Audit Logs Table for Super Admin Actions
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(50) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50), -- companies, users, subscriptions, etc.
    entity_id VARCHAR(50),
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_object_id (object_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_entity_type (entity_type),
    INDEX idx_entity_id (entity_id),
    INDEX idx_created_at (created_at)
);

-- Update existing companies table to include business type
ALTER TABLE companies
ADD COLUMN IF NOT EXISTS business_type_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS business_category VARCHAR(100),
ADD COLUMN IF NOT EXISTS subscription_plan VARCHAR(50) DEFAULT 'trial',
ADD COLUMN IF NOT EXISTS subscription_expires DATETIME,
ADD INDEX idx_business_type_id (business_type_id),
ADD INDEX idx_subscription_plan (subscription_plan);

-- Update existing users table for enhanced functionality
ALTER TABLE users
ADD COLUMN IF NOT EXISTS business_role VARCHAR(50),
ADD COLUMN IF NOT EXISTS preferences JSON,
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE,
ADD INDEX idx_business_role (business_role);

-- Insert default business types
INSERT INTO business_types (id, name, description, icon, default_modules, default_categories, default_features, default_templates, is_active, sort_order) VALUES
('jewellery', 'Jewellery Business', 'Specialized for jewellery stores, designers, and manufacturers', 'fas fa-gem',
 '["inventory", "customers", "orders", "repairs", "appraisals"]',
 '["rings", "necklaces", "earrings", "bracelets", "watches", "custom_designs"]',
 '["precious_metals_tracking", "stone_certification", "custom_design_tools", "repair_tracking"]',
 '["jewellery_invoice", "appraisal_certificate", "repair_receipt"]',
 TRUE, 1),

('retail', 'Retail Business', 'Perfect for retail stores, shops, and e-commerce businesses', 'fas fa-store',
 '["inventory", "pos", "customers", "orders", "suppliers"]',
 '["electronics", "clothing", "home_goods", "books", "sports", "beauty"]',
 '["barcode_scanning", "multi_location", "loyalty_programs", "discount_management"]',
 '["retail_invoice", "receipt", "purchase_order"]',
 TRUE, 2),

('education', 'Education Services', 'Designed for schools, training centers, and educational institutions', 'fas fa-graduation-cap',
 '["students", "courses", "instructors", "schedules", "assessments"]',
 '["academic_courses", "vocational_training", "online_courses", "certifications"]',
 '["student_portal", "grade_management", "attendance_tracking", "course_materials"]',
 '["student_invoice", "certificate", "progress_report"]',
 TRUE, 3),

('healthcare', 'Healthcare Services', 'Tailored for clinics, hospitals, and healthcare providers', 'fas fa-heartbeat',
 '["patients", "appointments", "treatments", "billing", "records"]',
 '["general_medicine", "dental", "physiotherapy", "diagnostics", "pharmacy"]',
 '["patient_records", "appointment_scheduling", "prescription_management", "insurance_billing"]',
 '["medical_invoice", "prescription", "treatment_plan"]',
 TRUE, 4),

('consulting', 'Consulting Services', 'Ideal for consultants, agencies, and professional services', 'fas fa-briefcase',
 '["clients", "projects", "time_tracking", "proposals", "contracts"]',
 '["business_consulting", "it_services", "marketing", "legal", "financial"]',
 '["project_management", "time_billing", "proposal_generation", "client_portal"]',
 '["consulting_proposal", "project_invoice", "service_agreement"]',
 TRUE, 5),

('manufacturing', 'Manufacturing', 'Built for manufacturers, suppliers, and production companies', 'fas fa-industry',
 '["production", "inventory", "suppliers", "quality", "orders"]',
 '["raw_materials", "finished_goods", "machinery", "tools", "packaging"]',
 '["production_planning", "quality_control", "supplier_management", "batch_tracking"]',
 '["production_order", "quality_report", "supplier_invoice"]',
 TRUE, 6)
ON DUPLICATE KEY UPDATE
name = VALUES(name),
description = VALUES(description),
icon = VALUES(icon),
default_modules = VALUES(default_modules),
default_categories = VALUES(default_categories),
default_features = VALUES(default_features),
default_templates = VALUES(default_templates),
is_active = VALUES(is_active),
sort_order = VALUES(sort_order);

-- Insert enhanced pricing plans
INSERT INTO pricing_plans (id, name, description, short_description, price_monthly, price_yearly, trial_days, features, limits_data, business_types, is_trial_available, is_visible, is_popular, sort_order) VALUES
('trial', 'Free Trial', 'Try all features free for 14 days', 'Full access trial', 0.00, 0.00, 14,
 '["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Basic Analytics", "Email Notifications", "Data Export"]',
 '{"max_customers": 50, "max_invoices": 20, "max_quotations": 20, "max_users": 2, "storage_gb": 1}',
 '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
 FALSE, FALSE, FALSE, 0),

('basic', 'Business Plan', 'Complete business management solution with all essential features', 'Everything you need to run your business', 500.00, 5000.00, 14,
 '["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Business Analytics", "Email Notifications", "Data Export", "Multi-user Access", "24/7 Support", "Custom Branding", "API Access"]',
 '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": 10, "storage_gb": 50}',
 '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
 TRUE, TRUE, TRUE, 1),

('premium', 'Premium Plan', 'Advanced features for growing businesses', 'Scale your business with premium tools', 1000.00, 10000.00, 14,
 '["Everything in Business Plan", "Advanced Analytics", "Custom Reports", "Priority Support", "White Label", "Advanced Integrations", "Bulk Operations", "Advanced Security"]',
 '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": 25, "storage_gb": 100}',
 '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
 TRUE, TRUE, FALSE, 2),

('enterprise', 'Enterprise Plan', 'Custom solutions for large organizations', 'Tailored for enterprise needs', 2500.00, 25000.00, 30,
 '["Everything in Premium Plan", "Custom Development", "Dedicated Support", "On-premise Deployment", "Advanced Security", "Custom Integrations", "Training & Onboarding"]',
 '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": -1, "storage_gb": 500}',
 '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
 TRUE, TRUE, FALSE, 3)
ON DUPLICATE KEY UPDATE
name = VALUES(name),
description = VALUES(description),
short_description = VALUES(short_description),
price_monthly = VALUES(price_monthly),
price_yearly = VALUES(price_yearly),
trial_days = VALUES(trial_days),
features = VALUES(features),
limits_data = VALUES(limits_data),
business_types = VALUES(business_types),
is_trial_available = VALUES(is_trial_available),
is_visible = VALUES(is_visible),
is_popular = VALUES(is_popular),
sort_order = VALUES(sort_order);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('company_name', 'Bizma', 'string', 'company', 'Company name displayed across the platform', TRUE),
('company_email', '<EMAIL>', 'string', 'company', 'Primary company email address', TRUE),
('company_phone', '+91-**********', 'string', 'company', 'Company contact phone number', TRUE),
('company_address', 'Business District, Tech City, India', 'string', 'company', 'Company physical address', TRUE),
('company_website', 'https://bizma.com', 'string', 'company', 'Company website URL', TRUE),
('support_email', '<EMAIL>', 'string', 'support', 'Support team email address', TRUE),
('trial_duration_days', '14', 'number', 'subscription', 'Default trial duration in days', FALSE),
('razorpay_key_id', '', 'string', 'payment', 'Razorpay Key ID for payment processing', FALSE),
('razorpay_key_secret', '', 'string', 'payment', 'Razorpay Key Secret for payment processing', FALSE),
('payment_currency', 'INR', 'string', 'payment', 'Default payment currency', TRUE),
('enable_trial_extension', 'true', 'boolean', 'subscription', 'Allow super admin to extend trials', FALSE),
('max_trial_extensions', '2', 'number', 'subscription', 'Maximum number of trial extensions allowed', FALSE),
('auto_suspend_expired_trials', 'true', 'boolean', 'subscription', 'Automatically suspend expired trials', FALSE),
('grace_period_days', '3', 'number', 'subscription', 'Grace period before suspending expired subscriptions', FALSE)
ON DUPLICATE KEY UPDATE
setting_value = VALUES(setting_value),
setting_type = VALUES(setting_type),
category = VALUES(category),
description = VALUES(description),
is_public = VALUES(is_public);

-- Insert default policy pages with template variables
INSERT INTO policy_pages (object_id, page_type, title, content, template_variables, is_active, version, effective_date) VALUES
('policy_terms_001', 'terms', 'Terms and Conditions',
'<h1>Terms and Conditions</h1>
<p>Last updated: {{EFFECTIVE_DATE}}</p>

<h2>1. Acceptance of Terms</h2>
<p>By accessing and using {{COMPANY_NAME}} services, you accept and agree to be bound by the terms and provision of this agreement.</p>

<h2>2. Service Description</h2>
<p>{{COMPANY_NAME}} provides business management software solutions including customer management, invoicing, quotations, and related services.</p>

<h2>3. Trial Period</h2>
<p>We offer a {{TRIAL_DAYS}} day free trial period. During this period, you have access to all features with certain limitations.</p>

<h2>4. Subscription and Payment</h2>
<p>After the trial period, continued use requires a paid subscription. Payments are processed securely through our payment partners.</p>

<h2>5. Data and Privacy</h2>
<p>Your data privacy is important to us. Please review our Privacy Policy for details on how we collect, use, and protect your information.</p>

<h2>6. Contact Information</h2>
<p>For questions about these Terms, please contact us at {{SUPPORT_EMAIL}} or {{COMPANY_PHONE}}.</p>

<p>{{COMPANY_NAME}}<br>
{{COMPANY_ADDRESS}}<br>
Email: {{COMPANY_EMAIL}}<br>
Phone: {{COMPANY_PHONE}}</p>',
'{"COMPANY_NAME": "company_name", "COMPANY_EMAIL": "company_email", "COMPANY_PHONE": "company_phone", "COMPANY_ADDRESS": "company_address", "SUPPORT_EMAIL": "support_email", "TRIAL_DAYS": "trial_duration_days", "EFFECTIVE_DATE": "current_date"}',
TRUE, '1.0', NOW()),

('policy_privacy_001', 'privacy', 'Privacy Policy',
'<h1>Privacy Policy</h1>
<p>Last updated: {{EFFECTIVE_DATE}}</p>

<h2>1. Information We Collect</h2>
<p>{{COMPANY_NAME}} collects information you provide directly to us, such as when you create an account, use our services, or contact us for support.</p>

<h2>2. How We Use Your Information</h2>
<p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

<h2>3. Information Sharing</h2>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

<h2>4. Data Security</h2>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

<h2>5. Contact Us</h2>
<p>If you have questions about this Privacy Policy, please contact us at {{SUPPORT_EMAIL}}.</p>

<p>{{COMPANY_NAME}}<br>
{{COMPANY_ADDRESS}}<br>
Email: {{COMPANY_EMAIL}}<br>
Phone: {{COMPANY_PHONE}}</p>',
'{"COMPANY_NAME": "company_name", "COMPANY_EMAIL": "company_email", "COMPANY_PHONE": "company_phone", "COMPANY_ADDRESS": "company_address", "SUPPORT_EMAIL": "support_email", "EFFECTIVE_DATE": "current_date"}',
TRUE, '1.0', NOW()),

('policy_refund_001', 'refund', 'Refund Policy',
'<h1>Refund Policy</h1>
<p>Last updated: {{EFFECTIVE_DATE}}</p>

<h2>1. Trial Period</h2>
<p>We offer a {{TRIAL_DAYS}} day free trial. No payment is required during the trial period.</p>

<h2>2. Subscription Refunds</h2>
<p>We offer a 30-day money-back guarantee for new subscriptions. If you are not satisfied with our service, you may request a full refund within 30 days of your initial payment.</p>

<h2>3. Refund Process</h2>
<p>To request a refund, please contact our support team at {{SUPPORT_EMAIL}}. Refunds will be processed within 5-7 business days.</p>

<h2>4. Cancellation</h2>
<p>You may cancel your subscription at any time. Your service will continue until the end of your current billing period.</p>

<h2>5. Contact Information</h2>
<p>For refund requests or questions, please contact us at {{SUPPORT_EMAIL}} or {{COMPANY_PHONE}}.</p>

<p>{{COMPANY_NAME}}<br>
{{COMPANY_ADDRESS}}<br>
Email: {{COMPANY_EMAIL}}<br>
Phone: {{COMPANY_PHONE}}</p>',
'{"COMPANY_NAME": "company_name", "COMPANY_EMAIL": "company_email", "COMPANY_PHONE": "company_phone", "COMPANY_ADDRESS": "company_address", "SUPPORT_EMAIL": "support_email", "TRIAL_DAYS": "trial_duration_days", "EFFECTIVE_DATE": "current_date"}',
TRUE, '1.0', NOW())
ON DUPLICATE KEY UPDATE
title = VALUES(title),
content = VALUES(content),
template_variables = VALUES(template_variables),
is_active = VALUES(is_active),
version = VALUES(version),
effective_date = VALUES(effective_date);
