<?php
require_once '../db-config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication and super admin role
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    if ($currentUser['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetSubscriptions();
            break;
        case 'POST':
            handleCreateSubscription();
            break;
        case 'PUT':
            handleUpdateSubscription();
            break;
        case 'DELETE':
            handleDeleteSubscription();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Super Admin Subscriptions API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

function handleGetSubscriptions() {
    global $conn;
    
    try {
        // Get all subscriptions with company and plan details
        $sql = "
            SELECT 
                s.*,
                c.name as company_name,
                c.email as company_email,
                c.business_type,
                pp.name as plan_name,
                pp.price_monthly,
                pp.price_yearly,
                u.name as user_name,
                u.email as user_email
            FROM subscriptions s
            LEFT JOIN companies c ON s.company_id = c.object_id
            LEFT JOIN pricing_plans pp ON s.plan_id = pp.id
            LEFT JOIN users u ON s.user_id = u.object_id
            ORDER BY s.created_at DESC
        ";
        
        $result = $conn->query($sql);
        $subscriptions = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // Parse JSON fields
                $features = !empty($row['features']) ? json_decode($row['features'], true) : [];
                $limits = !empty($row['limits_data']) ? json_decode($row['limits_data'], true) : [];
                $usage = !empty($row['usage_data']) ? json_decode($row['usage_data'], true) : [];
                $metadata = !empty($row['metadata']) ? json_decode($row['metadata'], true) : [];
                
                $subscriptions[] = [
                    'id' => $row['id'],
                    'object_id' => $row['object_id'],
                    'company_id' => $row['company_id'],
                    'company_name' => $row['company_name'],
                    'company_email' => $row['company_email'],
                    'business_type' => $row['business_type'],
                    'user_id' => $row['user_id'],
                    'user_name' => $row['user_name'],
                    'user_email' => $row['user_email'],
                    'plan_id' => $row['plan_id'],
                    'plan_name' => $row['plan_name'],
                    'price_monthly' => $row['price_monthly'],
                    'price_yearly' => $row['price_yearly'],
                    'status' => $row['status'],
                    'billing_cycle' => $row['billing_cycle'],
                    'price' => $row['price'],
                    'currency' => $row['currency'],
                    'trial_start_date' => $row['trial_start_date'],
                    'trial_end_date' => $row['trial_end_date'],
                    'trial_extended_days' => $row['trial_extended_days'],
                    'start_date' => $row['start_date'],
                    'end_date' => $row['end_date'],
                    'next_billing_date' => $row['next_billing_date'],
                    'payment_method' => $row['payment_method'],
                    'payment_gateway' => $row['payment_gateway'],
                    'gateway_subscription_id' => $row['gateway_subscription_id'],
                    'last_payment_date' => $row['last_payment_date'],
                    'last_payment_amount' => $row['last_payment_amount'],
                    'features' => $features,
                    'limits' => $limits,
                    'usage' => $usage,
                    'metadata' => $metadata,
                    'notes' => $row['notes'],
                    'created_at' => $row['created_at'],
                    'updated_at' => $row['updated_at']
                ];
            }
        }
        
        // Get subscription statistics
        $stats = getSubscriptionStats();
        
        echo json_encode([
            'success' => true,
            'data' => $subscriptions,
            'stats' => $stats,
            'total' => count($subscriptions)
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching subscriptions: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to fetch subscriptions',
            'error' => $e->getMessage()
        ]);
    }
}

function getSubscriptionStats() {
    global $conn;
    
    $stats = [
        'total_subscriptions' => 0,
        'active_subscriptions' => 0,
        'trial_subscriptions' => 0,
        'expired_subscriptions' => 0,
        'cancelled_subscriptions' => 0,
        'total_revenue' => 0,
        'monthly_revenue' => 0,
        'plan_distribution' => []
    ];
    
    try {
        // Total subscriptions
        $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions");
        if ($result) {
            $stats['total_subscriptions'] = $result->fetch_assoc()['count'];
        }
        
        // Active subscriptions
        $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'");
        if ($result) {
            $stats['active_subscriptions'] = $result->fetch_assoc()['count'];
        }
        
        // Trial subscriptions
        $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'trial'");
        if ($result) {
            $stats['trial_subscriptions'] = $result->fetch_assoc()['count'];
        }
        
        // Expired subscriptions
        $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'expired'");
        if ($result) {
            $stats['expired_subscriptions'] = $result->fetch_assoc()['count'];
        }
        
        // Cancelled subscriptions
        $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'cancelled'");
        if ($result) {
            $stats['cancelled_subscriptions'] = $result->fetch_assoc()['count'];
        }
        
        // Total revenue (sum of all payments)
        $result = $conn->query("SELECT SUM(last_payment_amount) as total FROM subscriptions WHERE last_payment_amount > 0");
        if ($result) {
            $row = $result->fetch_assoc();
            $stats['total_revenue'] = $row['total'] ?? 0;
        }
        
        // Monthly revenue (current month)
        $result = $conn->query("
            SELECT SUM(last_payment_amount) as monthly 
            FROM subscriptions 
            WHERE last_payment_date >= DATE_FORMAT(NOW(), '%Y-%m-01')
            AND last_payment_amount > 0
        ");
        if ($result) {
            $row = $result->fetch_assoc();
            $stats['monthly_revenue'] = $row['monthly'] ?? 0;
        }
        
        // Plan distribution
        $result = $conn->query("
            SELECT pp.name, COUNT(s.id) as count 
            FROM subscriptions s 
            LEFT JOIN pricing_plans pp ON s.plan_id = pp.id 
            GROUP BY s.plan_id, pp.name
        ");
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $stats['plan_distribution'][] = [
                    'plan_name' => $row['name'] ?? 'Unknown',
                    'count' => $row['count']
                ];
            }
        }
        
    } catch (Exception $e) {
        error_log("Error getting subscription stats: " . $e->getMessage());
    }
    
    return $stats;
}

function handleCreateSubscription() {
    global $conn;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        $required = ['company_id', 'plan_id', 'billing_cycle'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => "Missing required field: $field"]);
                return;
            }
        }
        
        // Generate object ID
        $objectId = generateId();
        
        // Get plan details
        $planStmt = $conn->prepare("SELECT * FROM pricing_plans WHERE id = ?");
        $planStmt->bind_param("i", $input['plan_id']);
        $planStmt->execute();
        $planResult = $planStmt->get_result();
        
        if ($planResult->num_rows === 0) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid plan ID']);
            return;
        }
        
        $plan = $planResult->fetch_assoc();
        $price = $input['billing_cycle'] === 'yearly' ? $plan['price_yearly'] : $plan['price_monthly'];
        
        // Calculate dates
        $startDate = date('Y-m-d H:i:s');
        $endDate = $input['billing_cycle'] === 'yearly' 
            ? date('Y-m-d H:i:s', strtotime('+1 year'))
            : date('Y-m-d H:i:s', strtotime('+1 month'));
        
        // Insert subscription
        $stmt = $conn->prepare("
            INSERT INTO subscriptions (
                object_id, company_id, user_id, plan_id, plan_name, status, 
                billing_cycle, price, currency, start_date, end_date, 
                next_billing_date, features, limits_data, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $status = $input['status'] ?? 'active';
        $currency = $input['currency'] ?? 'USD';
        $features = json_encode($plan['features'] ? json_decode($plan['features'], true) : []);
        $limits = json_encode($plan['limits_data'] ? json_decode($plan['limits_data'], true) : []);
        
        $stmt->bind_param("sssssssdsssss", 
            $objectId, $input['company_id'], $input['user_id'], $input['plan_id'], 
            $plan['name'], $status, $input['billing_cycle'], $price, $currency,
            $startDate, $endDate, $endDate, $features, $limits
        );
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Subscription created successfully',
                'data' => ['id' => $conn->insert_id, 'object_id' => $objectId]
            ]);
        } else {
            throw new Exception('Failed to create subscription');
        }
        
    } catch (Exception $e) {
        error_log("Error creating subscription: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create subscription',
            'error' => $e->getMessage()
        ]);
    }
}

function handleUpdateSubscription() {
    global $conn;
    
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $subscriptionId = trim($pathInfo, '/');
        
        if (empty($subscriptionId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Subscription ID required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Build update query dynamically
        $updateFields = [];
        $params = [];
        $types = '';
        
        $allowedFields = [
            'status', 'billing_cycle', 'price', 'currency', 'end_date', 
            'next_billing_date', 'payment_method', 'payment_gateway',
            'notes', 'trial_extended_days'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
                $types .= 's';
            }
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
            return;
        }
        
        $updateFields[] = "updated_at = NOW()";
        $params[] = $subscriptionId;
        $types .= 's';
        
        $sql = "UPDATE subscriptions SET " . implode(', ', $updateFields) . " WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Subscription updated successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Subscription not found']);
            }
        } else {
            throw new Exception('Failed to update subscription');
        }
        
    } catch (Exception $e) {
        error_log("Error updating subscription: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update subscription',
            'error' => $e->getMessage()
        ]);
    }
}

function handleDeleteSubscription() {
    global $conn;
    
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $subscriptionId = trim($pathInfo, '/');
        
        if (empty($subscriptionId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Subscription ID required']);
            return;
        }
        
        // Soft delete - update status to cancelled
        $stmt = $conn->prepare("UPDATE subscriptions SET status = 'cancelled', updated_at = NOW() WHERE object_id = ?");
        $stmt->bind_param("s", $subscriptionId);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Subscription cancelled successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Subscription not found']);
            }
        } else {
            throw new Exception('Failed to cancel subscription');
        }
        
    } catch (Exception $e) {
        error_log("Error cancelling subscription: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to cancel subscription',
            'error' => $e->getMessage()
        ]);
    }
}
?>