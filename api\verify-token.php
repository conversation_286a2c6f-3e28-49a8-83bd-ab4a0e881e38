<?php
/**
 * Token Verification Endpoint
 * Verifies authentication tokens for the frontend
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit;
    }

    // Get token from Authorization header
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] :
                  (isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '');

    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'No token provided']);
        exit;
    }

    $token = $matches[1];

    // Use the existing getCurrentUser function
    $currentUser = getCurrentUser($token);

    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Invalid or expired token']);
        exit;
    }
    
    // Return user data
    echo json_encode([
        'success' => true,
        'user' => [
            'id' => $currentUser['object_id'], // Use object_id as id for compatibility
            'object_id' => $currentUser['object_id'],
            'name' => $currentUser['name'],
            'email' => $currentUser['email'],
            'company_id' => $currentUser['company_id'],
            'company_name' => $currentUser['company_name'] ?? null,
            'role' => $currentUser['role'],
            'email_verified' => true // Assume verified if they have a valid token
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Token verification error: " . $e->getMessage());
}
?>
