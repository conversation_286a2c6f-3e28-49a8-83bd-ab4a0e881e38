<?php
require_once '../config/database.php';
require_once '../utils/SecurityUtils.php';
require_once '../utils/AuthUtils.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication
    $authResult = AuthUtils::verifyToken();
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    $user = $authResult['user'];
    $conn = Database::getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        handleVerifyPayment($conn, $user);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Payment Verification API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleVerifyPayment($conn, $user) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $razorpayOrderId = SecurityUtils::sanitizeInput($input['razorpay_order_id'] ?? '');
        $razorpayPaymentId = SecurityUtils::sanitizeInput($input['razorpay_payment_id'] ?? '');
        $razorpaySignature = SecurityUtils::sanitizeInput($input['razorpay_signature'] ?? '');
        $orderId = SecurityUtils::sanitizeInput($input['order_id'] ?? '');
        
        if (empty($razorpayOrderId) || empty($razorpayPaymentId) || empty($razorpaySignature) || empty($orderId)) {
            echo json_encode(['success' => false, 'message' => 'Missing payment verification data']);
            return;
        }
        
        // Get order from database
        $orderStmt = $conn->prepare("
            SELECT po.*, p.name as plan_name, p.trial_days 
            FROM payment_orders po
            LEFT JOIN plans p ON po.plan_id = p.id
            WHERE po.order_id = ? AND po.user_id = ?
        ");
        $orderStmt->bind_param("ss", $orderId, $user['object_id']);
        $orderStmt->execute();
        $orderResult = $orderStmt->get_result();
        
        if (!$order = $orderResult->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Order not found']);
            return;
        }
        
        // Verify Razorpay signature
        $razorpayKeySecret = 'your_key_secret'; // Replace with actual secret
        $expectedSignature = hash_hmac('sha256', $razorpayOrderId . '|' . $razorpayPaymentId, $razorpayKeySecret);
        
        if (!hash_equals($expectedSignature, $razorpaySignature)) {
            echo json_encode(['success' => false, 'message' => 'Payment verification failed']);
            return;
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Update payment order status
            $updateOrderStmt = $conn->prepare("
                UPDATE payment_orders 
                SET status = 'paid', razorpay_payment_id = ?, updated_at = NOW() 
                WHERE order_id = ?
            ");
            $updateOrderStmt->bind_param("ss", $razorpayPaymentId, $orderId);
            $updateOrderStmt->execute();
            
            // Create or update subscription
            $subscriptionId = createOrUpdateSubscription($conn, $order, $user);
            
            // Create payment transaction record
            $transactionId = 'txn_' . time() . '_' . rand(1000, 9999);
            $transactionStmt = $conn->prepare("
                INSERT INTO payment_transactions (transaction_id, order_id, payment_id, user_id, 
                                                 company_id, amount, currency, status, payment_method, 
                                                 gateway_response, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'success', 'razorpay', ?, NOW())
            ");
            $gatewayResponse = json_encode([
                'razorpay_order_id' => $razorpayOrderId,
                'razorpay_payment_id' => $razorpayPaymentId,
                'razorpay_signature' => $razorpaySignature
            ]);
            $transactionStmt->bind_param("sssssdss", $transactionId, $orderId, $razorpayPaymentId, 
                                        $user['object_id'], $order['company_id'], $order['amount'], 
                                        $order['currency'], $gatewayResponse);
            $transactionStmt->execute();
            
            // Generate invoice
            $invoiceId = generateInvoice($conn, $order, $transactionId, $user);
            
            // Commit transaction
            $conn->commit();
            
            echo json_encode([
                'success' => true, 
                'message' => 'Payment verified successfully',
                'subscription_id' => $subscriptionId,
                'transaction_id' => $transactionId,
                'invoice_id' => $invoiceId
            ]);
            
        } catch (Exception $e) {
            $conn->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Error verifying payment: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Payment verification failed: ' . $e->getMessage()]);
    }
}

function createOrUpdateSubscription($conn, $order, $user) {
    // Check if subscription already exists
    $existingStmt = $conn->prepare("
        SELECT * FROM subscriptions 
        WHERE company_id = ? AND plan_id = ? AND status IN ('active', 'trial')
    ");
    $existingStmt->bind_param("ss", $order['company_id'], $order['plan_id']);
    $existingStmt->execute();
    $existingResult = $existingStmt->get_result();
    
    if ($existing = $existingResult->fetch_assoc()) {
        // Update existing subscription
        $updateStmt = $conn->prepare("
            UPDATE subscriptions 
            SET status = 'active', billing_cycle = ?, amount = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $updateStmt->bind_param("sdi", $order['billing_cycle'], $order['amount'], $existing['id']);
        $updateStmt->execute();
        
        return $existing['object_id'];
    } else {
        // Create new subscription
        $subscriptionId = 'sub_' . time() . '_' . rand(1000, 9999);
        $nextBillingDate = date('Y-m-d', strtotime('+1 ' . ($order['billing_cycle'] === 'yearly' ? 'year' : 'month')));
        
        $createStmt = $conn->prepare("
            INSERT INTO subscriptions (object_id, company_id, user_id, plan_id, status, 
                                     billing_cycle, amount, next_billing_date, created_at)
            VALUES (?, ?, ?, ?, 'active', ?, ?, ?, NOW())
        ");
        $createStmt->bind_param("sssssds", $subscriptionId, $order['company_id'], $user['object_id'], 
                               $order['plan_id'], $order['billing_cycle'], $order['amount'], $nextBillingDate);
        $createStmt->execute();
        
        return $subscriptionId;
    }
}

function generateInvoice($conn, $order, $transactionId, $user) {
    $invoiceId = 'inv_' . time() . '_' . rand(1000, 9999);
    $invoiceNumber = 'BIZ-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    $invoiceStmt = $conn->prepare("
        INSERT INTO invoices (invoice_id, invoice_number, user_id, company_id, order_id, 
                             transaction_id, amount, currency, status, billing_cycle, 
                             plan_name, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'paid', ?, ?, NOW())
    ");
    $invoiceStmt->bind_param("ssssssdssss", $invoiceId, $invoiceNumber, $user['object_id'], 
                            $order['company_id'], $order['order_id'], $transactionId, 
                            $order['amount'], $order['currency'], $order['billing_cycle'], 
                            $order['plan_name']);
    $invoiceStmt->execute();
    
    return $invoiceId;
}

// Create required tables
function createPaymentTables($conn) {
    // Payment transactions table
    $transactionsSql = "
    CREATE TABLE IF NOT EXISTS payment_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transaction_id VARCHAR(100) UNIQUE NOT NULL,
        order_id VARCHAR(100) NOT NULL,
        payment_id VARCHAR(100) NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        status ENUM('success', 'failed', 'pending') DEFAULT 'pending',
        payment_method VARCHAR(50) DEFAULT 'razorpay',
        gateway_response TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_order_id (order_id),
        INDEX idx_user_id (user_id)
    )";
    
    // Invoices table
    $invoicesSql = "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id VARCHAR(100) UNIQUE NOT NULL,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        order_id VARCHAR(100) NOT NULL,
        transaction_id VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        status ENUM('paid', 'pending', 'cancelled') DEFAULT 'pending',
        billing_cycle VARCHAR(20),
        plan_name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_invoice_id (invoice_id),
        INDEX idx_user_id (user_id),
        INDEX idx_company_id (company_id)
    )";
    
    $conn->query($transactionsSql);
    $conn->query($invoicesSql);
}

// Initialize tables
try {
    $conn = Database::getConnection();
    createPaymentTables($conn);
} catch (Exception $e) {
    error_log("Error creating payment tables: " . $e->getMessage());
}
?>
