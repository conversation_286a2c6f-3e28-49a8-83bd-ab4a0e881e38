function DocumentViewer({ type, data, companyInfo, onEdit, onDelete, onClose }) {
    try {
        const documentRef = React.useRef(null);
        const [loading, setLoading] = React.useState(false);
        const [notification, setNotification] = React.useState(null);
        const [showExportOptions, setShowExportOptions] = React.useState(false);
        const documentTypeName = type.charAt(0).toUpperCase() + type.slice(1);

        const handlePrint = () => {
            if (documentRef.current) {
                try {
                    // Apply template settings from companyInfo
                    const printOptions = {
                        title: `${documentTypeName} Print`,
                        hideControls: false,
                        scaleFactor: 1.0
                    };
                    
                    // Apply paper size and orientation if available
                    if (companyInfo && companyInfo.templates) {
                        if (companyInfo.templates.paperSize) {
                            printOptions.paperSize = companyInfo.templates.paperSize;
                        }
                        if (companyInfo.templates.orientation) {
                            printOptions.orientation = companyInfo.templates.orientation;
                        }
                    }
                    
                    printDocumentAdvanced(documentRef.current, printOptions);
                    setNotification({
                        type: 'success',
                        message: 'Document sent to printer'
                    });
                } catch (error) {
                    console.error('Print error:', error);
                    setNotification({
                        type: 'error',
                        message: 'Failed to print document'
                    });
                }
            }
        };

        const handleDownloadPDF = async () => {
            if (documentRef.current) {
                try {
                    setLoading(true);
                    const docId = (data.objectData && data.objectData.invoiceNumber) ||
                                  (data.objectData && data.objectData.quotationNumber) ||
                                  (data.objectData && data.objectData.contractNumber) ||
                                  data.objectId.substring(0, 8);
                    
                    const filename = `${documentTypeName}-${docId}.pdf`;
                    
                    // Apply template settings from companyInfo
                    const pdfOptions = {
                        margin: 0,
                        format: 'a4',
                        orientation: 'portrait',
                        compress: true
                    };
                    
                    // Apply paper size and orientation if available
                    if (companyInfo && companyInfo.templates) {
                        if (companyInfo.templates.paperSize) {
                            pdfOptions.format = companyInfo.templates.paperSize;
                        }
                        if (companyInfo.templates.orientation) {
                            pdfOptions.orientation = companyInfo.templates.orientation;
                        }
                        
                        // Apply margins if available
                        if (companyInfo.templates.margins) {
                            const { top, right, bottom, left } = companyInfo.templates.margins;
                            pdfOptions.margin = [
                                Number(top) || 0,
                                Number(right) || 0,
                                Number(bottom) || 0,
                                Number(left) || 0
                            ];
                        }
                    }
                    
                    const success = await exportToPDF(documentRef.current, filename, pdfOptions);
                    
                    if (success) {
                        setNotification({
                            type: 'success',
                            message: 'PDF downloaded successfully'
                        });
                    } else {
                        throw new Error('PDF generation failed');
                    }
                } catch (error) {
                    console.error('PDF error:', error);
                    setNotification({
                        type: 'error',
                        message: 'Failed to generate PDF'
                    });
                } finally {
                    setLoading(false);
                    setShowExportOptions(false);
                }
            }
        };

        const handleDelete = async () => {
            try {
                setLoading(true);
                // Use objectId instead of id for deletion
                const objectId = data.objectId || data.id;
                
                if (!objectId) {
                    throw new Error('Document ID not found');
                }
                
                await trickleDeleteObject(type, objectId);
                
                setNotification({
                    type: 'success',
                    message: `${documentTypeName} deleted successfully`
                });
                
                // Delay closing to show the success notification
                setTimeout(() => {
                    if (onDelete) {
                        onDelete();
                    } else {
                        onClose();
                    }
                }, 1500);
            } catch (error) {
                console.error(`Error deleting ${type}:`, error);
                setNotification({
                    type: 'error',
                    message: `Failed to delete ${type}: ${error.message}`
                });
                setLoading(false);
            }
        };

        const handleExportAsImage = async () => {
            if (documentRef.current) {
                try {
                    setLoading(true);
                    const docId = (data.objectData && data.objectData.invoiceNumber) ||
                                  (data.objectData && data.objectData.quotationNumber) ||
                                  (data.objectData && data.objectData.contractNumber) ||
                                  data.objectId.substring(0, 8);
                    
                    const filename = `${documentTypeName}-${docId}.png`;
                    
                    // Create a clone of the document with proper styles for image export
                    const clone = documentRef.current.cloneNode(true);
                    clone.style.width = '1200px'; // Fixed width for better quality
                    clone.style.boxShadow = 'none';
                    clone.style.margin = '0';
                    
                    // Temporarily add to the document to compute styles
                    clone.style.position = 'absolute';
                    clone.style.left = '-9999px';
                    document.body.appendChild(clone);
                    
                    // Use html2canvas to convert to image
                    const canvas = await html2canvas(clone, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff'
                    });
                    
                    // Remove temporary element
                    document.body.removeChild(clone);
                    
                    // Convert to image and download
                    const image = canvas.toDataURL('image/png', 0.95);
                    const link = document.createElement('a');
                    link.href = image;
                    link.download = filename;
                    link.click();
                    
                    setNotification({
                        type: 'success',
                        message: 'Image downloaded successfully'
                    });
                } catch (error) {
                    console.error('Image export error:', error);
                    setNotification({
                        type: 'error',
                        message: 'Failed to generate image'
                    });
                } finally {
                    setLoading(false);
                    setShowExportOptions(false);
                }
            }
        };

        const renderDocument = () => {
            switch (type) {
                case 'invoice':
                    return <InvoiceTemplate invoice={data} companyInfo={companyInfo} />;
                case 'quotation':
                    return <QuotationTemplate quotation={data} companyInfo={companyInfo} />;
                case 'contract':
                    return <ContractTemplate contract={data} companyInfo={companyInfo} />;
                default:
                    return (
                        <div className="p-8 bg-red-50 border border-red-200 rounded-lg text-center">
                            <p className="text-red-600">Unknown document type: {type}</p>
                        </div>
                    );
            }
        };

        return (
            <div data-name="document-viewer" className="bg-gray-100 p-6 min-h-screen">
                <div className="bg-white rounded-lg shadow-sm p-4 flex flex-wrap justify-between items-center">
                    <div className="flex items-center">
                        <h2 className="text-lg font-semibold mr-4">
                            {documentTypeName} Preview
                        </h2>
                        <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium status-${data.objectData && data.objectData.status ? data.objectData.status : 'draft'}`}>
                            {data.objectData && data.objectData.status ? data.objectData.status.charAt(0).toUpperCase() + data.objectData.status.slice(1) : 'Draft'}
                        </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-3">
                        <div className="relative">
                            <Button
                                variant="secondary"
                                icon="fas fa-download"
                                onClick={() => setShowExportOptions(!showExportOptions)}
                            >
                                Export
                            </Button>
                            
                            {showExportOptions && (
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                                    <div className="py-1">
                                        <button
                                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            onClick={handleDownloadPDF}
                                        >
                                            <i className="fas fa-file-pdf mr-2 text-red-500"></i>
                                            Export as PDF
                                        </button>
                                        <button
                                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            onClick={handleExportAsImage}
                                        >
                                            <i className="fas fa-file-image mr-2 text-blue-500"></i>
                                            Export as Image
                                        </button>
                                        <button
                                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            onClick={handlePrint}
                                        >
                                            <i className="fas fa-print mr-2 text-green-500"></i>
                                            Print Document
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                        
                        <Button
                            variant="secondary"
                            icon="fas fa-edit"
                            onClick={onEdit}
                        >
                            Edit
                        </Button>
                        <Button
                            variant="danger"
                            icon="fas fa-trash"
                            onClick={handleDelete}
                        >
                            Delete
                        </Button>
                        <Button
                            variant="secondary"
                            icon="fas fa-times"
                            onClick={onClose}
                        >
                            Close
                        </Button>
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {loading && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl">
                            <div className="flex items-center space-x-3">
                                <i className="fas fa-spinner fa-spin text-blue-500"></i>
                                <span>Processing document...</span>
                            </div>
                        </div>
                    </div>
                )}

                <div className="mt-6 mb-10 pb-10 flex justify-center">
                    <div className="document-preview-wrapper" ref={documentRef}>
                        {renderDocument()}
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('DocumentViewer component error:', error);
        reportError(error);
        return null;
    }
}
