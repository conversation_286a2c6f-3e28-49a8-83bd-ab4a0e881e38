// Make ForgotPasswordForm component globally available
window.ForgotPasswordForm = function ForgotPasswordForm({ onSuccess, onSwitchToLogin }) {
    try {
        const [email, setEmail] = React.useState('');
        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [success, setSuccess] = React.useState(false);
        const [successMessage, setSuccessMessage] = React.useState('');

        const validateForm = () => {
            const newErrors = {};
            if (!email) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(email)) {
                newErrors.email = 'Invalid email format';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);

                // Make direct API call to handle new response format
                const response = await fetch('/api/auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'forgot-password',
                        email
                    })
                });

                const result = await response.json();

                if (result.success) {
                    setSuccessMessage(result.message || 'Password reset email sent successfully.');
                    setSuccess(true);
                    if (onSuccess) onSuccess();
                } else {
                    // Handle specific error cases
                    if (result.needsEmailConfig) {
                        setErrors({
                            submit: 'Email system not configured. Please contact administrator to set up email settings.'
                        });
                    } else {
                        setErrors({
                            submit: result.message || 'Failed to send reset email. Please try again.'
                        });
                    }
                }
            } catch (error) {
                console.error('Forgot password error:', error);
                setErrors({ submit: 'Failed to send reset email. Please try again.' });
            } finally {
                setLoading(false);
            }
        };

        if (success) {
            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-md w-full text-center">
                        <div className="rounded-lg bg-white p-8 shadow-sm">
                            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                                <i className="fas fa-check text-green-600 text-xl"></i>
                            </div>
                            <h2 className="mt-4 text-2xl font-semibold text-gray-900">Request Processed</h2>
                            <p className="mt-2 text-gray-600">
                                {successMessage || `We've sent password reset instructions to ${email}`}
                            </p>
                            <div className="mt-6">
                                {onSwitchToLogin ? (
                                    <button
                                        onClick={onSwitchToLogin}
                                        className="text-sm font-medium text-blue-600 hover:text-blue-500"
                                    >
                                        Back to login
                                    </button>
                                ) : (
                                    <a
                                        href="/login"
                                        className="text-sm font-medium text-blue-600 hover:text-blue-500"
                                    >
                                        Back to login
                                    </a>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Check if this is being used in a modal (has onSwitchToLogin prop)
        const isModal = !!onSwitchToLogin;

        return (
            <div data-name="forgot-password-form" className={isModal ? "p-6" : "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"}>
                <div className={isModal ? "w-full space-y-6" : "max-w-md w-full space-y-8"}>
                    <div>
                        <h2 className={`text-center font-extrabold text-gray-900 ${isModal ? 'text-2xl mb-4' : 'mt-6 text-3xl'}`}>
                            Reset your password
                        </h2>
                        <p className="mt-2 text-center text-sm text-gray-600">
                            Enter your email address and we'll send you instructions to reset your password.
                        </p>
                    </div>

                    <form className={isModal ? "mt-6 space-y-4" : "mt-8 space-y-6"} onSubmit={handleSubmit}>
                        <div>
                            <label htmlFor="email" className="sr-only">Email address</label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                autoComplete="email"
                                required
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className={`appearance-none relative block w-full px-3 py-2 border ${
                                    errors.email ? 'border-red-300' : 'border-gray-300'
                                } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                placeholder="Email address"
                            />
                            {errors.email && (
                                <p className="mt-1 text-xs text-red-600">{errors.email}</p>
                            )}
                        </div>

                        {errors.submit && (
                            <div className="text-red-600 text-sm text-center">
                                {errors.submit}
                            </div>
                        )}

                        <div>
                            <Button
                                type="submit"
                                loading={loading}
                                disabled={loading}
                                className="w-full"
                            >
                                Send Reset Link
                            </Button>
                        </div>

                        <div className={`text-sm text-center ${isModal ? 'mt-4' : 'mt-6'}`}>
                            {onSwitchToLogin ? (
                                <button
                                    onClick={onSwitchToLogin}
                                    className="font-medium text-blue-600 hover:text-blue-500"
                                >
                                    Back to login
                                </button>
                            ) : (
                                <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
                                    Back to login
                                </a>
                            )}
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ForgotPasswordForm component error:', error);
        reportError(error);
        return null;
    }
}
