<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test getApiUrl Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .url-test { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test getApiUrl Function Fix</h1>
        
        <div class="test-section">
            <h2>URL Generation Tests</h2>
            <div id="url-tests"></div>
        </div>
        
        <div class="test-section">
            <h2>Live API Tests</h2>
            <button onclick="testSubscriptionAPI()">Test Subscription API</button>
            <button onclick="testSubscriptionManagementAPI()">Test Subscription Management API</button>
            <div id="api-test-results"></div>
        </div>
    </div>

    <!-- Include config.js -->
    <script src="/biz/config.js"></script>

    <script>
        // Test URL generation
        function testUrlGeneration() {
            const testCases = [
                '/subscription',
                '/subscription?limit=10',
                '/subscription-management/current',
                '/super-admin/plans',
                '/auth/login',
                'subscription',
                'subscription?limit=10'
            ];
            
            const resultsDiv = document.getElementById('url-tests');
            let html = '<h3>URL Generation Results:</h3>';
            
            testCases.forEach(endpoint => {
                const generatedUrl = window.getApiUrl(endpoint);
                html += `
                    <div class="url-test">
                        <strong>Input:</strong> ${endpoint}<br>
                        <strong>Generated:</strong> ${generatedUrl}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return fetch(url, {
                ...options,
                headers
            });
        }
        
        async function testSubscriptionAPI() {
            const resultsDiv = document.getElementById('api-test-results');
            resultsDiv.innerHTML = '<p>Testing subscription API...</p>';
            
            try {
                const url = window.getApiUrl('/subscription?limit=10');
                console.log('Testing URL:', url);
                
                const response = await makeAuthenticatedRequest(url);
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultsDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultsDiv.innerHTML = `
                    <h3>Subscription API Test</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${response.ok ? '<p><strong>✅ Success!</strong></p>' : '<p><strong>❌ Failed</strong></p>'}
                `;
            } catch (error) {
                resultsDiv.className = 'test-section error';
                resultsDiv.innerHTML = `
                    <h3>Subscription API Test</h3>
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        async function testSubscriptionManagementAPI() {
            const resultsDiv = document.getElementById('api-test-results');
            resultsDiv.innerHTML = '<p>Testing subscription management API...</p>';
            
            try {
                const url = window.getApiUrl('/subscription-management/current');
                console.log('Testing URL:', url);
                
                const response = await makeAuthenticatedRequest(url);
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultsDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultsDiv.innerHTML = `
                    <h3>Subscription Management API Test</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${response.ok ? '<p><strong>✅ Success!</strong></p>' : '<p><strong>❌ Failed</strong></p>'}
                `;
            } catch (error) {
                resultsDiv.className = 'test-section error';
                resultsDiv.innerHTML = `
                    <h3>Subscription Management API Test</h3>
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        // Run URL generation tests on load
        window.addEventListener('load', function() {
            testUrlGeneration();
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                document.getElementById('api-test-results').innerHTML = 
                    '<div class="test-section info"><p>⚠️ No auth token found. Please login first to test API calls.</p></div>';
            }
        });
    </script>
</body>
</html>
