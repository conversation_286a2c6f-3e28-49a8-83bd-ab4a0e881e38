<?php
/**
 * PhonePe Payment Gateway Integration
 * Handles subscription billing and invoice payments
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../db-config.php';

class PhonePePayment {
    private $merchantId;
    private $saltKey;
    private $saltIndex;
    private $environment;
    private $callbackUrl;
    private $redirectUrl;
    private $baseUrl;

    public function __construct() {
        $config = Config::getPhonePe();
        $this->merchantId = $config['merchant_id'];
        $this->saltKey = $config['salt_key'];
        $this->saltIndex = $config['salt_index'];
        $this->environment = $config['environment'];
        $this->callbackUrl = $config['callback_url'];
        $this->redirectUrl = $config['redirect_url'];
        
        // Set base URL based on environment
        $this->baseUrl = $this->environment === 'production' 
            ? 'https://api.phonepe.com/apis/hermes'
            : 'https://api-preprod.phonepe.com/apis/pg-sandbox';
    }

    /**
     * Create payment for subscription
     */
    public function createSubscriptionPayment($subscriptionId, $amount, $userId, $companyId) {
        global $conn;
        
        $transactionId = 'SUB_' . $subscriptionId . '_' . time();
        $merchantUserId = 'USER_' . $userId;
        
        $paymentData = [
            'merchantId' => $this->merchantId,
            'merchantTransactionId' => $transactionId,
            'merchantUserId' => $merchantUserId,
            'amount' => $amount * 100, // Convert to paise
            'redirectUrl' => $this->redirectUrl . '?type=subscription&id=' . $subscriptionId,
            'redirectMode' => 'POST',
            'callbackUrl' => $this->callbackUrl,
            'paymentInstrument' => [
                'type' => 'PAY_PAGE'
            ]
        ];

        // Store payment record
        $paymentId = $this->storePaymentRecord($transactionId, 'subscription', $subscriptionId, $amount, $userId, $companyId);
        
        return $this->initiatePayment($paymentData, $paymentId);
    }

    /**
     * Create payment for invoice
     */
    public function createInvoicePayment($invoiceId, $amount, $userId, $companyId) {
        global $conn;
        
        $transactionId = 'INV_' . $invoiceId . '_' . time();
        $merchantUserId = 'USER_' . $userId;
        
        $paymentData = [
            'merchantId' => $this->merchantId,
            'merchantTransactionId' => $transactionId,
            'merchantUserId' => $merchantUserId,
            'amount' => $amount * 100, // Convert to paise
            'redirectUrl' => $this->redirectUrl . '?type=invoice&id=' . $invoiceId,
            'redirectMode' => 'POST',
            'callbackUrl' => $this->callbackUrl,
            'paymentInstrument' => [
                'type' => 'PAY_PAGE'
            ]
        ];

        // Store payment record
        $paymentId = $this->storePaymentRecord($transactionId, 'invoice', $invoiceId, $amount, $userId, $companyId);
        
        return $this->initiatePayment($paymentData, $paymentId);
    }

    /**
     * Initiate payment with PhonePe
     */
    private function initiatePayment($paymentData, $paymentId) {
        $jsonData = json_encode($paymentData);
        $base64Data = base64_encode($jsonData);
        
        $checksum = hash('sha256', $base64Data . '/pg/v1/pay' . $this->saltKey) . '###' . $this->saltIndex;
        
        $headers = [
            'Content-Type: application/json',
            'X-VERIFY: ' . $checksum
        ];
        
        $requestData = [
            'request' => $base64Data
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->baseUrl . '/pg/v1/pay');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $responseData = json_decode($response, true);
            if ($responseData['success'] && isset($responseData['data']['instrumentResponse']['redirectInfo']['url'])) {
                // Update payment record with PhonePe transaction ID
                $this->updatePaymentRecord($paymentId, [
                    'phonepe_transaction_id' => $responseData['data']['merchantTransactionId'],
                    'status' => 'initiated'
                ]);
                
                return [
                    'success' => true,
                    'payment_url' => $responseData['data']['instrumentResponse']['redirectInfo']['url'],
                    'transaction_id' => $responseData['data']['merchantTransactionId']
                ];
            }
        }
        
        return [
            'success' => false,
            'error' => 'Failed to initiate payment',
            'response' => $response
        ];
    }

    /**
     * Verify payment status
     */
    public function verifyPayment($transactionId) {
        $checksum = hash('sha256', '/pg/v1/status/' . $this->merchantId . '/' . $transactionId . $this->saltKey) . '###' . $this->saltIndex;
        
        $headers = [
            'Content-Type: application/json',
            'X-VERIFY: ' . $checksum,
            'X-MERCHANT-ID: ' . $this->merchantId
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->baseUrl . '/pg/v1/status/' . $this->merchantId . '/' . $transactionId);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $responseData = json_decode($response, true);
            return $responseData;
        }
        
        return ['success' => false, 'error' => 'Failed to verify payment'];
    }

    /**
     * Store payment record in database
     */
    private function storePaymentRecord($transactionId, $type, $referenceId, $amount, $userId, $companyId) {
        global $conn;
        
        $paymentId = generateId();
        $sql = "INSERT INTO payments (object_id, company_id, user_id, transaction_id, type, reference_id, amount, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssssd", $paymentId, $companyId, $userId, $transactionId, $type, $referenceId, $amount);
        $stmt->execute();
        
        return $paymentId;
    }

    /**
     * Update payment record
     */
    private function updatePaymentRecord($paymentId, $data) {
        global $conn;
        
        $setParts = [];
        $values = [];
        $types = '';
        
        foreach ($data as $key => $value) {
            $setParts[] = "$key = ?";
            $values[] = $value;
            $types .= 's';
        }
        
        $values[] = $paymentId;
        $types .= 's';
        
        $sql = "UPDATE payments SET " . implode(', ', $setParts) . ", updated_at = NOW() WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$values);
        $stmt->execute();
    }

    /**
     * Handle payment callback
     */
    public function handleCallback($callbackData) {
        // Verify callback authenticity
        if (!$this->verifyCallback($callbackData)) {
            return ['success' => false, 'error' => 'Invalid callback'];
        }
        
        $transactionId = $callbackData['transactionId'];
        $status = $callbackData['code'] === 'PAYMENT_SUCCESS' ? 'completed' : 'failed';
        
        // Update payment status
        global $conn;
        $sql = "UPDATE payments SET status = ?, phonepe_response = ?, updated_at = NOW() WHERE transaction_id = ?";
        $stmt = $conn->prepare($sql);
        $response = json_encode($callbackData);
        $stmt->bind_param("sss", $status, $response, $transactionId);
        $stmt->execute();
        
        if ($status === 'completed') {
            // Handle successful payment
            $this->processSuccessfulPayment($transactionId);
        }
        
        return ['success' => true, 'status' => $status];
    }

    /**
     * Verify callback authenticity
     */
    private function verifyCallback($callbackData) {
        // Implement callback verification logic
        // This should verify the checksum sent by PhonePe
        return true; // Simplified for now
    }

    /**
     * Process successful payment
     */
    private function processSuccessfulPayment($transactionId) {
        global $conn;
        
        // Get payment details
        $sql = "SELECT * FROM payments WHERE transaction_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $transactionId);
        $stmt->execute();
        $payment = $stmt->get_result()->fetch_assoc();
        
        if (!$payment) return;
        
        if ($payment['type'] === 'subscription') {
            $this->activateSubscription($payment['reference_id'], $payment['amount']);
        } elseif ($payment['type'] === 'invoice') {
            $this->markInvoicePaid($payment['reference_id'], $payment['amount']);
        }
    }

    /**
     * Activate subscription after successful payment
     */
    private function activateSubscription($subscriptionId, $amount) {
        global $conn;
        
        $sql = "UPDATE subscriptions SET status = 'active', last_payment_date = NOW(), 
                next_payment_date = DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $subscriptionId);
        $stmt->execute();
    }

    /**
     * Mark invoice as paid
     */
    private function markInvoicePaid($invoiceId, $amount) {
        global $conn;
        
        $sql = "UPDATE invoices SET amount_paid = amount_paid + ?, status = 'paid', paid_at = NOW() WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ds", $amount, $invoiceId);
        $stmt->execute();
    }
}
?>
