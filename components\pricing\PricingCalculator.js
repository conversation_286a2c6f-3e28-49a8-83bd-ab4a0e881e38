// Pricing Calculator Component with Accurate Savings Display
function PricingCalculator({ plan, billingCycle, onCycleChange, showSavings = true }) {
    const monthlyPrice = plan?.price_monthly || 0;
    const yearlyPrice = plan?.price_yearly || 0;
    
    // Calculate accurate savings
    const monthlyCostPerYear = monthlyPrice * 12;
    const actualSavings = monthlyCostPerYear - yearlyPrice;
    const savingsPercentage = monthlyCostPerYear > 0 ? Math.round((actualSavings / monthlyCostPerYear) * 100) : 0;
    
    const currentPrice = billingCycle === 'yearly' ? yearlyPrice : monthlyPrice;
    const currentPeriod = billingCycle === 'yearly' ? 'year' : 'month';
    
    return (
        <div className="pricing-calculator">
            {/* Billing Cycle Toggle */}
            <div className="flex justify-center mb-6">
                <div className="bg-gray-100 p-1 rounded-lg flex">
                    <button
                        onClick={() => onCycleChange && onCycleChange('monthly')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                            billingCycle === 'monthly'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                        }`}
                    >
                        Monthly
                    </button>
                    <button
                        onClick={() => onCycleChange && onCycleChange('yearly')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-all relative ${
                            billingCycle === 'yearly'
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                        }`}
                    >
                        Yearly
                        {showSavings && actualSavings > 0 && (
                            <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full whitespace-nowrap">
                                Save {savingsPercentage}%
                            </span>
                        )}
                    </button>
                </div>
            </div>

            {/* Price Display */}
            <div className="text-center">
                <div className="flex items-baseline justify-center mb-2">
                    <span className="text-4xl font-bold text-gray-900">
                        ₹{currentPrice.toLocaleString()}
                    </span>
                    <span className="text-lg text-gray-600 ml-2">
                        /{currentPeriod}
                    </span>
                </div>

                {/* Savings Information */}
                {showSavings && billingCycle === 'yearly' && actualSavings > 0 && (
                    <div className="space-y-1">
                        <p className="text-green-600 font-medium">
                            Save ₹{actualSavings.toLocaleString()} per year
                        </p>
                        <p className="text-sm text-gray-600">
                            Compared to monthly billing (₹{monthlyCostPerYear.toLocaleString()}/year)
                        </p>
                    </div>
                )}

                {/* Monthly equivalent for yearly billing */}
                {billingCycle === 'yearly' && yearlyPrice > 0 && (
                    <p className="text-sm text-gray-600 mt-2">
                        ₹{Math.round(yearlyPrice / 12).toLocaleString()}/month when billed annually
                    </p>
                )}
            </div>
        </div>
    );
}

// Enhanced Pricing Toggle Component
function PricingToggle({ billingCycle, onCycleChange, plans = [] }) {
    // Calculate maximum savings across all plans
    const maxSavings = React.useMemo(() => {
        return plans.reduce((max, plan) => {
            const monthlyCost = (plan.price_monthly || 0) * 12;
            const yearlyCost = plan.price_yearly || 0;
            const savings = monthlyCost - yearlyCost;
            const percentage = monthlyCost > 0 ? Math.round((savings / monthlyCost) * 100) : 0;
            return Math.max(max, percentage);
        }, 0);
    }, [plans]);

    return (
        <div className="flex justify-center mb-8">
            <div className="bg-gray-100 p-1 rounded-lg flex">
                <button
                    onClick={() => onCycleChange('monthly')}
                    className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                        billingCycle === 'monthly'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                    }`}
                >
                    Monthly
                </button>
                <button
                    onClick={() => onCycleChange('yearly')}
                    className={`px-6 py-3 rounded-md text-sm font-medium transition-all relative ${
                        billingCycle === 'yearly'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                    }`}
                >
                    Yearly
                    {maxSavings > 0 && (
                        <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                            Save up to {maxSavings}%
                        </span>
                    )}
                </button>
            </div>
        </div>
    );
}

// Price Comparison Component
function PriceComparison({ plan, billingCycle }) {
    const monthlyPrice = plan?.price_monthly || 0;
    const yearlyPrice = plan?.price_yearly || 0;
    const monthlyCostPerYear = monthlyPrice * 12;
    const savings = monthlyCostPerYear - yearlyPrice;
    const savingsPercentage = monthlyCostPerYear > 0 ? Math.round((savings / monthlyCostPerYear) * 100) : 0;

    if (billingCycle !== 'yearly' || savings <= 0) {
        return null;
    }

    return (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm font-medium text-green-800">
                        Yearly Savings
                    </p>
                    <p className="text-xs text-green-600">
                        vs. monthly billing
                    </p>
                </div>
                <div className="text-right">
                    <p className="text-lg font-bold text-green-800">
                        ₹{savings.toLocaleString()}
                    </p>
                    <p className="text-xs text-green-600">
                        ({savingsPercentage}% off)
                    </p>
                </div>
            </div>
            
            <div className="mt-3 pt-3 border-t border-green-200">
                <div className="flex justify-between text-xs text-green-700">
                    <span>Monthly: ₹{monthlyCostPerYear.toLocaleString()}/year</span>
                    <span>Yearly: ₹{yearlyPrice.toLocaleString()}/year</span>
                </div>
            </div>
        </div>
    );
}

// Pricing Summary Component
function PricingSummary({ plan, billingCycle, showComparison = true }) {
    const currentPrice = billingCycle === 'yearly' ? plan?.price_yearly : plan?.price_monthly;
    const period = billingCycle === 'yearly' ? 'year' : 'month';

    return (
        <div className="pricing-summary">
            <div className="text-center mb-4">
                <div className="flex items-baseline justify-center">
                    <span className="text-3xl font-bold text-gray-900">
                        ₹{(currentPrice || 0).toLocaleString()}
                    </span>
                    <span className="text-gray-600 ml-1">/{period}</span>
                </div>
                
                {billingCycle === 'yearly' && plan?.price_yearly > 0 && (
                    <p className="text-sm text-gray-600 mt-1">
                        ₹{Math.round((plan.price_yearly || 0) / 12).toLocaleString()}/month billed annually
                    </p>
                )}
            </div>

            {showComparison && (
                <PriceComparison plan={plan} billingCycle={billingCycle} />
            )}
        </div>
    );
}

// Make components globally available
window.PricingCalculator = PricingCalculator;
window.PricingToggle = PricingToggle;
window.PriceComparison = PriceComparison;
window.PricingSummary = PricingSummary;
