<?php
/**
 * Super Admin System Settings API
 * Handles system-wide configuration settings
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Verify super admin access
    $user = getCurrentUser();
    if (!$user || $user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Super admin access required']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            getSystemSettings();
            break;
            
        case 'POST':
        case 'PUT':
            updateSystemSettings();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log('System Settings API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function getSystemSettings() {
    global $conn;

    $sql = "SELECT setting_key, setting_value, setting_type, description, is_public
            FROM system_settings
            ORDER BY setting_key ASC";
    
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . $conn->error);
    }
    
    $settings = [];
    $categorized = [];
    
    while ($row = $result->fetch_assoc()) {
        // Convert value based on type
        $value = $row['setting_value'];
        switch ($row['setting_type']) {
            case 'number':
                $value = is_numeric($value) ? (float)$value : 0;
                break;
            case 'boolean':
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'json':
                $value = json_decode($value, true) ?: [];
                break;
        }
        
        $setting = [
            'key' => $row['setting_key'],
            'value' => $value,
            'type' => $row['setting_type'],
            'category' => 'general', // Default category since column doesn't exist
            'description' => $row['description'],
            'is_public' => (bool)$row['is_public']
        ];

        $settings[$row['setting_key']] = $setting;

        // Group by category based on setting key prefix
        $category = 'general';
        if (strpos($row['setting_key'], 'company_') === 0) $category = 'company';
        elseif (strpos($row['setting_key'], 'payment_') === 0 || strpos($row['setting_key'], 'razorpay_') === 0) $category = 'payment';
        elseif (strpos($row['setting_key'], 'trial_') === 0 || strpos($row['setting_key'], 'subscription_') === 0) $category = 'subscription';
        elseif (strpos($row['setting_key'], 'support_') === 0) $category = 'support';

        $setting['category'] = $category;

        if (!isset($categorized[$category])) {
            $categorized[$category] = [];
        }
        $categorized[$category][] = $setting;
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'settings' => $settings,
            'categorized' => $categorized
        ]
    ]);
}

function updateSystemSettings() {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['settings'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        return;
    }
    
    $conn->begin_transaction();
    
    try {
        $updated = 0;
        $created = 0;
        
        foreach ($input['settings'] as $key => $data) {
            $value = $data['value'] ?? '';
            $type = $data['type'] ?? 'string';
            $category = $data['category'] ?? 'general';
            $description = $data['description'] ?? '';
            $isPublic = isset($data['is_public']) ? (bool)$data['is_public'] : false;
            
            // Convert value based on type
            switch ($type) {
                case 'json':
                    $value = is_array($value) ? json_encode($value) : $value;
                    break;
                case 'boolean':
                    $value = $value ? 'true' : 'false';
                    break;
                case 'number':
                    $value = (string)$value;
                    break;
                default:
                    $value = (string)$value;
                    break;
            }
            
            // Check if setting exists
            $checkStmt = $conn->prepare("SELECT setting_key FROM system_settings WHERE setting_key = ?");
            $checkStmt->bind_param("s", $key);
            $checkStmt->execute();
            $exists = $checkStmt->get_result()->num_rows > 0;
            
            if ($exists) {
                // Update existing setting
                $updateStmt = $conn->prepare("
                    UPDATE system_settings SET 
                        setting_value = ?, 
                        setting_type = ?, 
                        category = ?, 
                        description = ?, 
                        is_public = ?,
                        updated_at = NOW()
                    WHERE setting_key = ?
                ");
                $updateStmt->bind_param("ssssss", $value, $type, $category, $description, $isPublic, $key);
                $updateStmt->execute();
                $updated++;
            } else {
                // Create new setting
                $insertStmt = $conn->prepare("
                    INSERT INTO system_settings (
                        setting_key, setting_value, setting_type, category, 
                        description, is_public, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");
                $insertStmt->bind_param("ssssss", $key, $value, $type, $category, $description, $isPublic);
                $insertStmt->execute();
                $created++;
            }
        }
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Settings updated successfully',
            'data' => [
                'updated' => $updated,
                'created' => $created
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log('Settings update error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update settings']);
    }
}
?>
