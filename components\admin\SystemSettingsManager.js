// System Settings Manager Component for Super Admin
function SystemSettingsManager({ authContext, setNotification }) {
    const [settings, setSettings] = React.useState({});
    const [categorizedSettings, setCategorizedSettings] = React.useState({});
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);
    const [activeCategory, setActiveCategory] = React.useState('company');

    React.useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/system-settings'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setSettings(data.data.settings || {});
                    setCategorizedSettings(data.data.categorized || {});
                    
                    // Set first available category as active
                    const categories = Object.keys(data.data.categorized || {});
                    if (categories.length > 0 && !categories.includes(activeCategory)) {
                        setActiveCategory(categories[0]);
                    }
                }
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load system settings'
            });
        } finally {
            setLoading(false);
        }
    };

    const updateSetting = (key, value) => {
        setSettings(prev => ({
            ...prev,
            [key]: {
                ...prev[key],
                value: value
            }
        }));
    };

    const saveSettings = async () => {
        setSaving(true);
        try {
            const response = await fetch(window.getApiUrl('/super-admin/system-settings'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    settings: settings
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: 'Settings saved successfully'
                    });
                } else {
                    setNotification({
                        type: 'error',
                        message: data.message || 'Failed to save settings'
                    });
                }
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            setNotification({
                type: 'error',
                message: 'Failed to save settings'
            });
        } finally {
            setSaving(false);
        }
    };

    const renderSettingInput = (setting) => {
        const { key, value, type, description } = setting;

        switch (type) {
            case 'boolean':
                return (
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => updateSetting(key, e.target.checked)}
                            className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{description}</span>
                    </label>
                );

            case 'number':
                return (
                    <input
                        type="number"
                        value={value}
                        onChange={(e) => updateSetting(key, parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                );

            case 'json':
                return (
                    <textarea
                        rows={4}
                        value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
                        onChange={(e) => {
                            try {
                                const parsed = JSON.parse(e.target.value);
                                updateSetting(key, parsed);
                            } catch (err) {
                                updateSetting(key, e.target.value);
                            }
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                        placeholder="Enter valid JSON"
                    />
                );

            case 'html':
                return (
                    <textarea
                        rows={6}
                        value={value}
                        onChange={(e) => updateSetting(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                        placeholder="Enter HTML content"
                    />
                );

            default:
                return (
                    <input
                        type={key.includes('password') || key.includes('secret') ? 'password' : 'text'}
                        value={value}
                        onChange={(e) => updateSetting(key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                );
        }
    };

    const getCategoryIcon = (category) => {
        const icons = {
            company: 'fas fa-building',
            payment: 'fas fa-credit-card',
            subscription: 'fas fa-calendar-alt',
            support: 'fas fa-headset',
            general: 'fas fa-cog'
        };
        return icons[category] || 'fas fa-cog';
    };

    const getCategoryName = (category) => {
        const names = {
            company: 'Company Information',
            payment: 'Payment Settings',
            subscription: 'Subscription Settings',
            support: 'Support Settings',
            general: 'General Settings'
        };
        return names[category] || category.charAt(0).toUpperCase() + category.slice(1);
    };

    if (loading) {
        return (
            <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading system settings...</p>
            </div>
        );
    }

    const categories = Object.keys(categorizedSettings);

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">System Settings</h3>
                <button
                    onClick={saveSettings}
                    disabled={saving}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                    {saving ? (
                        <>
                            <i className="fas fa-spinner fa-spin mr-2"></i>
                            Saving...
                        </>
                    ) : (
                        <>
                            <i className="fas fa-save mr-2"></i>
                            Save Settings
                        </>
                    )}
                </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Category Navigation */}
                <div className="lg:col-span-1">
                    <nav className="space-y-1">
                        {categories.map((category) => (
                            <button
                                key={category}
                                onClick={() => setActiveCategory(category)}
                                className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                    activeCategory === category
                                        ? 'bg-blue-100 text-blue-700'
                                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                }`}
                            >
                                <i className={`${getCategoryIcon(category)} mr-2`}></i>
                                {getCategoryName(category)}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Settings Content */}
                <div className="lg:col-span-3">
                    {categorizedSettings[activeCategory] && (
                        <div className="bg-white rounded-lg border border-gray-200 p-6">
                            <h4 className="text-lg font-medium text-gray-900 mb-6">
                                <i className={`${getCategoryIcon(activeCategory)} mr-2`}></i>
                                {getCategoryName(activeCategory)}
                            </h4>

                            <div className="space-y-6">
                                {categorizedSettings[activeCategory].map((setting) => (
                                    <div key={setting.key} className="space-y-2">
                                        <label className="block text-sm font-medium text-gray-700">
                                            {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                            {setting.description && (
                                                <span className="text-gray-500 font-normal ml-1">
                                                    - {setting.description}
                                                </span>
                                            )}
                                        </label>
                                        {renderSettingInput(setting)}
                                        {setting.type === 'json' && (
                                            <p className="text-xs text-gray-500">
                                                Enter valid JSON format
                                            </p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {!categorizedSettings[activeCategory] && (
                        <div className="text-center py-12">
                            <i className="fas fa-cog text-gray-300 text-4xl mb-4"></i>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No Settings Found</h3>
                            <p className="text-gray-600">No settings available for this category.</p>
                        </div>
                    )}
                </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Quick Setup</h4>
                <p className="text-sm text-blue-700 mb-3">
                    Configure essential settings to get your SaaS platform ready for production.
                </p>
                <div className="flex space-x-3">
                    <button
                        onClick={() => setActiveCategory('payment')}
                        className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700"
                    >
                        Setup Payments
                    </button>
                    <button
                        onClick={() => setActiveCategory('company')}
                        className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700"
                    >
                        Company Info
                    </button>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.SystemSettingsManager = SystemSettingsManager;
