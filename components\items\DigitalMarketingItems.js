function DigitalMarketingItems() {
    try {
        const marketingItems = [
            {
                name: "Social Media Management - Basic",
                description: "Monthly management of 2 social media platforms with 12 posts per month, engagement monitoring, and monthly reporting.",
                price: 12000,
                category: "Digital Marketing",
                subcategory: "Social Media Management",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Social Media Management - Standard",
                description: "Monthly management of 4 social media platforms with 20 posts per month, engagement monitoring, community management, and bi-weekly reporting.",
                price: 25000,
                category: "Digital Marketing",
                subcategory: "Social Media Management",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Social Media Management - Premium",
                description: "Monthly management of all major social media platforms with 30+ posts per month, 24/7 engagement monitoring, community management, content creation, and weekly reporting.",
                price: 45000,
                category: "Digital Marketing",
                subcategory: "Social Media Management",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "SEO Package - Basic",
                description: "Monthly SEO services including keyword research, on-page optimization, and monthly ranking reports for up to 10 keywords.",
                price: 15000,
                category: "Digital Marketing",
                subcategory: "SEO Services",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "SEO Package - Standard",
                description: "Monthly SEO services including keyword research, on-page optimization, content recommendations, competitor analysis, and bi-weekly ranking reports for up to 20 keywords.",
                price: 30000,
                category: "Digital Marketing",
                subcategory: "SEO Services",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "SEO Package - Premium",
                description: "Comprehensive monthly SEO services including advanced keyword research, on-page & technical optimization, content creation, link building, competitor analysis, and weekly ranking reports for up to 50 keywords.",
                price: 60000,
                category: "Digital Marketing",
                subcategory: "SEO Services",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Google Ads Management - Basic",
                description: "Monthly management of Google Ads campaigns with budget up to ₹50,000, including campaign setup, optimization, and monthly reporting.",
                price: 10000,
                category: "Digital Marketing",
                subcategory: "PPC Advertising",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Google Ads Management - Standard",
                description: "Monthly management of Google Ads campaigns with budget up to ₹200,000, including campaign setup, ongoing optimization, A/B testing, and bi-weekly reporting.",
                price: 25000,
                category: "Digital Marketing",
                subcategory: "PPC Advertising",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Content Marketing Package",
                description: "Monthly content creation including 4 blog posts (800-1200 words each), content distribution, and performance tracking.",
                price: 20000,
                category: "Digital Marketing",
                subcategory: "Content Marketing",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Email Marketing - Basic",
                description: "Monthly email marketing service including 2 email campaigns, template design, list management, and performance analytics.",
                price: 8000,
                category: "Digital Marketing",
                subcategory: "Email Marketing",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Email Marketing - Standard",
                description: "Monthly email marketing service including 4 email campaigns, template design, list segmentation, A/B testing, and detailed performance analytics.",
                price: 15000,
                category: "Digital Marketing",
                subcategory: "Email Marketing",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Digital Marketing Analytics",
                description: "Monthly comprehensive analytics and reporting across all digital marketing channels with insights and recommendations.",
                price: 12000,
                category: "Digital Marketing",
                subcategory: "Analytics & Reporting",
                isRecurring: true,
                recurringPeriod: "monthly",
                itemType: "service"
            },
            {
                name: "Social Media Ad Campaign",
                description: "One-time setup and management of a social media advertising campaign across platforms (ad spend not included).",
                price: 15000,
                category: "Digital Marketing",
                subcategory: "PPC Advertising",
                isRecurring: false,
                itemType: "service"
            },
            {
                name: "Website SEO Audit",
                description: "Comprehensive one-time SEO audit of your website with detailed recommendations report.",
                price: 25000,
                category: "Digital Marketing",
                subcategory: "SEO Services",
                isRecurring: false,
                itemType: "service"
            }
        ];

        const [loading, setLoading] = React.useState(false);
        const [addedItems, setAddedItems] = React.useState([]);
        const [notification, setNotification] = React.useState(null);
        const [categoryId, setCategoryId] = React.useState(null);
        const [subcategoryIds, setSubcategoryIds] = React.useState({});

        // Function to create digital marketing category and subcategories
        const setupCategoriesAndItems = async () => {
            try {
                setLoading(true);
                
                // First check if Digital Marketing category already exists
                const categoriesResponse = await trickleListObjects('item_category', 100, true);
                let digitalMarketingCategory = categoriesResponse.items.find(
                    cat => cat.objectData.name === "Digital Marketing"
                );
                
                // Create Digital Marketing category if it doesn't exist
                if (!digitalMarketingCategory) {
                    const categoryData = {
                        name: "Digital Marketing",
                        description: "Online marketing services including social media, SEO, and content marketing",
                        createdAt: new Date().toISOString()
                    };
                    
                    const newCategory = await trickleCreateObject('item_category', categoryData);
                    digitalMarketingCategory = {
                        objectId: newCategory.objectId,
                        objectData: categoryData
                    };
                }
                
                setCategoryId(digitalMarketingCategory.objectId);
                
                // Get unique subcategories from marketing items
                const uniqueSubcategories = [...new Set(marketingItems.map(item => item.subcategory))];
                
                // Create subcategories
                const subcategoryMap = {};
                for (const subcategoryName of uniqueSubcategories) {
                    // Check if subcategory already exists
                    const subcategoriesResponse = await trickleListObjects(`item_subcategory:${digitalMarketingCategory.objectId}`, 100, true);
                    let existingSubcategory = subcategoriesResponse.items.find(
                        subcat => subcat.objectData.name === subcategoryName
                    );
                    
                    if (!existingSubcategory) {
                        const subcategoryData = {
                            name: subcategoryName,
                            description: `${subcategoryName} services for digital marketing`,
                            createdAt: new Date().toISOString()
                        };
                        
                        const newSubcategory = await trickleCreateObject(
                            `item_subcategory:${digitalMarketingCategory.objectId}`, 
                            subcategoryData
                        );
                        
                        subcategoryMap[subcategoryName] = newSubcategory.objectId;
                    } else {
                        subcategoryMap[subcategoryName] = existingSubcategory.objectId;
                    }
                }
                
                setSubcategoryIds(subcategoryMap);
                
                // Create service items
                for (const item of marketingItems) {
                    // Check if item with this name already exists
                    const itemsResponse = await trickleListObjects('item', 100, true);
                    const existingItem = itemsResponse.items.find(
                        existingItem => existingItem.objectData.name === item.name
                    );
                    
                    if (!existingItem) {
                        const itemData = {
                            ...item,
                            category: digitalMarketingCategory.objectId,
                            subcategory: subcategoryMap[item.subcategory],
                            isActive: true,
                            createdAt: new Date().toISOString()
                        };
                        
                        await trickleCreateObject('item', itemData);
                        setAddedItems(prev => [...prev, item.name]);
                    }
                }
                
                setNotification({
                    type: 'success',
                    message: `Successfully added digital marketing services`
                });
                
            } catch (error) {
                console.error('Error setting up digital marketing items:', error);
                setNotification({
                    type: 'error',
                    message: 'Error adding digital marketing services'
                });
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="digital-marketing-items" className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">Digital Marketing Services</h2>
                    <p className="mb-6">
                        Add a complete set of digital marketing services to your items catalog. This will create a Digital Marketing category with subcategories and popular service offerings.
                    </p>
                    
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Services that will be added:</h3>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                            <li>Social Media Management (Basic, Standard, Premium)</li>
                            <li>SEO Packages (Basic, Standard, Premium)</li>
                            <li>Google Ads Management</li>
                            <li>Content Marketing Packages</li>
                            <li>Email Marketing Services</li>
                            <li>Analytics & Reporting</li>
                        </ul>
                    </div>
                    
                    <div className="flex justify-center">
                        <Button
                            onClick={setupCategoriesAndItems}
                            loading={loading}
                            disabled={loading}
                            icon="fas fa-plus"
                        >
                            Add Digital Marketing Services
                        </Button>
                    </div>
                    
                    {addedItems.length > 0 && (
                        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                            <h4 className="font-medium text-green-700 mb-2">Successfully added:</h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm text-green-600">
                                {addedItems.map((item, index) => (
                                    <li key={index}>{item}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
                
                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('DigitalMarketingItems component error:', error);
        reportError(error);
        return null;
    }
}
