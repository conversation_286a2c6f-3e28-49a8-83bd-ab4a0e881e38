function LeadTasks({ leadId }) {
    const [tasks, setTasks] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [newTask, setNewTask] = React.useState({
        title: '',
        description: '',
        dueDate: '',
        priority: 'medium',
        status: 'pending'
    });

    React.useEffect(() => {
        fetchTasks();
    }, [leadId]);

    const fetchTasks = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('authToken');
            const response = await fetch(window.getApiUrl(`/object/task/${leadId}/list`), {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error('Failed to fetch tasks');
            }
            
            const data = await response.json();
            setTasks(data.items || []);
        } catch (error) {
            console.error('Error fetching tasks:', error);
        } finally {
            setLoading(false);
        }
    };

    // Rest of your component code...
}
