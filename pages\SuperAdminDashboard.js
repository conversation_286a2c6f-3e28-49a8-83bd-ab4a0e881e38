function SuperAdminDashboard() {
    const authContext = React.useContext(window.AuthContext || AuthContext);
    const [dashboardData, setDashboardData] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [activeTab, setActiveTab] = React.useState('overview');
    const [notification, setNotification] = React.useState(null);
    const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

    // State for different modules
    const [plans, setPlans] = React.useState([]);
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [companies, setCompanies] = React.useState([]);
    const [users, setUsers] = React.useState([]);
    const [subscriptions, setSubscriptions] = React.useState([]);
    const [paymentGateways, setPaymentGateways] = React.useState([]);
    const [systemSettings, setSystemSettings] = React.useState({});

    React.useEffect(() => {
        console.log('SuperAdminDashboard - Auth context:', authContext);
        console.log('SuperAdminDashboard - User:', authContext?.user);
        console.log('SuperAdminDashboard - User role:', authContext?.user?.role);

        // Check if authContext is available and user is super admin
        if (!authContext || !authContext.user) {
            console.log('SuperAdminDashboard - Auth context or user not available yet');
            return; // Wait for auth context to be available
        }

        if (authContext.user.role !== 'super_admin') {
            console.error('SuperAdminDashboard - Access denied: User is not a super admin');
            console.log('SuperAdminDashboard - User role:', authContext.user.role);
            // Don't redirect here, let app.js handle the redirect
            return;
        }

        console.log('SuperAdminDashboard - User is a super admin, loading dashboard data');
        // User is a super admin, load dashboard data
        loadDashboardData();
        loadAllModuleData();
    }, [authContext]);

    const loadDashboardData = async () => {
        if (!authContext || !authContext.token) {
            console.error('No auth context or token available');
            return;
        }

        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/dashboard'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load dashboard data');
            }

            const data = await response.json();
            setDashboardData(data.data);
        } catch (error) {
            console.error('Error loading dashboard:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load dashboard data'
            });
        } finally {
            setLoading(false);
        }
    };

    const loadAllModuleData = async () => {
        if (!authContext || !authContext.token) return;

        try {
            // Load all module data in parallel
            const [plansRes, businessTypesRes, companiesRes, usersRes, subscriptionsRes] = await Promise.all([
                fetch(window.getApiUrl('/super-admin/plans'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/business-types'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/companies'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/users'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/subscriptions/list'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                })
            ]);

            // Process responses
            if (plansRes.ok) {
                const plansData = await plansRes.json();
                setPlans(plansData.data || []);
            }

            if (businessTypesRes.ok) {
                const businessTypesData = await businessTypesRes.json();
                setBusinessTypes(businessTypesData.data || []);
            }

            if (companiesRes.ok) {
                const companiesData = await companiesRes.json();
                setCompanies(companiesData.data || []);
            }

            if (usersRes.ok) {
                const usersData = await usersRes.json();
                setUsers(usersData.data || []);
            }

            if (subscriptionsRes.ok) {
                const subscriptionsData = await subscriptionsRes.json();
                setSubscriptions(subscriptionsData.data || []);
            }

        } catch (error) {
            console.error('Error loading module data:', error);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Show loading if auth context is not available yet
    if (!authContext) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading Super Admin Dashboard...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
                            <p className="mt-1 text-sm text-gray-500">Bizma SaaS Management Console</p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Super Admin
                            </span>
                            <span className="text-sm text-gray-500">
                                {authContext.user.name}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Navigation Tabs */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav className="flex space-x-8">
                        {[
                            { id: 'overview', name: 'Overview', icon: '📊', description: 'Dashboard overview and statistics' },
                            { id: 'plans', name: 'Plans Management', icon: '💼', description: 'Manage subscription plans and pricing' },
                            { id: 'business-types', name: 'Business Types', icon: '🏪', description: 'Configure business categories and templates' },
                            { id: 'companies', name: 'Companies', icon: '🏢', description: 'Manage registered companies' },
                            { id: 'users', name: 'Users', icon: '👥', description: 'User management and roles' },
                            { id: 'subscriptions', name: 'Subscriptions', icon: '💳', description: 'Monitor active subscriptions' },
                            { id: 'payments', name: 'Payment Gateways', icon: '💰', description: 'Configure payment providers' },
                            { id: 'analytics', name: 'Analytics', icon: '📈', description: 'Business intelligence and reports' },
                            { id: 'settings', name: 'System Settings', icon: '⚙️', description: 'Global system configuration' }
                        ].map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === tab.id
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <span className="mr-2">{tab.icon}</span>
                                {tab.name}
                            </button>
                        ))}
                    </nav>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {activeTab === 'overview' && (
                    <OverviewTab dashboardData={dashboardData} formatCurrency={formatCurrency} formatDate={formatDate} />
                )}
                {activeTab === 'companies' && (
                    <CompaniesTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'users' && (
                    <UsersTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'subscriptions' && (
                    <SubscriptionsTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'plans' && (
                    <PlansManagementTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'payments' && (
                    <PaymentGatewaysTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'business-types' && (
                    <BusinessTypesTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'analytics' && (
                    <AnalyticsTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'settings' && (
                    <SettingsTab authContext={authContext} setNotification={setNotification} />
                )}
            </div>

            {/* Notification */}
            {notification && (
                <Notification
                    type={notification.type}
                    message={notification.message}
                    onClose={() => setNotification(null)}
                />
            )}
        </div>
    );
}

// Overview Tab Component
function OverviewTab({ dashboardData, formatCurrency, formatDate }) {
    if (!dashboardData) return <div>Loading...</div>;

    const { stats, recent_companies, subscription_distribution } = dashboardData;

    return (
        <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">🏢</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Companies</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.total_companies}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div className="bg-gray-50 px-5 py-3">
                        <div className="text-sm">
                            <span className="text-green-600 font-medium">+{stats.new_companies_week}</span>
                            <span className="text-gray-500"> this week</span>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">👥</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.total_users}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div className="bg-gray-50 px-5 py-3">
                        <div className="text-sm">
                            <span className="text-green-600 font-medium">+{stats.new_users_week}</span>
                            <span className="text-gray-500"> this week</span>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">💳</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active Subscriptions</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.active_subscriptions}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">💰</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.monthly_revenue)}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Companies */}
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Companies</h3>
                    <div className="overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {recent_companies.map((company, index) => (
                                    <tr key={index}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {company.company_name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div>
                                                <div className="font-medium">{company.owner_name}</div>
                                                <div className="text-gray-400">{company.owner_email}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {formatDate(company.created_at)}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Companies Tab Component
function CompaniesTab({ authContext, setNotification }) {
    const [companies, setCompanies] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [statusFilter, setStatusFilter] = React.useState('');

    React.useEffect(() => {
        // Only load companies if we have proper auth context
        if (authContext && authContext.token && authContext.user && authContext.user.role === 'super_admin') {
            loadCompanies();
        }
    }, [searchTerm, statusFilter, authContext]);

    const loadCompanies = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (statusFilter) params.append('status', statusFilter);

            const response = await fetch(`${window.getApiUrl('/super-admin/companies')}?${params}`, {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load companies');
            }

            const data = await response.json();
            setCompanies(data.data.companies);
        } catch (error) {
            console.error('Error loading companies:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load companies'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleStatusChange = async (companyId, newStatus) => {
        try {
            const action = newStatus === 'suspended' ? 'suspend' : 'activate';
            const response = await fetch(window.getApiUrl(`/super-admin/companies/${action}`), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ company_id: companyId })
            });

            if (!response.ok) {
                throw new Error(`Failed to ${action} company`);
            }

            setNotification({
                type: 'success',
                message: `Company ${action}d successfully`
            });

            loadCompanies();
        } catch (error) {
            console.error('Error updating company status:', error);
            setNotification({
                type: 'error',
                message: `Failed to update company status`
            });
        }
    };

    return (
        <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="Search companies, owners..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Statuses</option>
                            <option value="active">Active</option>
                            <option value="suspended">Suspended</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Companies Table */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Companies Management</h3>

                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-2 text-gray-500">Loading companies...</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {companies.map((company) => (
                                        <tr key={company.object_id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{company.name}</div>
                                                    <div className="text-sm text-gray-500">{company.email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{company.owner_name}</div>
                                                    <div className="text-sm text-gray-500">{company.owner_email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {company.user_count}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {company.current_plan || 'Free'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    company.status === 'active' ? 'bg-green-100 text-green-800' :
                                                    company.status === 'suspended' ? 'bg-red-100 text-red-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {company.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex space-x-2">
                                                    {company.status === 'active' ? (
                                                        <button
                                                            onClick={() => handleStatusChange(company.object_id, 'suspended')}
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            Suspend
                                                        </button>
                                                    ) : (
                                                        <button
                                                            onClick={() => handleStatusChange(company.object_id, 'active')}
                                                            className="text-green-600 hover:text-green-900"
                                                        >
                                                            Activate
                                                        </button>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

// Users Tab Component
function UsersTab({ authContext, setNotification }) {
    const [users, setUsers] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [roleFilter, setRoleFilter] = React.useState('');

    React.useEffect(() => {
        loadUsers();
    }, [searchTerm, roleFilter]);

    const loadUsers = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (roleFilter) params.append('role', roleFilter);

            const response = await fetch(window.getApiUrl(`/super-admin/users/list?${params}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load users');
            }

            const data = await response.json();
            setUsers(data.data.users);
        } catch (error) {
            console.error('Error loading users:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load users'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="Search users..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                        <select
                            value={roleFilter}
                            onChange={(e) => setRoleFilter(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Roles</option>
                            <option value="admin">Admin</option>
                            <option value="user">User</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Users Table */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Users Management</h3>

                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-2 text-gray-500">Loading users...</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {users.map((user) => (
                                        <tr key={user.object_id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                                    <div className="text-sm text-gray-500">{user.email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {user.company_name || 'No Company'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                                                    user.role === 'user' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {user.role}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    user.status === 'active' ? 'bg-green-100 text-green-800' :
                                                    user.status === 'suspended' ? 'bg-red-100 text-red-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {user.status}
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

// Placeholder components for other tabs
function SubscriptionsTab({ authContext, setNotification }) {
    const [subscriptions, setSubscriptions] = React.useState([]);
    const [stats, setStats] = React.useState({});
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [statusFilter, setStatusFilter] = React.useState('all');
    const [showExtendTrialModal, setShowExtendTrialModal] = React.useState(false);
    const [selectedSubscription, setSelectedSubscription] = React.useState(null);
    const [daysToAdd, setDaysToAdd] = React.useState(7);
    const [processing, setProcessing] = React.useState(false);

    React.useEffect(() => {
        // Only fetch subscriptions if we have proper auth context
        if (authContext && authContext.token && authContext.user && authContext.user.role === 'super_admin') {
            fetchSubscriptions();
        }
    }, [authContext]);

    const fetchSubscriptions = async () => {
        try {
            const response = await fetch(window.getApiUrl('/super-admin/subscriptions/list'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                setSubscriptions(data.data || []);
                setStats(data.stats || {});
            }
        } catch (error) {
            console.error('Error fetching subscriptions:', error);
            setNotification({ type: 'error', message: 'Failed to load subscriptions' });
        } finally {
            setLoading(false);
        }
    };
    
    const extendTrial = async () => {
        if (!selectedSubscription || !daysToAdd) return;
        
        setProcessing(true);
        try {
            const response = await fetch(window.getApiUrl(`/super-admin/subscriptions/${selectedSubscription.id}/extend-trial`), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ daysToAdd })
            });
            
            const data = await response.json();
            
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: `Trial extended by ${daysToAdd} days successfully`
                });
                
                // Close modal and refresh subscriptions
                setShowExtendTrialModal(false);
                fetchSubscriptions();
            } else {
                throw new Error(data.message || 'Failed to extend trial');
            }
        } catch (error) {
            console.error('Error extending trial:', error);
            setNotification({
                type: 'error',
                message: 'Failed to extend trial: ' + (error.message || 'Unknown error')
            });
        } finally {
            setProcessing(false);
        }
    };

    const filteredSubscriptions = subscriptions.filter(sub => {
        const matchesSearch = (sub.company_name && sub.company_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
                            (sub.plan_id && sub.plan_id.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesStatus = statusFilter === 'all' || sub.status === statusFilter;
        return matchesSearch && matchesStatus;
    });

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">Loading subscriptions...</div>
            </div>
        );
    }

    return (
        <div className="bg-white shadow rounded-lg p-6">
            {/* Render ExtendTrialModal if showExtendTrialModal is true */}
            {showExtendTrialModal && selectedSubscription && (
                <ExtendTrialModal
                    subscription={selectedSubscription}
                    daysToAdd={daysToAdd}
                    setDaysToAdd={setDaysToAdd}
                    onCancel={() => setShowExtendTrialModal(false)}
                    onConfirm={extendTrial}
                    processing={processing}
                />
            )}
            
            <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">Subscription Management</h3>
                <button
                    onClick={fetchSubscriptions}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    <i className="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">📊</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Subscriptions</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.total_subscriptions || subscriptions.length}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">✅</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active Subscriptions</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.active_subscriptions || subscriptions.filter(s => s.status === 'active').length}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">🔄</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Trial Subscriptions</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.trial_subscriptions || subscriptions.filter(s => s.status === 'trial').length}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">💰</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                                    <dd className="text-lg font-medium text-gray-900">${stats.monthly_revenue || '0'}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="flex gap-4 mb-6">
                <input
                    type="text"
                    placeholder="Search subscriptions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="expired">Expired</option>
                </select>
            </div>

            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Billing</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {filteredSubscriptions.map((subscription) => (
                            <tr key={subscription.id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{subscription.company_name}</div>
                                    <div className="text-sm text-gray-500">{subscription.company_email}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{subscription.plan_id}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                                        subscription.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                    }`}>
                                        {subscription.status}
                                    </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${subscription.amount}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {subscription.next_billing_date || 'N/A'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button 
                                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                                        onClick={() => {
                                            // View subscription details
                                            console.log('View subscription', subscription);
                                        }}
                                    >
                                        View
                                    </button>
                                    {subscription.plan_id === 'trial' && (
                                        <button 
                                            className="text-green-600 hover:text-green-900 mr-3"
                                            onClick={() => {
                                                setSelectedSubscription(subscription);
                                                setShowExtendTrialModal(true);
                                            }}
                                        >
                                            Extend Trial
                                        </button>
                                    )}
                                    <button 
                                        className="text-red-600 hover:text-red-900"
                                        onClick={() => {
                                            // Cancel subscription
                                            console.log('Cancel subscription', subscription);
                                        }}
                                    >
                                        Cancel
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
                {filteredSubscriptions.length === 0 && (
                    <div className="text-center py-8 text-gray-500">No subscriptions found</div>
                )}
            </div>
        </div>
    );
}

function AnalyticsTab({ authContext, setNotification }) {
    const [analytics, setAnalytics] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [timeRange, setTimeRange] = React.useState('30');

    React.useEffect(() => {
        fetchAnalytics();
    }, [timeRange]);

    const fetchAnalytics = async () => {
        try {
            const response = await fetch(window.getApiUrl(`/super-admin/analytics?days=${timeRange}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                setAnalytics(data.data);
            }
        } catch (error) {
            console.error('Error fetching analytics:', error);
            setNotification({ type: 'error', message: 'Failed to load analytics' });
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">Loading analytics...</div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-medium text-gray-900">Analytics & Reports</h3>
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                </div>

                {analytics && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div className="bg-blue-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">$</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                                    <p className="text-2xl font-semibold text-gray-900">${analytics.revenue && analytics.revenue.total ? analytics.revenue.total : '0'}</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">↗</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">Growth Rate</p>
                                    <p className="text-2xl font-semibold text-gray-900">{analytics.growth && analytics.growth.rate ? analytics.growth.rate : '0'}%</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">👥</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">New Customers</p>
                                    <p className="text-2xl font-semibold text-gray-900">{analytics.customers && analytics.customers.new ? analytics.customers.new : '0'}</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-orange-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">%</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">Churn Rate</p>
                                    <p className="text-2xl font-semibold text-gray-900">{analytics.churn && analytics.churn.rate ? analytics.churn.rate : '0'}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-md font-medium text-gray-900 mb-4">Top Performing Plans</h4>
                        {analytics && analytics.top_plans && analytics.top_plans.map((plan, index) => (
                            <div key={index} className="flex justify-between items-center py-2">
                                <span className="text-sm text-gray-600">{plan.name}</span>
                                <span className="text-sm font-medium text-gray-900">{plan.count} subscribers</span>
                            </div>
                        )) || <p className="text-gray-500">No data available</p>}
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-md font-medium text-gray-900 mb-4">Recent Activity</h4>
                        {analytics && analytics.recent_activity && analytics.recent_activity.map((activity, index) => (
                            <div key={index} className="flex justify-between items-center py-2">
                                <span className="text-sm text-gray-600">{activity.description}</span>
                                <span className="text-xs text-gray-500">{activity.time}</span>
                            </div>
                        )) || <p className="text-gray-500">No recent activity</p>}
                    </div>
                </div>
            </div>
        </div>
    );
}

function SettingsTab({ authContext, setNotification }) {
    const [settings, setSettings] = React.useState({
        site_name: 'Bizma',
        site_description: 'Business Management SaaS Platform',
        maintenance_mode: false,
        registration_enabled: true,
        email_verification_required: true,
        max_companies_per_user: 5,
        default_trial_days: 14,
        support_email: '<EMAIL>'
    });
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);

    React.useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            const response = await fetch(window.getApiUrl('/super-admin/settings'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success && data.data.settings) {
                // Convert string boolean values to actual booleans
                const normalizedSettings = { ...data.data.settings };

                // Convert string booleans to actual booleans
                const booleanFields = ['maintenance_mode', 'registration_enabled', 'email_verification_required'];
                booleanFields.forEach(field => {
                    if (normalizedSettings[field] !== undefined) {
                        if (typeof normalizedSettings[field] === 'string') {
                            normalizedSettings[field] = normalizedSettings[field] === 'true' || normalizedSettings[field] === '1';
                        } else {
                            normalizedSettings[field] = Boolean(normalizedSettings[field]);
                        }
                    }
                });

                setSettings({ ...settings, ...normalizedSettings });
            }
        } catch (error) {
            console.error('Error fetching settings:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            const response = await fetch(window.getApiUrl('/super-admin/settings'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ settings })
            });
            const data = await response.json();
            if (data.success) {
                setNotification({ type: 'success', message: 'Settings saved successfully' });
            } else {
                setNotification({ type: 'error', message: data.message || 'Failed to save settings' });
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            setNotification({ type: 'error', message: 'Failed to save settings' });
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (key, value) => {
        setSettings(prev => ({ ...prev, [key]: value }));
    };

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">Loading settings...</div>
            </div>
        );
    }

    return (
        <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">System Settings</h3>
                <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                    {saving ? 'Saving...' : 'Save Changes'}
                </button>
            </div>

            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                        <input
                            type="text"
                            value={settings.site_name}
                            onChange={(e) => handleInputChange('site_name', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                        <input
                            type="email"
                            value={settings.support_email}
                            onChange={(e) => handleInputChange('support_email', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                    <textarea
                        value={settings.site_description}
                        onChange={(e) => handleInputChange('site_description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Max Companies per User</label>
                        <input
                            type="number"
                            value={settings.max_companies_per_user}
                            onChange={(e) => handleInputChange('max_companies_per_user', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Default Trial Days</label>
                        <input
                            type="number"
                            value={settings.default_trial_days}
                            onChange={(e) => handleInputChange('default_trial_days', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>

                <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">System Controls</h4>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Maintenance Mode</label>
                            <p className="text-sm text-gray-500">Temporarily disable access to the application</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={Boolean(settings.maintenance_mode)}
                                onChange={(e) => handleInputChange('maintenance_mode', e.target.checked)}
                                className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Registration Enabled</label>
                            <p className="text-sm text-gray-500">Allow new users to register</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={Boolean(settings.registration_enabled)}
                                onChange={(e) => handleInputChange('registration_enabled', e.target.checked)}
                                className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Email Verification Required</label>
                            <p className="text-sm text-gray-500">Require email verification for new accounts</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={Boolean(settings.email_verification_required)}
                                onChange={(e) => handleInputChange('email_verification_required', e.target.checked)}
                                className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Enhanced CMS Tab Component with Plan Management
function CMSTab({ authContext, setNotification }) {
    const [activeSection, setActiveSection] = React.useState('plans');
    const [plans, setPlans] = React.useState([]);
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [systemSettings, setSystemSettings] = React.useState({});
    const [policyPages, setPolicyPages] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);
    const [showPlanModal, setShowPlanModal] = React.useState(false);
    const [editingPlan, setEditingPlan] = React.useState(null);
    const [showPolicyModal, setShowPolicyModal] = React.useState(false);
    const [editingPolicy, setEditingPolicy] = React.useState(null);
    
    // Load CMS data when component mounts
    React.useEffect(() => {
        // Only load CMS data if we have proper auth context and user is super admin
        if (authContext && authContext.token && authContext.user && authContext.user.role === 'super_admin') {
            loadCMSData();
        }
    }, [authContext]);

    const loadCMSData = async () => {
        try {
            setLoading(true);

            // Load all CMS data in parallel
            const [plansRes, businessTypesRes, settingsRes, policiesRes] = await Promise.all([
                fetch(window.getApiUrl('/super-admin/plans'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/business-types'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/system-settings'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/policy-pages'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                })
            ]);

            // Process plans data
            if (plansRes.ok) {
                const plansData = await plansRes.json();
                if (plansData.success) {
                    setPlans(plansData.data || []);
                }
            }

            // Process business types data
            if (businessTypesRes.ok) {
                const businessTypesData = await businessTypesRes.json();
                if (businessTypesData.success) {
                    setBusinessTypes(businessTypesData.data || []);
                }
            }

            // Process system settings data
            if (settingsRes.ok) {
                const settingsData = await settingsRes.json();
                if (settingsData.success) {
                    setSystemSettings(settingsData.data || {});
                }
            }

            // Process policy pages data
            if (policiesRes.ok) {
                const policiesData = await policiesRes.json();
                if (policiesData.success) {
                    setPolicyPages(policiesData.data || []);
                }
            }

        } catch (error) {
            console.error('Error loading CMS data:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load CMS data'
            });
        } finally {
            setLoading(false);
        }
    };

    // Plan management functions
    const handleCreatePlan = () => {
        setEditingPlan({
            id: '',
            name: '',
            description: '',
            short_description: '',
            price_monthly: 0,
            price_yearly: 0,
            trial_days: 14,
            features: [],
            limits_data: {},
            business_types: [],
            is_trial_available: true,
            is_visible: true,
            is_popular: false,
            sort_order: 0
        });
        setShowPlanModal(true);
    };

    const handleEditPlan = (plan) => {
        setEditingPlan({
            ...plan,
            features: Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]'),
            limits_data: typeof plan.limits_data === 'object' ? plan.limits_data : JSON.parse(plan.limits_data || '{}'),
            business_types: Array.isArray(plan.business_types) ? plan.business_types : JSON.parse(plan.business_types || '[]')
        });
        setShowPlanModal(true);
    };

    const handleSavePlan = async (planData) => {
        setSaving(true);
        try {
            const url = planData.id ?
                window.getApiUrl(`/super-admin/plans/${planData.id}`) :
                window.getApiUrl('/super-admin/plans');

            const method = planData.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    section,
                    content: content[section]
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: `${section.charAt(0).toUpperCase() + section.slice(1)} content updated successfully`
                });
            } else {
                throw new Error(data.message || 'Failed to update content');
            }
        } catch (error) {
            console.error('Error saving content:', error);
            setNotification({
                type: 'error',
                message: 'Failed to update content: ' + (error.message || 'Unknown error')
            });
        } finally {
            setSaving(false);
        }
    };

    const handleDeletePlan = async (planId) => {
        if (!confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/plans/${planId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: 'Plan deleted successfully'
                });
                loadCMSData(); // Reload data
            } else {
                setNotification({
                    type: 'error',
                    message: data.message || 'Failed to delete plan'
                });
            }
        } catch (error) {
            console.error('Error deleting plan:', error);
            setNotification({
                type: 'error',
                message: 'Failed to delete plan'
            });
        }
    };

    const renderPlansManagement = () => (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Pricing Plans Management</h3>
                <button
                    onClick={handleCreatePlan}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Create New Plan
                </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {plans.map((plan) => (
                    <div key={plan.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                        <div className="flex justify-between items-start mb-4">
                            <div>
                                <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                                <p className="text-sm text-gray-600">{plan.short_description}</p>
                            </div>
                            <div className="flex space-x-2">
                                {plan.is_popular && (
                                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        Popular
                                    </span>
                                )}
                                {!plan.is_visible && (
                                    <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                        Hidden
                                    </span>
                                )}
                            </div>
                        </div>

                        <div className="mb-4">
                            <div className="flex items-baseline">
                                <span className="text-2xl font-bold text-gray-900">₹{plan.price_monthly}</span>
                                <span className="text-gray-600 ml-1">/month</span>
                            </div>
                            <div className="text-sm text-gray-600">
                                ₹{plan.price_yearly}/year (Save ₹{(plan.price_monthly * 12) - plan.price_yearly})
                            </div>
                        </div>

                        <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-2">Features:</p>
                            <div className="space-y-1">
                                {(Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]')).slice(0, 3).map((feature, index) => (
                                    <div key={index} className="flex items-center text-sm text-gray-600">
                                        <i className="fas fa-check text-green-500 mr-2 text-xs"></i>
                                        {feature}
                                    </div>
                                ))}
                                {(Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]')).length > 3 && (
                                    <div className="text-xs text-gray-500">
                                        +{(Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]')).length - 3} more features
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            <button
                                onClick={() => handleEditPlan(plan)}
                                className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-md hover:bg-blue-100 text-sm"
                            >
                                <i className="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button
                                onClick={() => handleDeletePlan(plan.id)}
                                className="flex-1 bg-red-50 text-red-600 px-3 py-2 rounded-md hover:bg-red-100 text-sm"
                            >
                                <i className="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {plans.length === 0 && (
                <div className="text-center py-12">
                    <i className="fas fa-credit-card text-gray-300 text-4xl mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No plans found</h3>
                    <p className="text-gray-600 mb-4">Create your first pricing plan to get started.</p>
                    <button
                        onClick={handleCreatePlan}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Create Plan
                    </button>
                </div>
            )}
        </div>
    );

    const renderContactContent = () => (
        <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Support Email
                    </label>
                    <input
                        type="email"
                        value={content.contact.email}
                        onChange={(e) => handleContentChange('contact', 'email', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                    </label>
                    <input
                        type="text"
                        value={content.contact.phone}
                        onChange={(e) => handleContentChange('contact', 'phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Address
                </label>
                <textarea
                    rows={4}
                    value={content.contact.address}
                    onChange={(e) => handleContentChange('contact', 'address', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Hours
                </label>
                <textarea
                    rows={4}
                    value={content.contact.business_hours}
                    onChange={(e) => handleContentChange('contact', 'business_hours', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
            </div>

            <button
                onClick={() => saveContent('contact')}
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
                {saving ? 'Saving...' : 'Save Contact Information'}
            </button>
        </div>
    );

    return (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-6">Content Management System</h2>

                    {/* Section Navigation */}
                    <div className="border-b border-gray-200 mb-6">
                        <nav className="-mb-px flex space-x-8">
                            {[
                                { id: 'plans', name: 'Pricing Plans', icon: '💰' },
                                { id: 'business-types', name: 'Business Types', icon: '🏢' },
                                { id: 'templates', name: 'Business Templates', icon: '📋' },
                                { id: 'companies', name: 'Company Management', icon: '🏬' },
                                { id: 'system-settings', name: 'System Settings', icon: '⚙️' },
                                { id: 'policies', name: 'Policy Pages', icon: '📄' }
                            ].map((section) => (
                                <button
                                    key={section.id}
                                    onClick={() => setActiveSection(section.id)}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        activeSection === section.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    <span className="mr-2">{section.icon}</span>
                                    {section.name}
                                </button>
                            ))}
                        </nav>
                    </div>

                    {/* Content Sections */}
                    {activeSection === 'plans' && renderPlansManagement()}
                    {activeSection === 'business-types' && (
                        <BusinessTypesManager
                            authContext={authContext}
                            setNotification={setNotification}
                        />
                    )}
                    {activeSection === 'templates' && (
                        <BusinessTypeTemplates
                            authContext={authContext}
                            setNotification={setNotification}
                        />
                    )}
                    {activeSection === 'companies' && (
                        <CompanyManagement
                            authContext={authContext}
                            setNotification={setNotification}
                        />
                    )}
                    {activeSection === 'system-settings' && (
                        <SystemSettingsManager
                            authContext={authContext}
                            setNotification={setNotification}
                        />
                    )}
                    {activeSection === 'policies' && (
                        <PolicyPagesManager
                            authContext={authContext}
                            setNotification={setNotification}
                        />
                    )}
                </div>
            </div>

            {/* Plan Modal */}
            {showPlanModal && (
                <PlanModal
                    isOpen={showPlanModal}
                    onClose={() => {
                        setShowPlanModal(false);
                        setEditingPlan(null);
                    }}
                    plan={editingPlan}
                    onSave={handleSavePlan}
                    businessTypes={businessTypes}
                />
            )}
        </div>
    );
}

// Plans Management Tab Component
function PlansManagementTab({ authContext, setNotification }) {
    const [plans, setPlans] = React.useState([]);
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showPlanModal, setShowPlanModal] = React.useState(false);
    const [editingPlan, setEditingPlan] = React.useState(null);

    React.useEffect(() => {
        loadPlansData();
    }, []);

    const loadPlansData = async () => {
        try {
            setLoading(true);
            const [plansResponse, businessTypesResponse] = await Promise.all([
                fetch(window.getApiUrl('/super-admin/plans'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                }),
                fetch(window.getApiUrl('/super-admin/business-types'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                })
            ]);

            if (plansResponse.ok) {
                const plansData = await plansResponse.json();
                setPlans(plansData.data || []);
            }

            if (businessTypesResponse.ok) {
                const businessTypesData = await businessTypesResponse.json();
                setBusinessTypes(businessTypesData.data || []);
            }
        } catch (error) {
            console.error('Error loading plans data:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load plans data'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleCreatePlan = () => {
        setEditingPlan({
            id: '',
            name: '',
            description: '',
            price_monthly: '',
            price_yearly: '',
            features: '',
            max_users: '',
            max_storage: '',
            business_types: [],
            is_active: true,
            is_popular: false
        });
        setShowPlanModal(true);
    };

    const handleEditPlan = (plan) => {
        setEditingPlan({ ...plan });
        setShowPlanModal(true);
    };

    const handleSavePlan = async () => {
        try {
            const url = editingPlan.id
                ? window.getApiUrl(`/super-admin/plans/${editingPlan.id}`)
                : window.getApiUrl('/super-admin/plans');

            const method = editingPlan.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(editingPlan)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Plan ${editingPlan.id ? 'updated' : 'created'} successfully`
                    });
                    setShowPlanModal(false);
                    setEditingPlan(null);
                    loadPlansData();
                } else {
                    throw new Error(data.message || 'Failed to save plan');
                }
            } else {
                throw new Error('Failed to save plan');
            }
        } catch (error) {
            console.error('Error saving plan:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to save plan'
            });
        }
    };

    const handleDeletePlan = async (planId) => {
        if (!confirm('Are you sure you want to delete this plan?')) return;

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/plans/${planId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: 'Plan deleted successfully'
                    });
                    loadPlansData();
                } else {
                    throw new Error(data.message || 'Failed to delete plan');
                }
            } else {
                throw new Error('Failed to delete plan');
            }
        } catch (error) {
            console.error('Error deleting plan:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to delete plan'
            });
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Plans Management</h2>
                    <p className="text-sm text-gray-600">Manage subscription plans, pricing, and features</p>
                </div>
                <button
                    onClick={handleCreatePlan}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Create New Plan
                </button>
            </div>

            {/* Plans Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {plans.map((plan) => (
                    <div key={plan.id} className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                        <div className="p-6">
                            <div className="flex items-start justify-between mb-4">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                                    {plan.is_popular && (
                                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 mt-1">
                                            Popular
                                        </span>
                                    )}
                                </div>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    plan.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                    {plan.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </div>

                            <p className="text-sm text-gray-600 mb-4">{plan.description}</p>

                            <div className="mb-4">
                                <div className="text-2xl font-bold text-gray-900">
                                    ₹{plan.price_monthly}/month
                                </div>
                                <div className="text-sm text-gray-500">
                                    ₹{plan.price_yearly}/year (Save {Math.round((1 - (plan.price_yearly / 12) / plan.price_monthly) * 100)}%)
                                </div>
                            </div>

                            <div className="space-y-2 mb-4">
                                <div className="text-xs text-gray-500">
                                    <i className="fas fa-users mr-1"></i>
                                    Up to {plan.max_users} users
                                </div>
                                <div className="text-xs text-gray-500">
                                    <i className="fas fa-database mr-1"></i>
                                    {plan.max_storage}GB storage
                                </div>
                            </div>

                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleEditPlan(plan)}
                                    className="flex-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
                                >
                                    Edit
                                </button>
                                <button
                                    onClick={() => handleDeletePlan(plan.id)}
                                    className="flex-1 px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100"
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Plan Modal */}
            {showPlanModal && (
                <PlanModal
                    plan={editingPlan}
                    setPlan={setEditingPlan}
                    businessTypes={businessTypes}
                    onSave={handleSavePlan}
                    onCancel={() => {
                        setShowPlanModal(false);
                        setEditingPlan(null);
                    }}
                />
            )}
        </div>
    );
}

// Payment Gateways Tab Component
function PaymentGatewaysTab({ authContext, setNotification }) {
    const [gateways, setGateways] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showGatewayModal, setShowGatewayModal] = React.useState(false);
    const [editingGateway, setEditingGateway] = React.useState(null);

    React.useEffect(() => {
        loadPaymentGateways();
    }, []);

    const loadPaymentGateways = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/payment-gateways'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setGateways(data.data || []);
            }
        } catch (error) {
            console.error('Error loading payment gateways:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load payment gateways'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleCreateGateway = () => {
        setEditingGateway({
            id: '',
            name: '',
            provider: 'razorpay',
            api_key: '',
            api_secret: '',
            webhook_secret: '',
            test_mode: true,
            is_active: true,
            supported_currencies: 'INR,USD',
            configuration: '{}'
        });
        setShowGatewayModal(true);
    };

    const handleEditGateway = (gateway) => {
        setEditingGateway({ ...gateway });
        setShowGatewayModal(true);
    };

    const handleSaveGateway = async () => {
        try {
            const url = editingGateway.id
                ? window.getApiUrl(`/super-admin/payment-gateways/${editingGateway.id}`)
                : window.getApiUrl('/super-admin/payment-gateways');

            const method = editingGateway.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(editingGateway)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Payment gateway ${editingGateway.id ? 'updated' : 'created'} successfully`
                    });
                    setShowGatewayModal(false);
                    setEditingGateway(null);
                    loadPaymentGateways();
                } else {
                    throw new Error(data.message || 'Failed to save payment gateway');
                }
            } else {
                throw new Error('Failed to save payment gateway');
            }
        } catch (error) {
            console.error('Error saving payment gateway:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to save payment gateway'
            });
        }
    };

    const handleDeleteGateway = async (gatewayId) => {
        if (!confirm('Are you sure you want to delete this payment gateway?')) return;

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/payment-gateways/${gatewayId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: 'Payment gateway deleted successfully'
                    });
                    loadPaymentGateways();
                } else {
                    throw new Error(data.message || 'Failed to delete payment gateway');
                }
            } else {
                throw new Error('Failed to delete payment gateway');
            }
        } catch (error) {
            console.error('Error deleting payment gateway:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to delete payment gateway'
            });
        }
    };

    const handleTestGateway = async (gatewayId) => {
        try {
            const response = await fetch(window.getApiUrl(`/super-admin/payment-gateways/${gatewayId}/test`), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setNotification({
                    type: data.success ? 'success' : 'error',
                    message: data.message || (data.success ? 'Gateway test successful' : 'Gateway test failed')
                });
            } else {
                throw new Error('Failed to test payment gateway');
            }
        } catch (error) {
            console.error('Error testing payment gateway:', error);
            setNotification({
                type: 'error',
                message: 'Failed to test payment gateway'
            });
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Payment Gateways</h2>
                    <p className="text-sm text-gray-600">Configure payment processors and gateway settings</p>
                </div>
                <button
                    onClick={handleCreateGateway}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Add Payment Gateway
                </button>
            </div>

            {/* Payment Gateways Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {gateways.map((gateway) => (
                    <div key={gateway.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                        <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center">
                                <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
                                    <i className="fas fa-credit-card text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900">{gateway.name}</h4>
                                    <span className="text-sm text-gray-500 capitalize">{gateway.provider}</span>
                                </div>
                            </div>
                            <div className="flex flex-col space-y-1">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    gateway.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                    {gateway.is_active ? 'Active' : 'Inactive'}
                                </span>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    gateway.test_mode ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'
                                }`}>
                                    {gateway.test_mode ? 'Test' : 'Live'}
                                </span>
                            </div>
                        </div>

                        <div className="space-y-2 mb-4">
                            <div className="text-xs text-gray-500">
                                <strong>Currencies:</strong> {gateway.supported_currencies}
                            </div>
                            <div className="text-xs text-gray-500">
                                <strong>API Key:</strong> {gateway.api_key ? '••••••••' : 'Not configured'}
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            <button
                                onClick={() => handleEditGateway(gateway)}
                                className="flex-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
                            >
                                Edit
                            </button>
                            <button
                                onClick={() => handleTestGateway(gateway.id)}
                                className="flex-1 px-3 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-md hover:bg-green-100"
                            >
                                Test
                            </button>
                            <button
                                onClick={() => handleDeleteGateway(gateway.id)}
                                className="flex-1 px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Payment Gateway Modal */}
            {showGatewayModal && (
                <PaymentGatewayModal
                    gateway={editingGateway}
                    setGateway={setEditingGateway}
                    onSave={handleSaveGateway}
                    onCancel={() => {
                        setShowGatewayModal(false);
                        setEditingGateway(null);
                    }}
                />
            )}
        </div>
    );
}

// Business Types Tab Component
function BusinessTypesTab({ authContext, setNotification }) {
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showTypeModal, setShowTypeModal] = React.useState(false);
    const [editingType, setEditingType] = React.useState(null);

    React.useEffect(() => {
        loadBusinessTypes();
    }, []);

    const loadBusinessTypes = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/business-types'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setBusinessTypes(data.data || []);
            }
        } catch (error) {
            console.error('Error loading business types:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load business types'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleCreateType = () => {
        setEditingType({
            id: '',
            name: '',
            description: '',
            icon: 'fas fa-building',
            color: 'blue',
            default_modules: '',
            default_categories: '',
            default_features: '',
            default_templates: '',
            is_active: true
        });
        setShowTypeModal(true);
    };

    const handleEditType = (type) => {
        setEditingType({ ...type });
        setShowTypeModal(true);
    };

    const handleSaveType = async () => {
        try {
            const url = editingType.id
                ? window.getApiUrl(`/super-admin/business-types/${editingType.id}`)
                : window.getApiUrl('/super-admin/business-types');

            const method = editingType.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(editingType)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Business type ${editingType.id ? 'updated' : 'created'} successfully`
                    });
                    setShowTypeModal(false);
                    setEditingType(null);
                    loadBusinessTypes();
                } else {
                    throw new Error(data.message || 'Failed to save business type');
                }
            } else {
                throw new Error('Failed to save business type');
            }
        } catch (error) {
            console.error('Error saving business type:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to save business type'
            });
        }
    };

    const handleDeleteType = async (typeId) => {
        if (!confirm('Are you sure you want to delete this business type?')) return;

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/business-types/${typeId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: 'Business type deleted successfully'
                    });
                    loadBusinessTypes();
                } else {
                    throw new Error(data.message || 'Failed to delete business type');
                }
            } else {
                throw new Error('Failed to delete business type');
            }
        } catch (error) {
            console.error('Error deleting business type:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to delete business type'
            });
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Business Types Management</h2>
                    <p className="text-sm text-gray-600">Manage business categories and templates</p>
                </div>
                <button
                    onClick={handleCreateType}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Create Business Type
                </button>
            </div>

            {/* Business Types Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {businessTypes.map((type) => (
                    <div key={type.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                        <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center">
                                <div className={`w-10 h-10 rounded-lg bg-${type.color}-100 flex items-center justify-center mr-3`}>
                                    <i className={`${type.icon} text-${type.color}-600`}></i>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900">{type.name}</h4>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        type.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}>
                                        {type.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-4">{type.description}</p>

                        <div className="space-y-2 mb-4">
                            <div className="text-xs text-gray-500">
                                <strong>Categories:</strong> {type.default_categories || 'None'}
                            </div>
                            <div className="text-xs text-gray-500">
                                <strong>Features:</strong> {type.default_features || 'None'}
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            <button
                                onClick={() => handleEditType(type)}
                                className="flex-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
                            >
                                Edit
                            </button>
                            <button
                                onClick={() => handleDeleteType(type.id)}
                                className="flex-1 px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Business Type Modal */}
            {showTypeModal && (
                <BusinessTypeModal
                    type={editingType}
                    setType={setEditingType}
                    onSave={handleSaveType}
                    onCancel={() => {
                        setShowTypeModal(false);
                        setEditingType(null);
                    }}
                />
            )}
        </div>
    );
}

// Plan Modal Component
function PlanModal({ plan, setPlan, businessTypes, onSave, onCancel }) {
    const handleInputChange = (field, value) => {
        setPlan(prev => ({ ...prev, [field]: value }));
    };

    const handleBusinessTypeToggle = (typeId) => {
        setPlan(prev => ({
            ...prev,
            business_types: prev.business_types.includes(typeId)
                ? prev.business_types.filter(id => id !== typeId)
                : [...prev.business_types, typeId]
        }));
    };

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {plan.id ? 'Edit Plan' : 'Create New Plan'}
                    </h3>

                    <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Plan Name</label>
                                <input
                                    type="text"
                                    value={plan.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter plan name"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Plan ID</label>
                                <input
                                    type="text"
                                    value={plan.id}
                                    onChange={(e) => handleInputChange('id', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="plan-id"
                                    disabled={!!plan.id}
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea
                                value={plan.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Plan description"
                            />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Monthly Price (₹)</label>
                                <input
                                    type="number"
                                    value={plan.price_monthly}
                                    onChange={(e) => handleInputChange('price_monthly', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="0"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Yearly Price (₹)</label>
                                <input
                                    type="number"
                                    value={plan.price_yearly}
                                    onChange={(e) => handleInputChange('price_yearly', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="0"
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Max Users</label>
                                <input
                                    type="number"
                                    value={plan.max_users}
                                    onChange={(e) => handleInputChange('max_users', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Unlimited"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Max Storage (GB)</label>
                                <input
                                    type="number"
                                    value={plan.max_storage}
                                    onChange={(e) => handleInputChange('max_storage', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Unlimited"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Features (comma-separated)</label>
                            <textarea
                                value={plan.features}
                                onChange={(e) => handleInputChange('features', e.target.value)}
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Feature 1, Feature 2, Feature 3"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Business Types</label>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {businessTypes.map((type) => (
                                    <label key={type.id} className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={plan.business_types.includes(type.id)}
                                            onChange={() => handleBusinessTypeToggle(type.id)}
                                            className="mr-2"
                                        />
                                        <span className="text-sm">{type.name}</span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={plan.is_active}
                                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm">Active</span>
                            </label>

                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={plan.is_popular}
                                    onChange={(e) => handleInputChange('is_popular', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm">Popular</span>
                            </label>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                        <button
                            onClick={onCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={onSave}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                        >
                            {plan.id ? 'Update Plan' : 'Create Plan'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Payment Gateway Modal Component
function PaymentGatewayModal({ gateway, setGateway, onSave, onCancel }) {
    const handleInputChange = (field, value) => {
        setGateway(prev => ({ ...prev, [field]: value }));
    };

    const providerOptions = [
        { value: 'razorpay', label: 'Razorpay' },
        { value: 'stripe', label: 'Stripe' },
        { value: 'paypal', label: 'PayPal' },
        { value: 'payu', label: 'PayU' },
        { value: 'cashfree', label: 'Cashfree' }
    ];

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {gateway.id ? 'Edit Payment Gateway' : 'Add New Payment Gateway'}
                    </h3>

                    <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Gateway Name</label>
                                <input
                                    type="text"
                                    value={gateway.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter gateway name"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Provider</label>
                                <select
                                    value={gateway.provider}
                                    onChange={(e) => handleInputChange('provider', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    {providerOptions.map((option) => (
                                        <option key={option.value} value={option.value}>{option.label}</option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                                <input
                                    type="text"
                                    value={gateway.api_key}
                                    onChange={(e) => handleInputChange('api_key', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter API key"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">API Secret</label>
                                <input
                                    type="password"
                                    value={gateway.api_secret}
                                    onChange={(e) => handleInputChange('api_secret', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter API secret"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Webhook Secret</label>
                            <input
                                type="password"
                                value={gateway.webhook_secret}
                                onChange={(e) => handleInputChange('webhook_secret', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Enter webhook secret"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Supported Currencies (comma-separated)</label>
                            <input
                                type="text"
                                value={gateway.supported_currencies}
                                onChange={(e) => handleInputChange('supported_currencies', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="INR,USD,EUR"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Configuration (JSON)</label>
                            <textarea
                                value={gateway.configuration}
                                onChange={(e) => handleInputChange('configuration', e.target.value)}
                                rows={4}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder='{"theme": {"color": "#3399cc"}}'
                            />
                        </div>

                        <div className="flex items-center space-x-4">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={gateway.test_mode}
                                    onChange={(e) => handleInputChange('test_mode', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm">Test Mode</span>
                            </label>

                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={gateway.is_active}
                                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm">Active</span>
                            </label>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                        <button
                            onClick={onCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={onSave}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                        >
                            {gateway.id ? 'Update Gateway' : 'Add Gateway'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Business Type Modal Component
function BusinessTypeModal({ type, setType, onSave, onCancel }) {
    const handleInputChange = (field, value) => {
        setType(prev => ({ ...prev, [field]: value }));
    };

    const colorOptions = [
        'blue', 'green', 'purple', 'red', 'yellow', 'indigo', 'pink', 'gray'
    ];

    const iconOptions = [
        'fas fa-building', 'fas fa-store', 'fas fa-gem', 'fas fa-utensils',
        'fas fa-car', 'fas fa-laptop', 'fas fa-heart', 'fas fa-home',
        'fas fa-briefcase', 'fas fa-cogs', 'fas fa-paint-brush', 'fas fa-music'
    ];

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {type.id ? 'Edit Business Type' : 'Create New Business Type'}
                    </h3>

                    <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Type Name</label>
                                <input
                                    type="text"
                                    value={type.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter business type name"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Type ID</label>
                                <input
                                    type="text"
                                    value={type.id}
                                    onChange={(e) => handleInputChange('id', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="business-type-id"
                                    disabled={!!type.id}
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea
                                value={type.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Business type description"
                            />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Icon</label>
                                <select
                                    value={type.icon}
                                    onChange={(e) => handleInputChange('icon', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    {iconOptions.map((icon) => (
                                        <option key={icon} value={icon}>{icon}</option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
                                <select
                                    value={type.color}
                                    onChange={(e) => handleInputChange('color', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    {colorOptions.map((color) => (
                                        <option key={color} value={color}>{color}</option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Default Categories (comma-separated)</label>
                            <textarea
                                value={type.default_categories}
                                onChange={(e) => handleInputChange('default_categories', e.target.value)}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Category 1, Category 2, Category 3"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Default Features (comma-separated)</label>
                            <textarea
                                value={type.default_features}
                                onChange={(e) => handleInputChange('default_features', e.target.value)}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Feature 1, Feature 2, Feature 3"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Default Modules (comma-separated)</label>
                            <textarea
                                value={type.default_modules}
                                onChange={(e) => handleInputChange('default_modules', e.target.value)}
                                rows={2}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="inventory, sales, customers, reports"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Default Templates (JSON)</label>
                            <textarea
                                value={type.default_templates}
                                onChange={(e) => handleInputChange('default_templates', e.target.value)}
                                rows={4}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder='{"items": [], "categories": []}'
                            />
                        </div>

                        <div className="flex items-center">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={type.is_active}
                                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm">Active</span>
                            </label>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                        <button
                            onClick={onCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={onSave}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                        >
                            {type.id ? 'Update Business Type' : 'Create Business Type'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Extend Trial Modal Component
function ExtendTrialModal({ subscription, daysToAdd, setDaysToAdd, onCancel, onConfirm, processing }) {
    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Extend Trial Period
                    </h3>

                    <div className="space-y-4">
                        <div>
                            <p className="text-sm text-gray-600 mb-2">
                                Company: <strong>{subscription.company_name}</strong>
                            </p>
                            <p className="text-sm text-gray-600 mb-4">
                                Current Plan: <strong>{subscription.plan_id}</strong>
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Days to Add
                            </label>
                            <input
                                type="number"
                                value={daysToAdd}
                                onChange={(e) => setDaysToAdd(parseInt(e.target.value) || 0)}
                                min="1"
                                max="365"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Enter number of days"
                            />
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                        <button
                            onClick={onCancel}
                            disabled={processing}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={onConfirm}
                            disabled={processing || !daysToAdd}
                            className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50"
                        >
                            {processing ? 'Extending...' : `Extend by ${daysToAdd} days`}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.SuperAdminDashboard = SuperAdminDashboard;
