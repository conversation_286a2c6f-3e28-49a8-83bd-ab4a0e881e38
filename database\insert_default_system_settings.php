<?php
/**
 * Insert Default System Settings
 */

require_once __DIR__ . '/../api/db-config.php';

echo "⚙️ Inserting default system settings...\n\n";

try {
    $defaultSettings = [
        // Company Information
        [
            'setting_key' => 'company_name',
            'setting_value' => 'Bizma',
            'setting_type' => 'string',
            'category' => 'company',
            'description' => 'Company name displayed across the platform',
            'is_public' => true
        ],
        [
            'setting_key' => 'company_email',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'string',
            'category' => 'company',
            'description' => 'Primary company email address',
            'is_public' => true
        ],
        [
            'setting_key' => 'company_phone',
            'setting_value' => '+91-9876543210',
            'setting_type' => 'string',
            'category' => 'company',
            'description' => 'Company contact phone number',
            'is_public' => true
        ],
        [
            'setting_key' => 'company_address',
            'setting_value' => 'Business District, Tech City, India',
            'setting_type' => 'string',
            'category' => 'company',
            'description' => 'Company physical address',
            'is_public' => true
        ],
        [
            'setting_key' => 'company_website',
            'setting_value' => 'https://bizma.com',
            'setting_type' => 'string',
            'category' => 'company',
            'description' => 'Company website URL',
            'is_public' => true
        ],

        // Support Settings
        [
            'setting_key' => 'support_email',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'string',
            'category' => 'support',
            'description' => 'Support team email address',
            'is_public' => true
        ],

        // Subscription Settings
        [
            'setting_key' => 'trial_duration_days',
            'setting_value' => '14',
            'setting_type' => 'number',
            'category' => 'subscription',
            'description' => 'Default trial duration in days',
            'is_public' => false
        ],
        [
            'setting_key' => 'enable_trial_extension',
            'setting_value' => 'true',
            'setting_type' => 'boolean',
            'category' => 'subscription',
            'description' => 'Allow super admin to extend trials',
            'is_public' => false
        ],
        [
            'setting_key' => 'max_trial_extensions',
            'setting_value' => '2',
            'setting_type' => 'number',
            'category' => 'subscription',
            'description' => 'Maximum number of trial extensions allowed',
            'is_public' => false
        ],
        [
            'setting_key' => 'auto_suspend_expired_trials',
            'setting_value' => 'true',
            'setting_type' => 'boolean',
            'category' => 'subscription',
            'description' => 'Automatically suspend expired trials',
            'is_public' => false
        ],
        [
            'setting_key' => 'grace_period_days',
            'setting_value' => '3',
            'setting_type' => 'number',
            'category' => 'subscription',
            'description' => 'Grace period before suspending expired subscriptions',
            'is_public' => false
        ],

        // Payment Settings
        [
            'setting_key' => 'payment_currency',
            'setting_value' => 'INR',
            'setting_type' => 'string',
            'category' => 'payment',
            'description' => 'Default payment currency',
            'is_public' => true
        ],
        [
            'setting_key' => 'razorpay_key_id',
            'setting_value' => '',
            'setting_type' => 'string',
            'category' => 'payment',
            'description' => 'Razorpay Key ID for payment processing',
            'is_public' => false
        ],
        [
            'setting_key' => 'razorpay_key_secret',
            'setting_value' => '',
            'setting_type' => 'string',
            'category' => 'payment',
            'description' => 'Razorpay Key Secret for payment processing',
            'is_public' => false
        ],
        [
            'setting_key' => 'razorpay_webhook_secret',
            'setting_value' => '',
            'setting_type' => 'string',
            'category' => 'payment',
            'description' => 'Razorpay Webhook Secret for payment verification',
            'is_public' => false
        ],
        [
            'setting_key' => 'enable_test_mode',
            'setting_value' => 'true',
            'setting_type' => 'boolean',
            'category' => 'payment',
            'description' => 'Enable test mode for payments (use test keys)',
            'is_public' => false
        ],

        // General Settings
        [
            'setting_key' => 'app_name',
            'setting_value' => 'Bizma',
            'setting_type' => 'string',
            'category' => 'general',
            'description' => 'Application name',
            'is_public' => true
        ],
        [
            'setting_key' => 'app_version',
            'setting_value' => '1.0.0',
            'setting_type' => 'string',
            'category' => 'general',
            'description' => 'Application version',
            'is_public' => true
        ],
        [
            'setting_key' => 'maintenance_mode',
            'setting_value' => 'false',
            'setting_type' => 'boolean',
            'category' => 'general',
            'description' => 'Enable maintenance mode',
            'is_public' => false
        ],
        [
            'setting_key' => 'allow_registration',
            'setting_value' => 'true',
            'setting_type' => 'boolean',
            'category' => 'general',
            'description' => 'Allow new user registrations',
            'is_public' => false
        ]
    ];

    $stmt = $conn->prepare("
        INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        setting_type = VALUES(setting_type),
        description = VALUES(description),
        is_public = VALUES(is_public),
        updated_at = NOW()
    ");

    $inserted = 0;
    $updated = 0;

    foreach ($defaultSettings as $setting) {
        // Check if setting exists
        $checkStmt = $conn->prepare("SELECT setting_key FROM system_settings WHERE setting_key = ?");
        $checkStmt->bind_param("s", $setting['setting_key']);
        $checkStmt->execute();
        $exists = $checkStmt->get_result()->num_rows > 0;

        $stmt->bind_param("ssssi",
            $setting['setting_key'],
            $setting['setting_value'],
            $setting['setting_type'],
            $setting['description'],
            $setting['is_public']
        );

        if ($stmt->execute()) {
            if ($exists) {
                echo "✅ Updated setting: {$setting['setting_key']}\n";
                $updated++;
            } else {
                echo "✅ Inserted setting: {$setting['setting_key']}\n";
                $inserted++;
            }
        } else {
            echo "❌ Failed to insert setting: {$setting['setting_key']} - " . $stmt->error . "\n";
        }
    }

    echo "\n📊 Summary:\n";
    echo "✅ Inserted: $inserted settings\n";
    echo "🔄 Updated: $updated settings\n";
    echo "\n🎉 Default system settings configured successfully!\n";

} catch (Exception $e) {
    echo "💥 Failed to insert system settings: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ System settings setup completed.\n";
?>
