<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CRUD API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test CRUD API for Activities, Tasks, and Notes</h1>
    
    <div class="test-section">
        <h2>Test Configuration</h2>
        <label>Lead ID: <input type="text" id="leadId" value="id_687204469e5e6" /></label><br><br>
        <label>Auth Token: <input type="text" id="authToken" value="mock-token-123" /></label><br><br>
        <button onclick="setTestData()">Set Test Data</button>
    </div>

    <div class="test-section">
        <h2>Activities Test</h2>
        <button onclick="testCreateActivity()">Create Activity</button>
        <button onclick="testListActivities()">List Activities</button>
        <div id="activityResults"></div>
    </div>

    <div class="test-section">
        <h2>Tasks Test</h2>
        <button onclick="testCreateTask()">Create Task</button>
        <button onclick="testListTasks()">List Tasks</button>
        <div id="taskResults"></div>
    </div>

    <div class="test-section">
        <h2>Notes Test</h2>
        <button onclick="testCreateNote()">Create Note</button>
        <button onclick="testListNotes()">List Notes</button>
        <div id="noteResults"></div>
    </div>

    <script>
        let leadId = 'id_687204469e5e6';
        let authToken = 'mock-token-123';

        function setTestData() {
            leadId = document.getElementById('leadId').value;
            authToken = document.getElementById('authToken').value;
            localStorage.setItem('authToken', authToken);
            log('info', 'Test data set: Lead ID = ' + leadId + ', Auth Token = ' + authToken);
        }

        function log(type, message, containerId = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (containerId) {
                const container = document.getElementById(containerId);
                const div = document.createElement('div');
                div.className = type;
                div.innerHTML = `<strong>${timestamp}:</strong> ${message}`;
                container.appendChild(div);
            }
        }

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                log('info', `Making ${method} request to: ${url}`);
                if (data) {
                    log('info', `Request data: ${JSON.stringify(data, null, 2)}`);
                }

                const response = await fetch(url, options);
                const responseText = await response.text();
                
                log('info', `Response status: ${response.status} ${response.statusText}`);
                log('info', `Response body: ${responseText}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }

                try {
                    return JSON.parse(responseText);
                } catch (e) {
                    return responseText;
                }
            } catch (error) {
                log('error', `Request failed: ${error.message}`);
                throw error;
            }
        }

        async function testCreateActivity() {
            const container = document.getElementById('activityResults');
            container.innerHTML = '<h3>Creating Activity...</h3>';
            
            try {
                const activityData = {
                    type: 'call',
                    description: 'Test activity created at ' + new Date().toISOString(),
                    lead_id: leadId,
                    activity_date: new Date().toISOString()
                };

                const result = await makeRequest('/biz/api/api.php/activity', 'POST', activityData);
                log('success', 'Activity created successfully!', 'activityResults');
                log('info', `<pre>${JSON.stringify(result, null, 2)}</pre>`, 'activityResults');
            } catch (error) {
                log('error', `Failed to create activity: ${error.message}`, 'activityResults');
            }
        }

        async function testListActivities() {
            const container = document.getElementById('activityResults');
            container.innerHTML = '<h3>Listing Activities...</h3>';
            
            try {
                const result = await makeRequest(`/biz/api/api.php/activity:${leadId}`);
                log('success', 'Activities retrieved successfully!', 'activityResults');
                log('info', `<pre>${JSON.stringify(result, null, 2)}</pre>`, 'activityResults');
            } catch (error) {
                log('error', `Failed to list activities: ${error.message}`, 'activityResults');
            }
        }

        async function testCreateTask() {
            const container = document.getElementById('taskResults');
            container.innerHTML = '<h3>Creating Task...</h3>';
            
            try {
                const taskData = {
                    title: 'Test Task',
                    description: 'Test task created at ' + new Date().toISOString(),
                    dueDate: '2024-12-31',
                    priority: 'medium',
                    status: 'pending',
                    lead_id: leadId
                };

                const result = await makeRequest('/biz/api/api.php/task', 'POST', taskData);
                log('success', 'Task created successfully!', 'taskResults');
                log('info', `<pre>${JSON.stringify(result, null, 2)}</pre>`, 'taskResults');
            } catch (error) {
                log('error', `Failed to create task: ${error.message}`, 'taskResults');
            }
        }

        async function testListTasks() {
            const container = document.getElementById('taskResults');
            container.innerHTML = '<h3>Listing Tasks...</h3>';
            
            try {
                const result = await makeRequest(`/biz/api/api.php/task:${leadId}`);
                log('success', 'Tasks retrieved successfully!', 'taskResults');
                log('info', `<pre>${JSON.stringify(result, null, 2)}</pre>`, 'taskResults');
            } catch (error) {
                log('error', `Failed to list tasks: ${error.message}`, 'taskResults');
            }
        }

        async function testCreateNote() {
            const container = document.getElementById('noteResults');
            container.innerHTML = '<h3>Creating Note...</h3>';
            
            try {
                const noteData = {
                    content: 'Test note created at ' + new Date().toISOString(),
                    lead_id: leadId,
                    type: 'note',
                    status: 'active'
                };

                const result = await makeRequest('/biz/api/api.php/note', 'POST', noteData);
                log('success', 'Note created successfully!', 'noteResults');
                log('info', `<pre>${JSON.stringify(result, null, 2)}</pre>`, 'noteResults');
            } catch (error) {
                log('error', `Failed to create note: ${error.message}`, 'noteResults');
            }
        }

        async function testListNotes() {
            const container = document.getElementById('noteResults');
            container.innerHTML = '<h3>Listing Notes...</h3>';
            
            try {
                const result = await makeRequest(`/biz/api/api.php/note:${leadId}`);
                log('success', 'Notes retrieved successfully!', 'noteResults');
                log('info', `<pre>${JSON.stringify(result, null, 2)}</pre>`, 'noteResults');
            } catch (error) {
                log('error', `Failed to list notes: ${error.message}`, 'noteResults');
            }
        }

        // Set initial test data
        setTestData();
    </script>
</body>
</html>