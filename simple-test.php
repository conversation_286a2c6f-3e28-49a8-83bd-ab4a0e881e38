<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    echo json_encode(['step' => 1, 'message' => 'Starting test']);
    
    // Test if we can access the register.php file
    $registerPath = __DIR__ . '/api/register.php';
    echo json_encode(['step' => 2, 'register_path' => $registerPath, 'exists' => file_exists($registerPath)]);
    
    if (!file_exists($registerPath)) {
        throw new Exception('Register.php not found');
    }
    
    // Test basic POST to register.php
    $postData = json_encode([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'TestPassword123!',
        'company_name' => 'Test Company'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $url = 'http://localhost/biz/api/register.php';
    $result = file_get_contents($url, false, $context);
    
    echo json_encode(['step' => 3, 'api_response' => $result]);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
}
?>
