<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Current User</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffe6e6; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e6ffe6; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>🔍 Debug Current User & Lead API</h1>
    
    <div class="info">
        <h3>Current Browser State</h3>
        <div id="browser-state"></div>
    </div>
    
    <div class="info">
        <h3>API Test Results</h3>
        <div id="api-results"></div>
    </div>
    
    <button onclick="testLeadAPI()">Test Lead API</button>
    <button onclick="testWithDifferentTokens()">Test With Different Tokens</button>
    <button onclick="clearStorage()">Clear Storage & Reload</button>

    <script>
        // Include the config
        const script = document.createElement('script');
        script.src = 'config.js';
        document.head.appendChild(script);
        
        script.onload = function() {
            // Helper function to get API URL
            window.getApiUrl = function(endpoint) {
                return window.APP_CONFIG.API_BASE_URL + endpoint;
            };
            
            displayBrowserState();
        };

        function displayBrowserState() {
            const authToken = localStorage.getItem('authToken');
            const lastCompanyId = localStorage.getItem('lastCompanyId');
            const userInfo = localStorage.getItem('userInfo');
            
            const html = `
                <p><strong>Auth Token:</strong> ${authToken ? authToken.substring(0, 20) + '...' : 'None'}</p>
                <p><strong>Last Company ID:</strong> ${lastCompanyId || 'None'}</p>
                <p><strong>User Info:</strong> ${userInfo || 'None'}</p>
                <p><strong>Session Storage:</strong></p>
                <pre>${JSON.stringify(Object.keys(sessionStorage).reduce((obj, key) => {
                    obj[key] = sessionStorage.getItem(key);
                    return obj;
                }, {}), null, 2)}</pre>
            `;
            
            document.getElementById('browser-state').innerHTML = html;
        }

        async function testLeadAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>Testing Lead API...</p>';
            
            try {
                const authToken = localStorage.getItem('authToken');
                if (!authToken) {
                    resultsDiv.innerHTML = '<div class="error">❌ No auth token found in localStorage</div>';
                    return;
                }
                
                const response = await fetch(window.getApiUrl('/lead'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                let html = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h4>Lead API Response (Status: ${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
                if (response.ok && data.items) {
                    html += `
                        <div class="info">
                            <h4>📊 Summary</h4>
                            <p><strong>Total Leads:</strong> ${data.items.length}</p>
                            ${data.items.length > 0 ? `
                                <p><strong>Lead Names:</strong></p>
                                <ul>
                                    ${data.items.map(lead => `<li>${lead.objectData.name} (${lead.objectData.email})</li>`).join('')}
                                </ul>
                            ` : '<p>No leads found for current user/company</p>'}
                        </div>
                    `;
                }
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testWithDifferentTokens() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>Testing with different tokens...</p>';
            
            const tokens = [
                {
                    name: '<EMAIL> (super_admin_001)',
                    token: 'f3ec33818c5fbcef5960c16d3504da9e0d00b14a3239605616cca81e047fde98'
                },
                {
                    name: '<EMAIL> (super_admin_company)',
                    token: 'ca71f5889e060443405df192e1b6311de9cb43a26fcaf2277b8a87c20dc32b18'
                },
                {
                    name: 'Test Token (super_admin_001)',
                    token: 'test_super_admin_super_admin_001'
                }
            ];
            
            let html = '';
            
            for (const tokenInfo of tokens) {
                try {
                    const response = await fetch(window.getApiUrl('/lead'), {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${tokenInfo.token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const data = await response.json();
                    
                    html += `
                        <div class="${response.ok ? 'success' : 'error'}">
                            <h4>${tokenInfo.name} (Status: ${response.status})</h4>
                            <p><strong>Leads found:</strong> ${data.items ? data.items.length : 'Error'}</p>
                            ${data.items && data.items.length > 0 ? `
                                <p><strong>Lead names:</strong> ${data.items.map(l => l.objectData.name).join(', ')}</p>
                            ` : ''}
                            <details>
                                <summary>Full Response</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="error">
                            <h4>${tokenInfo.name}</h4>
                            <p>❌ Error: ${error.message}</p>
                        </div>
                    `;
                }
            }
            
            resultsDiv.innerHTML = html;
        }

        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            location.reload();
        }
    </script>
</body>
</html>
