function TemplatePreview({ type, companyInfo, onClose }) {
    try {
        const [loading, setLoading] = React.useState(true);
        const [previewData, setPreviewData] = React.useState(null);
        const previewRef = React.useRef(null);
        const [scale, setScale] = React.useState(0.6);
        const [notification, setNotification] = React.useState(null);
        const documentTypeName = type.charAt(0).toUpperCase() + type.slice(1);

        React.useEffect(() => {
            generatePreviewData();
        }, [type]);

        const generatePreviewData = async () => {
            try {
                setLoading(true);
                let sampleData;

                switch (type) {
                    case 'invoice':
                        sampleData = createSampleInvoice();
                        break;
                    case 'quotation':
                        sampleData = createSampleQuotation();
                        break;
                    case 'contract':
                        sampleData = createSampleContract();
                        break;
                    default:
                        sampleData = createSampleInvoice();
                }

                setPreviewData(sampleData);
            } catch (error) {
                console.error('Error generating preview data:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to generate preview'
                });
            } finally {
                setLoading(false);
            }
        };

        const createSampleInvoice = () => {
            return {
                objectId: 'sample-invoice',
                objectData: {
                    invoiceNumber: 'INV-SAMPLE',
                    customer: {
                        name: 'Sample Customer',
                        company: 'Sample Company Ltd.',
                        email: '<EMAIL>',
                        phone: '+91 9876543210',
                        address: '123 Business Park\nSample City, 400001',
                        gst: 'GSTIN12345678901'
                    },
                    items: [
                        { description: 'Website Development', quantity: 1, price: 50000 },
                        { description: 'Content Creation', quantity: 5, price: 2000 },
                        { description: 'SEO Setup', quantity: 1, price: 15000 }
                    ],
                    subtotal: 75000,
                    tax: 13500,
                    taxRate: 18,
                    total: 88500,
                    dueDate: new Date(Date.now() + 15*24*60*60*1000).toISOString().split('T')[0],
                    status: 'sent',
                    notes: 'Thank you for your business. This is a sample invoice for template preview purposes.',
                    terms: 'Payment due within 15 days of invoice date. Late payments are subject to a 2% monthly fee.'
                },
                createdAt: new Date().toISOString()
            };
        };

        const createSampleQuotation = () => {
            return {
                objectId: 'sample-quotation',
                objectData: {
                    quotationNumber: 'QT-SAMPLE',
                    customer: {
                        name: 'Sample Customer',
                        company: 'Sample Company Ltd.',
                        email: '<EMAIL>',
                        phone: '+91 9876543210',
                        address: '123 Business Park\nSample City, 400001',
                        gst: 'GSTIN12345678901'
                    },
                    items: [
                        { description: 'Digital Marketing Services', quantity: 1, price: 30000 },
                        { description: 'SEO Optimization', quantity: 1, price: 15000 },
                        { description: 'Social Media Management', quantity: 3, price: 5000 }
                    ],
                    subtotal: 60000,
                    tax: 10800,
                    taxRate: 18,
                    total: 70800,
                    validUntil: new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0],
                    status: 'draft',
                    notes: 'This is a sample quotation for template preview purposes.',
                    terms: 'This quote is valid for 30 days from the date of issue.'
                },
                createdAt: new Date().toISOString()
            };
        };

        const createSampleContract = () => {
            return {
                objectId: 'sample-contract',
                objectData: {
                    contractNumber: 'CT-SAMPLE',
                    customer: {
                        name: 'Sample Customer',
                        company: 'Sample Company Ltd.',
                        email: '<EMAIL>',
                        phone: '+91 9876543210',
                        address: '123 Business Park\nSample City, 400001',
                        gst: 'GSTIN12345678901'
                    },
                    title: 'Web Development Services Agreement',
                    description: 'Contract for the development of a corporate website',
                    startDate: new Date().toISOString().split('T')[0],
                    endDate: new Date(Date.now() + 90*24*60*60*1000).toISOString().split('T')[0],
                    value: 100000,
                    status: 'draft',
                    type: 'service',
                    terms: 'This is a sample contract for template preview purposes. It includes standard terms and conditions for web development services.',
                    scope: 'Design and development of a responsive corporate website with content management system.'
                },
                createdAt: new Date().toISOString()
            };
        };

        const handlePrint = () => {
            if (previewRef.current) {
                try {
                    const printOptions = {
                        title: `${documentTypeName} Preview`,
                        hideControls: false,
                        scaleFactor: 1.0
                    };
                    
                    // Apply paper size and orientation if available
                    if (companyInfo && companyInfo.templates) {
                        if (companyInfo.templates.paperSize) {
                            printOptions.paperSize = companyInfo.templates.paperSize;
                        }
                        if (companyInfo.templates.orientation) {
                            printOptions.orientation = companyInfo.templates.orientation;
                        }
                    }
                    
                    printDocumentAdvanced(previewRef.current, printOptions);
                } catch (error) {
                    console.error('Print error:', error);
                    setNotification({
                        type: 'error',
                        message: 'Failed to print preview'
                    });
                }
            }
        };

        const handleExportPDF = async () => {
            if (previewRef.current) {
                try {
                    setLoading(true);
                    const filename = `${documentTypeName}-Template-Preview.pdf`;
                    
                    // Apply template settings
                    const pdfOptions = {
                        margin: 0,
                        format: companyInfo && companyInfo.templates && companyInfo.templates.paperSize ? companyInfo.templates.paperSize : 'a4',
                        orientation: companyInfo && companyInfo.templates && companyInfo.templates.orientation ? companyInfo.templates.orientation : 'portrait',
                        compress: true
                    };
                    
                    // Apply margins if available
                    if (companyInfo && companyInfo.templates && companyInfo.templates.margins) {
                        const { top, right, bottom, left } = companyInfo.templates.margins;
                        pdfOptions.margin = [
                            Number(top) || 0,
                            Number(right) || 0,
                            Number(bottom) || 0,
                            Number(left) || 0
                        ];
                    }
                    
                    const success = await exportToPDF(previewRef.current, filename, pdfOptions);
                    
                    if (success) {
                        setNotification({
                            type: 'success',
                            message: 'PDF downloaded successfully'
                        });
                    } else {
                        throw new Error('PDF generation failed');
                    }
                } catch (error) {
                    console.error('PDF error:', error);
                    setNotification({
                        type: 'error',
                        message: 'Failed to generate PDF'
                    });
                } finally {
                    setLoading(false);
                }
            }
        };

        const renderTemplate = () => {
            switch (type) {
                case 'invoice':
                    return <InvoiceTemplate invoice={previewData} companyInfo={companyInfo} />;
                case 'quotation':
                    return <QuotationTemplate quotation={previewData} companyInfo={companyInfo} />;
                case 'contract':
                    return <ContractTemplate contract={previewData} companyInfo={companyInfo} />;
                default:
                    return <div className="p-4 bg-red-50 text-red-600">Unknown template type</div>;
            }
        };

        return (
            <div data-name="template-preview" className="p-4">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-lg font-medium">{documentTypeName} Template Preview</h2>
                    <div className="flex space-x-2">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Zoom:</span>
                            <input
                                type="range"
                                min="0.4"
                                max="1"
                                step="0.1"
                                value={scale}
                                onChange={(e) => setScale(parseFloat(e.target.value))}
                                className="w-24"
                            />
                            <span className="text-sm text-gray-600">{Math.round(scale * 100)}%</span>
                        </div>
                        <Button
                            variant="secondary"
                            icon="fas fa-print"
                            onClick={handlePrint}
                        >
                            Print
                        </Button>
                        <Button
                            variant="secondary"
                            icon="fas fa-file-pdf"
                            onClick={handleExportPDF}
                        >
                            Export PDF
                        </Button>
                        <Button
                            variant="secondary"
                            icon="fas fa-times"
                            onClick={onClose}
                        >
                            Close
                        </Button>
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {loading ? (
                    <div className="flex justify-center items-center h-64">
                        <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                    </div>
                ) : (
                    <div className="bg-gray-200 p-4 rounded-lg overflow-auto" style={{ maxHeight: '80vh' }}>
                        <div 
                            style={{ 
                                transform: `scale(${scale})`, 
                                transformOrigin: 'top center',
                                width: `${100/scale}%`
                            }}
                            className="flex justify-center"
                        >
                            <div ref={previewRef} className="shadow-xl">
                                {renderTemplate()}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('TemplatePreview component error:', error);
        reportError(error);
        return null;
    }
}
