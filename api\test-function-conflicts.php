<?php
/**
 * Test for function conflicts resolution
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Test including both handlers without conflicts
    require_once __DIR__ . '/db-config.php';
    require_once __DIR__ . '/handlers/super-admin-handler.php';
    require_once __DIR__ . '/handlers/settings-handler.php';
    
    // Check if functions exist and are unique
    $functions = [
        'getSystemSettings' => function_exists('getSystemSettings'),
        'getCompanySettings' => function_exists('getCompanySettings'),
        'updateSystemSettings' => function_exists('updateSystemSettings'),
        'updateCompanySettings' => function_exists('updateCompanySettings'),
        'handleSettings' => function_exists('handleSettings'),
        'handleSuperAdmin' => function_exists('handleSuperAdmin')
    ];
    
    echo json_encode([
        'success' => true,
        'message' => 'No function conflicts detected',
        'functions' => $functions,
        'all_functions_exist' => !in_array(false, $functions),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Function conflict detected: ' . $e->getMessage(),
        'type' => 'Fatal Error'
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'type' => 'Exception'
    ]);
}
?>
