<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'db-config.php';

// Simple token verification
function verifyToken($token) {
    if (!$token) return null;
    
    try {
        $payload = json_decode(base64_decode($token), true);
        if (!$payload || !isset($payload['exp']) || $payload['exp'] < time()) {
            return null;
        }
        return $payload;
    } catch (Exception $e) {
        return null;
    }
}

// Get token from Authorization header
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? '';
$token = str_replace('Bearer ', '', $authHeader);

$user = verifyToken($token);
if (!$user) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get user profile
        $stmt = $pdo->prepare("
            SELECT object_id, name, email, role, company_name, business_type, 
                   phone, address, created_at, last_login 
            FROM users 
            WHERE object_id = ?
        ");
        $stmt->execute([$user['user_id']]);
        $userProfile = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$userProfile) {
            throw new Exception('User not found');
        }
        
        // Get subscription info
        $stmt = $pdo->prepare("
            SELECT s.*, p.name as plan_name, p.price_monthly, p.price_yearly 
            FROM subscriptions s 
            LEFT JOIN pricing_plans p ON s.plan_id = p.object_id 
            WHERE s.user_id = ? 
            ORDER BY s.created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$user['user_id']]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'user' => $userProfile,
                'subscription' => $subscription
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Update user profile
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $name = trim($input['name'] ?? '');
        $phone = trim($input['phone'] ?? '');
        $address = trim($input['address'] ?? '');
        $company_name = trim($input['company_name'] ?? '');
        
        if (empty($name)) {
            throw new Exception('Name is required');
        }
        
        // Update user profile
        $stmt = $pdo->prepare("
            UPDATE users 
            SET name = ?, phone = ?, address = ?, company_name = ?, updated_at = NOW() 
            WHERE object_id = ?
        ");
        $stmt->execute([$name, $phone, $address, $company_name, $user['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    error_log("Database error in user-profile: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
?>
