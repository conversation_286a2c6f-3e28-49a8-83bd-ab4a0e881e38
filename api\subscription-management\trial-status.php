<?php
require_once '../db-config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        handleGetTrialStatus($currentUser);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Trial Status API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetTrialStatus($conn, $user) {
    try {
        // Get user's company
        $companyStmt = $conn->prepare("SELECT object_id FROM companies WHERE owner_id = ? OR id = ?");
        $companyStmt->bind_param("ss", $user['object_id'], $user['company_id']);
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if ($companyRow = $companyResult->fetch_assoc()) {
            $companyId = $companyRow['object_id'];
            
            // Check for active subscription
            $subscriptionStmt = $conn->prepare("
                SELECT s.*, p.name as plan_name, p.trial_days, p.price_monthly, p.price_yearly
                FROM subscriptions s
                LEFT JOIN plans p ON s.plan_id = p.id
                WHERE s.company_id = ? 
                ORDER BY s.created_at DESC 
                LIMIT 1
            ");
            $subscriptionStmt->bind_param("s", $companyId);
            $subscriptionStmt->execute();
            $subscriptionResult = $subscriptionStmt->get_result();
            
            if ($subscriptionRow = $subscriptionResult->fetch_assoc()) {
                $trialEndDate = $subscriptionRow['trial_end_date'];
                $currentDate = new DateTime();
                $endDate = new DateTime($trialEndDate);
                
                $status = 'active';
                $daysLeft = 0;
                $isTrialActive = false;
                
                if ($trialEndDate && $currentDate < $endDate) {
                    $status = 'trial';
                    $isTrialActive = true;
                    $interval = $currentDate->diff($endDate);
                    $daysLeft = $interval->days;
                } elseif ($trialEndDate && $currentDate >= $endDate) {
                    $status = 'expired';
                } elseif ($subscriptionRow['status'] === 'active') {
                    $status = 'active';
                }
                
                $trialData = [
                    'status' => $status,
                    'is_trial_active' => $isTrialActive,
                    'trial_end_date' => $trialEndDate,
                    'days_left' => $daysLeft,
                    'plan_name' => $subscriptionRow['plan_name'],
                    'trial_days' => $subscriptionRow['trial_days'],
                    'subscription_status' => $subscriptionRow['status'],
                    'created_at' => $subscriptionRow['created_at']
                ];
                
                echo json_encode(['success' => true, 'data' => $trialData]);
            } else {
                // No subscription found - user might be eligible for trial
                echo json_encode([
                    'success' => true, 
                    'data' => [
                        'status' => 'no_subscription',
                        'is_trial_active' => false,
                        'trial_end_date' => null,
                        'days_left' => 0,
                        'eligible_for_trial' => true
                    ]
                ]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Company not found']);
        }
    } catch (Exception $e) {
        error_log("Error getting trial status: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to get trial status']);
    }
}
?>
