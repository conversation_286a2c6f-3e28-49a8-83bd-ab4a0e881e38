function CompanySwitcher() {
    try {
        const { companies, currentCompany, switchCompany, createCompany } = useAuth();
        const [showCreateModal, setShowCreateModal] = React.useState(false);
        const [newCompanyData, setNewCompanyData] = React.useState({
            name: '',
            email: '',
            phone: '',
            address: ''
        });
        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);

        const handleCreateCompany = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                await createCompany(newCompanyData);
                setShowCreateModal(false);
                setNewCompanyData({
                    name: '',
                    email: '',
                    phone: '',
                    address: ''
                });
            } catch (error) {
                setErrors({ submit: error.message });
            } finally {
                setLoading(false);
            }
        };

        const validateForm = () => {
            const newErrors = {};
            if (!newCompanyData.name) {
                newErrors.name = 'Company name is required';
            }
            if (!newCompanyData.email) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(newCompanyData.email)) {
                newErrors.email = 'Invalid email format';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setNewCompanyData(prev => ({
                ...prev,
                [name]: value
            }));
            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }
        };

        return (
            <div data-name="company-switcher" className="relative">
                <div className="flex items-center space-x-4">
                    <select
                        value={currentCompany && currentCompany.objectId ? currentCompany.objectId : ''}
                        onChange={(e) => switchCompany(e.target.value)}
                        className="block w-48 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                        {companies.map(company => (
                            <option key={company.objectId} value={company.objectId}>
                                {company.objectData.name}
                            </option>
                        ))}
                    </select>
                    <Button
                        variant="secondary"
                        icon="fas fa-plus"
                        onClick={() => setShowCreateModal(true)}
                    >
                        New Company
                    </Button>
                </div>

                {showCreateModal && (
                    <Modal
                        isOpen={showCreateModal}
                        onClose={() => setShowCreateModal(false)}
                        title="Create New Company"
                    >
                        <form onSubmit={handleCreateCompany} className="space-y-6 p-6">
                            <Input
                                label="Company Name"
                                name="name"
                                value={newCompanyData.name}
                                onChange={handleInputChange}
                                error={errors.name}
                                required
                            />
                            <Input
                                label="Company Email"
                                name="email"
                                type="email"
                                value={newCompanyData.email}
                                onChange={handleInputChange}
                                error={errors.email}
                                required
                            />
                            <Input
                                label="Phone"
                                name="phone"
                                value={newCompanyData.phone}
                                onChange={handleInputChange}
                                error={errors.phone}
                            />
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Address
                                </label>
                                <textarea
                                    name="address"
                                    value={newCompanyData.address}
                                    onChange={handleInputChange}
                                    rows={3}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>

                            {errors.submit && (
                                <div className="text-red-600 text-sm">
                                    {errors.submit}
                                </div>
                            )}

                            <div className="flex justify-end space-x-4">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={() => setShowCreateModal(false)}
                                    disabled={loading}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    loading={loading}
                                    disabled={loading}
                                >
                                    Create Company
                                </Button>
                            </div>
                        </form>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('CompanySwitcher component error:', error);
        reportError(error);
        return null;
    }
}
