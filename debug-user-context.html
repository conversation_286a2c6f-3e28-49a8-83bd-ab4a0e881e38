<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug User Context - Bizma</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="config.js"></script>
    <script src="components/auth/AuthContext.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        function DebugApp() {
            const auth = React.useContext(window.AuthContext);
            const [testResult, setTestResult] = React.useState(null);

            const testSuperAdminNavigation = () => {
                console.log('Testing super admin navigation');
                console.log('Auth context:', auth);
                console.log('User:', auth?.user);
                console.log('User role:', auth?.user?.role);
                console.log('Is super admin:', auth?.user?.role === 'super_admin');
                
                setTestResult({
                    authContext: auth,
                    user: auth?.user,
                    userRole: auth?.user?.role,
                    isSuperAdmin: auth?.user?.role === 'super_admin',
                    isAuthenticated: auth?.isAuthenticated
                });
                
                // Test the navigation event
                window.dispatchEvent(new CustomEvent('app-navigate', { 
                    detail: { 
                        page: 'super-admin',
                        id: null,
                        action: null,
                        params: {}
                    } 
                }));
            };

            if (auth?.loading) {
                return React.createElement('div', { className: 'container' }, [
                    React.createElement('h1', { key: 'title' }, '🔄 Loading...'),
                    React.createElement('p', { key: 'loading' }, 'Checking authentication status...')
                ]);
            }

            return React.createElement('div', { className: 'container' }, [
                React.createElement('h1', { key: 'title' }, '👤 User Context Debug'),
                
                React.createElement('div', { key: 'auth-status', className: 'status info' }, [
                    React.createElement('h3', { key: 'h3' }, 'Authentication Status'),
                    React.createElement('p', { key: 'loading' }, `Loading: ${auth?.loading}`),
                    React.createElement('p', { key: 'authenticated' }, `Authenticated: ${auth?.isAuthenticated}`),
                    React.createElement('p', { key: 'has-user' }, `Has User: ${!!auth?.user}`),
                    auth?.error && React.createElement('p', { key: 'error', style: { color: 'red' } }, `Error: ${auth.error}`)
                ]),

                auth?.user && React.createElement('div', { key: 'user-data', className: 'status success' }, [
                    React.createElement('h3', { key: 'h3' }, 'User Data'),
                    React.createElement('pre', { key: 'pre' }, JSON.stringify(auth.user, null, 2))
                ]),

                React.createElement('div', { key: 'role-check', className: 'status info' }, [
                    React.createElement('h3', { key: 'h3' }, 'Role Check'),
                    React.createElement('p', { key: 'role' }, `Role: ${auth?.user?.role || 'No role'}`),
                    React.createElement('p', { key: 'super' }, `Is Super Admin: ${auth?.user?.role === 'super_admin'}`),
                    React.createElement('p', { key: 'admin' }, `Is Admin: ${auth?.isAdmin}`),
                    React.createElement('p', { key: 'super-flag' }, `Super Admin Flag: ${auth?.isSuperAdmin}`)
                ]),

                React.createElement('div', { key: 'actions', className: 'status info' }, [
                    React.createElement('h3', { key: 'h3' }, 'Actions'),
                    React.createElement('button', { 
                        key: 'test-nav',
                        onClick: testSuperAdminNavigation 
                    }, 'Test Super Admin Navigation'),
                    React.createElement('button', { 
                        key: 'refresh',
                        onClick: () => window.location.reload() 
                    }, 'Refresh Page')
                ]),

                testResult && React.createElement('div', { key: 'test-result', className: 'status info' }, [
                    React.createElement('h3', { key: 'h3' }, 'Test Result'),
                    React.createElement('pre', { key: 'pre' }, JSON.stringify(testResult, null, 2))
                ])
            ]);
        }

        function App() {
            return React.createElement(window.AuthProvider, {}, 
                React.createElement(DebugApp)
            );
        }

        ReactDOM.render(React.createElement(App), document.getElementById('root'));
    </script>
</body>
</html>
