<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Authentication - Complete Solution</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #e6ffe6; }
        .error { background: #ffe6e6; }
        .info { background: #f0f8ff; }
        .warning { background: #fff3cd; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
        .user-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .step { border-left: 4px solid #007cba; padding-left: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🔧 Fix Authentication - Complete Solution</h1>
    
    <div class="warning">
        <h3>⚠️ Current Issue</h3>
        <p>You're getting <strong>401 Unauthorized</strong> errors because:</p>
        <ul>
            <li>No valid authentication token in browser</li>
            <li>Token might be expired or invalid</li>
            <li>User needs to log in properly</li>
        </ul>
    </div>
    
    <div class="step">
        <h3>Step 1: Quick Fix - Set Valid Token</h3>
        <p>Choose a user to login as (this will set a valid token immediately):</p>
        
        <div class="user-card">
            <h4>👤 <EMAIL></h4>
            <p><strong>Role:</strong> Admin | <strong>Company:</strong> comp_1752134375_334</p>
            <p><strong>Access:</strong> 2 leads, customers, quotations</p>
            <button onclick="quickLogin('<EMAIL>')" style="background: #28a745;">Quick Login</button>
        </div>
        
        <div class="user-card">
            <h4>👤 <EMAIL></h4>
            <p><strong>Role:</strong> Super Admin | <strong>Company:</strong> super_admin_company</p>
            <p><strong>Access:</strong> 1 lead, all admin features</p>
            <button onclick="quickLogin('<EMAIL>')" style="background: #dc3545;">Quick Login</button>
        </div>
        
        <div class="user-card">
            <h4>👤 <EMAIL></h4>
            <p><strong>Role:</strong> Super Admin | <strong>Company:</strong> super_admin_001</p>
            <p><strong>Access:</strong> Full system access</p>
            <button onclick="quickLogin('<EMAIL>')" style="background: #6f42c1;">Quick Login</button>
        </div>
    </div>
    
    <div class="step">
        <h3>Step 2: Proper Login (Alternative)</h3>
        <p>Or login properly using the login form:</p>
        
        <form id="login-form">
            <div class="form-group">
                <label for="email">Email:</label>
                <select id="email">
                    <option value="<EMAIL>"><EMAIL></option>
                    <option value="<EMAIL>"><EMAIL></option>
                    <option value="<EMAIL>"><EMAIL></option>
                </select>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="password123">
            </div>
            <button type="button" onclick="properLogin()">Login via API</button>
        </form>
    </div>
    
    <div class="step">
        <h3>Step 3: Test & Verify</h3>
        <button onclick="testAuthentication()">Test Current Auth</button>
        <button onclick="testAllEndpoints()">Test All API Endpoints</button>
        <button onclick="goToDashboard()">Go to Dashboard</button>
    </div>
    
    <div id="results"></div>

    <script>
        // Include the config
        const script = document.createElement('script');
        script.src = 'config.js';
        document.head.appendChild(script);
        
        script.onload = function() {
            window.getApiUrl = function(endpoint) {
                return window.APP_CONFIG.API_BASE_URL + endpoint;
            };
            
            checkCurrentAuth();
        };

        const users = {
            '<EMAIL>': {
                token: '56fc5ba5bb153bb5b5233837d48dabedbe7f0b71a87c0fb43b4d8f4ef466c116',
                company: 'comp_1752134375_334',
                name: 'Venkatesan Masilamani',
                role: 'admin'
            },
            '<EMAIL>': {
                token: 'ca71f5889e060443405df192e1b6311de9cb43a26fcaf2277b8a87c20dc32b18',
                company: 'super_admin_company',
                name: 'Super Admin',
                role: 'super_admin'
            },
            '<EMAIL>': {
                token: 'f3ec33818c5fbcef5960c16d3504da9e0d00b14a3239605616cca81e047fde98',
                company: 'super_admin_001',
                name: 'Super Administrator',
                role: 'super_admin'
            }
        };

        function checkCurrentAuth() {
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                document.getElementById('results').innerHTML = `
                    <div class="error">
                        <h4>❌ No Authentication Token Found</h4>
                        <p>This is why you're getting 401 errors. Please use Step 1 or Step 2 above to login.</p>
                    </div>
                `;
            } else {
                document.getElementById('results').innerHTML = `
                    <div class="info">
                        <h4>ℹ️ Token Found</h4>
                        <p>Token: ${authToken.substring(0, 30)}...</p>
                        <p>Click "Test Current Auth" to verify if it's working.</p>
                    </div>
                `;
            }
        }

        function quickLogin(email) {
            const user = users[email];
            if (!user) {
                document.getElementById('results').innerHTML = '<div class="error">❌ User not found</div>';
                return;
            }
            
            // Set authentication
            localStorage.setItem('authToken', user.token);
            localStorage.setItem('lastCompanyId', user.company);
            
            // Set user info
            const userInfo = {
                email: email,
                name: user.name,
                role: user.role,
                company_id: user.company,
                object_id: email.split('@')[0] + '_' + Date.now()
            };
            localStorage.setItem('userInfo', JSON.stringify(userInfo));
            
            document.getElementById('results').innerHTML = `
                <div class="success">
                    <h4>✅ Quick Login Successful!</h4>
                    <p><strong>User:</strong> ${email}</p>
                    <p><strong>Role:</strong> ${user.role}</p>
                    <p><strong>Company:</strong> ${user.company}</p>
                    <p><strong>Token Set:</strong> ${user.token.substring(0, 30)}...</p>
                    <p>🎉 <strong>The 401 errors should now be fixed!</strong></p>
                    <button onclick="testAuthentication()" style="background: #17a2b8;">Test Auth Now</button>
                    <button onclick="goToDashboard()" style="background: #28a745;">Go to Dashboard</button>
                </div>
            `;
        }

        async function properLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="info">🔐 Logging in via API...</div>';
            
            try {
                const response = await fetch(window.getApiUrl('/auth'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password,
                        remember_me: true
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.token) {
                        localStorage.setItem('authToken', data.token);
                        localStorage.setItem('lastCompanyId', data.user.company_id);
                        localStorage.setItem('userInfo', JSON.stringify(data.user));
                        
                        resultsDiv.innerHTML = `
                            <div class="success">
                                <h4>✅ API Login Successful!</h4>
                                <p><strong>User:</strong> ${data.user.name} (${data.user.email})</p>
                                <p><strong>Role:</strong> ${data.user.role}</p>
                                <p><strong>Company:</strong> ${data.user.company_id}</p>
                                <p><strong>Token:</strong> ${data.token.substring(0, 30)}...</p>
                                <p>🎉 <strong>Authentication is now properly set up!</strong></p>
                                <button onclick="goToDashboard()" style="background: #28a745;">Go to Dashboard</button>
                            </div>
                        `;
                    } else {
                        throw new Error('No token received from server');
                    }
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || `Login failed: ${response.status}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ API Login Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Try using the Quick Login buttons above instead.</p>
                    </div>
                `;
            }
        }

        async function testAuthentication() {
            const resultsDiv = document.getElementById('results');
            const authToken = localStorage.getItem('authToken');
            
            if (!authToken) {
                resultsDiv.innerHTML = '<div class="error">❌ No auth token. Please login first.</div>';
                return;
            }
            
            resultsDiv.innerHTML = '<div class="info">🧪 Testing authentication...</div>';
            
            try {
                // Test verify-token endpoint
                const verifyResponse = await fetch('/biz/api/verify-token.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const verifyData = await verifyResponse.json();
                
                // Test leads endpoint
                const leadsResponse = await fetch(window.getApiUrl('/lead'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const leadsData = await leadsResponse.json();
                
                resultsDiv.innerHTML = `
                    <div class="${verifyResponse.ok && leadsResponse.ok ? 'success' : 'error'}">
                        <h4>🧪 Authentication Test Results</h4>
                        
                        <h5>Verify Token Endpoint:</h5>
                        <p><strong>Status:</strong> ${verifyResponse.status} ${verifyResponse.ok ? '✅' : '❌'}</p>
                        ${verifyResponse.ok ? `
                            <p><strong>User:</strong> ${verifyData.user.name} (${verifyData.user.email})</p>
                            <p><strong>Company:</strong> ${verifyData.user.company_id}</p>
                        ` : `
                            <p><strong>Error:</strong> ${verifyData.message}</p>
                        `}
                        
                        <h5>Leads API Endpoint:</h5>
                        <p><strong>Status:</strong> ${leadsResponse.status} ${leadsResponse.ok ? '✅' : '❌'}</p>
                        ${leadsResponse.ok ? `
                            <p><strong>Leads Found:</strong> ${leadsData.items ? leadsData.items.length : 0}</p>
                        ` : `
                            <p><strong>Error:</strong> ${leadsData.error || 'Unknown error'}</p>
                        `}
                        
                        ${verifyResponse.ok && leadsResponse.ok ? `
                            <div class="success" style="margin-top: 15px;">
                                <h5>🎉 Authentication is Working!</h5>
                                <p>All API endpoints should now work properly. The 401 errors are fixed!</p>
                                <button onclick="goToDashboard()" style="background: #28a745;">Go to Dashboard Now</button>
                            </div>
                        ` : `
                            <div class="error" style="margin-top: 15px;">
                                <h5>❌ Authentication Issues Remain</h5>
                                <p>Try using the Quick Login buttons above.</p>
                            </div>
                        `}
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Test Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAllEndpoints() {
            const resultsDiv = document.getElementById('results');
            const authToken = localStorage.getItem('authToken');
            
            if (!authToken) {
                resultsDiv.innerHTML = '<div class="error">❌ No auth token. Please login first.</div>';
                return;
            }
            
            resultsDiv.innerHTML = '<div class="info">🧪 Testing all API endpoints...</div>';
            
            const endpoints = [
                { name: 'Leads', url: '/lead' },
                { name: 'Customers', url: '/customer' },
                { name: 'Subscription', url: '/subscription-management/current' },
                { name: 'Trial Status', url: '/subscription-management/trial-status' }
            ];
            
            let html = '<h4>🧪 API Endpoints Test Results</h4>';
            let allWorking = true;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(window.getApiUrl(endpoint.url), {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const isWorking = response.ok;
                    if (!isWorking) allWorking = false;
                    
                    html += `
                        <p><strong>${endpoint.name}:</strong> 
                        ${response.status} ${isWorking ? '✅' : '❌'}</p>
                    `;
                    
                } catch (error) {
                    allWorking = false;
                    html += `<p><strong>${endpoint.name}:</strong> Error ❌</p>`;
                }
            }
            
            html = `
                <div class="${allWorking ? 'success' : 'error'}">
                    ${html}
                    ${allWorking ? `
                        <div class="success" style="margin-top: 15px;">
                            <h5>🎉 All Endpoints Working!</h5>
                            <p>The 401 Unauthorized errors have been completely fixed!</p>
                            <button onclick="goToDashboard()" style="background: #28a745;">Go to Dashboard</button>
                        </div>
                    ` : `
                        <div class="error" style="margin-top: 15px;">
                            <h5>❌ Some Endpoints Still Failing</h5>
                            <p>Try refreshing the page or using a different user token.</p>
                        </div>
                    `}
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }

        function goToDashboard() {
            window.location.href = '/biz/';
        }
    </script>
</body>
</html>
