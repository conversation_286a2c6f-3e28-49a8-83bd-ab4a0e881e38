<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Flow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <div id="root"></div>

    <script>
        // Mock utility functions
        window.isEmailValid = function(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        };
    </script>

    <script type="text/babel" src="/biz/components/auth/LoginForm.js"></script>

    <script type="text/babel">
        function TestLoginFlow() {
            const [logs, setLogs] = React.useState([]);
            const [loginResult, setLoginResult] = React.useState(null);

            const addLog = (message, type = 'log') => {
                const timestamp = new Date().toLocaleTimeString();
                setLogs(prev => [...prev, { message: `${timestamp}: ${message}`, type }]);
                console.log(message);
            };

            const handleLogin = (user, token) => {
                addLog(`✅ Login successful! User: ${user.name}, Token: ${token.substring(0, 30)}...`, 'success');
                setLoginResult({ user, token });
                
                // Test the token immediately
                testTokenVerification(token);
            };

            const testTokenVerification = async (token) => {
                addLog('🔍 Testing token verification...', 'log');
                
                try {
                    const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({ token: token })
                    });

                    const data = await response.json();
                    
                    if (data.success && data.user) {
                        addLog(`✅ Token verification successful! User: ${data.user.name}`, 'success');
                    } else {
                        addLog(`❌ Token verification failed: ${data.error}`, 'error');
                    }
                } catch (error) {
                    addLog(`❌ Token verification error: ${error.message}`, 'error');
                }
            };

            const clearLogs = () => {
                setLogs([]);
                setLoginResult(null);
            };

            const clearToken = () => {
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('rememberMe');
                addLog('🗑️ Cleared all tokens from localStorage', 'log');
            };

            return (
                <div className="container mx-auto p-6">
                    <h1 className="text-3xl font-bold mb-6">🧪 Test Login Flow</h1>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h2 className="text-xl font-semibold mb-4">Login Form</h2>
                            <div className="border rounded-lg p-4">
                                <LoginForm onLogin={handleLogin} />
                            </div>
                        </div>
                        
                        <div>
                            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
                            <div className="space-y-2 mb-4">
                                <button 
                                    onClick={clearLogs}
                                    className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
                                >
                                    Clear Logs
                                </button>
                                <button 
                                    onClick={clearToken}
                                    className="bg-red-500 text-white px-4 py-2 rounded"
                                >
                                    Clear Tokens
                                </button>
                            </div>
                            
                            {loginResult && (
                                <div className="bg-green-100 border border-green-400 rounded p-4 mb-4">
                                    <h3 className="font-semibold">Login Result:</h3>
                                    <p><strong>User:</strong> {loginResult.user.name}</p>
                                    <p><strong>Email:</strong> {loginResult.user.email}</p>
                                    <p><strong>Role:</strong> {loginResult.user.role}</p>
                                    <p><strong>Token:</strong> {loginResult.token.substring(0, 50)}...</p>
                                </div>
                            )}
                            
                            <div className="max-h-96 overflow-y-auto">
                                {logs.map((log, index) => (
                                    <div key={index} className={`log ${log.type}`}>
                                        {log.message}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestLoginFlow />, document.getElementById('root'));
    </script>
</body>
</html>