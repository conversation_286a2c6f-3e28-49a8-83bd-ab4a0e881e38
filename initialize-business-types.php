<?php
/**
 * Initialize Business Types Table with Default Data
 */

require_once 'api/config/database.php';

header('Content-Type: text/plain');

echo "=== Initializing Business Types ===\n\n";

try {
    $conn = Database::getConnection();
    
    // Create business_types table if it doesn't exist
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS business_types (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        icon VARCHAR(50) DEFAULT 'fas fa-building',
        color VARCHAR(20) DEFAULT 'blue',
        default_modules TEXT,
        default_categories TEXT,
        default_features TEXT,
        default_templates TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($createTableSQL)) {
        echo "✅ Business types table created/verified\n";
    } else {
        throw new Exception("Failed to create business_types table: " . $conn->error);
    }
    
    // Default business types data
    $businessTypes = [
        [
            'id' => 'jewellery',
            'name' => 'Jewellery Business',
            'description' => 'Specialized for jewellery stores, designers, and manufacturers',
            'icon' => 'fas fa-gem',
            'color' => 'purple',
            'default_modules' => 'inventory,sales,customers,repairs,certifications',
            'default_categories' => 'gold,silver,platinum,diamonds,gemstones,watches',
            'default_features' => 'precious_metals_tracking,stone_certification,custom_design_tools,repair_tracking',
            'default_templates' => 'jewellery_invoice,repair_receipt,certification_template'
        ],
        [
            'id' => 'retail',
            'name' => 'Retail Business',
            'description' => 'Perfect for retail stores, shops, and e-commerce businesses',
            'icon' => 'fas fa-store',
            'color' => 'blue',
            'default_modules' => 'inventory,sales,customers,pos,loyalty',
            'default_categories' => 'electronics,clothing,accessories,home_goods,books',
            'default_features' => 'barcode_scanning,multi_location_support,loyalty_programs,discount_management',
            'default_templates' => 'retail_invoice,receipt,loyalty_card'
        ],
        [
            'id' => 'education',
            'name' => 'Education Services',
            'description' => 'Designed for schools, training centers, and educational institutions',
            'icon' => 'fas fa-graduation-cap',
            'color' => 'green',
            'default_modules' => 'students,courses,attendance,grades,fees',
            'default_categories' => 'academic_courses,vocational_training,certifications,workshops',
            'default_features' => 'student_portal,grade_management,attendance_tracking,course_materials',
            'default_templates' => 'student_report,certificate,fee_receipt'
        ],
        [
            'id' => 'healthcare',
            'name' => 'Healthcare Services',
            'description' => 'Tailored for clinics, hospitals, and healthcare providers',
            'icon' => 'fas fa-heartbeat',
            'color' => 'red',
            'default_modules' => 'patients,appointments,prescriptions,billing,insurance',
            'default_categories' => 'consultations,procedures,medications,lab_tests,imaging',
            'default_features' => 'patient_records,appointment_scheduling,prescription_management,insurance_billing',
            'default_templates' => 'prescription,medical_report,appointment_card'
        ],
        [
            'id' => 'consulting',
            'name' => 'Consulting Services',
            'description' => 'Ideal for consultants, agencies, and professional services',
            'icon' => 'fas fa-briefcase',
            'color' => 'indigo',
            'default_modules' => 'projects,clients,time_tracking,billing,proposals',
            'default_categories' => 'strategy,marketing,finance,hr,technology',
            'default_features' => 'project_management,time_billing,proposal_generation,client_portal',
            'default_templates' => 'proposal,invoice,project_report'
        ],
        [
            'id' => 'manufacturing',
            'name' => 'Manufacturing',
            'description' => 'Built for manufacturers and production companies',
            'icon' => 'fas fa-industry',
            'color' => 'orange',
            'default_modules' => 'production,inventory,quality,suppliers,orders',
            'default_categories' => 'raw_materials,finished_goods,machinery,tools,packaging',
            'default_features' => 'production_planning,quality_control,supplier_management,order_tracking',
            'default_templates' => 'production_order,quality_report,supplier_invoice'
        ],
        [
            'id' => 'restaurant',
            'name' => 'Restaurant & Food Service',
            'description' => 'Perfect for restaurants, cafes, and food service businesses',
            'icon' => 'fas fa-utensils',
            'color' => 'yellow',
            'default_modules' => 'menu,orders,kitchen,tables,delivery',
            'default_categories' => 'appetizers,main_course,desserts,beverages,specials',
            'default_features' => 'table_management,kitchen_display,delivery_tracking,menu_management',
            'default_templates' => 'menu,order_receipt,delivery_note'
        ],
        [
            'id' => 'automotive',
            'name' => 'Automotive Services',
            'description' => 'Designed for auto repair shops, dealerships, and service centers',
            'icon' => 'fas fa-car',
            'color' => 'gray',
            'default_modules' => 'vehicles,services,parts,customers,appointments',
            'default_categories' => 'engine_repair,body_work,maintenance,parts,accessories',
            'default_features' => 'vehicle_history,service_scheduling,parts_inventory,warranty_tracking',
            'default_templates' => 'service_estimate,repair_invoice,warranty_certificate'
        ]
    ];
    
    // Insert business types
    $insertSQL = "
        INSERT INTO business_types (id, name, description, icon, color, default_modules, 
                                   default_categories, default_features, default_templates, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        description = VALUES(description),
        icon = VALUES(icon),
        color = VALUES(color),
        default_modules = VALUES(default_modules),
        default_categories = VALUES(default_categories),
        default_features = VALUES(default_features),
        default_templates = VALUES(default_templates),
        updated_at = NOW()
    ";
    
    $stmt = $conn->prepare($insertSQL);
    
    foreach ($businessTypes as $type) {
        $stmt->bind_param("sssssssss", 
            $type['id'], 
            $type['name'], 
            $type['description'], 
            $type['icon'], 
            $type['color'],
            $type['default_modules'], 
            $type['default_categories'], 
            $type['default_features'], 
            $type['default_templates']
        );
        
        if ($stmt->execute()) {
            echo "✅ Business type '{$type['name']}' created/updated\n";
        } else {
            echo "❌ Failed to create business type '{$type['name']}': " . $stmt->error . "\n";
        }
    }
    
    // Verify the data
    echo "\n--- Verification ---\n";
    $result = $conn->query("SELECT COUNT(*) as count FROM business_types WHERE is_active = 1");
    $row = $result->fetch_assoc();
    echo "✅ Total active business types: " . $row['count'] . "\n";
    
    // Show all business types
    echo "\n--- Active Business Types ---\n";
    $result = $conn->query("SELECT id, name, description FROM business_types WHERE is_active = 1 ORDER BY name");
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['id']}: {$row['name']}\n";
        echo "  {$row['description']}\n\n";
    }
    
    echo "=== Business Types Initialization Complete ===\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    error_log("Business Types Initialization Error: " . $e->getMessage());
}
?>
