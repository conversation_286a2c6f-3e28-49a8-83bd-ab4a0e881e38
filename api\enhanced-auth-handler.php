<?php
/**
 * Enhanced Authentication Handler
 * Provides robust authentication with token refresh, session management, and security features
 */

require_once 'db-config.php';
require_once 'utils/ErrorHandler.php';

class EnhancedAuthHandler {
    private $conn;
    private $tokenExpiry = 3600; // 1 hour
    private $refreshTokenExpiry = 604800; // 7 days
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Enhanced login with token refresh capability
     */
    public function login($email, $password, $rememberMe = false) {
        try {
            // Validate input
            if (empty($email) || empty($password)) {
                return ['success' => false, 'error' => 'Email and password are required'];
            }
            
            // Check user credentials
            $stmt = $this->conn->prepare("
                SELECT u.*, c.name as company_name, c.industry, c.subscription_id 
                FROM users u 
                LEFT JOIN companies c ON u.company_id = c.object_id 
                WHERE u.email = ? AND u.status = 'active'
            ");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                return ['success' => false, 'error' => 'Invalid credentials'];
            }
            
            $user = $result->fetch_assoc();
            
            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                return ['success' => false, 'error' => 'Invalid credentials'];
            }
            
            // Generate tokens
            $accessToken = $this->generateAccessToken($user);
            $refreshToken = $this->generateRefreshToken($user);
            
            // Store refresh token in database
            $this->storeRefreshToken($user['object_id'], $refreshToken, $rememberMe);
            
            // Update last login
            $this->updateLastLogin($user['object_id']);
            
            // Get subscription details
            $subscription = $this->getUserSubscription($user['company_id']);
            
            // Prepare response
            $response = [
                'success' => true,
                'user' => [
                    'id' => $user['object_id'],
                    'email' => $user['email'],
                    'name' => $user['name'],
                    'role' => $user['role'],
                    'company_id' => $user['company_id'],
                    'company_name' => $user['company_name'],
                    'industry' => $user['industry'],
                    'last_login' => $user['last_login']
                ],
                'tokens' => [
                    'access_token' => $accessToken,
                    'refresh_token' => $refreshToken,
                    'expires_in' => $this->tokenExpiry
                ],
                'subscription' => $subscription
            ];
            
            return $response;
            
        } catch (Exception $e) {
            error_log("Enhanced Auth Login Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Authentication failed'];
        }
    }
    
    /**
     * Verify access token
     */
    public function verifyToken($token) {
        try {
            $decoded = $this->decodeToken($token);
            
            if (!$decoded) {
                return ['success' => false, 'error' => 'Invalid token'];
            }
            
            // Check if token is expired
            if ($decoded['exp'] < time()) {
                return ['success' => false, 'error' => 'Token expired'];
            }
            
            // Get fresh user data
            $stmt = $this->conn->prepare("
                SELECT u.*, c.name as company_name, c.industry 
                FROM users u 
                LEFT JOIN companies c ON u.company_id = c.object_id 
                WHERE u.object_id = ? AND u.status = 'active'
            ");
            $stmt->bind_param("s", $decoded['user_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                return ['success' => false, 'error' => 'User not found'];
            }
            
            $user = $result->fetch_assoc();
            $subscription = $this->getUserSubscription($user['company_id']);
            
            return [
                'success' => true,
                'user' => [
                    'id' => $user['object_id'],
                    'email' => $user['email'],
                    'name' => $user['name'],
                    'role' => $user['role'],
                    'company_id' => $user['company_id'],
                    'company_name' => $user['company_name'],
                    'industry' => $user['industry']
                ],
                'subscription' => $subscription
            ];
            
        } catch (Exception $e) {
            error_log("Token Verification Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Token verification failed'];
        }
    }
    
    /**
     * Refresh access token using refresh token
     */
    public function refreshToken($refreshToken) {
        try {
            // Verify refresh token
            $stmt = $this->conn->prepare("
                SELECT rt.*, u.* 
                FROM refresh_tokens rt 
                JOIN users u ON rt.user_id = u.object_id 
                WHERE rt.token = ? AND rt.expires_at > NOW() AND rt.is_active = 1
            ");
            $stmt->bind_param("s", $refreshToken);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                return ['success' => false, 'error' => 'Invalid refresh token'];
            }
            
            $tokenData = $result->fetch_assoc();
            
            // Generate new access token
            $newAccessToken = $this->generateAccessToken($tokenData);
            
            return [
                'success' => true,
                'access_token' => $newAccessToken,
                'expires_in' => $this->tokenExpiry
            ];
            
        } catch (Exception $e) {
            error_log("Token Refresh Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Token refresh failed'];
        }
    }
    
    /**
     * Logout and invalidate tokens
     */
    public function logout($refreshToken) {
        try {
            // Invalidate refresh token
            $stmt = $this->conn->prepare("UPDATE refresh_tokens SET is_active = 0 WHERE token = ?");
            $stmt->bind_param("s", $refreshToken);
            $stmt->execute();
            
            return ['success' => true, 'message' => 'Logged out successfully'];
            
        } catch (Exception $e) {
            error_log("Logout Error: " . $e->getMessage());
            return ['success' => false, 'error' => 'Logout failed'];
        }
    }
    
    /**
     * Generate access token (simple implementation)
     */
    private function generateAccessToken($user) {
        $payload = [
            'user_id' => $user['object_id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'company_id' => $user['company_id'],
            'iat' => time(),
            'exp' => time() + $this->tokenExpiry
        ];
        
        return base64_encode(json_encode($payload));
    }
    
    /**
     * Generate refresh token
     */
    private function generateRefreshToken($user) {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * Decode token (simple implementation)
     */
    private function decodeToken($token) {
        try {
            $decoded = json_decode(base64_decode($token), true);
            return $decoded;
        } catch (Exception $e) {
            return false;
        }
    }

    
    /**
     * Store refresh token in database
     */
    private function storeRefreshToken($userId, $refreshToken, $rememberMe) {
        $expiresAt = $rememberMe ? 
            date('Y-m-d H:i:s', time() + $this->refreshTokenExpiry) : 
            date('Y-m-d H:i:s', time() + 86400); // 1 day if not remember me
        
        $stmt = $this->conn->prepare("
            INSERT INTO refresh_tokens (user_id, token, expires_at, is_active, created_at) 
            VALUES (?, ?, ?, 1, NOW())
        ");
        $stmt->bind_param("sss", $userId, $refreshToken, $expiresAt);
        $stmt->execute();
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($userId) {
        $stmt = $this->conn->prepare("UPDATE users SET last_login = NOW() WHERE object_id = ?");
        $stmt->bind_param("s", $userId);
        $stmt->execute();
    }
    
    /**
     * Get user subscription details
     */
    private function getUserSubscription($companyId) {
        $stmt = $this->conn->prepare("
            SELECT s.*, p.name as plan_name, p.features, p.limits_data 
            FROM subscriptions s 
            JOIN pricing_plans p ON s.plan_id = p.id 
            WHERE s.company_id = ? AND s.status IN ('active', 'trial')
            ORDER BY s.created_at DESC LIMIT 1
        ");
        $stmt->bind_param("s", $companyId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $subscription = $result->fetch_assoc();
            $subscription['features'] = json_decode($subscription['features'], true);
            $subscription['limits'] = json_decode($subscription['limits_data'], true);
            return $subscription;
        }
        
        return null;
    }
}

// Handle authentication requests
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'GET') {
        $input = [];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $rawInput = file_get_contents('php://input');
            $input = json_decode($rawInput, true) ?: [];
        }
        
        $authHandler = new EnhancedAuthHandler($conn);
        $action = $_GET['action'] ?? 'login';
        
        switch ($action) {
            case 'login':
                if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                    $result = ['success' => false, 'error' => 'POST method required for login'];
                    break;
                }
                $result = $authHandler->login(
                    $input['email'] ?? '',
                    $input['password'] ?? '',
                    $input['rememberMe'] ?? false
                );
                break;
                
            case 'verify':
                // Try to get token from Authorization header first
                $token = $_SERVER['HTTP_AUTHORIZATION'] ?? $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] ?? '';
                $token = str_replace('Bearer ', '', $token);
                
                // If not found in header, try POST body
                if (empty($token)) {
                    $token = $input['token'] ?? '';
                }
                
                if (empty($token)) {
                    $result = ['success' => false, 'error' => 'No token provided'];
                } else {
                    $result = $authHandler->verifyToken($token);
                }
                break;
                
            case 'refresh':
                $result = $authHandler->refreshToken($input['refresh_token'] ?? '');
                break;
                
            case 'logout':
                $result = $authHandler->logout($input['refresh_token'] ?? '');
                break;
                
            default:
                $result = ['success' => false, 'error' => 'Invalid action: ' . $action];
        }
        
        echo json_encode($result);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    error_log("Enhanced Auth Handler Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Authentication service error']);
}
?>