function UpgradeModal({ plan, isOpen, onClose, onConfirm, loading }) {
    try {
        if (!isOpen || !plan) return null;

        return (
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                title="Confirm Subscription Upgrade"
            >
                <div className="p-6">
                    <div className="mb-6">
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            Upgrade to {plan.name}
                        </h3>
                        <p className="text-gray-600">
                            You're about to upgrade your subscription to the {plan.name} plan.
                        </p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg mb-6">
                        <div className="flex justify-between mb-2">
                            <span className="text-gray-600">Plan Price</span>
                            <span className="font-semibold">₹{plan.price}/{plan.billingPeriod}</span>
                        </div>
                        <div className="flex justify-between mb-4">
                            <span className="text-gray-600">Billing Period</span>
                            <span className="font-semibold">{plan.billingPeriod}</span>
                        </div>
                        <div className="border-t pt-4">
                            <div className="flex justify-between">
                                <span className="font-semibold">Total Amount</span>
                                <span className="font-semibold">₹{plan.price}</span>
                            </div>
                        </div>
                    </div>

                    <div className="mb-6">
                        <h4 className="font-medium mb-2">What's included:</h4>
                        <ul className="space-y-2">
                            {plan.features.map((feature, index) => (
                                <li key={index} className="flex items-center text-gray-600">
                                    <i className="fas fa-check text-green-500 mr-2"></i>
                                    {feature}
                                </li>
                            ))}
                        </ul>
                    </div>

                    <div className="text-sm text-gray-500 mb-6">
                        <p>By upgrading, you agree to our terms of service and billing policy.</p>
                        <p>Your new plan will be activated immediately upon confirmation.</p>
                    </div>

                    <div className="flex justify-end space-x-4">
                        <Button
                            variant="secondary"
                            onClick={onClose}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={onConfirm}
                            loading={loading}
                            disabled={loading}
                        >
                            Confirm Upgrade
                        </Button>
                    </div>
                </div>
            </Modal>
        );
    } catch (error) {
        console.error('UpgradeModal component error:', error);
        reportError(error);
        return null;
    }
}
