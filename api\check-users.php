<?php
/**
 * Check users in database and create test user if needed
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'error' => 'Users table does not exist'
        ]);
        exit;
    }
    
    // Check current users
    $result = $conn->query("SELECT object_id, name, email, role, status, auth_token, token_expires FROM users LIMIT 10");
    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
    
    // If no users exist, create a test user
    if (empty($users)) {
        $userId = 'user_' . time() . '_test';
        $passwordHash = password_hash('test123', PASSWORD_DEFAULT);
        $token = bin2hex(random_bytes(32));
        $tokenExpires = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        $sql = "INSERT INTO users (object_id, name, email, password_hash, auth_token, token_expires, status, role) VALUES (?, 'Test User', '<EMAIL>', ?, ?, ?, 'active', 'admin')";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $userId, $passwordHash, $token, $tokenExpires);
        
        if ($stmt->execute()) {
            $users[] = [
                'object_id' => $userId,
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'status' => 'active',
                'auth_token' => $token,
                'token_expires' => $tokenExpires
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'users_count' => count($users),
        'users' => $users
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
