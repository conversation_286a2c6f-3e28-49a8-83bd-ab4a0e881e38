// Plan Modal Component for Creating/Editing Plans
function PlanModal({ isOpen, onClose, plan, onSave, businessTypes = [] }) {
    const [formData, setFormData] = React.useState({
        id: '',
        name: '',
        description: '',
        short_description: '',
        price_monthly: 0,
        price_yearly: 0,
        trial_days: 14,
        features: [],
        limits_data: {},
        business_types: [],
        is_trial_available: true,
        is_visible: true,
        is_popular: false,
        sort_order: 0
    });
    const [newFeature, setNewFeature] = React.useState('');
    const [saving, setSaving] = React.useState(false);

    React.useEffect(() => {
        if (plan) {
            setFormData({
                ...plan,
                features: Array.isArray(plan.features) ? plan.features : [],
                limits_data: typeof plan.limits_data === 'object' ? plan.limits_data : {},
                business_types: Array.isArray(plan.business_types) ? plan.business_types : []
            });
        } else {
            setFormData({
                id: '',
                name: '',
                description: '',
                short_description: '',
                price_monthly: 0,
                price_yearly: 0,
                trial_days: 14,
                features: [],
                limits_data: {},
                business_types: [],
                is_trial_available: true,
                is_visible: true,
                is_popular: false,
                sort_order: 0
            });
        }
    }, [plan]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleLimitChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            limits_data: {
                ...prev.limits_data,
                [field]: value === '' ? null : (value === '-1' ? -1 : parseInt(value))
            }
        }));
    };

    const addFeature = () => {
        if (newFeature.trim()) {
            setFormData(prev => ({
                ...prev,
                features: [...prev.features, newFeature.trim()]
            }));
            setNewFeature('');
        }
    };

    const removeFeature = (index) => {
        setFormData(prev => ({
            ...prev,
            features: prev.features.filter((_, i) => i !== index)
        }));
    };

    const handleBusinessTypeToggle = (typeId) => {
        setFormData(prev => ({
            ...prev,
            business_types: prev.business_types.includes(typeId)
                ? prev.business_types.filter(id => id !== typeId)
                : [...prev.business_types, typeId]
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);
        
        try {
            await onSave(formData);
        } catch (error) {
            console.error('Error saving plan:', error);
        } finally {
            setSaving(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            {plan ? 'Edit Plan' : 'Create New Plan'}
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Basic Information */}
                            <div className="space-y-4">
                                <h4 className="text-md font-medium text-gray-900">Basic Information</h4>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Plan ID *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.id}
                                        onChange={(e) => handleInputChange('id', e.target.value)}
                                        disabled={!!plan}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                                        required
                                    />
                                    {!!plan && (
                                        <p className="text-xs text-gray-500 mt-1">Plan ID cannot be changed</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Plan Name *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Short Description
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.short_description}
                                        onChange={(e) => handleInputChange('short_description', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Brief description for plan cards"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Description
                                    </label>
                                    <textarea
                                        rows={3}
                                        value={formData.description}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Detailed plan description"
                                    />
                                </div>
                            </div>

                            {/* Pricing */}
                            <div className="space-y-4">
                                <h4 className="text-md font-medium text-gray-900">Pricing</h4>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Monthly Price (₹) *
                                    </label>
                                    <input
                                        type="number"
                                        step="0.01"
                                        value={formData.price_monthly}
                                        onChange={(e) => handleInputChange('price_monthly', parseFloat(e.target.value) || 0)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Yearly Price (₹) *
                                    </label>
                                    <input
                                        type="number"
                                        step="0.01"
                                        value={formData.price_yearly}
                                        onChange={(e) => handleInputChange('price_yearly', parseFloat(e.target.value) || 0)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required
                                    />
                                    {formData.price_monthly > 0 && formData.price_yearly > 0 && (
                                        <p className="text-xs text-green-600 mt-1">
                                            Yearly saves ₹{((formData.price_monthly * 12) - formData.price_yearly).toFixed(2)}
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Trial Days
                                    </label>
                                    <input
                                        type="number"
                                        value={formData.trial_days}
                                        onChange={(e) => handleInputChange('trial_days', parseInt(e.target.value) || 0)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Sort Order
                                    </label>
                                    <input
                                        type="number"
                                        value={formData.sort_order}
                                        onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 0)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Features */}
                        <div>
                            <h4 className="text-md font-medium text-gray-900 mb-3">Features</h4>
                            <div className="space-y-2">
                                {formData.features.map((feature, index) => (
                                    <div key={index} className="flex items-center space-x-2">
                                        <span className="flex-1 px-3 py-2 bg-gray-50 rounded-md text-sm">
                                            {feature}
                                        </span>
                                        <button
                                            type="button"
                                            onClick={() => removeFeature(index)}
                                            className="text-red-600 hover:text-red-800"
                                        >
                                            <i className="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                ))}
                                <div className="flex space-x-2">
                                    <input
                                        type="text"
                                        value={newFeature}
                                        onChange={(e) => setNewFeature(e.target.value)}
                                        placeholder="Add a feature..."
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                                    />
                                    <button
                                        type="button"
                                        onClick={addFeature}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                    >
                                        Add
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Settings */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={formData.is_trial_available}
                                    onChange={(e) => handleInputChange('is_trial_available', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm text-gray-700">Trial Available</span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={formData.is_visible}
                                    onChange={(e) => handleInputChange('is_visible', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm text-gray-700">Visible to Users</span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={formData.is_popular}
                                    onChange={(e) => handleInputChange('is_popular', e.target.checked)}
                                    className="mr-2"
                                />
                                <span className="text-sm text-gray-700">Mark as Popular</span>
                            </label>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                disabled={saving}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                disabled={saving}
                            >
                                {saving ? (
                                    <>
                                        <i className="fas fa-spinner fa-spin mr-2"></i>
                                        Saving...
                                    </>
                                ) : (
                                    plan ? 'Update Plan' : 'Create Plan'
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.PlanModal = PlanModal;
