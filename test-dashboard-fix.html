<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Dashboard API Fix</h1>
        
        <div class="test-section info">
            <h3>Authentication Status</h3>
            <p id="auth-status">Checking...</p>
            <button onclick="checkAuth()">Refresh Auth Status</button>
            <button onclick="goToLogin()">Go to Login</button>
        </div>
        
        <div class="test-section">
            <h3>Test Individual Endpoints</h3>
            <button onclick="testEndpoint('customer')">Test Customer</button>
            <button onclick="testEndpoint('quotation')">Test Quotation</button>
            <button onclick="testEndpoint('invoice')">Test Invoice</button>
            <button onclick="testEndpoint('contract')">Test Contract</button>
            <button onclick="testEndpoint('subscription')">Test Subscription</button>
            <div id="endpoint-results"></div>
        </div>
        
        <div class="test-section">
            <h3>Test trickleListObjects Function</h3>
            <button onclick="testTrickleFunction()">Test All Endpoints via trickleListObjects</button>
            <div id="trickle-results"></div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="/biz/config.js"></script>
    <script src="/biz/utils/api-utils.js"></script>

    <script>
        // Initialize page
        window.addEventListener('load', function() {
            checkAuth();
        });
        
        function checkAuth() {
            const token = localStorage.getItem('authToken');
            const authStatus = document.getElementById('auth-status');
            
            if (token) {
                authStatus.innerHTML = `
                    ✅ <strong>Authenticated</strong><br>
                    Token: ${token.substring(0, 30)}...<br>
                    <small>Token stored in localStorage</small>
                `;
            } else {
                authStatus.innerHTML = `
                    ❌ <strong>Not Authenticated</strong><br>
                    No auth token found in localStorage<br>
                    <small>Please login first</small>
                `;
            }
        }
        
        function goToLogin() {
            window.location.href = '/biz/login.html';
        }
        
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return fetch(url, {
                ...options,
                headers
            });
        }
        
        async function testEndpoint(endpoint) {
            const resultsDiv = document.getElementById('endpoint-results');
            resultsDiv.innerHTML = `<p>Testing ${endpoint} endpoint...</p>`;
            
            try {
                const url = window.getApiUrl(`/${endpoint}?limit=10`);
                console.log(`Testing ${endpoint} at:`, url);
                
                const response = await makeAuthenticatedRequest(url);
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = { raw: text };
                }
                
                const statusClass = response.ok ? 'success' : (response.status === 401 ? 'warning' : 'error');
                resultsDiv.className = 'test-section ' + statusClass;
                resultsDiv.innerHTML = `
                    <h3>${endpoint.charAt(0).toUpperCase() + endpoint.slice(1)} Endpoint Test</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <h4>Response:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    ${response.ok ? '<p><strong>✅ Success!</strong></p>' : 
                      response.status === 401 ? '<p><strong>⚠️ Authentication Required</strong></p>' : 
                      '<p><strong>❌ Failed</strong></p>'}
                `;
            } catch (error) {
                resultsDiv.className = 'test-section error';
                resultsDiv.innerHTML = `
                    <h3>${endpoint.charAt(0).toUpperCase() + endpoint.slice(1)} Endpoint Test</h3>
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        async function testTrickleFunction() {
            const resultsDiv = document.getElementById('trickle-results');
            resultsDiv.innerHTML = '<p>Testing trickleListObjects for all endpoints...</p>';
            
            const endpoints = ['customer', 'quotation', 'invoice', 'contract', 'subscription'];
            const results = {};
            
            for (const endpoint of endpoints) {
                try {
                    console.log(`Testing trickleListObjects('${endpoint}', 10)...`);
                    const data = await trickleListObjects(endpoint, 10);
                    results[endpoint] = { success: true, data };
                } catch (error) {
                    console.error(`trickleListObjects('${endpoint}') error:`, error);
                    results[endpoint] = { success: false, error: error.message };
                }
            }
            
            let html = '<h3>trickleListObjects Test Results</h3>';
            let allSuccess = true;
            
            for (const [endpoint, result] of Object.entries(results)) {
                if (!result.success) allSuccess = false;
                html += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <strong>${endpoint}:</strong> 
                        ${result.success ? 
                          `✅ Success (${result.data.items ? result.data.items.length : 0} items)` : 
                          `❌ Failed - ${result.error}`}
                    </div>
                `;
            }
            
            resultsDiv.className = 'test-section ' + (allSuccess ? 'success' : 'warning');
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
