// Website Footer Component
function WebsiteFooter({ currentPage = 'home' }) {
    
    const scrollToSection = (sectionId) => {
        if (currentPage === 'home') {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            // If not on home page, navigate to home with hash
            window.location.href = `index.html#${sectionId}`;
        }
    };

    return (
        <footer className="bg-gray-900 text-white py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                    <div className="md:col-span-2">
                        <div className="flex items-center space-x-2 mb-4">
                            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-lg">B</span>
                            </div>
                            <span className="text-2xl font-bold">Bizma</span>
                        </div>
                        <p className="text-gray-400 mb-6 max-w-md">
                            The all-in-one business management platform designed for modern entrepreneurs. 
                            Streamline your operations, manage customers, and grow your business with confidence.
                        </p>
                        <div className="flex space-x-4">
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <i className="fab fa-facebook-f text-xl"></i>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <i className="fab fa-twitter text-xl"></i>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <i className="fab fa-linkedin-in text-xl"></i>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">
                                <i className="fab fa-instagram text-xl"></i>
                            </a>
                        </div>
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold mb-4">Product</h4>
                        <ul className="space-y-3 text-gray-400">
                            <li>
                                {currentPage === 'home' ? (
                                    <button 
                                        onClick={() => scrollToSection('features')}
                                        className="hover:text-white transition-colors text-left"
                                    >
                                        Features
                                    </button>
                                ) : (
                                    <a href="index.html#features" className="hover:text-white transition-colors">
                                        Features
                                    </a>
                                )}
                            </li>
                            <li>
                                {currentPage === 'home' ? (
                                    <button 
                                        onClick={() => scrollToSection('pricing')}
                                        className="hover:text-white transition-colors text-left"
                                    >
                                        Pricing
                                    </button>
                                ) : (
                                    <a href="index.html#pricing" className="hover:text-white transition-colors">
                                        Pricing
                                    </a>
                                )}
                            </li>
                            <li>
                                <a href="about.html" className="hover:text-white transition-colors">
                                    About Us
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold mb-4">Legal & Support</h4>
                        <ul className="space-y-3 text-gray-400">
                            <li><a href="contact.html" className="hover:text-white transition-colors">Contact Us</a></li>
                            <li><a href="privacy.html" className="hover:text-white transition-colors">Privacy Policy</a></li>
                            <li><a href="terms.html" className="hover:text-white transition-colors">Terms & Conditions</a></li>
                            <li><a href="refund.html" className="hover:text-white transition-colors">Refund Policy</a></li>
                        </ul>
                    </div>
                </div>
                
                <div className="border-t border-gray-800 pt-8">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="text-gray-400 mb-4 md:mb-0">
                            <p>&copy; {new Date().getFullYear()} Bizma. All rights reserved.</p>
                        </div>
                        <div className="flex items-center space-x-6 text-sm text-gray-400">
                            <span className="flex items-center">
                                <i className="fas fa-shield-alt mr-2"></i>
                                Secure & Reliable
                            </span>
                            <span className="flex items-center">
                                <i className="fas fa-headset mr-2"></i>
                                24/7 Support
                            </span>
                            <span className="flex items-center">
                                <i className="fas fa-globe mr-2"></i>
                                Made in India
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}

// Make component globally available
window.WebsiteFooter = WebsiteFooter;
