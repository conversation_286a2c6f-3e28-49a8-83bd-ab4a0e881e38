<?php
require_once 'api/db-config.php';

echo "=== Initializing SaaS Subscription System ===\n\n";

try {
    // 1. Check and create trial subscriptions for existing companies without subscriptions
    echo "1. Setting up trial subscriptions for existing companies...\n";
    
    // Get Free Trial plan
    $trialPlanResult = $conn->query("SELECT * FROM pricing_plans WHERE name = 'Free Trial' LIMIT 1");
    if ($trialPlanResult && $trialPlanResult->num_rows > 0) {
        $trialPlan = $trialPlanResult->fetch_assoc();
        echo "   Found Free Trial plan (ID: {$trialPlan['id']})\n";
        
        // Get companies without active subscriptions
        $companiesResult = $conn->query("
            SELECT c.* FROM companies c 
            LEFT JOIN subscriptions s ON c.object_id = s.company_id AND s.status IN ('active', 'trial')
            WHERE s.id IS NULL
        ");
        
        if ($companiesResult && $companiesResult->num_rows > 0) {
            while ($company = $companiesResult->fetch_assoc()) {
                echo "   Creating trial subscription for: {$company['name']}\n";
                
                // Generate subscription object ID
                $subscriptionId = generateId();
                
                // Calculate trial dates
                $trialStartDate = date('Y-m-d H:i:s');
                $trialEndDate = date('Y-m-d H:i:s', strtotime('+14 days'));
                
                // Create trial subscription
                $stmt = $conn->prepare("
                    INSERT INTO subscriptions (
                        object_id, company_id, user_id, plan_id, plan_name, status, 
                        billing_cycle, price, currency, trial_start_date, trial_end_date,
                        start_date, end_date, features, limits_data, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, 'trial', 'monthly', 0, 'USD', ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");
                
                $features = json_encode($trialPlan['features'] ? json_decode($trialPlan['features'], true) : []);
                $limits = json_encode($trialPlan['limits_data'] ? json_decode($trialPlan['limits_data'], true) : []);
                
                $stmt->bind_param("sssssssssss", 
                    $subscriptionId, $company['object_id'], $company['owner_id'], 
                    $trialPlan['id'], $trialPlan['name'], $trialStartDate, $trialEndDate,
                    $trialStartDate, $trialEndDate, $features, $limits
                );
                
                if ($stmt->execute()) {
                    echo "     ✓ Trial subscription created successfully\n";
                } else {
                    echo "     ✗ Failed to create trial subscription: " . $stmt->error . "\n";
                }
            }
        } else {
            echo "   All companies already have subscriptions\n";
        }
    } else {
        echo "   ✗ Free Trial plan not found\n";
    }
    
    // 2. Update company subscription references
    echo "\n2. Updating company subscription references...\n";
    $updateResult = $conn->query("
        UPDATE companies c 
        JOIN subscriptions s ON c.object_id = s.company_id 
        SET c.subscription_id = s.object_id 
        WHERE c.subscription_id IS NULL OR c.subscription_id = ''
    ");
    
    if ($updateResult) {
        echo "   ✓ Company subscription references updated\n";
    } else {
        echo "   ✗ Failed to update company references: " . $conn->error . "\n";
    }
    
    // 3. Create default usage tracking
    echo "\n3. Setting up usage tracking...\n";
    $usageResult = $conn->query("
        INSERT IGNORE INTO usage_tracking (company_id, month_year, leads_created, invoices_sent, storage_used, created_at)
        SELECT c.object_id, DATE_FORMAT(NOW(), '%Y-%m'), 0, 0, 0, NOW()
        FROM companies c
    ");
    
    if ($usageResult) {
        echo "   ✓ Usage tracking initialized\n";
    } else {
        echo "   Note: Usage tracking table may not exist yet\n";
    }
    
    // 4. Display current subscription status
    echo "\n4. Current Subscription Status:\n";
    $statusResult = $conn->query("
        SELECT 
            s.status,
            COUNT(*) as count,
            GROUP_CONCAT(c.name SEPARATOR ', ') as companies
        FROM subscriptions s
        JOIN companies c ON s.company_id = c.object_id
        GROUP BY s.status
    ");
    
    if ($statusResult && $statusResult->num_rows > 0) {
        while ($row = $statusResult->fetch_assoc()) {
            echo "   {$row['status']}: {$row['count']} companies\n";
            if (strlen($row['companies']) < 100) {
                echo "     Companies: {$row['companies']}\n";
            }
        }
    }
    
    // 5. Display plan distribution
    echo "\n5. Plan Distribution:\n";
    $planResult = $conn->query("
        SELECT 
            pp.name as plan_name,
            COUNT(s.id) as subscription_count,
            SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_count,
            SUM(CASE WHEN s.status = 'trial' THEN 1 ELSE 0 END) as trial_count
        FROM pricing_plans pp
        LEFT JOIN subscriptions s ON pp.id = s.plan_id
        GROUP BY pp.id, pp.name
        ORDER BY subscription_count DESC
    ");
    
    if ($planResult && $planResult->num_rows > 0) {
        while ($row = $planResult->fetch_assoc()) {
            echo "   {$row['plan_name']}: {$row['subscription_count']} total ({$row['active_count']} active, {$row['trial_count']} trial)\n";
        }
    }
    
    echo "\n=== Subscription System Initialization Complete ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

$conn->close();
?>