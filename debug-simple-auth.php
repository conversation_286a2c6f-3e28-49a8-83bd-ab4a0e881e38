<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

echo "Debug: Starting authentication debug\n";

try {
    // Check if config can be loaded
    require_once __DIR__ . '/config/config.php';
    echo "Debug: Config loaded successfully\n";
    
    Config::load();
    echo "Debug: Config initialized\n";
    
    // Check database connection
    $db = Config::getDatabase();
    echo "Debug: Database config: " . json_encode($db) . "\n";
    
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    echo "Debug: Database connected successfully\n";
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows === 0) {
        throw new Exception('Users table does not exist');
    }
    echo "Debug: Users table exists\n";
    
    // Check if superadmin user exists
    $stmt = $conn->prepare("SELECT id, email, role, status FROM users WHERE email = ?");
    $email = '<EMAIL>';
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo "Debug: Superadmin user does not exist, creating...\n";
        
        // Create superadmin user
        $name = 'Super Administrator';
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $role = 'super_admin';
        $status = 'active';
        $object_id = 'user_' . time() . '_' . rand(100, 999);
        
        $stmt = $conn->prepare("INSERT INTO users (object_id, name, email, password_hash, role, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("ssssss", $object_id, $name, $email, $password_hash, $role, $status);
        
        if ($stmt->execute()) {
            echo "Debug: Superadmin user created successfully\n";
        } else {
            throw new Exception('Failed to create superadmin user: ' . $stmt->error);
        }
    } else {
        $user = $result->fetch_assoc();
        echo "Debug: Superadmin user exists: " . json_encode($user) . "\n";
    }
    
    // Test password verification
    $stmt = $conn->prepare("SELECT password_hash FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    if (password_verify('admin123', $user['password_hash'])) {
        echo "Debug: Password verification successful\n";
    } else {
        echo "Debug: Password verification failed\n";
    }
    
    // Test the actual login process
    echo "Debug: Testing login process...\n";
    
    $stmt = $conn->prepare("
        SELECT u.*, c.name as company_name
        FROM users u
        LEFT JOIN companies c ON u.company_id = c.object_id
        WHERE u.email = ? AND u.status = 'active'
    ");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('User not found in login query');
    }
    
    $user = $result->fetch_assoc();
    echo "Debug: User found: " . json_encode($user) . "\n";
    
    if (!password_verify('admin123', $user['password_hash'])) {
        throw new Exception('Password verification failed');
    }
    
    echo "Debug: Login process successful\n";
    
    // Generate token
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + 24 * 3600);
    
    // Update user with token
    $stmt = $conn->prepare("UPDATE users SET auth_token = ?, token_expires = ? WHERE id = ?");
    $stmt->bind_param("ssi", $token, $expires, $user['id']);
    $stmt->execute();
    
    echo "Debug: Token generated and stored\n";
    
    echo json_encode([
        'success' => true,
        'message' => 'Authentication debug completed successfully',
        'user' => [
            'id' => $user['object_id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'company_id' => $user['company_id'],
            'company_name' => $user['company_name']
        ],
        'token' => $token
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
