<?php
// Convert database row to object
function rowToObject($row, $objectType) {
    $objectData = [];
    
    // Map database columns to object properties based on object type
    switch ($objectType) {
        case 'customer':
            $objectData = [
                'name' => $row['name'],
                'email' => $row['email'] ?? '',
                'phone' => $row['phone'] ?? '',
                'company' => $row['company'] ?? '',
                'address' => $row['address'] ?? '',
                'type' => $row['type'] ?? 'individual',
                'status' => $row['status'] ?? 'active',
                'notes' => $row['notes'] ?? ''
            ];
            break;
            
        case 'invoice':
            $objectData = [
                'invoiceNumber' => $row['invoice_number'],
                'customer' => $row['customer_id'],
                'subtotal' => (float)$row['subtotal'],
                'tax' => (float)$row['tax'],
                'taxRate' => (float)$row['tax_rate'],
                'discount' => (float)$row['discount'],
                'total' => (float)$row['total'],
                'amountPaid' => (float)$row['amount_paid'],
                'balance' => (float)$row['balance'],
                'dueDate' => $row['due_date'],
                'status' => $row['status'],
                'paymentMethod' => $row['payment_method'],
                'paidAt' => $row['paid_at'],
                'notes' => $row['notes'],
                'terms' => $row['terms']
            ];
            break;
            
        case 'quotation':
            $objectData = [
                'quotationNumber' => $row['quotation_number'],
                'customer' => $row['customer_id'],
                'projectName' => $row['project_name'],
                'subtotal' => (float)$row['subtotal'],
                'tax' => (float)$row['tax'],
                'taxRate' => (float)$row['tax_rate'],
                'discount' => (float)$row['discount'],
                'total' => (float)$row['total'],
                'validUntil' => $row['valid_until'],
                'status' => $row['status'],
                'notes' => $row['notes'],
                'terms' => $row['terms']
            ];
            break;
            
        case 'contract':
            $objectData = [
                'contractNumber' => $row['contract_number'],
                'customer' => $row['customer_id'],
                'title' => $row['title'],
                'description' => $row['description'],
                'startDate' => $row['start_date'],
                'endDate' => $row['end_date'],
                'value' => (float)$row['value'],
                'status' => $row['status'],
                'type' => $row['type'],
                'scope' => $row['scope'],
                'paymentTerms' => $row['payment_terms'],
                'terms' => $row['terms'],
                'renewalTerms' => $row['renewal_terms'],
                'isRecurring' => (bool)$row['is_recurring'],
                'billingCycle' => $row['billing_cycle'],
                'customerSignature' => $row['customer_signature'],
                'signedAt1' => $row['signed_at1'],
                'signedAt2' => $row['signed_at2']
            ];
            break;
            
        case 'item':
            $objectData = [
                'name' => $row['name'],
                'description' => $row['description'],
                'category' => $row['category_id'],
                'subcategory' => $row['subcategory_id'],
                'sku' => $row['sku'],
                'price' => (float)$row['price'],
                'costPrice' => (float)$row['cost_price'],
                'tax' => (float)$row['tax'],
                'stockQuantity' => (int)$row['stock_quantity'],
                'unit' => $row['unit'],
                'isActive' => (bool)$row['is_active'],
                'isRecurring' => (bool)$row['is_recurring'],
                'recurringPeriod' => $row['recurring_period'],
                'itemType' => $row['item_type']
            ];
            break;
            
        case 'item_category':
        case 'item_subcategory':
            $objectData = [
                'name' => $row['name'],
                'description' => $row['description']
            ];
            if ($objectType === 'item_subcategory' && isset($row['parent_id'])) {
                $objectData['parentId'] = $row['parent_id'];
            }
            break;
            
        case 'lead':
            $objectData = [
                'name' => $row['name'],
                'email' => $row['email'],
                'phone' => $row['phone'],
                'company' => $row['company'],
                'position' => $row['position'] ?? '',  // Handle null values
                'source' => $row['source'],
                'status' => $row['status'],
                'priority' => $row['priority'] ?? 'medium',  // Handle null values
                'value' => (float)($row['value'] ?? 0),
                'notes' => $row['notes'] ?? '',
                'assignedTo' => $row['assigned_to'] ?? '',  // Convert to camelCase for frontend
                'followUpDate' => $row['next_follow_up'] ?? '',  // Convert to camelCase for frontend
                'tags' => isset($row['tags']) && $row['tags'] ? json_decode($row['tags'], true) : []  // Convert JSON to array
            ];
            break;
            
        case 'activity':
            $objectData = [
                'leadId' => $row['lead_id'],
                'type' => $row['type'],
                'description' => $row['description'],
                'activityDate' => $row['created_at'] ?? $row['updated_at'],
                'createdAt' => $row['created_at'],
                'updatedAt' => $row['updated_at']
            ];
            break;
            
        case 'task':
            $objectData = [
                'leadId' => $row['lead_id'],
                'title' => $row['title'],
                'description' => $row['description'] ?? '',
                'dueDate' => $row['due_date'],
                'priority' => $row['priority'],
                'status' => $row['status'],
                'createdAt' => $row['created_at'],
                'updatedAt' => $row['updated_at']
            ];
            break;
            
        case 'note':
            $objectData = [
                'leadId' => $row['lead_id'],
                'content' => $row['content'],
                'createdAt' => $row['created_at'],
                'updatedAt' => $row['updated_at']
            ];
            break;
            
        case 'settings':
            $objectData = [
                'companyName' => $row['company_name'],
                'companyEmail' => $row['company_email'],
                'companyPhone' => $row['company_phone'],
                'companyAddress' => $row['company_address'],
                'companyWebsite' => $row['company_website'],
                'taxRate' => (float)$row['tax_rate'],
                'currency' => $row['currency'],
                'dateFormat' => $row['date_format'],
                'theme' => $row['theme'],
                'logo' => $row['logo'],
                'signature' => $row['signature'],
                'companyGST' => $row['company_gst'],
                'authorizedName' => $row['authorized_name'],
                'bankDetails' => $row['bank_details'],
                'upiId' => $row['upi_id'],
                'defaultPaymentTerms' => $row['default_payment_terms'],
                'defaultNotes' => $row['default_notes']
            ];
            
            // Parse JSON fields
            if (!empty($row['notifications'])) {
                $objectData['notifications'] = json_decode($row['notifications'], true);
            }
            if (!empty($row['templates'])) {
                $objectData['templates'] = json_decode($row['templates'], true);
            }
            break;

        case 'company':
            $objectData = [
                'name' => $row['name'],
                'email' => $row['email'],
                'phone' => $row['phone'],
                'address' => $row['address'],
                'website' => $row['website'],
                'ownerId' => $row['owner_id'],
                'subscriptionId' => $row['subscription_id'],
                'status' => $row['status']
            ];

            // Parse JSON fields
            if (!empty($row['members'])) {
                $objectData['members'] = json_decode($row['members'], true);
            }
            break;
    }
    
    // Filter out null values
    $objectData = array_filter($objectData, function($value) {
        return $value !== null;
    });
    
    return [
        'objectId' => $row['object_id'],
        'objectType' => $objectType,
        'objectData' => $objectData,
        'createdAt' => $row['created_at'],
        'updatedAt' => $row['updated_at']
    ];
}

// Map object type to database table
function mapObjectTypeToTable($objectType) {
    $mapping = [
        'customer' => 'customers',
        'invoice' => 'invoices',
        'quotation' => 'quotations',
        'contract' => 'contracts',
        'item' => 'items',
        'item_category' => 'item_categories',
        'item_subcategory' => 'item_subcategories',
        'lead' => 'leads',
        'settings' => 'settings',
        'subscription' => 'subscriptions',
        'activity' => 'activities',
        'task' => 'tasks',
        'note' => 'notes',
        'company' => 'companies'
    ];

    return isset($mapping[$objectType]) ? $mapping[$objectType] : null;
}
?>
