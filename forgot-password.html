<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Bizma</title>
    <meta name="description" content="Reset your Bizma account password">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <!-- Load Layout Components -->
    <script type="text/babel" src="components/layout/WebsiteHeader.js"></script>
    <script type="text/babel" src="components/layout/WebsiteFooter.js"></script>
    
    <script type="text/babel">
        // Forgot Password Page Component
        function ForgotPasswordPage() {
            const [email, setEmail] = React.useState('');
            const [loading, setLoading] = React.useState(false);
            const [message, setMessage] = React.useState('');
            const [error, setError] = React.useState('');
            const [submitted, setSubmitted] = React.useState(false);

            React.useEffect(() => {
                document.title = 'Forgot Password - Bizma';
            }, []);

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');
                setMessage('');

                try {
                    const response = await fetch('api/auth-handler.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'forgot_password',
                            email: email
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        setMessage('Password reset instructions have been sent to your email address.');
                        setSubmitted(true);
                    } else {
                        setError(data.message || 'Failed to send reset email. Please try again.');
                    }
                } catch (error) {
                    console.error('Forgot password error:', error);
                    setError('Network error. Please check your connection and try again.');
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="min-h-screen bg-white">
                    {/* Header */}
                    <WebsiteHeader currentPage="forgot-password" />

                    {/* Forgot Password Form Section */}
                    <div className="bg-gray-50 py-12">
                        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="bg-white rounded-lg shadow-lg p-8">
                                <div className="text-center mb-8">
                                    <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <i className="fas fa-key text-white text-2xl"></i>
                                    </div>
                                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                                        Forgot Password?
                                    </h1>
                                    <p className="text-gray-600">
                                        {submitted 
                                            ? "Check your email for reset instructions"
                                            : "Enter your email address and we'll send you instructions to reset your password"
                                        }
                                    </p>
                                </div>

                                {!submitted ? (
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                                Email address
                                            </label>
                                            <input
                                                id="email"
                                                name="email"
                                                type="email"
                                                autoComplete="email"
                                                required
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Enter your email address"
                                            />
                                        </div>

                                        {error && (
                                            <div className="rounded-lg bg-red-50 p-4 border border-red-200">
                                                <div className="flex">
                                                    <div className="flex-shrink-0">
                                                        <i className="fas fa-exclamation-circle text-red-400"></i>
                                                    </div>
                                                    <div className="ml-3">
                                                        <p className="text-sm font-medium text-red-800">
                                                            {error}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        <button
                                            type="submit"
                                            disabled={loading}
                                            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                        >
                                            {loading ? (
                                                <>
                                                    <i className="fas fa-spinner fa-spin mr-2"></i>
                                                    Sending...
                                                </>
                                            ) : (
                                                'Send Reset Instructions'
                                            )}
                                        </button>
                                    </form>
                                ) : (
                                    <div className="text-center">
                                        {message && (
                                            <div className="rounded-lg bg-green-50 p-4 border border-green-200 mb-6">
                                                <div className="flex">
                                                    <div className="flex-shrink-0">
                                                        <i className="fas fa-check-circle text-green-400"></i>
                                                    </div>
                                                    <div className="ml-3">
                                                        <p className="text-sm font-medium text-green-800">
                                                            {message}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        <div className="space-y-4">
                                            <p className="text-sm text-gray-600">
                                                Didn't receive the email? Check your spam folder or try again.
                                            </p>
                                            <button
                                                onClick={() => {
                                                    setSubmitted(false);
                                                    setEmail('');
                                                    setMessage('');
                                                    setError('');
                                                }}
                                                className="text-blue-600 hover:text-blue-500 font-medium text-sm"
                                            >
                                                Try again
                                            </button>
                                        </div>
                                    </div>
                                )}

                                <div className="mt-6">
                                    <div className="relative">
                                        <div className="absolute inset-0 flex items-center">
                                            <div className="w-full border-t border-gray-300"></div>
                                        </div>
                                        <div className="relative flex justify-center text-sm">
                                            <span className="px-2 bg-white text-gray-500">
                                                Remember your password?
                                            </span>
                                        </div>
                                    </div>

                                    <div className="mt-6">
                                        <a
                                            href="login.html"
                                            className="w-full flex justify-center py-3 px-4 border border-blue-600 rounded-lg shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                        >
                                            Back to Sign In
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <WebsiteFooter currentPage="forgot-password" />
                </div>
            );
        }

        const { createRoot } = ReactDOM;
        const root = createRoot(document.getElementById('root'));
        root.render(<ForgotPasswordPage />);
    </script>
</body>
</html>
