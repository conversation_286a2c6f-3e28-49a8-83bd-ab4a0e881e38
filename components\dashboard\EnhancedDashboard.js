/**
 * Enhanced User Dashboard
 * Provides comprehensive dashboard with real-time data, analytics, and quick actions
 */

window.EnhancedDashboard = function EnhancedDashboard() {
    const authContext = React.useContext(window.AuthContext);
    const [dashboardData, setDashboardData] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);
    const [refreshing, setRefreshing] = React.useState(false);
    const [timeRange, setTimeRange] = React.useState('30'); // days
    
    React.useEffect(() => {
        loadDashboardData();
        
        // Set up auto-refresh every 5 minutes
        const interval = setInterval(loadDashboardData, 5 * 60 * 1000);
        return () => clearInterval(interval);
    }, [timeRange]);
    
    const loadDashboardData = async () => {
        try {
            setRefreshing(true);
            
            const [statsResponse, subscriptionResponse, recentActivityResponse] = await Promise.all([
                fetch(window.getApiUrl(`/dashboard/stats?days=${timeRange}`), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                }),
                fetch(window.getApiUrl('/subscription-management/current'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                }),
                fetch(window.getApiUrl('/dashboard/recent-activity'), {
                    headers: {
                        'Authorization': `Bearer ${authContext.token}`,
                        'Content-Type': 'application/json'
                    }
                })
            ]);
            
            const stats = statsResponse.ok ? await statsResponse.json() : { success: false };
            const subscription = subscriptionResponse.ok ? await subscriptionResponse.json() : { success: false };
            const recentActivity = recentActivityResponse.ok ? await recentActivityResponse.json() : { success: false };
            
            setDashboardData({
                stats: stats.success ? stats.data : getDefaultStats(),
                subscription: subscription.success ? subscription.subscription : null,
                recentActivity: recentActivity.success ? recentActivity.data : [],
                lastUpdated: new Date()
            });
            
            setError(null);
        } catch (err) {
            console.error('Dashboard loading error:', err);
            setError('Failed to load dashboard data');
            setDashboardData({
                stats: getDefaultStats(),
                subscription: null,
                recentActivity: [],
                lastUpdated: new Date()
            });
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };
    
    const getDefaultStats = () => ({
        customers: { total: 0, change: 0 },
        invoices: { total: 0, amount: 0, change: 0 },
        quotations: { total: 0, amount: 0, change: 0 },
        leads: { total: 0, converted: 0, change: 0 },
        revenue: { total: 0, change: 0 },
        pending_payments: { total: 0, amount: 0 }
    });
    
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0
        }).format(amount || 0);
    };
    
    const formatNumber = (num) => {
        return new Intl.NumberFormat('en-IN').format(num || 0);
    };
    
    const getChangeColor = (change) => {
        if (change > 0) return 'text-green-600';
        if (change < 0) return 'text-red-600';
        return 'text-gray-600';
    };
    
    const getChangeIcon = (change) => {
        if (change > 0) return 'fas fa-arrow-up';
        if (change < 0) return 'fas fa-arrow-down';
        return 'fas fa-minus';
    };
    
    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading dashboard...</p>
                </div>
            </div>
        );
    }
    
    if (error && !dashboardData) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-red-700">{error}</p>
                <button
                    onClick={loadDashboardData}
                    className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                >
                    Retry
                </button>
            </div>
        );
    }
    
    const { stats, subscription, recentActivity, lastUpdated } = dashboardData;
    
    return (
        <div className="enhanced-dashboard space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        Welcome back, {authContext.user?.name}!
                    </h1>
                    <p className="text-gray-600">
                        Here's what's happening with your business today.
                    </p>
                </div>
                
                <div className="flex items-center space-x-4">
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                    
                    <button
                        onClick={loadDashboardData}
                        disabled={refreshing}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                        {refreshing ? (
                            <i className="fas fa-spinner fa-spin"></i>
                        ) : (
                            <i className="fas fa-refresh"></i>
                        )}
                        <span className="ml-2">Refresh</span>
                    </button>
                </div>
            </div>
            
            {/* Subscription Status */}
            {subscription && (
                <div className={`p-4 rounded-lg border ${
                    subscription.current_status === 'trial' ? 'bg-blue-50 border-blue-200' :
                    subscription.current_status === 'active' ? 'bg-green-50 border-green-200' :
                    'bg-red-50 border-red-200'
                }`}>
                    <div className="flex justify-between items-center">
                        <div>
                            <h3 className="font-semibold">
                                {subscription.plan_name} Plan
                                {subscription.current_status === 'trial' && (
                                    <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                        Trial
                                    </span>
                                )}
                            </h3>
                            <p className="text-sm text-gray-600">
                                {subscription.current_status === 'trial' ? (
                                    `${subscription.days_remaining} days remaining in trial`
                                ) : subscription.current_status === 'active' ? (
                                    `Active until ${new Date(subscription.expires_at).toLocaleDateString()}`
                                ) : (
                                    'Subscription expired'
                                )}
                            </p>
                        </div>
                        
                        {subscription.current_status === 'trial' && subscription.days_remaining <= 3 && (
                            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Upgrade Now
                            </button>
                        )}
                    </div>
                </div>
            )}
            
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Customers */}
                <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Total Customers</p>
                            <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.customers.total)}</p>
                        </div>
                        <div className="p-3 bg-blue-100 rounded-full">
                            <i className="fas fa-users text-blue-600"></i>
                        </div>
                    </div>
                    <div className="mt-4 flex items-center">
                        <i className={`${getChangeIcon(stats.customers.change)} ${getChangeColor(stats.customers.change)} text-sm`}></i>
                        <span className={`ml-1 text-sm ${getChangeColor(stats.customers.change)}`}>
                            {Math.abs(stats.customers.change)}% from last period
                        </span>
                    </div>
                </div>
                
                {/* Revenue */}
                <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                            <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.revenue.total)}</p>
                        </div>
                        <div className="p-3 bg-green-100 rounded-full">
                            <i className="fas fa-rupee-sign text-green-600"></i>
                        </div>
                    </div>
                    <div className="mt-4 flex items-center">
                        <i className={`${getChangeIcon(stats.revenue.change)} ${getChangeColor(stats.revenue.change)} text-sm`}></i>
                        <span className={`ml-1 text-sm ${getChangeColor(stats.revenue.change)}`}>
                            {Math.abs(stats.revenue.change)}% from last period
                        </span>
                    </div>
                </div>
                
                {/* Invoices */}
                <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Invoices</p>
                            <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.invoices.total)}</p>
                        </div>
                        <div className="p-3 bg-purple-100 rounded-full">
                            <i className="fas fa-file-invoice text-purple-600"></i>
                        </div>
                    </div>
                    <div className="mt-4">
                        <p className="text-sm text-gray-600">
                            Total: {formatCurrency(stats.invoices.amount)}
                        </p>
                    </div>
                </div>
                
                {/* Leads */}
                <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Active Leads</p>
                            <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.leads.total)}</p>
                        </div>
                        <div className="p-3 bg-orange-100 rounded-full">
                            <i className="fas fa-chart-line text-orange-600"></i>
                        </div>
                    </div>
                    <div className="mt-4">
                        <p className="text-sm text-gray-600">
                            Converted: {stats.leads.converted} ({stats.leads.total > 0 ? Math.round((stats.leads.converted / stats.leads.total) * 100) : 0}%)
                        </p>
                    </div>
                </div>
            </div>
            
            {/* Quick Actions */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i className="fas fa-plus-circle text-blue-600 text-2xl mb-2"></i>
                        <p className="text-sm font-medium">New Customer</p>
                    </button>
                    <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i className="fas fa-file-invoice text-green-600 text-2xl mb-2"></i>
                        <p className="text-sm font-medium">Create Invoice</p>
                    </button>
                    <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i className="fas fa-quote-right text-purple-600 text-2xl mb-2"></i>
                        <p className="text-sm font-medium">New Quotation</p>
                    </button>
                    <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <i className="fas fa-user-plus text-orange-600 text-2xl mb-2"></i>
                        <p className="text-sm font-medium">Add Lead</p>
                    </button>
                </div>
            </div>
            
            {/* Recent Activity */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Recent Activity</h3>
                    <p className="text-sm text-gray-500">
                        Last updated: {lastUpdated.toLocaleTimeString()}
                    </p>
                </div>
                
                {recentActivity.length > 0 ? (
                    <div className="space-y-3">
                        {recentActivity.slice(0, 5).map((activity, index) => (
                            <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div className="flex-shrink-0">
                                    <i className={`fas ${activity.icon || 'fa-circle'} text-gray-600`}></i>
                                </div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium">{activity.title}</p>
                                    <p className="text-xs text-gray-600">{activity.description}</p>
                                </div>
                                <div className="text-xs text-gray-500">
                                    {new Date(activity.created_at).toLocaleTimeString()}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <i className="fas fa-inbox text-4xl mb-4"></i>
                        <p>No recent activity</p>
                    </div>
                )}
            </div>
            
            {/* Pending Payments */}
            {stats.pending_payments.total > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center">
                        <i className="fas fa-exclamation-triangle text-yellow-600 mr-3"></i>
                        <div>
                            <h4 className="font-medium text-yellow-800">Pending Payments</h4>
                            <p className="text-sm text-yellow-700">
                                You have {stats.pending_payments.total} pending payments worth {formatCurrency(stats.pending_payments.amount)}
                            </p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};