<?php
/**
 * Apply Business Template API
 * Applies business-specific templates to companies
 */

require_once 'db-config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['business_type']) || !isset($input['company_id'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Business type and company ID are required'
            ]);
            exit;
        }
        
        $businessType = $input['business_type'];
        $companyId = $input['company_id'];
        
        // Define business templates
        $templates = [
            'Retail Business' => [
                'items' => [
                    ['name' => 'Product Sales', 'category' => 'Products', 'price' => 0, 'unit' => 'piece'],
                    ['name' => 'Gift Wrapping', 'category' => 'Services', 'price' => 50, 'unit' => 'service'],
                    ['name' => 'Home Delivery', 'category' => 'Services', 'price' => 100, 'unit' => 'delivery'],
                    ['name' => 'Installation Service', 'category' => 'Services', 'price' => 500, 'unit' => 'service']
                ],
                'settings' => [
                    'invoice_header' => 'SALES INVOICE',
                    'invoice_terms' => 'Payment due within 30 days. Returns accepted within 7 days with receipt.',
                    'invoice_footer' => 'Thank you for shopping with us!',
                    'quotation_header' => 'PRICE QUOTATION',
                    'quotation_terms' => 'Prices valid for 30 days. Subject to stock availability.',
                    'quotation_footer' => 'We look forward to serving you!'
                ]
            ],
            'Healthcare Services' => [
                'items' => [
                    ['name' => 'Consultation Fee', 'category' => 'Consultation', 'price' => 500, 'unit' => 'visit'],
                    ['name' => 'Diagnostic Test', 'category' => 'Diagnostics', 'price' => 1000, 'unit' => 'test'],
                    ['name' => 'Treatment Package', 'category' => 'Treatment', 'price' => 5000, 'unit' => 'package'],
                    ['name' => 'Follow-up Visit', 'category' => 'Consultation', 'price' => 300, 'unit' => 'visit']
                ],
                'settings' => [
                    'invoice_header' => 'MEDICAL BILL',
                    'invoice_terms' => 'Payment due immediately. Insurance claims to be processed separately.',
                    'invoice_footer' => 'Wishing you good health!',
                    'quotation_header' => 'TREATMENT ESTIMATE',
                    'quotation_terms' => 'Estimate valid for 15 days. Actual costs may vary based on treatment.',
                    'quotation_footer' => 'Your health is our priority!'
                ]
            ],
            'Consulting Services' => [
                'items' => [
                    ['name' => 'Strategy Consultation', 'category' => 'Consulting', 'price' => 5000, 'unit' => 'hour'],
                    ['name' => 'Project Management', 'category' => 'Management', 'price' => 8000, 'unit' => 'hour'],
                    ['name' => 'Training Session', 'category' => 'Training', 'price' => 3000, 'unit' => 'session'],
                    ['name' => 'Report Preparation', 'category' => 'Documentation', 'price' => 2000, 'unit' => 'report']
                ],
                'settings' => [
                    'invoice_header' => 'PROFESSIONAL SERVICES INVOICE',
                    'invoice_terms' => 'Payment due within 15 days. Late payments subject to 2% monthly interest.',
                    'invoice_footer' => 'Thank you for choosing our services!',
                    'quotation_header' => 'SERVICE PROPOSAL',
                    'quotation_terms' => 'Proposal valid for 45 days. Rates subject to scope changes.',
                    'quotation_footer' => 'We look forward to partnering with you!'
                ]
            ],
            'Manufacturing' => [
                'items' => [
                    ['name' => 'Custom Manufacturing', 'category' => 'Production', 'price' => 10000, 'unit' => 'unit'],
                    ['name' => 'Quality Testing', 'category' => 'QA', 'price' => 500, 'unit' => 'test'],
                    ['name' => 'Packaging Service', 'category' => 'Services', 'price' => 200, 'unit' => 'package'],
                    ['name' => 'Shipping & Handling', 'category' => 'Logistics', 'price' => 1000, 'unit' => 'shipment']
                ],
                'settings' => [
                    'invoice_header' => 'MANUFACTURING INVOICE',
                    'invoice_terms' => 'Payment due within 45 days. Quality guarantee for 1 year.',
                    'invoice_footer' => 'Quality is our commitment!',
                    'quotation_header' => 'MANUFACTURING QUOTE',
                    'quotation_terms' => 'Quote valid for 60 days. Minimum order quantities apply.',
                    'quotation_footer' => 'Let us manufacture your success!'
                ]
            ],
            'Education Services' => [
                'items' => [
                    ['name' => 'Course Fee', 'category' => 'Education', 'price' => 15000, 'unit' => 'course'],
                    ['name' => 'Study Materials', 'category' => 'Materials', 'price' => 2000, 'unit' => 'set'],
                    ['name' => 'Examination Fee', 'category' => 'Assessment', 'price' => 1000, 'unit' => 'exam'],
                    ['name' => 'Certificate Fee', 'category' => 'Certification', 'price' => 500, 'unit' => 'certificate']
                ],
                'settings' => [
                    'invoice_header' => 'EDUCATION FEE INVOICE',
                    'invoice_terms' => 'Fees due before course commencement. Refund policy as per terms.',
                    'invoice_footer' => 'Empowering minds, building futures!',
                    'quotation_header' => 'COURSE FEE STRUCTURE',
                    'quotation_terms' => 'Fees valid for current academic session. Early bird discounts available.',
                    'quotation_footer' => 'Invest in your future with us!'
                ]
            ],
            'Jewellery Business' => [
                'items' => [
                    ['name' => 'Gold Jewellery', 'category' => 'Gold', 'price' => 50000, 'unit' => 'piece'],
                    ['name' => 'Diamond Jewellery', 'category' => 'Diamond', 'price' => 100000, 'unit' => 'piece'],
                    ['name' => 'Silver Jewellery', 'category' => 'Silver', 'price' => 5000, 'unit' => 'piece'],
                    ['name' => 'Custom Design', 'category' => 'Services', 'price' => 10000, 'unit' => 'design'],
                    ['name' => 'Repair Service', 'category' => 'Services', 'price' => 1000, 'unit' => 'service']
                ],
                'settings' => [
                    'invoice_header' => 'JEWELLERY INVOICE',
                    'invoice_terms' => 'Payment due at delivery. Buyback guarantee available. Hallmark certified.',
                    'invoice_footer' => 'Crafting memories, one piece at a time!',
                    'quotation_header' => 'JEWELLERY QUOTATION',
                    'quotation_terms' => 'Prices subject to gold/silver rate changes. Custom orders require advance payment.',
                    'quotation_footer' => 'Your trust is our treasure!'
                ]
            ]
        ];
        
        if (!isset($templates[$businessType])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid business type'
            ]);
            exit;
        }
        
        $template = $templates[$businessType];
        $results = [
            'items' => [],
            'settings' => null,
            'success' => true,
            'errors' => []
        ];
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Create items
            foreach ($template['items'] as $item) {
                $stmt = $conn->prepare("INSERT INTO items (name, category, price, unit, company_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
                $stmt->bind_param("ssdss", $item['name'], $item['category'], $item['price'], $item['unit'], $companyId);
                
                if ($stmt->execute()) {
                    $results['items'][] = [
                        'id' => $conn->insert_id,
                        'name' => $item['name'],
                        'category' => $item['category'],
                        'price' => $item['price'],
                        'unit' => $item['unit']
                    ];
                } else {
                    $results['errors'][] = "Failed to create item: {$item['name']}";
                }
            }
            
            // Apply settings
            foreach ($template['settings'] as $key => $value) {
                $stmt = $conn->prepare("INSERT INTO company_settings (company_id, setting_key, setting_value, created_at) VALUES (?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()");
                $stmt->bind_param("sss", $companyId, $key, $value);
                
                if (!$stmt->execute()) {
                    $results['errors'][] = "Failed to apply setting: $key";
                }
            }
            
            // Update company business type
            $stmt = $conn->prepare("UPDATE companies SET business_type = ?, template_applied = 1, updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("ss", $businessType, $companyId);
            $stmt->execute();
            
            $conn->commit();
            
            $results['settings'] = $template['settings'];
            $results['message'] = "Business template applied successfully";
            
            echo json_encode($results);
            
        } catch (Exception $e) {
            $conn->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Apply Business Template Error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to apply business template',
            'details' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

$conn->close();
?>