<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Simple</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test API Simple Access</h1>
        
        <div class="test-section">
            <h2>Test 1: Enhanced Auth Handler with Query Params</h2>
            <button onclick="testEnhancedAuthQuery()">Test ?action=login</button>
            <div id="enhanced-query-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: API with Query Params</h2>
            <button onclick="testApiQuery()">Test api.php?endpoint=auth&action=login</button>
            <div id="api-query-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Direct Enhanced Auth Handler</h2>
            <button onclick="testDirectEnhanced()">Test enhanced-auth-handler.php directly</button>
            <div id="direct-enhanced-result"></div>
        </div>
    </div>

    <script>
        async function testEnhancedAuthQuery() {
            const resultDiv = document.getElementById('enhanced-query-result');
            resultDiv.innerHTML = '<p>Testing enhanced auth with query params...</p>';
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'BhaviGani@56',
                        rememberMe: false
                    })
                });
                
                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (data && data.success ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success ? '<p><strong>✅ Login Successful!</strong></p>' : '<p><strong>❌ Login Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testApiQuery() {
            const resultDiv = document.getElementById('api-query-result');
            resultDiv.innerHTML = '<p>Testing API with query params...</p>';
            
            try {
                const response = await fetch('/biz/api/api.php?endpoint=auth&action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'BhaviGani@56',
                        rememberMe: false
                    })
                });
                
                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (data && data.success ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success ? '<p><strong>✅ Login Successful!</strong></p>' : '<p><strong>❌ Login Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testDirectEnhanced() {
            const resultDiv = document.getElementById('direct-enhanced-result');
            resultDiv.innerHTML = '<p>Testing direct enhanced auth handler...</p>';
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'BhaviGani@56',
                        rememberMe: false
                    })
                });
                
                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (data && data.success ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success ? '<p><strong>✅ Login Successful!</strong></p>' : '<p><strong>❌ Login Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
