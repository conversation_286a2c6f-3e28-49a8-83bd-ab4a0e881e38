// NotificationContainer Component
function NotificationContainer() {
    // Create container element
    const container = document.createElement('div');
    container.className = 'fixed top-4 right-4 z-50 w-full max-w-sm space-y-4';
    container.id = 'notification-container';
    
    return container;
}

// Simple notification function
function createNotification(type, message, duration = 5000) {
    const container = document.getElementById('notification-container') || document.body;
    
    const notification = document.createElement('div');
    notification.className = `p-4 rounded-lg shadow-lg border-l-4 ${getNotificationClasses(type)} transform transition-all duration-300 ease-in-out`;
    
    const content = document.createElement('div');
    content.className = 'flex items-center';
    
    const icon = document.createElement('i');
    icon.className = `${getNotificationIcon(type)} mr-3`;
    
    const text = document.createElement('span');
    text.textContent = message;
    
    const closeBtn = document.createElement('button');
    closeBtn.className = 'ml-auto text-gray-400 hover:text-gray-600';
    closeBtn.innerHTML = '&times;';
    closeBtn.onclick = () => notification.remove();
    
    content.appendChild(icon);
    content.appendChild(text);
    content.appendChild(closeBtn);
    notification.appendChild(content);
    
    container.appendChild(notification);
    
    // Auto remove
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
    
    return notification;
}

function getNotificationClasses(type) {
    switch (type) {
        case 'success':
            return 'bg-green-50 border-green-400 text-green-800';
        case 'error':
            return 'bg-red-50 border-red-400 text-red-800';
        case 'warning':
            return 'bg-yellow-50 border-yellow-400 text-yellow-800';
        case 'info':
        default:
            return 'bg-blue-50 border-blue-400 text-blue-800';
    }
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'fas fa-check-circle text-green-500';
        case 'error':
            return 'fas fa-exclamation-circle text-red-500';
        case 'warning':
            return 'fas fa-exclamation-triangle text-yellow-500';
        case 'info':
        default:
            return 'fas fa-info-circle text-blue-500';
    }
}

// Make components globally available
window.NotificationContainer = NotificationContainer;
window.createNotification = createNotification;

// Initialize notification container on page load
document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('notification-container')) {
        const container = NotificationContainer();
        document.body.appendChild(container);
    }
});
