/**
 * Emergency fix for missing utility components
 * This script creates fallbacks for critical components
 */

// Prevent problematic component files from being loaded by creating fallbacks early
console.log('🔧 Creating component fallbacks to prevent loading errors...');

// Create ErrorMessage fallback
if (typeof window.ErrorMessage === 'undefined') {
    console.warn('Creating ErrorMessage fallback');
    window.ErrorMessage = function({ error, onRetry }) {
        const errorMessage = typeof error === 'string' ? error : (error?.message || 'Something went wrong. Please try again.');
        return React.createElement('div', {
            className: 'bg-red-50 border border-red-200 rounded-md p-4'
        }, [
            React.createElement('div', {
                key: 'content',
                className: 'flex'
            }, [
                React.createElement('div', {
                    key: 'icon',
                    className: 'flex-shrink-0'
                }, React.createElement('i', {
                    className: 'fas fa-exclamation-circle text-red-500'
                })),
                React.createElement('div', {
                    key: 'text',
                    className: 'ml-3'
                }, [
                    React.createElement('h3', {
                        key: 'title',
                        className: 'text-sm font-medium text-red-800'
                    }, 'Error'),
                    React.createElement('p', {
                        key: 'message',
                        className: 'mt-1 text-sm text-red-700'
                    }, errorMessage),
                    onRetry && React.createElement('button', {
                        key: 'retry',
                        onClick: onRetry,
                        className: 'mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200'
                    }, 'Try Again')
                ])
            ])
        ]);
    };
}

// Create NotificationContainer fallback
if (typeof window.NotificationContainer === 'undefined') {
    console.warn('Creating NotificationContainer fallback');
    window.NotificationContainer = function() {
        return React.createElement('div', {
            id: 'notification-container',
            className: 'fixed top-4 right-4 z-50'
        });
    };
}

// Create TrialBanner fallback
if (typeof window.TrialBanner === 'undefined') {
    console.warn('Creating TrialBanner fallback');
    window.TrialBanner = function({ trialData, onUpgrade }) {
        if (!trialData || !trialData.is_trial) return null;

        const daysRemaining = trialData.trial_days_remaining || 0;
        const isExpired = trialData.expired || daysRemaining <= 0;

        return React.createElement('div', {
            className: isExpired ? 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4' : 'bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4'
        }, [
            React.createElement('div', {
                key: 'content',
                className: 'flex items-center justify-between'
            }, [
                React.createElement('span', {
                    key: 'text'
                }, isExpired ? 'Your trial has expired' : `${daysRemaining} days left in trial`),
                onUpgrade && React.createElement('button', {
                    key: 'button',
                    onClick: onUpgrade,
                    className: 'bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded'
                }, 'Upgrade Now')
            ])
        ]);
    };
}

// Create ExtendTrialModal fallback
if (typeof window.ExtendTrialModal === 'undefined') {
    console.warn('Creating ExtendTrialModal fallback');
    window.ExtendTrialModal = function({ onCancel, onConfirm }) {
        return React.createElement('div', {
            className: 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50'
        }, React.createElement('div', {
            className: 'bg-white rounded-lg shadow-xl p-6 w-full max-w-md'
        }, [
            React.createElement('h3', {
                key: 'title',
                className: 'text-lg font-medium text-gray-900 mb-4'
            }, 'Extend Trial'),
            React.createElement('p', {
                key: 'message',
                className: 'text-gray-600 mb-4'
            }, 'Would you like to extend your trial?'),
            React.createElement('div', {
                key: 'buttons',
                className: 'flex justify-end space-x-3'
            }, [
                React.createElement('button', {
                    key: 'cancel',
                    onClick: onCancel,
                    className: 'px-4 py-2 text-gray-600 hover:text-gray-800'
                }, 'Cancel'),
                React.createElement('button', {
                    key: 'confirm',
                    onClick: onConfirm,
                    className: 'px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700'
                }, 'Confirm')
            ])
        ]));
    };
}

// Create LoadingSpinner if it doesn't exist
if (typeof window.LoadingSpinner === 'undefined') {
    console.warn('Creating LoadingSpinner fallback');
    window.LoadingSpinner = function({ size = 'md', text = 'Loading...', center = true }) {
        const sizeClass = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-12 w-12' : 'h-8 w-8';
        const centerClass = center ? 'flex items-center justify-center' : '';
        
        return React.createElement('div', { 
            className: `flex flex-col items-center ${centerClass}` 
        }, [
            React.createElement('div', { 
                key: 'spinner',
                className: `animate-spin rounded-full ${sizeClass} border-b-2 border-blue-600`
            }),
            text && React.createElement('p', { 
                key: 'text',
                className: 'mt-2 text-gray-600' 
            }, text)
        ]);
    };
}

// Create ErrorMessage if it doesn't exist
if (typeof window.ErrorMessage === 'undefined') {
    console.warn('Creating ErrorMessage fallback');
    window.ErrorMessage = function({ error, onRetry }) {
        const message = typeof error === 'string' ? error : (error?.message || 'An error occurred');
        
        return React.createElement('div', { 
            className: 'bg-red-50 border border-red-200 rounded-md p-4 my-4' 
        }, [
            React.createElement('p', { 
                key: 'message',
                className: 'text-red-700' 
            }, message),
            onRetry && React.createElement('button', { 
                key: 'retry',
                onClick: onRetry,
                className: 'mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200'
            }, 'Try Again')
        ]);
    };
}

// Create NotificationContainer if it doesn't exist
if (typeof window.NotificationContainer === 'undefined') {
    console.warn('Creating NotificationContainer fallback');
    window.NotificationContainer = function() {
        return React.createElement('div', { 
            id: 'notification-container',
            className: 'fixed top-4 right-4 z-50 flex flex-col gap-2'
        });
    };
}

// Create showNotification if it doesn't exist
if (typeof window.showNotification === 'undefined') {
    console.warn('Creating showNotification fallback');
    window.showNotification = function({ type = 'info', message, duration = 5000 }) {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // Create a simple alert for critical notifications
        if (type === 'error' || type === 'warning') {
            setTimeout(() => alert(`${type.toUpperCase()}: ${message}`), 100);
        }
    };
}

console.log('Emergency component fallbacks loaded successfully');