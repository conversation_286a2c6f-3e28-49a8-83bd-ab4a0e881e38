function CompanySettings({ formData, handleInputChange }) {
    try {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                        label="Company Name"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        required
                    />
                    <Input
                        label="Company Email"
                        name="companyEmail"
                        type="email"
                        value={formData.companyEmail}
                        onChange={handleInputChange}
                        required
                    />
                    <Input
                        label="Company Phone"
                        name="companyPhone"
                        value={formData.companyPhone}
                        onChange={handleInputChange}
                    />
                    <Input
                        label="Company Website"
                        name="companyWebsite"
                        value={formData.companyWebsite}
                        onChange={handleInputChange}
                    />
                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Company Address
                        </label>
                        <textarea
                            name="companyAddress"
                            value={formData.companyAddress || ''}
                            onChange={handleInputChange}
                            rows={3}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>
                    <Input
                        label="GSTIN"
                        name="companyGST"
                        value={formData.companyGST}
                        onChange={handleInputChange}
                    />
                    <Input
                        label="Authorized Signatory Name"
                        name="authorizedName"
                        value={formData.authorizedName}
                        onChange={handleInputChange}
                    />
                    <Input
                        label="UPI ID (for QR payments)"
                        name="upiId"
                        value={formData.upiId}
                        onChange={handleInputChange}
                        placeholder="yourname@upi"
                    />
                </div>

                <div>
                    <h2 className="text-lg font-medium mb-4">Banking Details</h2>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Payment Instructions / Bank Details
                        </label>
                        <textarea
                            name="bankDetails"
                            value={formData.bankDetails || ''}
                            onChange={handleInputChange}
                            rows={4}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Account Name: Your Company Name&#10;Account Number: **********&#10;IFSC Code: ABCD0123456&#10;Bank Name: Your Bank&#10;Branch: Your Branch"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                            These details will appear on invoices for payments.
                        </p>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('CompanySettings component error:', error);
        reportError(error);
        return null;
    }
}
