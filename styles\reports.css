/* Reports Page Styles */

.reports-dashboard {
    padding: 1.5rem;
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.reports-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.date-range-selector {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.date-range-selector:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Report Cards Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: transform 0.2s, box-shadow 0.2s;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.report-card h3 {
    font-size: 1.125rem;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.report-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.report-metric:last-child {
    border-bottom: none;
}

.report-metric-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.report-metric-value {
    font-weight: 600;
    color: #1f2937;
}

.report-metric-value.currency {
    color: #059669;
}

.report-metric-value.danger {
    color: #dc2626;
}

/* Revenue Chart Styles */
.revenue-chart {
    margin-top: 1rem;
}

.revenue-month {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.revenue-month-name {
    color: #6b7280;
}

.revenue-month-amount {
    font-weight: 500;
    color: #059669;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.375rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    display: block;
}

.stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 0.25rem;
}

/* Type Breakdown */
.type-breakdown {
    margin-top: 1rem;
}

.type-breakdown h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.type-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 0.875rem;
}

.type-name {
    color: #6b7280;
    text-transform: capitalize;
}

.type-count {
    font-weight: 500;
    color: #1f2937;
}

/* Loading State */
.reports-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 16rem;
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.loading-spinner {
    font-size: 2rem;
    color: #3b82f6;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .reports-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .report-card {
        padding: 1rem;
    }
}

@media (max-width: 640px) {
    .reports-dashboard {
        padding: 1rem;
    }
    
    .report-metric {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .reports-dashboard {
        padding: 0;
    }
    
    .date-range-selector {
        display: none;
    }
    
    .report-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .reports-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .report-card {
        background-color: #1f2937;
        border-color: #374151;
    }
    
    .report-card h3 {
        color: #f9fafb;
        border-color: #374151;
    }
    
    .report-metric-label {
        color: #9ca3af;
    }
    
    .report-metric-value {
        color: #f9fafb;
    }
    
    .stat-item {
        background-color: #374151;
    }
    
    .stat-value {
        color: #f9fafb;
    }
}
