<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Utility Scripts Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .success { color: green; }
        .error { color: red; }
        .loading { color: blue; }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow: auto;
        }
        .script-status {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .loaded { background-color: #e6ffe6; }
        .failed { background-color: #ffebeb; }
    </style>
</head>
<body>
    <h1>Utility Scripts Test</h1>
    <p>This page tests the loading of utility scripts.</p>
    
    <div id="status" class="loading">Loading scripts...</div>
    
    <h2>Script Status</h2>
    <div id="script-status"></div>
    
    <h2>Available Utilities</h2>
    <pre id="available-utilities"></pre>
    
    <!-- Load Configuration -->
    <script src="config.js"></script>
    
    <!-- Custom Script Loader -->
    <script>
        // Get the base path from APP_CONFIG or use a default for XAMPP setup
        const basePath = window.APP_CONFIG ? window.APP_CONFIG.BASE_PATH : '/biz';
        
        // List of utility scripts to load in order
        const utilityScripts = [
            basePath + '/utils/ValidationUtils.js',
            basePath + '/utils/dataCache.js',
            basePath + '/utils/NotificationManager.js',
            basePath + '/utils/NetworkStatus.js',
            basePath + '/utils/ApiClient.js',
            basePath + '/components/common/LoadingSpinner.js',
            basePath + '/components/common/ErrorMessage.js',
            basePath + '/components/common/NotificationContainer.js',
            basePath + '/components/subscriptions/TrialBanner.js',
            basePath + '/components/subscriptions/ExtendTrialModal.js'
        ];
        
        // Function to load a script dynamically
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    updateScriptStatus(src, true);
                    resolve();
                };
                script.onerror = (e) => {
                    updateScriptStatus(src, false);
                    reject(new Error(`Failed to load script: ${src}`));
                };
                document.head.appendChild(script);
            });
        }
        
        // Update script status in the UI
        function updateScriptStatus(src, success) {
            const statusDiv = document.getElementById('script-status');
            const scriptDiv = document.createElement('div');
            scriptDiv.className = `script-status ${success ? 'loaded' : 'failed'}`;
            scriptDiv.textContent = `${success ? '✅' : '❌'} ${src}`;
            statusDiv.appendChild(scriptDiv);
        }
        
        // Load all utility scripts
        async function loadUtilities() {
            const statusDiv = document.getElementById('status');
            let loadedCount = 0;
            let failedScripts = [];
            
            for (const script of utilityScripts) {
                try {
                    await loadScript(script);
                    loadedCount++;
                } catch (error) {
                    console.error(`Failed to load: ${script}`, error);
                    failedScripts.push(script);
                }
            }
            
            // Update status
            if (failedScripts.length === 0) {
                statusDiv.className = 'success';
                statusDiv.textContent = `✅ All ${loadedCount} scripts loaded successfully`;
            } else {
                statusDiv.className = 'error';
                statusDiv.textContent = `⚠️ Loaded ${loadedCount}/${utilityScripts.length} scripts. ${failedScripts.length} failed.`;
            }
            
            // Show available utilities
            const availableUtilitiesDiv = document.getElementById('available-utilities');
            const utilities = Object.keys(window).filter(key => {
                return typeof window[key] === 'function' && 
                       (key.includes('Spinner') || 
                        key.includes('Error') || 
                        key.includes('Notification') || 
                        key.includes('Utils') ||
                        key.includes('Cache') ||
                        key.includes('Client'));
            });
            
            availableUtilitiesDiv.textContent = JSON.stringify(utilities, null, 2);
        }
        
        // Load utilities when the document is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadUtilities);
        } else {
            loadUtilities();
        }
    </script>
</body>
</html>