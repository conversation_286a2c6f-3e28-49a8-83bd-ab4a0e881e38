#!/bin/bash

# Business SaaS Setup Script
# This script sets up the complete Next.js SaaS application

echo "🚀 Setting up Business SaaS Application..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before continuing."
    echo "   Required variables:"
    echo "   - DATABASE_URL (MySQL connection string)"
    echo "   - NEXTAUTH_SECRET (random secret key)"
    echo "   - STRIPE_PUBLISHABLE_KEY and STRIPE_SECRET_KEY"
    echo "   - RESEND_API_KEY (for emails)"
    echo ""
    read -p "Press Enter after configuring .env file..."
fi

# Generate Prisma client
echo "🗄️  Generating Prisma client..."
npm run db:generate

# Check database connection
echo "🔍 Checking database connection..."
if npm run db:push > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed. Please check your DATABASE_URL in .env"
    exit 1
fi

# Run database migrations
echo "🗄️  Setting up database schema..."
npm run db:push

# Seed the database
echo "🌱 Seeding database with initial data..."
npm run db:seed

# Build the application
echo "🏗️  Building application..."
npm run build

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Start the development server: npm run dev"
echo "2. Visit http://localhost:3000"
echo "3. Create your first admin account"
echo "4. Configure your business settings"
echo ""
echo "📚 Documentation:"
echo "- README.md - Complete setup and usage guide"
echo "- /docs - Additional documentation"
echo ""
echo "🆘 Need help?"
echo "- Check the troubleshooting section in README.md"
echo "- Visit our documentation at https://docs.businesssaas.com"
echo "- Contact <NAME_EMAIL>"
echo ""
echo "Happy building! 🚀"
