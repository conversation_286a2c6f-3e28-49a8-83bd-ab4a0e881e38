<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

require_once 'db-config.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $email = trim($input['email'] ?? '');
    $password = $input['password'] ?? '';
    
    // Validate input
    if (empty($email) || empty($password)) {
        throw new Exception('Email and password are required');
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // Check if user exists
    $stmt = $pdo->prepare("SELECT object_id, name, email, password, role, company_name, is_active FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('Invalid email or password');
    }
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        throw new Exception('Invalid email or password');
    }
    
    // Check if user is active
    if (!$user['is_active']) {
        throw new Exception('Account is deactivated. Please contact support.');
    }
    
    // Generate JWT token (simple version)
    $payload = [
        'user_id' => $user['object_id'],
        'email' => $user['email'],
        'role' => $user['role'],
        'exp' => time() + (24 * 60 * 60) // 24 hours
    ];
    
    $token = base64_encode(json_encode($payload));
    
    // Get subscription info if user is not super admin
    $subscription = null;
    if ($user['role'] !== 'super_admin') {
        $stmt = $pdo->prepare("
            SELECT s.*, p.name as plan_name, p.price_monthly, p.price_yearly 
            FROM subscriptions s 
            LEFT JOIN pricing_plans p ON s.plan_id = p.object_id 
            WHERE s.user_id = ? 
            ORDER BY s.created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$user['object_id']]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($subscription) {
            // Calculate trial status
            $subscription['is_trial'] = $subscription['status'] === 'trial';
            $subscription['trial_days_remaining'] = 0;
            $subscription['expired'] = false;
            
            if ($subscription['is_trial'] && $subscription['trial_end_date']) {
                $trialEnd = new DateTime($subscription['trial_end_date']);
                $now = new DateTime();
                $diff = $now->diff($trialEnd);
                
                if ($trialEnd > $now) {
                    $subscription['trial_days_remaining'] = $diff->days;
                } else {
                    $subscription['expired'] = true;
                    $subscription['trial_days_remaining'] = 0;
                }
            }
        }
    }
    
    // Update last login
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE object_id = ?");
    $stmt->execute([$user['object_id']]);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'data' => [
            'token' => $token,
            'user' => [
                'object_id' => $user['object_id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'company_name' => $user['company_name']
            ],
            'subscription' => $subscription
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    error_log("Database error in login: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
?>
