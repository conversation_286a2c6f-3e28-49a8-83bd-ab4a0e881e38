<?php
/**
 * Create Missing Database Tables
 * Creates any missing tables needed for the enhanced features
 */

require_once 'api/db-config.php';

echo "<h1>Creating Missing Database Tables</h1>\n";

$tables = [
    'user_preferences' => "
        CREATE TABLE IF NOT EXISTS user_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            preference_key VARCHAR(100) NOT NULL,
            preference_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_preference (user_id, preference_key),
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'user_goals' => "
        CREATE TABLE IF NOT EXISTS user_goals (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            goal VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'onboarding_data' => "
        CREATE TABLE IF NOT EXISTS onboarding_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            company_id VARCHAR(50) NOT NULL,
            onboarding_data JSON,
            completed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_onboarding (user_id),
            INDEX idx_company_id (company_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'company_settings' => "
        CREATE TABLE IF NOT EXISTS company_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id VARCHAR(50) NOT NULL,
            setting_key VARCHAR(100) NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_company_setting (company_id, setting_key),
            INDEX idx_company_id (company_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'items' => "
        CREATE TABLE IF NOT EXISTS items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            category VARCHAR(100),
            price DECIMAL(10,2) DEFAULT 0.00,
            unit VARCHAR(50) DEFAULT 'piece',
            company_id VARCHAR(50) NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_company_id (company_id),
            INDEX idx_category (category)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    "
];

$success = 0;
$errors = 0;

foreach ($tables as $tableName => $sql) {
    try {
        if ($conn->query($sql)) {
            echo "<p>✅ Table '$tableName' created/verified successfully</p>\n";
            $success++;
        } else {
            echo "<p>❌ Error creating table '$tableName': " . $conn->error . "</p>\n";
            $errors++;
        }
    } catch (Exception $e) {
        echo "<p>❌ Exception creating table '$tableName': " . $e->getMessage() . "</p>\n";
        $errors++;
    }
}

// Add missing columns to existing tables
$alterQueries = [
    "ALTER TABLE users ADD COLUMN IF NOT EXISTS onboarding_completed TINYINT(1) DEFAULT 0",
    "ALTER TABLE companies ADD COLUMN IF NOT EXISTS business_type VARCHAR(100)",
    "ALTER TABLE companies ADD COLUMN IF NOT EXISTS template_applied TINYINT(1) DEFAULT 0",
    "ALTER TABLE companies ADD COLUMN IF NOT EXISTS company_size VARCHAR(50)",
    "ALTER TABLE companies ADD COLUMN IF NOT EXISTS location VARCHAR(255)"
];

echo "<h2>Adding Missing Columns</h2>\n";

foreach ($alterQueries as $query) {
    try {
        $conn->query($query);
        echo "<p>✅ Column update executed successfully</p>\n";
    } catch (Exception $e) {
        // Ignore errors for columns that already exist
        if (strpos($e->getMessage(), 'Duplicate column name') === false) {
            echo "<p>⚠️ Column update note: " . $e->getMessage() . "</p>\n";
        }
    }
}

echo "<h2>Summary</h2>\n";
echo "<p><strong>Tables Created/Verified:</strong> $success</p>\n";
echo "<p><strong>Errors:</strong> $errors</p>\n";

if ($errors === 0) {
    echo "<p style='color: green; font-weight: bold;'>🎉 All database tables are ready!</p>\n";
} else {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Some issues encountered, but core functionality should work.</p>\n";
}

$conn->close();
?>