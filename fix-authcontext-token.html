<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix AuthContext Token</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; }
        .step h3 { margin-top: 0; color: #495057; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix AuthContext Token Issue</h1>
        <p>This page will automatically fix the "Invalid token" error by getting a fresh authentication token.</p>
        
        <div class="step">
            <h3>Step 1: Check Current Token</h3>
            <button onclick="checkCurrentToken()">Check Current Token</button>
            <div id="tokenStatus"></div>
        </div>

        <div class="step">
            <h3>Step 2: Get Fresh Token</h3>
            <button onclick="getFreshToken()" id="refreshBtn">Get Fresh Token</button>
            <div id="refreshStatus"></div>
        </div>

        <div class="step">
            <h3>Step 3: Test AuthContext</h3>
            <button onclick="testAuthContext()" id="testBtn" disabled>Test AuthContext</button>
            <div id="authStatus"></div>
        </div>

        <div class="step">
            <h3>Step 4: Instructions</h3>
            <div class="info">
                <strong>After getting a fresh token:</strong>
                <ol>
                    <li>The AuthContext error should be resolved</li>
                    <li>You can now use any page that depends on authentication</li>
                    <li>The token will be valid for 1 hour</li>
                    <li>If you see the error again, return to this page and refresh the token</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function checkCurrentToken() {
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                showStatus('tokenStatus', '❌ No token found in localStorage. You need to get a fresh token.', 'error');
                return;
            }

            try {
                const tokenData = JSON.parse(atob(token));
                const expiryTime = new Date(tokenData.exp * 1000);
                const isExpired = tokenData.exp < (Date.now() / 1000);
                const timeLeft = Math.max(0, Math.floor((tokenData.exp - Date.now() / 1000) / 60));
                
                if (isExpired) {
                    showStatus('tokenStatus', `
                        ❌ <strong>Token is EXPIRED</strong><br>
                        Expired: ${expiryTime.toLocaleString()}<br>
                        This is why you're getting "Invalid token" errors.
                    `, 'error');
                } else {
                    showStatus('tokenStatus', `
                        ✅ <strong>Token is valid</strong><br>
                        User: ${tokenData.email}<br>
                        Expires: ${expiryTime.toLocaleString()}<br>
                        Time left: ${timeLeft} minutes
                    `, 'success');
                }
            } catch (error) {
                showStatus('tokenStatus', `❌ Invalid token format: ${error.message}`, 'error');
            }
        }

        async function getFreshToken() {
            const refreshBtn = document.getElementById('refreshBtn');
            const testBtn = document.getElementById('testBtn');
            
            refreshBtn.disabled = true;
            refreshBtn.textContent = 'Getting token...';
            
            showStatus('refreshStatus', '🔄 Logging in to get fresh token...', 'info');
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('authToken', data.tokens.access_token);
                    
                    const tokenData = JSON.parse(atob(data.tokens.access_token));
                    const expiryTime = new Date(tokenData.exp * 1000);
                    
                    showStatus('refreshStatus', `
                        ✅ <strong>Fresh token obtained and stored!</strong><br>
                        User: ${data.user.name}<br>
                        Expires: ${expiryTime.toLocaleString()}<br>
                        Token: ${data.tokens.access_token.substring(0, 30)}...<br><br>
                        <em>The AuthContext error should now be resolved!</em>
                    `, 'success');
                    
                    testBtn.disabled = false;
                } else {
                    showStatus('refreshStatus', `❌ Login failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus('refreshStatus', `❌ Error: ${error.message}`, 'error');
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.textContent = 'Get Fresh Token';
            }
        }

        async function testAuthContext() {
            showStatus('authStatus', '🧪 Testing AuthContext verification...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('authStatus', '❌ No token found. Get a fresh token first.', 'error');
                return;
            }

            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.success && data.user) {
                        showStatus('authStatus', `
                            ✅ <strong>AuthContext test successful!</strong><br>
                            User: ${data.user.name} (${data.user.email})<br>
                            Role: ${data.user.role}<br>
                            Company: ${data.user.company_name}<br><br>
                            <em>🎉 The AuthContext should now work correctly in all pages!</em>
                        `, 'success');
                    } else {
                        showStatus('authStatus', `❌ Verification failed: ${data.error || 'Unknown error'}`, 'error');
                    }
                } else {
                    showStatus('authStatus', `❌ HTTP error: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('authStatus', `❌ Network error: ${error.message}`, 'error');
            }
        }

        // Auto-check token on page load
        window.onload = function() {
            checkCurrentToken();
        };
    </script>
</body>
</html>