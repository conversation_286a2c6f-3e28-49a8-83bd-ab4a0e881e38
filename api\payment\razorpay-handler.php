<?php
/**
 * Razorpay Payment Handler
 * Handles order creation, payment verification, and subscription management
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../db-config.php';

try {
    $user = getCurrentUser();
    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authentication required']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    // Extract action from path
    $action = $pathParts[3] ?? '';

    switch ($method) {
        case 'POST':
            if ($action === 'create-order') {
                createRazorpayOrder($user);
            } elseif ($action === 'verify') {
                verifyRazorpayPayment($user);
            } elseif ($action === 'webhook') {
                handleRazorpayWebhook();
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log('Razorpay Handler Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function createRazorpayOrder($user) {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        return;
    }
    
    $amount = $input['amount'] ?? 0; // Amount in paise
    $currency = $input['currency'] ?? 'INR';
    $planId = $input['plan_id'] ?? '';
    $billingCycle = $input['billing_cycle'] ?? 'monthly';
    
    if ($amount <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid amount']);
        return;
    }
    
    // Get Razorpay credentials from system settings
    $razorpayKeyId = getSystemSetting('razorpay_key_id');
    $razorpayKeySecret = getSystemSetting('razorpay_key_secret');
    
    if (!$razorpayKeyId || !$razorpayKeySecret) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Payment gateway not configured']);
        return;
    }
    
    try {
        // Create order using Razorpay API
        $orderData = [
            'amount' => $amount,
            'currency' => $currency,
            'receipt' => 'order_' . time() . '_' . $user['object_id'],
            'notes' => [
                'user_id' => $user['object_id'],
                'company_id' => $user['company_id'],
                'plan_id' => $planId,
                'billing_cycle' => $billingCycle
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.razorpay.com/v1/orders');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
        curl_setopt($ch, CURLOPT_USERPWD, $razorpayKeyId . ':' . $razorpayKeySecret);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('Failed to create Razorpay order: ' . $response);
        }
        
        $orderResponse = json_decode($response, true);
        
        if (!$orderResponse || !isset($orderResponse['id'])) {
            throw new Exception('Invalid response from Razorpay');
        }
        
        // Store order in database
        $transactionId = 'txn_' . time() . '_' . rand(100, 999);
        $stmt = $conn->prepare("
            INSERT INTO payment_transactions (
                object_id, subscription_id, company_id, user_id, transaction_type,
                amount, currency, status, payment_gateway, gateway_order_id,
                gateway_response, description, created_at
            ) VALUES (?, '', ?, ?, 'subscription', ?, ?, 'pending', 'razorpay', ?, ?, ?, NOW())
        ");
        
        $amountInRupees = $amount / 100;
        $description = "Subscription payment for plan: $planId ($billingCycle)";
        $gatewayResponse = json_encode($orderResponse);
        
        $stmt->bind_param("sssdssss",
            $transactionId,
            $user['company_id'],
            $user['object_id'],
            $amountInRupees,
            $currency,
            $orderResponse['id'],
            $gatewayResponse,
            $description
        );
        
        $stmt->execute();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'order_id' => $orderResponse['id'],
                'amount' => $orderResponse['amount'],
                'currency' => $orderResponse['currency'],
                'razorpay_key_id' => $razorpayKeyId,
                'transaction_id' => $transactionId
            ]
        ]);
        
    } catch (Exception $e) {
        error_log('Razorpay order creation error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to create payment order']);
    }
}

function verifyRazorpayPayment($user) {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        return;
    }
    
    $razorpayOrderId = $input['razorpay_order_id'] ?? '';
    $razorpayPaymentId = $input['razorpay_payment_id'] ?? '';
    $razorpaySignature = $input['razorpay_signature'] ?? '';
    $orderId = $input['order_id'] ?? '';
    
    if (!$razorpayOrderId || !$razorpayPaymentId || !$razorpaySignature) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Missing payment verification data']);
        return;
    }
    
    // Get Razorpay secret
    $razorpayKeySecret = getSystemSetting('razorpay_key_secret');
    
    if (!$razorpayKeySecret) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Payment gateway not configured']);
        return;
    }
    
    try {
        // Verify signature
        $expectedSignature = hash_hmac('sha256', $razorpayOrderId . '|' . $razorpayPaymentId, $razorpayKeySecret);
        
        if (!hash_equals($expectedSignature, $razorpaySignature)) {
            throw new Exception('Invalid payment signature');
        }
        
        $conn->begin_transaction();
        
        // Update transaction status
        $stmt = $conn->prepare("
            UPDATE payment_transactions SET 
                status = 'completed',
                gateway_payment_id = ?,
                gateway_signature = ?,
                processed_at = NOW(),
                updated_at = NOW()
            WHERE gateway_order_id = ? AND user_id = ?
        ");
        
        $stmt->bind_param("ssss", $razorpayPaymentId, $razorpaySignature, $razorpayOrderId, $user['object_id']);
        $stmt->execute();
        
        // Get transaction details
        $stmt = $conn->prepare("
            SELECT * FROM payment_transactions 
            WHERE gateway_order_id = ? AND user_id = ?
        ");
        $stmt->bind_param("ss", $razorpayOrderId, $user['object_id']);
        $stmt->execute();
        $transaction = $stmt->get_result()->fetch_assoc();
        
        if (!$transaction) {
            throw new Exception('Transaction not found');
        }
        
        // Parse gateway response to get plan details
        $gatewayResponse = json_decode($transaction['gateway_response'], true);
        $notes = $gatewayResponse['notes'] ?? [];
        $planId = $notes['plan_id'] ?? 'basic';
        $billingCycle = $notes['billing_cycle'] ?? 'monthly';
        
        // Update or create subscription
        $nextBilling = $billingCycle === 'yearly' ? 
            date('Y-m-d H:i:s', strtotime('+1 year')) : 
            date('Y-m-d H:i:s', strtotime('+1 month'));
        
        // Check if user has existing subscription
        $subStmt = $conn->prepare("
            SELECT object_id FROM subscriptions 
            WHERE user_id = ? AND status IN ('trial', 'active')
            ORDER BY created_at DESC LIMIT 1
        ");
        $subStmt->bind_param("s", $user['object_id']);
        $subStmt->execute();
        $existingSub = $subStmt->get_result()->fetch_assoc();
        
        if ($existingSub) {
            // Update existing subscription
            $updateStmt = $conn->prepare("
                UPDATE subscriptions SET 
                    status = 'active',
                    plan_id = ?,
                    billing_cycle = ?,
                    price = ?,
                    start_date = NOW(),
                    next_billing_date = ?,
                    payment_method = 'razorpay',
                    payment_gateway = 'razorpay',
                    gateway_subscription_id = ?,
                    last_payment_date = NOW(),
                    last_payment_amount = ?,
                    updated_at = NOW()
                WHERE object_id = ?
            ");
            
            $updateStmt->bind_param("ssdssdds",
                $planId,
                $billingCycle,
                $transaction['amount'],
                $nextBilling,
                $razorpayPaymentId,
                $transaction['amount'],
                $existingSub['object_id']
            );
            $updateStmt->execute();
            
            $subscriptionId = $existingSub['object_id'];
        } else {
            // Create new subscription
            $subscriptionId = 'sub_' . time() . '_' . rand(100, 999);
            $createStmt = $conn->prepare("
                INSERT INTO subscriptions (
                    object_id, company_id, user_id, plan_id, plan_name, status,
                    billing_cycle, price, start_date, next_billing_date,
                    payment_method, payment_gateway, gateway_subscription_id,
                    last_payment_date, last_payment_amount, created_at
                ) VALUES (?, ?, ?, ?, ?, 'active', ?, ?, NOW(), ?, 'razorpay', 'razorpay', ?, NOW(), ?, NOW())
            ");
            
            // Get plan name
            $planStmt = $conn->prepare("SELECT name FROM pricing_plans WHERE id = ?");
            $planStmt->bind_param("s", $planId);
            $planStmt->execute();
            $planResult = $planStmt->get_result()->fetch_assoc();
            $planName = $planResult['name'] ?? $planId;
            
            $createStmt->bind_param("sssssdssd",
                $subscriptionId,
                $user['company_id'],
                $user['object_id'],
                $planId,
                $planName,
                $billingCycle,
                $transaction['amount'],
                $nextBilling,
                $razorpayPaymentId,
                $transaction['amount']
            );
            $createStmt->execute();
        }
        
        // Update transaction with subscription ID
        $updateTxnStmt = $conn->prepare("
            UPDATE payment_transactions SET subscription_id = ? WHERE object_id = ?
        ");
        $updateTxnStmt->bind_param("ss", $subscriptionId, $transaction['object_id']);
        $updateTxnStmt->execute();
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment verified successfully',
            'data' => [
                'transaction_id' => $transaction['object_id'],
                'subscription_id' => $subscriptionId,
                'payment_id' => $razorpayPaymentId,
                'amount' => $transaction['amount'],
                'plan_id' => $planId,
                'billing_cycle' => $billingCycle,
                'next_billing_date' => $nextBilling
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log('Payment verification error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Payment verification failed']);
    }
}

function getSystemSetting($key) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    return $result ? $result['setting_value'] : null;
}

function handleRazorpayWebhook() {
    // TODO: Implement webhook handling for automatic payment status updates
    echo json_encode(['success' => true, 'message' => 'Webhook received']);
}
?>
