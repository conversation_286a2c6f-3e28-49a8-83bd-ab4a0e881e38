# Production .htaccess - Business Management SaaS

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Set proper MIME types for JavaScript files
<FilesMatch "\.(js)$">
    Header set Content-Type "application/javascript; charset=utf-8"
</FilesMatch>

# Set proper MIME types for CSS files
<FilesMatch "\.(css)$">
    Header set Content-Type "text/css; charset=utf-8"
</FilesMatch>

# Enable URL rewriting
RewriteEngine On

# Handle SPA routing - redirect to app.html for non-existing files
# except for existing files, directories, and specific file types
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !^/test-
RewriteCond %{REQUEST_URI} !^/debug-
RewriteCond %{REQUEST_URI} !^/comprehensive-test
RewriteCond %{REQUEST_URI} !^/components/
RewriteCond %{REQUEST_URI} !^/pages/
RewriteCond %{REQUEST_URI} !^/utils/
RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|svg|ico|pdf|doc|docx|xls|xlsx|html)$
RewriteRule ^(.*)$ app.html [QSA,L]

# HTTPS Redirect (uncomment when you have SSL)
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteCond %{HTTPS} off
#     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css application/javascript application/json
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
</IfModule>

# PHP Settings for shared hosting
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value memory_limit 128M
php_value max_execution_time 60

# Protect sensitive files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# Prevent directory browsing
Options -Indexes
