<?php
/**
 * Enhanced SaaS Migration Runner
 * Runs the enhanced database schema migration
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🚀 Starting Enhanced SaaS Migration...\n\n";

try {
    // Read the migration file
    $migrationFile = __DIR__ . '/migrations/enhanced_saas_schema.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read migration file");
    }
    
    echo "📖 Migration file loaded successfully\n";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "📝 Found " . count($statements) . " SQL statements to execute\n\n";
    
    // Execute each statement
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        if (empty(trim($statement))) continue;
        
        echo "Executing statement " . ($index + 1) . "... ";
        
        try {
            if ($conn->query($statement) === TRUE) {
                echo "✅ Success\n";
                $successCount++;
            } else {
                echo "❌ Error: " . $conn->error . "\n";
                $errorCount++;
                
                // Log the failed statement for debugging
                error_log("Failed SQL Statement: " . $statement);
                error_log("MySQL Error: " . $conn->error);
            }
        } catch (Exception $e) {
            echo "❌ Exception: " . $e->getMessage() . "\n";
            $errorCount++;
        }
    }
    
    echo "\n📊 Migration Summary:\n";
    echo "✅ Successful statements: $successCount\n";
    echo "❌ Failed statements: $errorCount\n";
    
    if ($errorCount === 0) {
        echo "\n🎉 Migration completed successfully!\n";
        
        // Verify key tables were created
        echo "\n🔍 Verifying table creation:\n";
        
        $tables = [
            'business_types',
            'pricing_plans', 
            'subscriptions',
            'payment_transactions',
            'system_settings',
            'policy_pages',
            'audit_logs'
        ];
        
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "✅ Table '$table' exists\n";
            } else {
                echo "❌ Table '$table' missing\n";
            }
        }
        
        // Check if data was inserted
        echo "\n📊 Checking seed data:\n";
        
        $result = $conn->query("SELECT COUNT(*) as count FROM business_types");
        $count = $result ? $result->fetch_assoc()['count'] : 0;
        echo "📋 Business types: $count\n";
        
        $result = $conn->query("SELECT COUNT(*) as count FROM pricing_plans");
        $count = $result ? $result->fetch_assoc()['count'] : 0;
        echo "💰 Pricing plans: $count\n";
        
        $result = $conn->query("SELECT COUNT(*) as count FROM system_settings");
        $count = $result ? $result->fetch_assoc()['count'] : 0;
        echo "⚙️ System settings: $count\n";
        
        $result = $conn->query("SELECT COUNT(*) as count FROM policy_pages");
        $count = $result ? $result->fetch_assoc()['count'] : 0;
        echo "📄 Policy pages: $count\n";
        
    } else {
        echo "\n⚠️ Migration completed with errors. Please check the logs.\n";
    }
    
} catch (Exception $e) {
    echo "💥 Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Migration process completed.\n";
?>
