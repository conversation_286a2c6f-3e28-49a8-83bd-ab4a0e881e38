import { User<PERSON><PERSON> } from '@prisma/client'
import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      role: UserRole
      companyId?: string
      company?: {
        id: string
        name: string
        businessType?: string
      }
    }
  }

  interface User {
    role: UserRole
    companyId?: string
    company?: {
      id: string
      name: string
      businessType?: string
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    companyId?: string
    company?: {
      id: string
      name: string
      businessType?: string
    }
  }
}
