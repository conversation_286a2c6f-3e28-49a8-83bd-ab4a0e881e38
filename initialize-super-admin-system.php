<?php
/**
 * Initialize Complete Super Admin System
 * This script creates all necessary tables and default data for the enhanced SaaS platform
 */

require_once 'api/config/database.php';

header('Content-Type: text/plain');

echo "=== Initializing Complete Super Admin System ===\n\n";

try {
    $conn = Database::getConnection();
    
    // 1. Create Business Type Templates Table
    echo "1. Creating business_type_templates table...\n";
    $templatesSql = "
    CREATE TABLE IF NOT EXISTS business_type_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        business_type_id VARCHAR(50) NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        categories JSON,
        subcategories JSON,
        items JSON,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_business_type (business_type_id),
        FOREIGN KEY (business_type_id) REFERENCES business_types(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($templatesSql)) {
        echo "✅ business_type_templates table created\n";
    } else {
        echo "❌ Error creating business_type_templates table: " . $conn->error . "\n";
    }
    
    // 2. Create Payment Orders Table
    echo "\n2. Creating payment_orders table...\n";
    $paymentOrdersSql = "
    CREATE TABLE IF NOT EXISTS payment_orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_id VARCHAR(100) UNIQUE NOT NULL,
        razorpay_order_id VARCHAR(100),
        user_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        plan_id VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
        status ENUM('created', 'paid', 'failed', 'cancelled') DEFAULT 'created',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_order_id (order_id),
        INDEX idx_user_id (user_id),
        INDEX idx_company_id (company_id)
    )";
    
    if ($conn->query($paymentOrdersSql)) {
        echo "✅ payment_orders table created\n";
    } else {
        echo "❌ Error creating payment_orders table: " . $conn->error . "\n";
    }
    
    // 3. Create Payment Transactions Table
    echo "\n3. Creating payment_transactions table...\n";
    $transactionsSql = "
    CREATE TABLE IF NOT EXISTS payment_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transaction_id VARCHAR(100) UNIQUE NOT NULL,
        order_id VARCHAR(100) NOT NULL,
        payment_id VARCHAR(100) NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        status ENUM('success', 'failed', 'pending') DEFAULT 'pending',
        payment_method VARCHAR(50) DEFAULT 'razorpay',
        gateway_response TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_order_id (order_id),
        INDEX idx_user_id (user_id)
    )";
    
    if ($conn->query($transactionsSql)) {
        echo "✅ payment_transactions table created\n";
    } else {
        echo "❌ Error creating payment_transactions table: " . $conn->error . "\n";
    }
    
    // 4. Create Invoices Table
    echo "\n4. Creating invoices table...\n";
    $invoicesSql = "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id VARCHAR(100) UNIQUE NOT NULL,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        order_id VARCHAR(100) NOT NULL,
        transaction_id VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        status ENUM('paid', 'pending', 'cancelled') DEFAULT 'pending',
        billing_cycle VARCHAR(20),
        plan_name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_invoice_id (invoice_id),
        INDEX idx_user_id (user_id),
        INDEX idx_company_id (company_id)
    )";
    
    if ($conn->query($invoicesSql)) {
        echo "✅ invoices table created\n";
    } else {
        echo "❌ Error creating invoices table: " . $conn->error . "\n";
    }
    
    // 5. Update Plans Table with Enhanced Fields
    echo "\n5. Updating plans table with enhanced fields...\n";
    $updatePlansSql = "
    ALTER TABLE plans 
    ADD COLUMN IF NOT EXISTS yearly_savings DECIMAL(10,2) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS is_trial_available BOOLEAN DEFAULT TRUE,
    ADD COLUMN IF NOT EXISTS is_visible BOOLEAN DEFAULT TRUE,
    ADD COLUMN IF NOT EXISTS sort_order INT DEFAULT 0,
    ADD COLUMN IF NOT EXISTS limits_data JSON,
    ADD COLUMN IF NOT EXISTS business_types JSON
    ";
    
    if ($conn->query($updatePlansSql)) {
        echo "✅ plans table updated with enhanced fields\n";
    } else {
        echo "❌ Error updating plans table: " . $conn->error . "\n";
    }
    
    // 6. Create Default Business Type Templates
    echo "\n6. Creating default business type templates...\n";
    
    $defaultTemplates = [
        [
            'business_type_id' => 'jewellery',
            'name' => 'Jewellery Business Default',
            'description' => 'Default template for jewellery businesses',
            'categories' => ['Gold Jewellery', 'Silver Jewellery', 'Diamond Jewellery', 'Repairs', 'Custom Orders'],
            'subcategories' => [
                'Gold Jewellery' => ['Rings', 'Necklaces', 'Earrings', 'Bracelets', 'Chains'],
                'Silver Jewellery' => ['Rings', 'Necklaces', 'Earrings', 'Bracelets'],
                'Diamond Jewellery' => ['Engagement Rings', 'Wedding Bands', 'Pendants', 'Earrings'],
                'Repairs' => ['Ring Sizing', 'Chain Repair', 'Stone Setting', 'Polishing'],
                'Custom Orders' => ['Design Consultation', 'CAD Design', 'Prototype', 'Final Product']
            ],
            'items' => [
                'Gold Jewellery.Rings' => ['Wedding Ring', 'Engagement Ring', 'Fashion Ring'],
                'Silver Jewellery.Necklaces' => ['Chain Necklace', 'Pendant Necklace', 'Statement Necklace'],
                'Repairs.Ring Sizing' => ['Size Up', 'Size Down', 'Reshaping']
            ],
            'is_default' => true
        ],
        [
            'business_type_id' => 'retail',
            'name' => 'Retail Business Default',
            'description' => 'Default template for retail businesses',
            'categories' => ['Electronics', 'Clothing', 'Accessories', 'Home & Garden', 'Books'],
            'subcategories' => [
                'Electronics' => ['Smartphones', 'Laptops', 'Tablets', 'Accessories'],
                'Clothing' => ['Men\'s Wear', 'Women\'s Wear', 'Kids Wear', 'Footwear'],
                'Accessories' => ['Bags', 'Watches', 'Jewelry', 'Sunglasses'],
                'Home & Garden' => ['Furniture', 'Decor', 'Kitchen', 'Garden Tools'],
                'Books' => ['Fiction', 'Non-Fiction', 'Educational', 'Children\'s Books']
            ],
            'items' => [
                'Electronics.Smartphones' => ['iPhone', 'Samsung Galaxy', 'OnePlus', 'Xiaomi'],
                'Clothing.Men\'s Wear' => ['Shirts', 'T-Shirts', 'Jeans', 'Formal Wear']
            ],
            'is_default' => true
        ],
        [
            'business_type_id' => 'education',
            'name' => 'Education Services Default',
            'description' => 'Default template for educational institutions',
            'categories' => ['Academic Courses', 'Vocational Training', 'Certifications', 'Workshops'],
            'subcategories' => [
                'Academic Courses' => ['Mathematics', 'Science', 'English', 'History'],
                'Vocational Training' => ['Computer Skills', 'Language Learning', 'Professional Development'],
                'Certifications' => ['IT Certifications', 'Language Certificates', 'Professional Licenses'],
                'Workshops' => ['Skill Development', 'Career Guidance', 'Personal Development']
            ],
            'items' => [
                'Academic Courses.Mathematics' => ['Algebra', 'Geometry', 'Calculus', 'Statistics'],
                'Vocational Training.Computer Skills' => ['Basic Computing', 'Microsoft Office', 'Programming', 'Web Design']
            ],
            'is_default' => true
        ]
    ];
    
    $templateStmt = $conn->prepare("
        INSERT INTO business_type_templates (business_type_id, name, description, categories, subcategories, items, is_default)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        description = VALUES(description),
        categories = VALUES(categories),
        subcategories = VALUES(subcategories),
        items = VALUES(items),
        is_default = VALUES(is_default)
    ");
    
    foreach ($defaultTemplates as $template) {
        $categoriesJson = json_encode($template['categories']);
        $subcategoriesJson = json_encode($template['subcategories']);
        $itemsJson = json_encode($template['items']);
        
        $templateStmt->bind_param("ssssssi", 
            $template['business_type_id'],
            $template['name'],
            $template['description'],
            $categoriesJson,
            $subcategoriesJson,
            $itemsJson,
            $template['is_default']
        );
        
        if ($templateStmt->execute()) {
            echo "✅ Template '{$template['name']}' created\n";
        } else {
            echo "❌ Failed to create template '{$template['name']}': " . $templateStmt->error . "\n";
        }
    }
    
    // 7. Update Plans with Enhanced Data
    echo "\n7. Updating plans with enhanced data...\n";
    
    $enhancedPlans = [
        [
            'id' => 'basic',
            'yearly_savings' => 1000,
            'limits_data' => ['max_users' => 5, 'max_leads' => 1000, 'max_storage' => 1024],
            'business_types' => ['retail', 'education', 'consulting']
        ],
        [
            'id' => 'professional',
            'yearly_savings' => 2000,
            'limits_data' => ['max_users' => 25, 'max_leads' => 10000, 'max_storage' => 10240],
            'business_types' => ['jewellery', 'healthcare', 'manufacturing', 'restaurant']
        ],
        [
            'id' => 'enterprise',
            'yearly_savings' => 5000,
            'limits_data' => ['max_users' => -1, 'max_leads' => -1, 'max_storage' => -1],
            'business_types' => ['automotive', 'manufacturing', 'healthcare']
        ]
    ];
    
    $updatePlanStmt = $conn->prepare("
        UPDATE plans 
        SET yearly_savings = ?, limits_data = ?, business_types = ?, is_visible = 1, sort_order = ?
        WHERE id = ?
    ");
    
    foreach ($enhancedPlans as $index => $plan) {
        $limitsJson = json_encode($plan['limits_data']);
        $businessTypesJson = json_encode($plan['business_types']);
        $sortOrder = $index + 1;
        
        $updatePlanStmt->bind_param("dssis", 
            $plan['yearly_savings'],
            $limitsJson,
            $businessTypesJson,
            $sortOrder,
            $plan['id']
        );
        
        if ($updatePlanStmt->execute()) {
            echo "✅ Plan '{$plan['id']}' updated with enhanced data\n";
        } else {
            echo "❌ Failed to update plan '{$plan['id']}': " . $updatePlanStmt->error . "\n";
        }
    }
    
    // 8. Verification
    echo "\n--- System Verification ---\n";
    
    $tables = ['business_type_templates', 'payment_orders', 'payment_transactions', 'invoices'];
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "✅ Table '$table': {$row['count']} records\n";
        } else {
            echo "❌ Error checking table '$table'\n";
        }
    }
    
    // Check business types
    $btResult = $conn->query("SELECT COUNT(*) as count FROM business_types WHERE is_active = 1");
    $btRow = $btResult->fetch_assoc();
    echo "✅ Active business types: {$btRow['count']}\n";
    
    // Check templates
    $templateResult = $conn->query("SELECT COUNT(*) as count FROM business_type_templates");
    $templateRow = $templateResult->fetch_assoc();
    echo "✅ Business type templates: {$templateRow['count']}\n";
    
    echo "\n=== Super Admin System Initialization Complete ===\n";
    echo "🎉 Your SaaS platform is now ready with:\n";
    echo "   ✅ Complete Plan Management CRUD\n";
    echo "   ✅ Payment Gateway Integration (Razorpay)\n";
    echo "   ✅ Company/Subscriber Management\n";
    echo "   ✅ Business Type Template System\n";
    echo "   ✅ Enhanced Admin CMS Panel\n";
    echo "   ✅ Auto-updating Policy Pages\n";
    echo "   ✅ Trial & Paid Plan Flow Management\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    error_log("Super Admin System Initialization Error: " . $e->getMessage());
}
?>
