<?php
/**
 * Simple insert for pricing plans
 */

require_once __DIR__ . '/../api/db-config.php';

echo "💰 Inserting pricing plans (simple approach)...\n\n";

try {
    // Insert plans using direct SQL
    $plans = [
        "INSERT INTO pricing_plans (id, name, description, short_description, price_monthly, price_yearly, trial_days, features, limits_data, business_types, is_trial_available, is_visible, is_popular, sort_order) VALUES 
        ('trial', 'Free Trial', 'Try all features free for 14 days', 'Full access trial', 0.00, 0.00, 14, 
         '[\"Customer Management\", \"Invoice Generation\", \"Quotation Management\", \"Contract Management\", \"Lead Tracking\", \"Basic Analytics\", \"Email Notifications\", \"Data Export\"]',
         '{\"max_customers\": 50, \"max_invoices\": 20, \"max_quotations\": 20, \"max_users\": 2, \"storage_gb\": 1}',
         '[\"jewellery\", \"retail\", \"education\", \"healthcare\", \"consulting\", \"manufacturing\"]',
         0, 0, 0, 0)
        ON DUPLICATE KEY UPDATE 
        name = VALUES(name), description = VALUES(description), short_description = VALUES(short_description), 
        price_monthly = VALUES(price_monthly), price_yearly = VALUES(price_yearly), trial_days = VALUES(trial_days),
        features = VALUES(features), limits_data = VALUES(limits_data), business_types = VALUES(business_types),
        is_trial_available = VALUES(is_trial_available), is_visible = VALUES(is_visible), is_popular = VALUES(is_popular), sort_order = VALUES(sort_order)",
        
        "INSERT INTO pricing_plans (id, name, description, short_description, price_monthly, price_yearly, trial_days, features, limits_data, business_types, is_trial_available, is_visible, is_popular, sort_order) VALUES 
        ('basic', 'Business Plan', 'Complete business management solution with all essential features', 'Everything you need to run your business', 500.00, 5000.00, 14, 
         '[\"Customer Management\", \"Invoice Generation\", \"Quotation Management\", \"Contract Management\", \"Lead Tracking\", \"Business Analytics\", \"Email Notifications\", \"Data Export\", \"Multi-user Access\", \"24/7 Support\", \"Custom Branding\", \"API Access\"]',
         '{\"max_customers\": -1, \"max_invoices\": -1, \"max_quotations\": -1, \"max_users\": 10, \"storage_gb\": 50}',
         '[\"jewellery\", \"retail\", \"education\", \"healthcare\", \"consulting\", \"manufacturing\"]',
         1, 1, 1, 1)
        ON DUPLICATE KEY UPDATE 
        name = VALUES(name), description = VALUES(description), short_description = VALUES(short_description), 
        price_monthly = VALUES(price_monthly), price_yearly = VALUES(price_yearly), trial_days = VALUES(trial_days),
        features = VALUES(features), limits_data = VALUES(limits_data), business_types = VALUES(business_types),
        is_trial_available = VALUES(is_trial_available), is_visible = VALUES(is_visible), is_popular = VALUES(is_popular), sort_order = VALUES(sort_order)",
        
        "INSERT INTO pricing_plans (id, name, description, short_description, price_monthly, price_yearly, trial_days, features, limits_data, business_types, is_trial_available, is_visible, is_popular, sort_order) VALUES 
        ('premium', 'Premium Plan', 'Advanced features for growing businesses', 'Scale your business with premium tools', 1000.00, 10000.00, 14, 
         '[\"Everything in Business Plan\", \"Advanced Analytics\", \"Custom Reports\", \"Priority Support\", \"White Label\", \"Advanced Integrations\", \"Bulk Operations\", \"Advanced Security\"]',
         '{\"max_customers\": -1, \"max_invoices\": -1, \"max_quotations\": -1, \"max_users\": 25, \"storage_gb\": 100}',
         '[\"jewellery\", \"retail\", \"education\", \"healthcare\", \"consulting\", \"manufacturing\"]',
         1, 1, 0, 2)
        ON DUPLICATE KEY UPDATE 
        name = VALUES(name), description = VALUES(description), short_description = VALUES(short_description), 
        price_monthly = VALUES(price_monthly), price_yearly = VALUES(price_yearly), trial_days = VALUES(trial_days),
        features = VALUES(features), limits_data = VALUES(limits_data), business_types = VALUES(business_types),
        is_trial_available = VALUES(is_trial_available), is_visible = VALUES(is_visible), is_popular = VALUES(is_popular), sort_order = VALUES(sort_order)"
    ];
    
    foreach ($plans as $index => $sql) {
        echo "Inserting plan " . ($index + 1) . "...\n";
        if ($conn->query($sql) === TRUE) {
            echo "✅ Success\n";
        } else {
            echo "❌ Error: " . $conn->error . "\n";
        }
    }
    
    // Verify the data
    echo "\n🔍 Verifying inserted data:\n";
    $result = $conn->query("SELECT id, name, price_monthly, price_yearly, is_visible FROM pricing_plans ORDER BY sort_order");
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "📋 Plan: {$row['id']} - {$row['name']} - Monthly: ₹{$row['price_monthly']} - Yearly: ₹{$row['price_yearly']} - Visible: " . ($row['is_visible'] ? 'Yes' : 'No') . "\n";
        }
    }
    
    echo "\n🎉 Pricing plans inserted successfully!\n";
    
} catch (Exception $e) {
    echo "💥 Insert failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Insert completed.\n";
?>
