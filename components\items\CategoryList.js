function CategoryList({ onCategoryClick, onSubcategoryClick }) {
    try {
        const [categories, setCategories] = React.useState([]);
        const [subcategories, setSubcategories] = React.useState({});
        const [expandedCategories, setExpandedCategories] = React.useState({});
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            fetchCategories();
        }, []);

        const fetchCategories = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');

                // Fetch categories
                const response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/item_category`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setCategories(data.items || []);

                    // Fetch subcategories for each category
                    const subcategoriesMap = {};
                    for (const category of (data.items || [])) {
                        const subcategoryResponse = await fetch(`${window.APP_CONFIG.API_BASE_URL}/item_subcategory:${category.objectId}`, {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (subcategoryResponse.ok) {
                            const subcategoryData = await subcategoryResponse.json();
                            subcategoriesMap[category.objectId] = subcategoryData.items || [];
                        } else {
                            subcategoriesMap[category.objectId] = [];
                        }
                    }
                    setSubcategories(subcategoriesMap);
                } else {
                    throw new Error('Failed to fetch categories');
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
            } finally {
                setLoading(false);
            }
        };

        const toggleCategory = (categoryId) => {
            setExpandedCategories(prev => ({
                ...prev,
                [categoryId]: !prev[categoryId]
            }));
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-32">
                    <i className="fas fa-spinner fa-spin text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="category-list" className="space-y-4">
                {categories.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No categories found</p>
                ) : (
                    categories.map(category => (
                        <div key={category.objectId} className="border rounded-lg overflow-hidden">
                            <div 
                                className="flex justify-between items-center p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
                                onClick={() => toggleCategory(category.objectId)}
                            >
                                <div className="flex items-center space-x-2" onClick={(e) => {
                                    e.stopPropagation();
                                    onCategoryClick(category);
                                }}>
                                    <i className="fas fa-folder text-yellow-500"></i>
                                    <span className="font-medium">{category.objectData.name}</span>
                                </div>
                                <div className="flex items-center">
                                    <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full mr-3">
                                        {subcategories[category.objectId] && subcategories[category.objectId].length ? subcategories[category.objectId].length : 0} subcategories
                                    </span>
                                    <i className={`fas fa-chevron-${expandedCategories[category.objectId] ? 'up' : 'down'} text-gray-500`}></i>
                                </div>
                            </div>
                            
                            {expandedCategories[category.objectId] && (
                                <div className="p-4 bg-white border-t">
                                    {subcategories[category.objectId] && subcategories[category.objectId].length > 0 ? (
                                        <ul className="space-y-2">
                                            {subcategories[category.objectId].map(subcategory => (
                                                <li 
                                                    key={subcategory.objectId}
                                                    className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer"
                                                    onClick={() => onSubcategoryClick(subcategory, category.objectId)}
                                                >
                                                    <i className="fas fa-tag text-blue-500"></i>
                                                    <span>{subcategory.objectData.name}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    ) : (
                                        <p className="text-gray-500 text-sm">No subcategories</p>
                                    )}
                                    
                                    <button 
                                        className="mt-4 text-sm text-blue-600 hover:text-blue-800 flex items-center"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onSubcategoryClick(null, category.objectId);
                                        }}
                                    >
                                        <i className="fas fa-plus mr-1"></i>
                                        Add Subcategory
                                    </button>
                                </div>
                            )}
                        </div>
                    ))
                )}
            </div>
        );
    } catch (error) {
        console.error('CategoryList component error:', error);
        reportError(error);
        return null;
    }
}
