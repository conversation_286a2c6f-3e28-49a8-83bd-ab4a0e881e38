// Utility to update subscription plans with new requirements
async function updateSubscriptionPlans() {
    try {
        const authContext = window.AuthContext?.getAuthContext?.() || 
                           JSON.parse(localStorage.getItem('authContext') || '{}');
        
        if (!authContext.token) {
            throw new Error('No authentication token found');
        }

        // Define the new plan structure
        const newPlans = [
            {
                id: 'trial',
                name: 'Trial Plan',
                description: 'Free trial for 5 days with full access to all features',
                short_description: 'Free for 5 Days',
                price_monthly: 0,
                price_yearly: 0,
                trial_days: 5,
                features: [
                    'Customer Management',
                    'Invoice Generation',
                    'Quotation Management',
                    'Contract Management',
                    'Leads Management',
                    'Inventory Management',
                    'Email Notifications',
                    'Custom Branding'
                ],
                limits_data: {
                    customers: 50,
                    invoices: 25,
                    quotations: 25,
                    contracts: 10,
                    leads: 50,
                    inventory_items: 100,
                    storage_mb: 500
                },
                business_types: [], // Available for all business types
                is_trial_available: true,
                is_visible: true,
                is_popular: false,
                sort_order: 1
            },
            {
                id: 'business',
                name: 'Business Plan',
                description: 'Complete business management solution with unlimited access to all features',
                short_description: 'Full Business Solution',
                price_monthly: 500,
                price_yearly: 5000,
                trial_days: 5,
                features: [
                    'Customer Management',
                    'Invoice Generation',
                    'Quotation Management',
                    'Contract Management',
                    'Leads Management',
                    'Inventory Management',
                    'Email Notifications',
                    'Custom Branding',
                    'Priority Support',
                    'Advanced Analytics',
                    'Data Export',
                    'API Access'
                ],
                limits_data: {
                    customers: -1, // Unlimited
                    invoices: -1,
                    quotations: -1,
                    contracts: -1,
                    leads: -1,
                    inventory_items: -1,
                    storage_mb: -1
                },
                business_types: [], // Available for all business types
                is_trial_available: true,
                is_visible: true,
                is_popular: true,
                sort_order: 2
            }
        ];

        // Update each plan
        for (const plan of newPlans) {
            console.log(`Updating plan: ${plan.name}`);
            
            const response = await fetch('/api/super-admin/plans.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'create_or_update',
                    plan: plan
                })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log(`✅ Successfully updated ${plan.name}`);
            } else {
                console.error(`❌ Failed to update ${plan.name}:`, result.message);
            }
        }

        console.log('🎉 Plan update completed!');
        return { success: true, message: 'Plans updated successfully' };

    } catch (error) {
        console.error('Error updating plans:', error);
        return { success: false, message: error.message };
    }
}

// Function to verify plans are updated correctly
async function verifyPlanUpdates() {
    try {
        const authContext = window.AuthContext?.getAuthContext?.() || 
                           JSON.parse(localStorage.getItem('authContext') || '{}');
        
        if (!authContext.token) {
            throw new Error('No authentication token found');
        }

        const response = await fetch('/api/super-admin/plans.php', {
            headers: {
                'Authorization': `Bearer ${authContext.token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (result.success) {
            console.log('📋 Current Plans:');
            result.plans.forEach(plan => {
                console.log(`- ${plan.name}: ₹${plan.price_monthly}/month, ₹${plan.price_yearly}/year`);
                console.log(`  Features: ${plan.features.length} features`);
                console.log(`  Trial Days: ${plan.trial_days}`);
                console.log('---');
            });
            return result.plans;
        } else {
            console.error('Failed to fetch plans:', result.message);
            return [];
        }
    } catch (error) {
        console.error('Error verifying plans:', error);
        return [];
    }
}

// Make functions globally available
window.updateSubscriptionPlans = updateSubscriptionPlans;
window.verifyPlanUpdates = verifyPlanUpdates;