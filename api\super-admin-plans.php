<?php
/**
 * Super Admin Plans Management API
 * Handles CRUD operations for pricing plans
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Verify super admin access
    $user = getCurrentUser();
    if (!$user || $user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Super admin access required']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    // Extract plan ID if present
    $planId = null;
    if (count($pathParts) > 3 && $pathParts[3] !== '') {
        $planId = $pathParts[3];
    }

    switch ($method) {
        case 'GET':
            if ($planId) {
                getSinglePlan($planId);
            } else {
                getAllPlans();
            }
            break;
            
        case 'POST':
            createPlan();
            break;
            
        case 'PUT':
            if ($planId) {
                updatePlan($planId);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Plan ID required for update']);
            }
            break;
            
        case 'DELETE':
            if ($planId) {
                deletePlan($planId);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Plan ID required for deletion']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log('Super Admin Plans API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function getAllPlans() {
    global $conn;
    
    $sql = "SELECT * FROM pricing_plans ORDER BY sort_order ASC, name ASC";
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . $conn->error);
    }
    
    $plans = [];
    while ($row = $result->fetch_assoc()) {
        // Parse JSON fields
        $row['features'] = json_decode($row['features'] ?? '[]', true);
        $row['limits_data'] = json_decode($row['limits_data'] ?? '{}', true);
        $row['business_types'] = json_decode($row['business_types'] ?? '[]', true);
        
        // Convert boolean fields
        $row['is_trial_available'] = (bool)$row['is_trial_available'];
        $row['is_visible'] = (bool)$row['is_visible'];
        $row['is_popular'] = (bool)$row['is_popular'];
        $row['is_active'] = (bool)$row['is_active'];
        
        $plans[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $plans,
        'count' => count($plans)
    ]);
}

function getSinglePlan($planId) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM pricing_plans WHERE id = ?");
    $stmt->bind_param("s", $planId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // Parse JSON fields
        $row['features'] = json_decode($row['features'] ?? '[]', true);
        $row['limits_data'] = json_decode($row['limits_data'] ?? '{}', true);
        $row['business_types'] = json_decode($row['business_types'] ?? '[]', true);
        
        // Convert boolean fields
        $row['is_trial_available'] = (bool)$row['is_trial_available'];
        $row['is_visible'] = (bool)$row['is_visible'];
        $row['is_popular'] = (bool)$row['is_popular'];
        $row['is_active'] = (bool)$row['is_active'];
        
        echo json_encode([
            'success' => true,
            'data' => $row
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Plan not found'
        ]);
    }
}

function createPlan() {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        return;
    }
    
    // Validate required fields
    $required = ['id', 'name', 'price_monthly', 'price_yearly'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Field '$field' is required"]);
            return;
        }
    }
    
    // Check if plan ID already exists
    $checkStmt = $conn->prepare("SELECT id FROM pricing_plans WHERE id = ?");
    $checkStmt->bind_param("s", $input['id']);
    $checkStmt->execute();
    if ($checkStmt->get_result()->num_rows > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Plan ID already exists']);
        return;
    }
    
    $stmt = $conn->prepare("
        INSERT INTO pricing_plans (
            id, name, description, short_description, price_monthly, price_yearly, 
            trial_days, features, limits_data, business_types, is_trial_available, 
            is_visible, is_popular, sort_order
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $features = json_encode($input['features'] ?? []);
    $limits_data = json_encode($input['limits_data'] ?? []);
    $business_types = json_encode($input['business_types'] ?? []);
    
    $stmt->bind_param("ssssddisssiii",
        $input['id'],
        $input['name'],
        $input['description'] ?? '',
        $input['short_description'] ?? '',
        $input['price_monthly'],
        $input['price_yearly'],
        $input['trial_days'] ?? 14,
        $features,
        $limits_data,
        $business_types,
        $input['is_trial_available'] ?? true,
        $input['is_visible'] ?? true,
        $input['is_popular'] ?? false,
        $input['sort_order'] ?? 0
    );
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Plan created successfully',
            'data' => ['id' => $input['id']]
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create plan: ' . $stmt->error
        ]);
    }
}

function updatePlan($planId) {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        return;
    }
    
    $stmt = $conn->prepare("
        UPDATE pricing_plans SET 
            name = ?, description = ?, short_description = ?, price_monthly = ?, 
            price_yearly = ?, trial_days = ?, features = ?, limits_data = ?, 
            business_types = ?, is_trial_available = ?, is_visible = ?, 
            is_popular = ?, sort_order = ?, updated_at = NOW()
        WHERE id = ?
    ");
    
    $features = json_encode($input['features'] ?? []);
    $limits_data = json_encode($input['limits_data'] ?? []);
    $business_types = json_encode($input['business_types'] ?? []);
    
    $stmt->bind_param("sssddiisssiiis",
        $input['name'],
        $input['description'] ?? '',
        $input['short_description'] ?? '',
        $input['price_monthly'],
        $input['price_yearly'],
        $input['trial_days'] ?? 14,
        $features,
        $limits_data,
        $business_types,
        $input['is_trial_available'] ?? true,
        $input['is_visible'] ?? true,
        $input['is_popular'] ?? false,
        $input['sort_order'] ?? 0,
        $planId
    );
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Plan updated successfully'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Plan not found'
            ]);
        }
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update plan: ' . $stmt->error
        ]);
    }
}

function deletePlan($planId) {
    global $conn;
    
    // Check if plan is being used by any subscriptions
    $checkStmt = $conn->prepare("SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = ?");
    $checkStmt->bind_param("s", $planId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    $count = $result->fetch_assoc()['count'];
    
    if ($count > 0) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => "Cannot delete plan. It is currently used by $count subscription(s)."
        ]);
        return;
    }
    
    $stmt = $conn->prepare("DELETE FROM pricing_plans WHERE id = ?");
    $stmt->bind_param("s", $planId);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Plan deleted successfully'
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Plan not found'
            ]);
        }
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to delete plan: ' . $stmt->error
        ]);
    }
}
?>
