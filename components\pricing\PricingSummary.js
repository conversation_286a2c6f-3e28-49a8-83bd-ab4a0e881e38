// Pricing Summary Component
function PricingSummary({ plan, billingCycle, showComparison = true }) {
    if (!plan) return null;

    const monthlyPrice = plan.price_monthly || 0;
    const yearlyPrice = plan.price_yearly || 0;
    const currentPrice = billingCycle === 'yearly' ? yearlyPrice : monthlyPrice;
    
    // Calculate savings for yearly billing
    const yearlySavings = billingCycle === 'yearly' && monthlyPrice > 0 
        ? (monthlyPrice * 12) - yearlyPrice 
        : 0;
    
    const savingsPercentage = yearlySavings > 0 
        ? Math.round((yearlySavings / (monthlyPrice * 12)) * 100) 
        : 0;

    return (
        <div className="text-center">
            {/* Main Price Display */}
            <div className="mb-2">
                <span className="text-4xl font-bold text-gray-900">
                    ₹{currentPrice.toLocaleString()}
                </span>
                <span className="text-gray-600 ml-1">
                    /{billingCycle === 'yearly' ? 'year' : 'month'}
                </span>
            </div>

            {/* Yearly Savings Badge */}
            {billingCycle === 'yearly' && yearlySavings > 0 && (
                <div className="mb-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Save ₹{yearlySavings.toLocaleString()} ({savingsPercentage}% off)
                    </span>
                </div>
            )}

            {/* Price Comparison */}
            {showComparison && billingCycle === 'yearly' && monthlyPrice > 0 && (
                <div className="text-sm text-gray-500">
                    <span className="line-through">₹{(monthlyPrice * 12).toLocaleString()}/year</span>
                    <span className="ml-2 text-green-600 font-medium">
                        ₹{yearlyPrice.toLocaleString()}/year
                    </span>
                </div>
            )}

            {/* Monthly Equivalent for Yearly Plans */}
            {billingCycle === 'yearly' && yearlyPrice > 0 && (
                <div className="text-sm text-gray-500 mt-1">
                    ₹{Math.round(yearlyPrice / 12).toLocaleString()}/month when billed annually
                </div>
            )}

            {/* Trial Information */}
            {plan.trial_days > 0 && (
                <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                        {plan.trial_days}-day free trial
                    </span>
                </div>
            )}
        </div>
    );
}

// Enhanced Pricing Summary with More Details
function EnhancedPricingSummary({ plan, billingCycle, showComparison = true, showFeatures = false }) {
    if (!plan) return null;

    const monthlyPrice = plan.price_monthly || 0;
    const yearlyPrice = plan.price_yearly || 0;
    const currentPrice = billingCycle === 'yearly' ? yearlyPrice : monthlyPrice;
    
    const yearlySavings = billingCycle === 'yearly' && monthlyPrice > 0 
        ? (monthlyPrice * 12) - yearlyPrice 
        : 0;
    
    const savingsPercentage = yearlySavings > 0 
        ? Math.round((yearlySavings / (monthlyPrice * 12)) * 100) 
        : 0;

    return (
        <div className="text-center">
            {/* Plan Name */}
            <div className="mb-4">
                <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
                {plan.description && (
                    <p className="text-sm text-gray-600 mt-1">{plan.description}</p>
                )}
            </div>

            {/* Main Price Display */}
            <div className="mb-4">
                <div className="flex items-baseline justify-center">
                    <span className="text-5xl font-bold text-gray-900">
                        ₹{currentPrice.toLocaleString()}
                    </span>
                    <span className="text-xl text-gray-600 ml-2">
                        /{billingCycle === 'yearly' ? 'year' : 'month'}
                    </span>
                </div>

                {/* Yearly Savings */}
                {billingCycle === 'yearly' && yearlySavings > 0 && (
                    <div className="mt-2">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            🎉 Save ₹{yearlySavings.toLocaleString()} ({savingsPercentage}% off)
                        </span>
                    </div>
                )}

                {/* Monthly Equivalent */}
                {billingCycle === 'yearly' && yearlyPrice > 0 && (
                    <div className="text-sm text-gray-500 mt-2">
                        Just ₹{Math.round(yearlyPrice / 12).toLocaleString()}/month
                    </div>
                )}
            </div>

            {/* Trial and Setup Info */}
            <div className="space-y-2">
                {plan.trial_days > 0 && (
                    <div className="flex items-center justify-center text-sm text-blue-600">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        {plan.trial_days}-day free trial
                    </div>
                )}
                
                <div className="flex items-center justify-center text-sm text-gray-500">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    No setup fees
                </div>
                
                <div className="flex items-center justify-center text-sm text-gray-500">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Cancel anytime
                </div>
            </div>

            {/* Key Features Preview */}
            {showFeatures && plan.features && plan.features.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Key Features:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                        {plan.features.slice(0, 3).map((feature, index) => (
                            <li key={index} className="flex items-center">
                                <svg className="w-3 h-3 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                                {feature}
                            </li>
                        ))}
                        {plan.features.length > 3 && (
                            <li className="text-xs text-gray-500">
                                +{plan.features.length - 3} more features
                            </li>
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
}

// Compact Pricing Summary for Cards
function CompactPricingSummary({ plan, billingCycle }) {
    if (!plan) return null;

    const monthlyPrice = plan.price_monthly || 0;
    const yearlyPrice = plan.price_yearly || 0;
    const currentPrice = billingCycle === 'yearly' ? yearlyPrice : monthlyPrice;
    
    const yearlySavings = billingCycle === 'yearly' && monthlyPrice > 0 
        ? (monthlyPrice * 12) - yearlyPrice 
        : 0;

    return (
        <div className="text-center">
            <div className="flex items-baseline justify-center">
                <span className="text-3xl font-bold text-gray-900">
                    ₹{currentPrice.toLocaleString()}
                </span>
                <span className="text-gray-600 ml-1">
                    /{billingCycle === 'yearly' ? 'yr' : 'mo'}
                </span>
            </div>
            
            {billingCycle === 'yearly' && yearlySavings > 0 && (
                <div className="mt-1">
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        Save ₹{yearlySavings.toLocaleString()}
                    </span>
                </div>
            )}
        </div>
    );
}

// Make components globally available
window.PricingSummary = PricingSummary;
window.EnhancedPricingSummary = EnhancedPricingSummary;
window.CompactPricingSummary = CompactPricingSummary;
