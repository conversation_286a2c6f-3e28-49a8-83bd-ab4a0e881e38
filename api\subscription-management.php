<?php
/**
 * Enhanced Subscription Management API
 * Handles trial and paid plan lifecycle management
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/utils/ErrorHandler.php';
require_once __DIR__ . '/db-config.php';

// Initialize error handler
ErrorHandler::init();

try {
    // Get current user using the function from db-config.php
    $user = getCurrentUser();
    if (!$user) {
        ErrorHandler::handleAuthError();
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path - handle both direct calls and api.php routing
    $apiIndex = array_search('api.php', $pathParts);
    if ($apiIndex !== false) {
        // Called through api.php routing: /biz/api/api.php/subscription-management/action
        $action = isset($pathParts[$apiIndex + 3]) ? $pathParts[$apiIndex + 3] : 'current';
    } else {
        // Direct call: /biz/api/subscription-management.php/action
        $action = $pathParts[3] ?? 'current';
    }

    switch ($method) {
        case 'GET':
            if ($action === 'current') {
                getCurrentSubscription($user);
            } elseif ($action === 'trial-status') {
                getTrialStatus($user);
            } elseif ($action === 'usage') {
                getUsageStats($user);
            } else {
                getSubscriptionHistory($user);
            }
            break;
            
        case 'POST':
            if ($action === 'start-trial') {
                startTrial($user);
            } elseif ($action === 'upgrade') {
                upgradePlan($user);
            } elseif ($action === 'extend-trial') {
                extendTrial($user);
            } elseif ($action === 'cancel') {
                cancelSubscription($user);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log('Subscription Management API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function getCurrentSubscription($user) {
    global $conn;
    
    // Get user's company first
    $companyId = $user['company_id'] ?? null;
    if (!$companyId) {
        echo json_encode([
            'success' => false,
            'message' => 'No company associated with user'
        ]);
        return;
    }

    $stmt = $conn->prepare("
        SELECT s.*, 'Basic Plan' as plan_name, '[]' as features, '{}' as limits_data, 0 as price_monthly, 0 as price_yearly
        FROM subscriptions s
        WHERE s.company_id = ? AND s.status IN ('trial', 'active')
        ORDER BY s.created_at DESC
        LIMIT 1
    ");
    $stmt->bind_param("s", $companyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($subscription = $result->fetch_assoc()) {
        // Parse JSON fields
        $subscription['features'] = json_decode($subscription['features'] ?? '[]', true);
        $subscription['limits_data'] = json_decode($subscription['limits_data'] ?? '{}', true);
        $subscription['usage_data'] = json_decode($subscription['usage_data'] ?? '{}', true);
        
        // Calculate trial/subscription status
        $now = new DateTime();
        $subscription['is_trial'] = $subscription['status'] === 'trial';
        
        if ($subscription['is_trial'] && $subscription['trial_end_date']) {
            $trialEnd = new DateTime($subscription['trial_end_date']);
            $subscription['trial_days_remaining'] = max(0, $now->diff($trialEnd)->days);
            $subscription['trial_expired'] = $now > $trialEnd;
        }
        
        if ($subscription['next_billing_date']) {
            $nextBilling = new DateTime($subscription['next_billing_date']);
            $subscription['days_until_billing'] = $now->diff($nextBilling)->days;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $subscription
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'data' => null,
            'message' => 'No active subscription found'
        ]);
    }
}

function getTrialStatus($user) {
    global $conn;
    
    // Get user's company first
    $companyId = $user['company_id'] ?? null;
    if (!$companyId) {
        echo json_encode([
            'success' => false,
            'message' => 'No company associated with user'
        ]);
        return;
    }

    $stmt = $conn->prepare("
        SELECT start_date as trial_start_date, end_date as trial_end_date, 0 as trial_extended_days, status
        FROM subscriptions
        WHERE company_id = ? AND status = 'trial'
        ORDER BY created_at DESC
        LIMIT 1
    ");
    $stmt->bind_param("s", $companyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($trial = $result->fetch_assoc()) {
        $now = new DateTime();
        $trialEnd = new DateTime($trial['trial_end_date']);
        
        $status = [
            'is_trial' => true,
            'trial_start_date' => $trial['trial_start_date'],
            'trial_end_date' => $trial['trial_end_date'],
            'trial_extended_days' => $trial['trial_extended_days'],
            'days_remaining' => max(0, $now->diff($trialEnd)->days),
            'expired' => $now > $trialEnd,
            'can_extend' => $trial['trial_extended_days'] < 2 // Max 2 extensions
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $status
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'data' => ['is_trial' => false]
        ]);
    }
}

function getUsageStats($user) {
    global $conn;
    
    // Get current subscription limits
    $stmt = $conn->prepare("
        SELECT s.limits_data, s.usage_data
        FROM subscriptions s
        WHERE s.user_id = ? AND s.status IN ('trial', 'active')
        ORDER BY s.created_at DESC
        LIMIT 1
    ");
    $stmt->bind_param("s", $user['object_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($subscription = $result->fetch_assoc()) {
        $limits = json_decode($subscription['limits_data'] ?? '{}', true);
        $usage = json_decode($subscription['usage_data'] ?? '{}', true);
        
        // Get actual usage from database
        $companyId = $user['company_id'];
        
        // Count customers
        $customerCount = $conn->query("SELECT COUNT(*) as count FROM customers WHERE company_id = '$companyId'")->fetch_assoc()['count'];
        
        // Count invoices
        $invoiceCount = $conn->query("SELECT COUNT(*) as count FROM invoices WHERE company_id = '$companyId'")->fetch_assoc()['count'];
        
        // Count quotations
        $quotationCount = $conn->query("SELECT COUNT(*) as count FROM quotations WHERE company_id = '$companyId'")->fetch_assoc()['count'];
        
        // Count users
        $userCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE company_id = '$companyId'")->fetch_assoc()['count'];
        
        $actualUsage = [
            'customers' => $customerCount,
            'invoices' => $invoiceCount,
            'quotations' => $quotationCount,
            'users' => $userCount,
            'storage_mb' => 0 // TODO: Calculate actual storage usage
        ];
        
        // Calculate usage percentages
        $usageStats = [];
        foreach ($limits as $key => $limit) {
            $limitKey = str_replace('max_', '', $key);
            $currentUsage = $actualUsage[$limitKey] ?? 0;
            
            $usageStats[$limitKey] = [
                'current' => $currentUsage,
                'limit' => $limit,
                'percentage' => $limit > 0 ? min(100, ($currentUsage / $limit) * 100) : 0,
                'unlimited' => $limit === -1
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $usageStats
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No active subscription found'
        ]);
    }
}

function startTrial($user) {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $planId = $input['plan_id'] ?? 'basic';
    
    // Check if user already has a trial
    $stmt = $conn->prepare("SELECT id FROM subscriptions WHERE user_id = ? AND status = 'trial'");
    $stmt->bind_param("s", $user['object_id']);
    $stmt->execute();
    if ($stmt->get_result()->num_rows > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Trial already active']);
        return;
    }
    
    // Get plan details
    $planStmt = $conn->prepare("SELECT * FROM pricing_plans WHERE id = ?");
    $planStmt->bind_param("s", $planId);
    $planStmt->execute();
    $plan = $planStmt->get_result()->fetch_assoc();
    
    if (!$plan) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid plan']);
        return;
    }
    
    $conn->begin_transaction();
    
    try {
        // Create trial subscription
        $subscriptionId = 'sub_' . time() . '_' . rand(100, 999);
        $trialStart = date('Y-m-d H:i:s');
        $trialEnd = date('Y-m-d H:i:s', strtotime("+{$plan['trial_days']} days"));
        
        $stmt = $conn->prepare("
            INSERT INTO subscriptions (
                object_id, company_id, user_id, plan_id, plan_name, status, 
                billing_cycle, price, trial_start_date, trial_end_date,
                features, limits_data, created_at
            ) VALUES (?, ?, ?, ?, ?, 'trial', 'monthly', 0, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->bind_param("sssssssss",
            $subscriptionId,
            $user['company_id'],
            $user['object_id'],
            $planId,
            $plan['name'],
            $trialStart,
            $trialEnd,
            $plan['features'],
            $plan['limits_data']
        );
        
        $stmt->execute();
        
        // Update company subscription status
        $companyStmt = $conn->prepare("UPDATE companies SET subscription_plan = ?, subscription_expires = ? WHERE object_id = ?");
        $companyStmt->bind_param("sss", $planId, $trialEnd, $user['company_id']);
        $companyStmt->execute();
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Trial started successfully',
            'data' => [
                'subscription_id' => $subscriptionId,
                'trial_end_date' => $trialEnd,
                'trial_days' => $plan['trial_days']
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
}

function upgradePlan($user) {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $planId = $input['plan_id'] ?? '';
    $billingCycle = $input['billing_cycle'] ?? 'monthly';
    
    if (!$planId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Plan ID required']);
        return;
    }
    
    // Get plan details
    $planStmt = $conn->prepare("SELECT * FROM pricing_plans WHERE id = ?");
    $planStmt->bind_param("s", $planId);
    $planStmt->execute();
    $plan = $planStmt->get_result()->fetch_assoc();
    
    if (!$plan) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid plan']);
        return;
    }
    
    $price = $billingCycle === 'yearly' ? $plan['price_yearly'] : $plan['price_monthly'];
    $nextBilling = $billingCycle === 'yearly' ? 
        date('Y-m-d H:i:s', strtotime('+1 year')) : 
        date('Y-m-d H:i:s', strtotime('+1 month'));
    
    $conn->begin_transaction();
    
    try {
        // Update current subscription to active
        $stmt = $conn->prepare("
            UPDATE subscriptions SET 
                status = 'active',
                billing_cycle = ?,
                price = ?,
                start_date = NOW(),
                next_billing_date = ?,
                updated_at = NOW()
            WHERE user_id = ? AND status = 'trial'
        ");
        
        $stmt->bind_param("sdss", $billingCycle, $price, $nextBilling, $user['object_id']);
        $stmt->execute();
        
        // Update company subscription
        $companyStmt = $conn->prepare("UPDATE companies SET subscription_plan = ?, subscription_expires = ? WHERE object_id = ?");
        $companyStmt->bind_param("sss", $planId, $nextBilling, $user['company_id']);
        $companyStmt->execute();
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Plan upgraded successfully',
            'data' => [
                'plan_id' => $planId,
                'billing_cycle' => $billingCycle,
                'price' => $price,
                'next_billing_date' => $nextBilling
            ]
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
}
?>
