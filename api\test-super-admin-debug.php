<?php
/**
 * Test super admin endpoints
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Test URI parsing for super admin endpoints
    $testUrls = [
        '/biz/api/api.php/super-admin/dashboard',
        '/biz/api/api.php/super-admin/subscriptions/list',
        '/biz/api/api.php/super-admin/companies/list',
        '/biz/api/api.php/super-admin/users/list'
    ];
    
    $results = [];
    
    foreach ($testUrls as $url) {
        $uri = parse_url($url, PHP_URL_PATH);
        $uri = explode('/', $uri);
        
        $apiIndex = array_search('api.php', $uri);
        $endpoint = isset($uri[$apiIndex + 1]) ? $uri[$apiIndex + 1] : null;
        $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'dashboard';
        $subAction = isset($uri[$apiIndex + 3]) ? $uri[$apiIndex + 3] : null;
        
        $results[] = [
            'url' => $url,
            'uri_parts' => $uri,
            'api_index' => $apiIndex,
            'endpoint' => $endpoint,
            'action' => $action,
            'sub_action' => $subAction
        ];
    }
    
    // Check if subscriptions table exists
    $checkTableSql = "SHOW TABLES LIKE 'subscriptions'";
    $result = $conn->query($checkTableSql);
    $subscriptionsTableExists = $result && $result->num_rows > 0;
    
    echo json_encode([
        'success' => true,
        'uri_parsing_tests' => $results,
        'subscriptions_table_exists' => $subscriptionsTableExists,
        'message' => 'URI parsing test completed'
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_PRETTY_PRINT);
}
?>
