function Reports() {
    try {
        return (
            <div data-name="reports-page">
                <h1 className="text-2xl font-bold mb-6">Reports & Analytics</h1>
                <ReportsDashboard />
            </div>
        );
    } catch (error) {
        console.error('Reports page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
