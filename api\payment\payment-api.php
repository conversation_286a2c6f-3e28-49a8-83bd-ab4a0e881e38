<?php
/**
 * Payment API Endpoints
 * Handles payment creation, verification, and callbacks
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/phonepe.php';
require_once __DIR__ . '/../db-config.php';

// Get current user
$user = getCurrentUser();
if (!$user && !in_array($_SERVER['REQUEST_URI'], ['/api/payment/callback'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Initialize PhonePe payment handler
$phonepe = new PhonePePayment();

switch ($method) {
    case 'POST':
        if (end($pathParts) === 'create-subscription-payment') {
            createSubscriptionPayment($phonepe, $user);
        } elseif (end($pathParts) === 'create-invoice-payment') {
            createInvoicePayment($phonepe, $user);
        } elseif (end($pathParts) === 'callback') {
            handlePaymentCallback($phonepe);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
        }
        break;
        
    case 'GET':
        if (end($pathParts) === 'verify') {
            verifyPayment($phonepe);
        } elseif (end($pathParts) === 'history') {
            getPaymentHistory($user);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

/**
 * Create subscription payment
 */
function createSubscriptionPayment($phonepe, $user) {
    error_log("Payment API - User object: " . json_encode($user));

    $input = json_decode(file_get_contents('php://input'), true);
    error_log("Payment API - Input data: " . json_encode($input));

    if (!isset($input['subscription_id']) || !isset($input['amount']) || !isset($input['company_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }

    // Get user ID - try different possible fields
    $userId = $user['object_id'] ?? $user['id'] ?? null;
    error_log("Payment API - User ID: " . $userId);

    if (!$userId) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid user ID']);
        return;
    }

    // Validate company access
    if (!validateCompanyAccess($input['company_id'], $userId)) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $result = $phonepe->createSubscriptionPayment(
        $input['subscription_id'],
        $input['amount'],
        $userId,
        $input['company_id']
    );
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'payment_url' => $result['payment_url'],
            'transaction_id' => $result['transaction_id']
        ]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
    }
}

/**
 * Create invoice payment
 */
function createInvoicePayment($phonepe, $user) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['invoice_id']) || !isset($input['amount']) || !isset($input['company_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    // Validate company access
    if (!validateCompanyAccess($input['company_id'], $user['object_id'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $result = $phonepe->createInvoicePayment(
        $input['invoice_id'],
        $input['amount'],
        $user['object_id'],
        $input['company_id']
    );
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'payment_url' => $result['payment_url'],
            'transaction_id' => $result['transaction_id']
        ]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
    }
}

/**
 * Handle payment callback from PhonePe
 */
function handlePaymentCallback($phonepe) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid callback data']);
        return;
    }
    
    $result = $phonepe->handleCallback($input);
    
    if ($result['success']) {
        echo json_encode(['success' => true, 'status' => $result['status']]);
    } else {
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
    }
}

/**
 * Verify payment status
 */
function verifyPayment($phonepe) {
    if (!isset($_GET['transaction_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Transaction ID required']);
        return;
    }
    
    $result = $phonepe->verifyPayment($_GET['transaction_id']);
    echo json_encode($result);
}

/**
 * Get payment history for user's companies
 */
function getPaymentHistory($user) {
    global $conn;
    
    $companyId = $_GET['company_id'] ?? null;
    
    if ($companyId && !validateCompanyAccess($companyId, $user['object_id'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        return;
    }
    
    $sql = "SELECT p.*, s.plan_name, i.invoice_number 
            FROM payments p 
            LEFT JOIN subscriptions s ON p.reference_id = s.object_id AND p.type = 'subscription'
            LEFT JOIN invoices i ON p.reference_id = i.object_id AND p.type = 'invoice'
            WHERE p.user_id = ?";
    
    $params = [$user['object_id']];
    $types = "s";
    
    if ($companyId) {
        $sql .= " AND p.company_id = ?";
        $params[] = $companyId;
        $types .= "s";
    }
    
    $sql .= " ORDER BY p.created_at DESC LIMIT 50";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $payments = [];
    while ($row = $result->fetch_assoc()) {
        $payments[] = $row;
    }
    
    echo json_encode(['payments' => $payments]);
}

/**
 * Generate unique ID
 */
function generateId() {
    return uniqid('pay_', true);
}
?>
