<?php
/**
 * Clean Validation Handler
 * Provides comprehensive validation and error handling for API operations
 */

class ValidationHandler {
    
    /**
     * Process and validate object data
     */
    public static function processObjectData($objectType, $data) {
        $errors = [];
        $sanitizedData = [];
        
        // Get validation rules for object type
        $rules = self::getValidationRules($objectType);
        
        // First pass: sanitize all input data
        foreach ($data as $field => $value) {
            $sanitizedData[$field] = self::sanitizeValue($value);
        }
        
        // Second pass: validate against rules
        foreach ($rules as $field => $rule) {
            $value = isset($sanitizedData[$field]) ? $sanitizedData[$field] : null;
            
            // Check required fields
            if (isset($rule['required']) && $rule['required']) {
                if ($value === null || $value === '' || (is_array($value) && empty($value))) {
                    $errors[$field] = "Field '$field' is required";
                    continue;
                }
            }
            
            // Skip validation if field is empty and not required
            if ($value === null || $value === '') {
                continue;
            }
            
            // Type validation
            if (isset($rule['type'])) {
                $typeError = self::validateType($field, $value, $rule['type']);
                if ($typeError) {
                    $errors[$field] = $typeError;
                    continue;
                }
            }
            
            // Length validation
            if (isset($rule['max_length']) && is_string($value)) {
                if (strlen($value) > $rule['max_length']) {
                    $errors[$field] = "Field '$field' must not exceed {$rule['max_length']} characters";
                }
            }
            
            if (isset($rule['min_length']) && is_string($value)) {
                if (strlen($value) < $rule['min_length']) {
                    $errors[$field] = "Field '$field' must be at least {$rule['min_length']} characters";
                }
            }
            
            // Custom validation
            if (isset($rule['custom'])) {
                $customError = self::validateCustom($field, $value, $rule['custom']);
                if ($customError) {
                    $errors[$field] = $customError;
                }
            }
        }
        
        return [
            'data' => $sanitizedData,
            'errors' => $errors,
            'valid' => empty($errors)
        ];
    }
    
    /**
     * Sanitize a single value
     */
    private static function sanitizeValue($value) {
        // Handle null values
        if ($value === null) {
            return $value;
        }
        
        // Handle arrays
        if (is_array($value)) {
            return array_map([self::class, 'sanitizeValue'], $value);
        }
        
        // Handle strings
        if (is_string($value)) {
            // Remove null bytes
            $value = str_replace("\0", '', $value);
            
            // Trim whitespace
            $value = trim($value);
            
            // Convert special characters to HTML entities for XSS protection
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }
        
        return $value;
    }
    
    /**
     * Validate data type
     */
    private static function validateType($field, $value, $type) {
        switch ($type) {
            case 'string':
                if (!is_string($value)) {
                    return "Field '$field' must be a string";
                }
                break;
                
            case 'number':
                if (!is_numeric($value)) {
                    return "Field '$field' must be a number";
                }
                break;
                
            case 'integer':
                if (!is_numeric($value) || !is_int($value + 0)) {
                    return "Field '$field' must be an integer";
                }
                break;
                
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "Field '$field' must be a valid email address";
                }
                break;
                
            case 'phone':
                // Basic phone validation - adjust regex as needed
                if (!preg_match('/^[\+]?[0-9\s\-\(\)]{7,20}$/', $value)) {
                    return "Field '$field' must be a valid phone number";
                }
                break;
                
            case 'date':
                if (!strtotime($value)) {
                    return "Field '$field' must be a valid date";
                }
                break;
                
            case 'url':
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    return "Field '$field' must be a valid URL";
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Custom validation rules
     */
    private static function validateCustom($field, $value, $rule) {
        switch ($rule) {
            case 'positive_number':
                if (is_numeric($value) && $value <= 0) {
                    return "Field '$field' must be a positive number";
                }
                break;
                
            case 'future_date':
                if (strtotime($value) && strtotime($value) <= time()) {
                    return "Field '$field' must be a future date";
                }
                break;
                
            case 'past_date':
                if (strtotime($value) && strtotime($value) >= time()) {
                    return "Field '$field' must be a past date";
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Get validation rules for object type
     */
    private static function getValidationRules($objectType) {
        $rules = [
            'customer' => [
                'name' => ['required' => true, 'type' => 'string', 'max_length' => 255],
                'email' => ['required' => true, 'type' => 'email', 'max_length' => 255],
                'phone' => ['type' => 'phone', 'max_length' => 50],
                'company' => ['max_length' => 255],
                'address' => ['max_length' => 500],
                'type' => ['max_length' => 50],
                'status' => ['max_length' => 20],
                'notes' => ['max_length' => 5000]
            ],
            
            'invoice' => [
                'customer' => ['required' => true],
                'invoiceNumber' => ['required' => true, 'max_length' => 100],
                'subtotal' => ['required' => true, 'type' => 'number', 'custom' => 'positive_number'],
                'tax' => ['type' => 'number'],
                'taxRate' => ['type' => 'number'],
                'discount' => ['type' => 'number'],
                'total' => ['required' => true, 'type' => 'number', 'custom' => 'positive_number'],
                'dueDate' => ['required' => true, 'type' => 'date'],
                'status' => ['required' => true, 'max_length' => 50]
            ],
            
            'quotation' => [
                'customer' => ['required' => true],
                'quotationNumber' => ['required' => true, 'max_length' => 100],
                'projectName' => ['max_length' => 255],
                'subtotal' => ['required' => true, 'type' => 'number', 'custom' => 'positive_number'],
                'tax' => ['type' => 'number'],
                'taxRate' => ['type' => 'number'],
                'discount' => ['type' => 'number'],
                'total' => ['required' => true, 'type' => 'number', 'custom' => 'positive_number'],
                'validUntil' => ['required' => true, 'type' => 'date', 'custom' => 'future_date'],
                'status' => ['required' => true, 'max_length' => 50]
            ],
            
            'contract' => [
                'customer' => ['required' => true],
                'contractNumber' => ['required' => true, 'max_length' => 100],
                'title' => ['required' => true, 'max_length' => 255],
                'startDate' => ['required' => true, 'type' => 'date'],
                'endDate' => ['required' => true, 'type' => 'date'],
                'value' => ['required' => true, 'type' => 'number', 'custom' => 'positive_number'],
                'status' => ['required' => true, 'max_length' => 50]
            ],
            
            'lead' => [
                'name' => ['required' => true, 'max_length' => 255],
                'email' => ['type' => 'email', 'max_length' => 255],
                'phone' => ['type' => 'phone', 'max_length' => 50],
                'company' => ['max_length' => 255],
                'position' => ['max_length' => 255],
                'source' => ['max_length' => 100],
                'status' => ['required' => true, 'max_length' => 50],
                'priority' => ['max_length' => 20],
                'value' => ['type' => 'number'],
                'notes' => ['max_length' => 5000],
                'assignedTo' => ['max_length' => 255],  // Form field name
                'followUpDate' => ['type' => 'date'],   // Form field name
                'tags' => ['type' => 'array']           // Form field name
            ],
            
            'item' => [
                'name' => ['required' => true, 'max_length' => 255],
                'description' => ['max_length' => 1000],
                'price' => ['required' => true, 'type' => 'number', 'custom' => 'positive_number'],
                'costPrice' => ['type' => 'number'],
                'tax' => ['type' => 'number'],
                'stockQuantity' => ['type' => 'integer'],
                'sku' => ['max_length' => 100]
            ],
            
            'activity' => [
                'type' => ['required' => true, 'max_length' => 50],
                'description' => ['required' => true, 'max_length' => 2000],
                'lead_id' => ['required' => true, 'max_length' => 255],
                'activity_date' => ['type' => 'date']
            ],
            
            'task' => [
                'title' => ['required' => true, 'max_length' => 255],
                'description' => ['max_length' => 2000],
                'dueDate' => ['type' => 'date'],
                'priority' => ['max_length' => 20],
                'status' => ['required' => true, 'max_length' => 50],
                'lead_id' => ['required' => true, 'max_length' => 255]
            ],
            
            'note' => [
                'content' => ['required' => true, 'max_length' => 5000],
                'lead_id' => ['required' => true, 'max_length' => 255]
            ]
        ];
        
        return isset($rules[$objectType]) ? $rules[$objectType] : [];
    }
    
    /**
     * Rate limiting functionality
     */
    public static function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $key = 'rate_limit_' . $identifier;
        $now = time();
        
        // Initialize or clean old requests
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [];
        }
        
        // Remove requests outside time window
        $_SESSION[$key] = array_filter($_SESSION[$key], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // Check if limit exceeded
        if (count($_SESSION[$key]) >= $maxRequests) {
            return [
                'allowed' => false,
                'message' => 'Rate limit exceeded',
                'retry_after' => $timeWindow
            ];
        }
        
        // Add current request
        $_SESSION[$key][] = $now;
        
        return [
            'allowed' => true,
            'remaining' => $maxRequests - count($_SESSION[$key])
        ];
    }
    
    /**
     * Format validation errors for API response
     */
    public static function formatValidationErrors($errors) {
        return [
            'error' => 'Validation failed',
            'validation_errors' => $errors,
            'message' => 'Please check the following fields: ' . implode(', ', array_keys($errors))
        ];
    }
}
?>