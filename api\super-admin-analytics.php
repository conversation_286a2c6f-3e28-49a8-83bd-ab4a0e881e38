<?php
/**
 * Super Admin Analytics API
 * Provides comprehensive analytics and monitoring data
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Verify super admin access
    $user = getCurrentUser();
    if (!$user || $user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Super admin access required']);
        exit;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $timeRange = $_GET['range'] ?? '30d';
        getAnalyticsData($timeRange);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log('Analytics API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function getAnalyticsData($timeRange) {
    global $conn;
    
    // Calculate date range
    $endDate = date('Y-m-d H:i:s');
    switch ($timeRange) {
        case '7d':
            $startDate = date('Y-m-d H:i:s', strtotime('-7 days'));
            break;
        case '90d':
            $startDate = date('Y-m-d H:i:s', strtotime('-90 days'));
            break;
        case '1y':
            $startDate = date('Y-m-d H:i:s', strtotime('-1 year'));
            break;
        default: // 30d
            $startDate = date('Y-m-d H:i:s', strtotime('-30 days'));
            break;
    }
    
    $analytics = [
        'revenue' => getRevenueAnalytics($startDate, $endDate),
        'subscriptions' => getSubscriptionAnalytics($startDate, $endDate),
        'users' => getUserAnalytics($startDate, $endDate),
        'trials' => getTrialAnalytics($startDate, $endDate),
        'performance' => getPerformanceAnalytics(),
        'charts' => getChartData($startDate, $endDate)
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $analytics,
        'time_range' => $timeRange,
        'period' => [
            'start' => $startDate,
            'end' => $endDate
        ]
    ]);
}

function getRevenueAnalytics($startDate, $endDate) {
    global $conn;
    
    // Total revenue in period
    $stmt = $conn->prepare("
        SELECT SUM(amount) as total_revenue, COUNT(*) as transaction_count
        FROM payment_transactions 
        WHERE status = 'completed' AND created_at BETWEEN ? AND ?
    ");
    $stmt->bind_param("ss", $startDate, $endDate);
    $stmt->execute();
    $revenue = $stmt->get_result()->fetch_assoc();
    
    // Monthly Recurring Revenue (MRR)
    $mrrStmt = $conn->prepare("
        SELECT SUM(price) as mrr
        FROM subscriptions 
        WHERE status = 'active' AND billing_cycle = 'monthly'
    ");
    $mrrStmt->execute();
    $mrr = $mrrStmt->get_result()->fetch_assoc()['mrr'] ?? 0;
    
    // Annual Recurring Revenue (ARR)
    $arrStmt = $conn->prepare("
        SELECT SUM(price) as arr
        FROM subscriptions 
        WHERE status = 'active' AND billing_cycle = 'yearly'
    ");
    $arrStmt->execute();
    $arr = $arrStmt->get_result()->fetch_assoc()['arr'] ?? 0;
    
    // Calculate ARPU (Average Revenue Per User)
    $activeUsersStmt = $conn->prepare("
        SELECT COUNT(DISTINCT user_id) as active_users
        FROM subscriptions 
        WHERE status = 'active'
    ");
    $activeUsersStmt->execute();
    $activeUsers = $activeUsersStmt->get_result()->fetch_assoc()['active_users'] ?? 1;
    
    $arpu = ($mrr + ($arr / 12)) / max($activeUsers, 1);
    
    return [
        'total' => $revenue['total_revenue'] ?? 0,
        'transaction_count' => $revenue['transaction_count'] ?? 0,
        'mrr' => $mrr,
        'arr' => $arr,
        'arpu' => $arpu,
        'growth' => 15.2, // Mock data - would calculate from previous period
        'mrr_growth' => 8.5,
        'arr_growth' => 12.3,
        'arpu_growth' => 5.1
    ];
}

function getSubscriptionAnalytics($startDate, $endDate) {
    global $conn;
    
    // Active subscriptions by status
    $statusStmt = $conn->prepare("
        SELECT status, COUNT(*) as count
        FROM subscriptions 
        GROUP BY status
    ");
    $statusStmt->execute();
    $statusResult = $statusStmt->get_result();
    
    $statusCounts = [];
    while ($row = $statusResult->fetch_assoc()) {
        $statusCounts[$row['status']] = $row['count'];
    }
    
    // Subscriptions by plan
    $planStmt = $conn->prepare("
        SELECT s.plan_name, COUNT(*) as count, SUM(s.price) as revenue
        FROM subscriptions s
        WHERE s.status = 'active'
        GROUP BY s.plan_name
    ");
    $planStmt->execute();
    $planResult = $planStmt->get_result();
    
    $byPlan = [];
    while ($row = $planResult->fetch_assoc()) {
        $byPlan[] = $row;
    }
    
    return [
        'active' => $statusCounts['active'] ?? 0,
        'active_trials' => $statusCounts['trial'] ?? 0,
        'paid_subscriptions' => $statusCounts['active'] ?? 0,
        'expired' => ($statusCounts['expired'] ?? 0) + ($statusCounts['cancelled'] ?? 0),
        'growth' => 12.5, // Mock data
        'by_plan' => $byPlan
    ];
}

function getUserAnalytics($startDate, $endDate) {
    global $conn;
    
    // Total users
    $totalStmt = $conn->prepare("SELECT COUNT(*) as total FROM users");
    $totalStmt->execute();
    $total = $totalStmt->get_result()->fetch_assoc()['total'];
    
    // New signups in period
    $newStmt = $conn->prepare("
        SELECT COUNT(*) as new_signups
        FROM users 
        WHERE created_at BETWEEN ? AND ?
    ");
    $newStmt->bind_param("ss", $startDate, $endDate);
    $newStmt->execute();
    $newSignups = $newStmt->get_result()->fetch_assoc()['new_signups'];
    
    // Active users (users with active subscriptions)
    $activeStmt = $conn->prepare("
        SELECT COUNT(DISTINCT s.user_id) as active_users
        FROM subscriptions s
        WHERE s.status IN ('active', 'trial')
    ");
    $activeStmt->execute();
    $activeUsers = $activeStmt->get_result()->fetch_assoc()['active_users'];
    
    return [
        'total' => $total,
        'new_signups' => $newSignups,
        'active_users' => $activeUsers,
        'churn_rate' => 5.2, // Mock data - would calculate from actual churn
        'retention_rate' => 94.8,
        'growth' => 18.3,
        'signup_growth' => 22.1,
        'active_growth' => 15.7,
        'churn_change' => -2.1,
        'retention_change' => 1.8
    ];
}

function getTrialAnalytics($startDate, $endDate) {
    global $conn;
    
    // Trial conversions
    $conversionStmt = $conn->prepare("
        SELECT 
            COUNT(CASE WHEN status = 'trial' THEN 1 END) as active_trials,
            COUNT(CASE WHEN status = 'active' AND trial_start_date IS NOT NULL THEN 1 END) as converted_trials
        FROM subscriptions
        WHERE created_at BETWEEN ? AND ?
    ");
    $conversionStmt->bind_param("ss", $startDate, $endDate);
    $conversionStmt->execute();
    $conversion = $conversionStmt->get_result()->fetch_assoc();
    
    $totalTrials = ($conversion['active_trials'] ?? 0) + ($conversion['converted_trials'] ?? 0);
    $conversionRate = $totalTrials > 0 ? (($conversion['converted_trials'] ?? 0) / $totalTrials) * 100 : 0;
    
    return [
        'active_trials' => $conversion['active_trials'] ?? 0,
        'converted_trials' => $conversion['converted_trials'] ?? 0,
        'conversion_rate' => $conversionRate,
        'conversion_growth' => 8.7 // Mock data
    ];
}

function getPerformanceAnalytics() {
    // Mock performance data - in real implementation, this would come from monitoring systems
    return [
        'uptime' => 99.95,
        'response_time' => 145,
        'error_rate' => 0.08,
        'database_performance' => 'excellent',
        'api_performance' => 'good',
        'storage_usage' => 75.2
    ];
}

function getChartData($startDate, $endDate) {
    global $conn;
    
    // Revenue trend data (daily)
    $revenueStmt = $conn->prepare("
        SELECT DATE(created_at) as date, SUM(amount) as revenue
        FROM payment_transactions 
        WHERE status = 'completed' AND created_at BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $revenueStmt->bind_param("ss", $startDate, $endDate);
    $revenueStmt->execute();
    $revenueResult = $revenueStmt->get_result();
    
    $revenueTrend = [];
    while ($row = $revenueResult->fetch_assoc()) {
        $revenueTrend[] = [
            'date' => $row['date'],
            'value' => $row['revenue']
        ];
    }
    
    // Subscription status distribution
    $subscriptionStmt = $conn->prepare("
        SELECT status, COUNT(*) as count
        FROM subscriptions 
        GROUP BY status
    ");
    $subscriptionStmt->execute();
    $subscriptionResult = $subscriptionStmt->get_result();
    
    $subscriptionDistribution = [];
    while ($row = $subscriptionResult->fetch_assoc()) {
        $subscriptionDistribution[] = [
            'label' => ucfirst($row['status']),
            'value' => $row['count']
        ];
    }
    
    return [
        'revenue' => $revenueTrend,
        'subscriptions' => $subscriptionDistribution
    ];
}
?>
