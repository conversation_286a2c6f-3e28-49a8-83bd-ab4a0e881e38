function formatCurrency(amount, currency = 'INR') {
    try {
        if (currency === 'INR') {
            // Format as Indian Rupee
            const formatter = new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            return formatter.format(amount);
        } else {
            // Format as other currency
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }
    } catch (error) {
        console.error('formatCurrency error:', error);
        return `${currency === 'INR' ? '₹' : currency} ${amount}`;
    }
}

function formatPhoneNumber(phoneNumber) {
    try {
        const cleaned = phoneNumber.replace(/\D/g, '');
        const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
        if (match) {
            return '(' + match[1] + ') ' + match[2] + '-' + match[3];
        }
        return phoneNumber;
    } catch (error) {
        console.error('formatPhoneNumber error:', error);
        return phoneNumber;
    }
}

function truncateText(text, maxLength) {
    try {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    } catch (error) {
        console.error('truncateText error:', error);
        return text;
    }
}

function formatFileSize(bytes) {
    try {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    } catch (error) {
        console.error('formatFileSize error:', error);
        return `${bytes} Bytes`;
    }
}

function formatPercentage(value, decimals = 2) {
    try {
        return `${(value * 100).toFixed(decimals)}%`;
    } catch (error) {
        console.error('formatPercentage error:', error);
        return `${value}%`;
    }
}

// Export functions to global scope
window.formatCurrency = formatCurrency;
window.formatPhoneNumber = formatPhoneNumber;
window.truncateText = truncateText;
window.formatFileSize = formatFileSize;
window.formatPercentage = formatPercentage;
