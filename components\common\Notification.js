function Notification({
    message,
    type = 'info',
    duration = 3000,
    position = 'top-right',
    onClose
}) {
    try {
        const [isVisible, setIsVisible] = React.useState(true);

        React.useEffect(() => {
            if (duration > 0) {
                const timer = setTimeout(() => {
                    setIsVisible(false);
                    if (onClose) onClose();
                }, duration);

                return () => clearTimeout(timer);
            }
        }, [duration, onClose]);

        if (!isVisible) return null;

        const typeClasses = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };

        const positionClasses = {
            'top-right': 'top-4 right-4',
            'top-left': 'top-4 left-4',
            'bottom-right': 'bottom-4 right-4',
            'bottom-left': 'bottom-4 left-4'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        return (
            <div
                data-name={`notification-${type}`}
                className={`
                    fixed ${positionClasses[position]} flex items-center p-4 rounded-lg shadow-lg
                    text-white ${typeClasses[type]} transition-all duration-300 z-50
                    transform translate-y-0 opacity-100
                `}
                role="alert"
            >
                <i className={`${icons[type]} mr-2`}></i>
                <p className="text-sm font-medium">{message}</p>
                <button
                    data-name="notification-close"
                    type="button"
                    className="ml-4 text-white hover:text-gray-200 focus:outline-none"
                    onClick={() => {
                        setIsVisible(false);
                        if (onClose) onClose();
                    }}
                >
                    <i className="fas fa-times"></i>
                </button>
            </div>
        );
    } catch (error) {
        console.error('Notification component error:', error);
        reportError(error);
        return null;
    }
}
