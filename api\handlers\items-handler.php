<?php
// Handle related items for invoices, quotations, and contracts
function handleRelatedItems(&$object, $objectType) {
    global $conn;
    
    if (!in_array($objectType, ['invoice', 'quotation', 'contract'])) {
        return;
    }
    
    $objectId = $object['objectId'];
    
    switch ($objectType) {
        case 'invoice':
            $table = 'invoice_items';
            $idField = 'invoice_id';
            break;
        case 'quotation':
            $table = 'quotation_items';
            $idField = 'quotation_id';
            break;
        case 'contract':
            $table = 'contract_items';
            $idField = 'contract_id';
            break;
    }
    
    $sql = "SELECT * FROM $table WHERE $idField = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        return;
    }
    
    $stmt->bind_param('s', $objectId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $items = [];
    while ($row = $result->fetch_assoc()) {
        $item = [
            'itemId' => $row['item_id'],
            'description' => $row['description'],
            'quantity' => (int)$row['quantity'],
            'price' => (float)$row['price']
        ];
        
        if (!empty($row['details'])) {
            $item['details'] = $row['details'];
        }
        
        if ($objectType === 'contract' && isset($row['recurring_period'])) {
            $item['recurringPeriod'] = $row['recurring_period'];
        }
        
        $items[] = $item;
    }
    
    $object['objectData']['items'] = $items;
    $stmt->close();
}

// Save related items for invoices, quotations, and contracts
function saveRelatedItems($parentId, $items, $objectType) {
    global $conn;
    
    if (!$items || !is_array($items)) {
        return;
    }
    
    switch ($objectType) {
        case 'invoice':
            $table = 'invoice_items';
            $idField = 'invoice_id';
            break;
        case 'quotation':
            $table = 'quotation_items';
            $idField = 'quotation_id';
            break;
        case 'contract':
            $table = 'contract_items';
            $idField = 'contract_id';
            break;
        default:
            return;
    }
    
    // Prepare base SQL statement
    $sql = "INSERT INTO $table ($idField, item_id, description, quantity, price, details";
    if ($objectType === 'contract') {
        $sql .= ", recurring_period";
    }
    $sql .= ") VALUES (?, ?, ?, ?, ?, ?";
    if ($objectType === 'contract') {
        $sql .= ", ?";
    }
    $sql .= ")";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return;
    }
    
    foreach ($items as $item) {
        if ($objectType === 'contract') {
            // Fix for bind_param - variables must be passed by reference
            $details = $item['details'] ?? '';
            $recurringPeriod = $item['recurringPeriod'] ?? '';
            $stmt->bind_param('sssiiss',
                $parentId,
                $item['itemId'],
                $item['description'],
                $item['quantity'],
                $item['price'],
                $details,
                $recurringPeriod
            );
        } else {
            // Fix for bind_param - variables must be passed by reference
            $details = $item['details'] ?? '';
            $stmt->bind_param('sssids',
                $parentId,
                $item['itemId'],
                $item['description'],
                $item['quantity'],
                $item['price'],
                $details
            );
        }
        
        $stmt->execute();
    }
    
    $stmt->close();
}

// Delete related items
function deleteRelatedItems($parentId, $objectType) {
    global $conn;
    
    switch ($objectType) {
        case 'invoice':
            $table = 'invoice_items';
            $idField = 'invoice_id';
            break;
        case 'quotation':
            $table = 'quotation_items';
            $idField = 'quotation_id';
            break;
        case 'contract':
            $table = 'contract_items';
            $idField = 'contract_id';
            break;
        default:
            return;
    }
    
    $sql = "DELETE FROM $table WHERE $idField = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        return;
    }
    
    $stmt->bind_param('s', $parentId);
    $stmt->execute();
    $stmt->close();
}
?>
