function Contracts() {
    try {
        const [showForm, setShowForm] = React.useState(false);
        const [showViewer, setShowViewer] = React.useState(false);
        const [selectedContract, setSelectedContract] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [companyInfo, setCompanyInfo] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [refreshKey, setRefreshKey] = React.useState(0);

        React.useEffect(() => {
            loadInitialData();
        }, []);

        const loadInitialData = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/settings'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.items && data.items.length > 0) {
                        setCompanyInfo(data.items[0].objectData);
                    }
                } else {
                    console.error('Failed to fetch settings');
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load company information'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleCreateContract = () => {
            setSelectedContract(null);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleEditContract = (contract) => {
            setSelectedContract(contract);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleViewContract = (contract) => {
            setSelectedContract(contract);
            setShowViewer(true);
            setShowForm(false);
        };

        const handleContractClick = (contract) => {
            handleViewContract(contract);
        };

        const handleFormSubmit = async () => {
            setShowForm(false);
            setSelectedContract(null);
            setNotification({
                type: 'success',
                message: selectedContract 
                    ? 'Contract updated successfully' 
                    : 'Contract created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the contract list instead of reloading the page
            setRefreshKey(prev => prev + 1);
        };

        const handleFormCancel = () => {
            setShowForm(false);
            setSelectedContract(null);
        };

        const handleViewerClose = () => {
            setShowViewer(false);
            setSelectedContract(null);
            // Don't reload the page, just refresh the contract list
            setRefreshKey(prev => prev + 1);
        };

        const handleContractDelete = () => {
            setShowViewer(false);
            setSelectedContract(null);
            setNotification({
                type: 'success',
                message: 'Contract deleted successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the contract list
            setRefreshKey(prev => prev + 1);
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="contracts-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Contracts</h1>
                    <Button
                        onClick={handleCreateContract}
                        icon="fas fa-plus"
                    >
                        Create Contract
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedContract ? 'Edit Contract' : 'New Contract'}
                        </h2>
                        <ContractForm
                            contract={selectedContract}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : showViewer && selectedContract && companyInfo ? (
                    <DocumentViewer
                        type="contract"
                        data={selectedContract}
                        companyInfo={companyInfo}
                        onEdit={() => handleEditContract(selectedContract)}
                        onDelete={handleContractDelete}
                        onClose={handleViewerClose}
                    />
                ) : (
                    <ContractList onContractClick={handleContractClick} key={refreshKey} />
                )}
            </div>
        );
    } catch (error) {
        console.error('Contracts page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
