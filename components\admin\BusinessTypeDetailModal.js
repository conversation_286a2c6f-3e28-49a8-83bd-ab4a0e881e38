// Business Type Detail Modal Component for Viewing Business Type Details
function BusinessTypeDetailModal({ isOpen, onClose, businessType }) {
    if (!isOpen || !businessType) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <div className="flex items-center">
                            <div className={`w-12 h-12 bg-${businessType.color}-100 rounded-lg flex items-center justify-center mr-4`}>
                                <i className={`${businessType.icon} text-2xl text-${businessType.color}-600`}></i>
                            </div>
                            <div>
                                <h3 className="text-xl font-bold text-gray-900">{businessType.name}</h3>
                                <p className="text-sm text-gray-600">{businessType.description}</p>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Basic Information */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h4>
                            <div className="space-y-3">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Business Type ID</label>
                                    <p className="text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded border">
                                        {businessType.id}
                                    </p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Status</label>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        businessType.status === 'active' 
                                            ? 'bg-green-100 text-green-800' 
                                            : 'bg-gray-100 text-gray-800'
                                    }`}>
                                        {businessType.status}
                                    </span>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Sort Order</label>
                                    <p className="text-sm text-gray-900">{businessType.sort_order || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Companies Using</label>
                                    <p className="text-sm text-gray-900 font-semibold">
                                        {businessType.companies_count || 0} companies
                                    </p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Total Items</label>
                                    <p className="text-sm text-gray-900 font-semibold">
                                        {businessType.total_items || 0} items
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Categories */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Categories</h4>
                            {businessType.categories && businessType.categories.length > 0 ? (
                                <div className="space-y-2">
                                    {businessType.categories.map((category, index) => (
                                        <div key={index} className="bg-white px-3 py-2 rounded border">
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-900">
                                                    {category.name || category}
                                                </span>
                                                {category.items_count && (
                                                    <span className="text-xs text-gray-500">
                                                        {category.items_count} items
                                                    </span>
                                                )}
                                            </div>
                                            {category.description && (
                                                <p className="text-xs text-gray-600 mt-1">{category.description}</p>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-gray-500 italic">No categories defined</p>
                            )}
                        </div>

                        {/* Default Features */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Default Features</h4>
                            {businessType.default_features && businessType.default_features.length > 0 ? (
                                <div className="space-y-2">
                                    {businessType.default_features.map((feature, index) => (
                                        <div key={index} className="flex items-center bg-white px-3 py-2 rounded border">
                                            <i className="fas fa-check text-green-500 mr-2"></i>
                                            <span className="text-sm text-gray-900">{feature}</span>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-gray-500 italic">No default features defined</p>
                            )}
                        </div>

                        {/* Template Information */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Template Information</h4>
                            <div className="space-y-3">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Template Component</label>
                                    <p className="text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded border">
                                        {businessType.template_component || `${businessType.id}Items.js`}
                                    </p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Template Status</label>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        businessType.template_exists 
                                            ? 'bg-green-100 text-green-800' 
                                            : 'bg-yellow-100 text-yellow-800'
                                    }`}>
                                        {businessType.template_exists ? 'Template Exists' : 'Template Missing'}
                                    </span>
                                </div>
                                {businessType.template_version && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Template Version</label>
                                        <p className="text-sm text-gray-900">{businessType.template_version}</p>
                                    </div>
                                )}
                                {businessType.last_updated && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                                        <p className="text-sm text-gray-900">
                                            {new Date(businessType.last_updated).toLocaleDateString()}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Usage Statistics */}
                    {businessType.usage_stats && (
                        <div className="mt-6 bg-gray-50 rounded-lg p-4">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {businessType.usage_stats.active_companies || 0}
                                    </div>
                                    <div className="text-sm text-gray-600">Active Companies</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-green-600">
                                        {businessType.usage_stats.total_items || 0}
                                    </div>
                                    <div className="text-sm text-gray-600">Total Items</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-purple-600">
                                        {businessType.usage_stats.avg_items_per_company || 0}
                                    </div>
                                    <div className="text-sm text-gray-600">Avg Items/Company</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-orange-600">
                                        {businessType.usage_stats.monthly_growth || 0}%
                                    </div>
                                    <div className="text-sm text-gray-600">Monthly Growth</div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Recent Activity */}
                    {businessType.recent_activity && businessType.recent_activity.length > 0 && (
                        <div className="mt-6 bg-gray-50 rounded-lg p-4">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h4>
                            <div className="space-y-2 max-h-40 overflow-y-auto">
                                {businessType.recent_activity.map((activity, index) => (
                                    <div key={index} className="flex items-center justify-between bg-white px-3 py-2 rounded border">
                                        <div>
                                            <span className="text-sm text-gray-900">{activity.description}</span>
                                            <div className="text-xs text-gray-500">{activity.company_name}</div>
                                        </div>
                                        <span className="text-xs text-gray-500">
                                            {new Date(activity.created_at).toLocaleDateString()}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                            Close
                        </button>
                        <button
                            onClick={() => {
                                // Navigate to template editor
                                window.location.hash = `#super-admin-business-types?edit=${businessType.id}`;
                                onClose();
                            }}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                            <i className="fas fa-edit mr-2"></i>
                            Edit Business Type
                        </button>
                        <button
                            onClick={() => {
                                // Navigate to template management
                                window.location.hash = `#super-admin-business-types?template=${businessType.id}`;
                                onClose();
                            }}
                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                        >
                            <i className="fas fa-code mr-2"></i>
                            Manage Template
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.BusinessTypeDetailModal = BusinessTypeDetailModal;
