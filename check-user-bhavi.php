<?php
require_once 'api/db-config.php';

$email = '<EMAIL>';
$password = 'Bhavi<PERSON><PERSON>@56';

echo "<h2>Checking user: $email</h2>\n";

// Check if user exists
$stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "<h3>✅ User found!</h3>\n";
    echo "<p><strong>Object ID:</strong> " . $user['object_id'] . "</p>\n";
    echo "<p><strong>Name:</strong> " . $user['name'] . "</p>\n";
    echo "<p><strong>Email:</strong> " . $user['email'] . "</p>\n";
    echo "<p><strong>Role:</strong> " . $user['role'] . "</p>\n";
    echo "<p><strong>Status:</strong> " . $user['status'] . "</p>\n";
    echo "<p><strong>Company ID:</strong> " . $user['company_id'] . "</p>\n";
    echo "<p><strong>Has password_hash:</strong> " . (!empty($user['password_hash']) ? 'Yes' : 'No') . "</p>\n";
    echo "<p><strong>Has password:</strong> " . (!empty($user['password']) ? 'Yes' : 'No') . "</p>\n";
    
    // Test password verification
    if (!empty($user['password_hash'])) {
        if (password_verify($password, $user['password_hash'])) {
            echo "<p>✅ Password verification successful with password_hash field</p>\n";
        } else {
            echo "<p>❌ Password verification failed with password_hash field</p>\n";
            echo "<p>Stored hash: " . substr($user['password_hash'], 0, 50) . "...</p>\n";
            
            // Try to update the password
            $newHash = password_hash($password, PASSWORD_DEFAULT);
            $updateStmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $updateStmt->bind_param("ss", $newHash, $email);
            if ($updateStmt->execute()) {
                echo "<p>✅ Password hash updated successfully</p>\n";
                echo "<p>New hash: " . substr($newHash, 0, 50) . "...</p>\n";
                
                // Test again
                if (password_verify($password, $newHash)) {
                    echo "<p>✅ Password verification now works with new hash</p>\n";
                } else {
                    echo "<p>❌ Password verification still fails with new hash</p>\n";
                }
            } else {
                echo "<p>❌ Failed to update password hash</p>\n";
            }
        }
    } elseif (!empty($user['password'])) {
        if (password_verify($password, $user['password'])) {
            echo "<p>✅ Password verification successful with password field</p>\n";
            
            // Migrate to password_hash field
            $newHash = password_hash($password, PASSWORD_DEFAULT);
            $updateStmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
            $updateStmt->bind_param("ss", $newHash, $email);
            if ($updateStmt->execute()) {
                echo "<p>✅ Migrated password to password_hash field</p>\n";
            }
        } else {
            echo "<p>❌ Password verification failed with password field</p>\n";
        }
    } else {
        echo "<p>❌ No password or password_hash found</p>\n";
        
        // Create password hash
        $newHash = password_hash($password, PASSWORD_DEFAULT);
        $updateStmt = $conn->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
        $updateStmt->bind_param("ss", $newHash, $email);
        if ($updateStmt->execute()) {
            echo "<p>✅ Created password hash for user</p>\n";
            echo "<p>New hash: " . substr($newHash, 0, 50) . "...</p>\n";
        } else {
            echo "<p>❌ Failed to create password hash</p>\n";
        }
    }
    
} else {
    echo "<h3>❌ User not found!</h3>\n";
    echo "<p>Creating user...</p>\n";
    
    // Create the user
    $userId = 'user_bhavi_' . time();
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
    $token = bin2hex(random_bytes(32));
    $tokenExpires = date('Y-m-d H:i:s', strtotime('+24 hours'));
    
    $sql = "INSERT INTO users (object_id, name, email, password_hash, auth_token, token_expires, status, email_verified, role) VALUES (?, 'Bhavi Tech Apps', ?, ?, ?, ?, 'active', TRUE, 'admin')";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssss", $userId, $email, $passwordHash, $token, $tokenExpires);
    
    if ($stmt->execute()) {
        echo "<p>✅ User created successfully</p>\n";
        echo "<p><strong>Object ID:</strong> $userId</p>\n";
        echo "<p><strong>Password Hash:</strong> " . substr($passwordHash, 0, 50) . "...</p>\n";
    } else {
        echo "<p>❌ Failed to create user: " . $conn->error . "</p>\n";
    }
}

$conn->close();
?>
