<?php
/**
 * Fix Leads Table Schema
 * Add missing columns to match the expected schema
 */

require_once 'api/db-config.php';

echo "🔧 Fixing leads table schema...\n\n";

try {
    // Check current table structure
    $result = $conn->query("DESCRIBE leads");
    $existingColumns = [];
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $existingColumns[] = $row['Field'];
        }
        echo "📋 Current columns: " . implode(', ', $existingColumns) . "\n\n";
    }
    
    // Define required columns that might be missing
    $requiredColumns = [
        'position' => "ADD COLUMN position VARCHAR(255) DEFAULT NULL",
        'priority' => "ADD COLUMN priority ENUM('low','medium','high') DEFAULT 'medium'",
        'assigned_to' => "ADD COLUMN assigned_to VARCHAR(255) DEFAULT NULL",
        'next_follow_up' => "ADD COLUMN next_follow_up DATE DEFAULT NULL"
    ];
    
    // Add missing columns
    foreach ($requiredColumns as $column => $alterStatement) {
        if (!in_array($column, $existingColumns)) {
            echo "➕ Adding column: $column\n";
            $sql = "ALTER TABLE leads $alterStatement";
            
            if ($conn->query($sql)) {
                echo "✅ Successfully added column: $column\n";
            } else {
                echo "❌ Failed to add column $column: " . $conn->error . "\n";
            }
        } else {
            echo "✓ Column $column already exists\n";
        }
    }
    
    // Add indexes for new columns
    $indexes = [
        'idx_leads_assigned_to' => "CREATE INDEX idx_leads_assigned_to ON leads(assigned_to)",
        'idx_leads_priority' => "CREATE INDEX idx_leads_priority ON leads(priority)",
        'idx_leads_next_follow_up' => "CREATE INDEX idx_leads_next_follow_up ON leads(next_follow_up)"
    ];
    
    echo "\n📊 Adding indexes...\n";
    foreach ($indexes as $indexName => $indexSql) {
        // Check if index exists
        $checkIndex = $conn->query("SHOW INDEX FROM leads WHERE Key_name = '$indexName'");
        if ($checkIndex->num_rows == 0) {
            if ($conn->query($indexSql)) {
                echo "✅ Added index: $indexName\n";
            } else {
                echo "❌ Failed to add index $indexName: " . $conn->error . "\n";
            }
        } else {
            echo "✓ Index $indexName already exists\n";
        }
    }
    
    // Show final table structure
    echo "\n📋 Final table structure:\n";
    $result = $conn->query("DESCRIBE leads");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Default']}\n";
        }
    }
    
    echo "\n✅ Leads table schema fixed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
