<?php
require_once 'api/db-config.php';

echo "<h1>User Check</h1>\n";

// Check if test user exists
$stmt = $conn->prepare("SELECT * FROM users WHERE email = '<EMAIL>'");
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "<p>✅ User exists: " . $user['email'] . "</p>\n";
    echo "<p>Password hash: " . substr($user['password'], 0, 20) . "...</p>\n";
    echo "<p>Status: " . ($user['is_active'] ? 'Active' : 'Inactive') . "</p>\n";
    
    // Test password
    if (password_verify('admin123', $user['password'])) {
        echo "<p>✅ Password 'admin123' is correct</p>\n";
    } else {
        echo "<p>❌ Password 'admin123' is incorrect</p>\n";
        
        // Create new password hash
        $newHash = password_hash('admin123', PASSWORD_DEFAULT);
        echo "<p>New hash would be: " . substr($newHash, 0, 30) . "...</p>\n";
        
        // Update password
        $updateStmt = $conn->prepare("UPDATE users SET password = ? WHERE email = '<EMAIL>'");
        $updateStmt->bind_param("s", $newHash);
        if ($updateStmt->execute()) {
            echo "<p>✅ Password updated successfully</p>\n";
        } else {
            echo "<p>❌ Failed to update password</p>\n";
        }
    }
} else {
    echo "<p>❌ User '<EMAIL>' not found</p>\n";
    
    // Create test user
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("INSERT INTO users (object_id, email, password, name, role, company_id, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, 1, NOW())");
    $objectId = 'test_user_001';
    $email = '<EMAIL>';
    $name = 'Test Admin';
    $role = 'admin';
    $companyId = 'super_admin_001';
    
    $stmt->bind_param("ssssss", $objectId, $email, $hashedPassword, $name, $role, $companyId);
    
    if ($stmt->execute()) {
        echo "<p>✅ Test user created successfully</p>\n";
    } else {
        echo "<p>❌ Failed to create test user: " . $conn->error . "</p>\n";
    }
}

$conn->close();
?>