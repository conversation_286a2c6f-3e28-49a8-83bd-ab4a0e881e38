@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

/* Custom component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Form elements */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  .form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-vertical;
  }
  
  /* Card components */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply bg-blue-100 text-blue-700;
  }
  
  .nav-link-inactive {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  
  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }
  
  .badge-secondary {
    @apply bg-gray-100 text-gray-800;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
  
  /* Modal */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
  }
  
  .modal-container {
    @apply fixed inset-0 z-10 overflow-y-auto;
  }
  
  .modal-content {
    @apply relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg;
  }
}

/* Custom utility classes */
@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }
  
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Layout utilities */
  .container-fluid {
    @apply w-full max-w-none px-4;
  }
  
  .container-sm {
    @apply max-w-screen-sm mx-auto px-4;
  }
  
  .container-md {
    @apply max-w-screen-md mx-auto px-4;
  }
  
  .container-lg {
    @apply max-w-screen-lg mx-auto px-4;
  }
  
  .container-xl {
    @apply max-w-screen-xl mx-auto px-4;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply shadow-none border border-gray-400;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-blue-800;
  }
  
  .btn-secondary {
    @apply border-2 border-gray-800;
  }
  
  .form-input, .form-select, .form-textarea {
    @apply border-2 border-gray-800;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}