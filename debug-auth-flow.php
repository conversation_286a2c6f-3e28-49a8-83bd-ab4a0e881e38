<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    // Get current token from localStorage (simulate)
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        echo json_encode([
            'step' => 'no_token',
            'message' => 'No token provided. Add ?token=YOUR_TOKEN to test'
        ]);
        exit;
    }
    
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Test token verification
    $stmt = $conn->prepare("
        SELECT u.*, c.name as company_name
        FROM users u
        LEFT JOIN companies c ON u.company_id = c.id
        WHERE u.auth_token = ? AND u.token_expires > NOW() AND u.status = 'active'
    ");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'step' => 'token_invalid',
            'message' => 'Token not found or expired',
            'token_checked' => $token,
            'current_time' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    $user = $result->fetch_assoc();
    
    // Check what the app.js expects
    $authContextExpected = [
        'id' => $user['id'],
        'object_id' => $user['object_id'],
        'name' => $user['name'],
        'email' => $user['email'],
        'company_id' => $user['company_id'],
        'company_name' => $user['company_name'],
        'role' => $user['role'],
        'email_verified' => (bool)$user['email_verified']
    ];
    
    // Check role specifically
    $roleCheck = [
        'role_value' => $user['role'],
        'is_super_admin' => $user['role'] === 'super_admin',
        'role_comparison' => [
            'exact_match' => $user['role'] === 'super_admin',
            'case_insensitive' => strtolower($user['role']) === 'super_admin',
            'trimmed' => trim($user['role']) === 'super_admin'
        ]
    ];
    
    echo json_encode([
        'step' => 'success',
        'message' => 'Token verification successful',
        'user_data' => $authContextExpected,
        'role_analysis' => $roleCheck,
        'raw_user' => $user,
        'app_routing_check' => [
            'should_allow_super_admin' => $user['role'] === 'super_admin',
            'expected_redirect' => $user['role'] === 'super_admin' ? 'super-admin dashboard' : 'regular dashboard'
        ]
    ]);
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'step' => 'error',
        'error' => $e->getMessage()
    ]);
}
?>
