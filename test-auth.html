<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 10px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>
    
    <div id="authStatus" class="info">Checking authentication...</div>
    
    <button onclick="checkAuth()">Check Auth Status</button>
    <button onclick="testPolicyAPI()">Test Policy API</button>
    <button onclick="clearAuth()">Clear Auth</button>
    
    <div id="results"></div>

    <script src="config.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function checkAuth() {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('user');
            
            log('Token: ' + (token ? token.substring(0, 20) + '...' : 'None'));
            log('User: ' + (user || 'None'));
            
            if (token) {
                // Decode token payload (if it's JWT)
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    log('Token payload: ' + JSON.stringify(payload, null, 2));
                } catch (e) {
                    log('Token is not JWT format');
                }
            }
        }

        async function testPolicyAPI() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                log('No auth token found', 'error');
                return;
            }

            try {
                const response = await fetch(window.getApiUrl('/super-admin/policy-pages'), {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log('API Response Status: ' + response.status);
                
                const data = await response.json();
                log('API Response: ' + JSON.stringify(data, null, 2), response.ok ? 'success' : 'error');
                
            } catch (error) {
                log('API Error: ' + error.message, 'error');
            }
        }

        function clearAuth() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            log('Authentication cleared', 'success');
        }

        // Check auth on page load
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
