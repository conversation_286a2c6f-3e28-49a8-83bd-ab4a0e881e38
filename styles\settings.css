.settings-tab-panel {
    display: none;
}

.settings-tab-panel.active {
    display: block;
}

.settings-tab-button {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.settings-tab-button.active {
    border-bottom-color: #3b82f6;
    color: #3b82f6;
}

.settings-tab-button:hover:not(.active) {
    border-bottom-color: #e5e7eb;
    color: #4b5563;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.settings-form-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.settings-preview-image {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.settings-upload-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-upload-button:hover {
    background-color: #e5e7eb;
}

.settings-upload-button i {
    margin-right: 0.5rem;
}

.settings-notification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.settings-notification-item:last-child {
    border-bottom: none;
}

@media (max-width: 640px) {
    .settings-tab-button {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}
