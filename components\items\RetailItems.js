function RetailItems() {
    try {
        const retailItems = [
            // Electronics
            {
                name: "Smartphone - Basic",
                description: "Entry-level smartphone with 4GB RAM, 64GB storage, dual camera setup.",
                price: 12000,
                category: "Electronics",
                subcategory: "Mobile Phones",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Smartphone - Premium",
                description: "High-end smartphone with 8GB RAM, 256GB storage, triple camera setup, fast charging.",
                price: 45000,
                category: "Electronics",
                subcategory: "Mobile Phones",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Laptop - Basic",
                description: "Entry-level laptop with Intel i3 processor, 8GB RAM, 256GB SSD, 14-inch display.",
                price: 35000,
                category: "Electronics",
                subcategory: "Computers",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Laptop - Gaming",
                description: "Gaming laptop with Intel i7 processor, 16GB RAM, 512GB SSD, dedicated graphics card.",
                price: 85000,
                category: "Electronics",
                subcategory: "Computers",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Wireless Headphones",
                description: "Bluetooth wireless headphones with noise cancellation and 20-hour battery life.",
                price: 3500,
                category: "Electronics",
                subcategory: "Audio",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Smart Watch",
                description: "Fitness tracking smartwatch with heart rate monitor, GPS, and 7-day battery life.",
                price: 8000,
                category: "Electronics",
                subcategory: "Wearables",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            
            // Clothing
            {
                name: "Men's Cotton T-Shirt",
                description: "100% cotton casual t-shirt available in multiple colors and sizes.",
                price: 500,
                category: "Clothing",
                subcategory: "Men's Wear",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Women's Kurti",
                description: "Traditional Indian kurti made from cotton fabric with embroidered design.",
                price: 800,
                category: "Clothing",
                subcategory: "Women's Wear",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Men's Formal Shirt",
                description: "Formal cotton shirt suitable for office wear, available in white and blue.",
                price: 1200,
                category: "Clothing",
                subcategory: "Men's Wear",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Women's Saree",
                description: "Traditional silk saree with intricate border design and matching blouse piece.",
                price: 2500,
                category: "Clothing",
                subcategory: "Women's Wear",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Kids' School Uniform Set",
                description: "Complete school uniform set including shirt, pants/skirt, and tie.",
                price: 1000,
                category: "Clothing",
                subcategory: "Kids' Wear",
                isRecurring: false,
                itemType: "product",
                unit: "set"
            },
            
            // Home & Kitchen
            {
                name: "Pressure Cooker - 5L",
                description: "Stainless steel pressure cooker with safety valve and 5-liter capacity.",
                price: 2000,
                category: "Home & Kitchen",
                subcategory: "Cookware",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Non-Stick Pan Set",
                description: "Set of 3 non-stick frying pans in different sizes with heat-resistant handles.",
                price: 1500,
                category: "Home & Kitchen",
                subcategory: "Cookware",
                isRecurring: false,
                itemType: "product",
                unit: "set"
            },
            {
                name: "Bed Sheet Set - Double",
                description: "Cotton bed sheet set for double bed including 2 pillow covers.",
                price: 800,
                category: "Home & Kitchen",
                subcategory: "Bedding",
                isRecurring: false,
                itemType: "product",
                unit: "set"
            },
            {
                name: "Table Lamp",
                description: "Modern LED table lamp with adjustable brightness and USB charging port.",
                price: 1200,
                category: "Home & Kitchen",
                subcategory: "Lighting",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            
            // Books & Stationery
            {
                name: "Notebook - A4 Size",
                description: "Ruled notebook with 200 pages, hard cover, suitable for office and school use.",
                price: 150,
                category: "Books & Stationery",
                subcategory: "Notebooks",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            {
                name: "Pen Set - Gel Pens",
                description: "Set of 10 gel pens in assorted colors with smooth writing experience.",
                price: 200,
                category: "Books & Stationery",
                subcategory: "Writing Instruments",
                isRecurring: false,
                itemType: "product",
                unit: "set"
            },
            {
                name: "Educational Book - Mathematics",
                description: "Comprehensive mathematics textbook for class 10 students with solved examples.",
                price: 400,
                category: "Books & Stationery",
                subcategory: "Educational Books",
                isRecurring: false,
                itemType: "product",
                unit: "piece"
            },
            
            // Services
            {
                name: "Home Delivery",
                description: "Free home delivery for orders above ₹500 within city limits.",
                price: 50,
                category: "Services",
                subcategory: "Delivery",
                isRecurring: false,
                itemType: "service",
                unit: "order"
            },
            {
                name: "Product Installation",
                description: "Professional installation service for electronics and appliances.",
                price: 300,
                category: "Services",
                subcategory: "Installation",
                isRecurring: false,
                itemType: "service",
                unit: "item"
            },
            {
                name: "Extended Warranty - 1 Year",
                description: "Extended warranty coverage for electronics beyond manufacturer warranty.",
                price: 1000,
                category: "Services",
                subcategory: "Warranty",
                isRecurring: false,
                itemType: "service",
                unit: "item"
            },
            {
                name: "Gift Wrapping",
                description: "Professional gift wrapping service with decorative paper and ribbon.",
                price: 100,
                category: "Services",
                subcategory: "Gift Services",
                isRecurring: false,
                itemType: "service",
                unit: "item"
            }
        ];

        const [loading, setLoading] = React.useState(false);
        const [addedItems, setAddedItems] = React.useState([]);
        const [notification, setNotification] = React.useState(null);

        const setupCategoriesAndItems = async () => {
            try {
                setLoading(true);
                
                // Get unique categories and subcategories
                const categories = [...new Set(retailItems.map(item => item.category))];
                const categoryMap = {};
                
                for (const categoryName of categories) {
                    // Check if category exists
                    const categoriesResponse = await trickleListObjects('item_category', 100, true);
                    let category = categoriesResponse.items.find(
                        cat => cat.objectData.name === categoryName
                    );
                    
                    if (!category) {
                        const categoryData = {
                            name: categoryName,
                            description: `${categoryName} products and services`,
                            createdAt: new Date().toISOString()
                        };
                        
                        const newCategory = await trickleCreateObject('item_category', categoryData);
                        category = {
                            objectId: newCategory.objectId,
                            objectData: categoryData
                        };
                    }
                    
                    categoryMap[categoryName] = category.objectId;
                    
                    // Create subcategories for this category
                    const subcategories = [...new Set(
                        retailItems
                            .filter(item => item.category === categoryName)
                            .map(item => item.subcategory)
                    )];
                    
                    for (const subcategoryName of subcategories) {
                        const subcategoriesResponse = await trickleListObjects(`item_subcategory:${category.objectId}`, 100, true);
                        let existingSubcategory = subcategoriesResponse.items.find(
                            subcat => subcat.objectData.name === subcategoryName
                        );
                        
                        if (!existingSubcategory) {
                            const subcategoryData = {
                                name: subcategoryName,
                                description: `${subcategoryName} products`,
                                createdAt: new Date().toISOString()
                            };
                            
                            await trickleCreateObject(
                                `item_subcategory:${category.objectId}`, 
                                subcategoryData
                            );
                        }
                    }
                }
                
                // Create items
                for (const item of retailItems) {
                    const itemsResponse = await trickleListObjects('item', 100, true);
                    const existingItem = itemsResponse.items.find(
                        existingItem => existingItem.objectData.name === item.name
                    );
                    
                    if (!existingItem) {
                        const itemData = {
                            ...item,
                            category: categoryMap[item.category],
                            isActive: true,
                            createdAt: new Date().toISOString()
                        };
                        
                        await trickleCreateObject('item', itemData);
                        setAddedItems(prev => [...prev, item.name]);
                    }
                }
                
                setNotification({
                    type: 'success',
                    message: `Successfully added retail items`
                });
                
            } catch (error) {
                console.error('Error setting up retail items:', error);
                setNotification({
                    type: 'error',
                    message: 'Error adding retail items'
                });
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="retail-items" className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">Retail Store Items</h2>
                    <p className="mb-6">
                        Add a comprehensive retail product catalog including electronics, clothing, home goods, and services.
                    </p>
                    
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Items that will be added:</h3>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                            <li>Electronics (Smartphones, Laptops, Audio, Wearables)</li>
                            <li>Clothing (Men's, Women's, Kids' Wear)</li>
                            <li>Home & Kitchen (Cookware, Bedding, Lighting)</li>
                            <li>Books & Stationery</li>
                            <li>Services (Delivery, Installation, Warranty)</li>
                        </ul>
                    </div>
                    
                    <div className="flex justify-center">
                        <Button
                            onClick={setupCategoriesAndItems}
                            loading={loading}
                            disabled={loading}
                            icon="fas fa-shopping-cart"
                        >
                            Add Retail Store Items
                        </Button>
                    </div>
                    
                    {addedItems.length > 0 && (
                        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                            <h4 className="font-medium text-green-700 mb-2">Successfully added:</h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm text-green-600">
                                {addedItems.map((item, index) => (
                                    <li key={index}>{item}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
                
                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('RetailItems component error:', error);
        reportError(error);
        return null;
    }
}
