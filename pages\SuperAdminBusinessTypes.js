function SuperAdminBusinessTypes() {
    try {
        const [businessTypes, setBusinessTypes] = React.useState([]);
        const [templates, setTemplates] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [selectedType, setSelectedType] = React.useState(null);
        const [showTemplateModal, setShowTemplateModal] = React.useState(false);
        const [notification, setNotification] = React.useState(null);

        // Available template components
        const templateComponents = {
            'digital_marketing': 'DigitalMarketingItems',
            'restaurant': 'RestaurantItems',
            'retail': 'RetailItems',
            'healthcare': 'HealthcareItems'
        };

        React.useEffect(() => {
            loadBusinessTypes();
            loadTemplates();
        }, []);

        const loadBusinessTypes = async () => {
            try {
                const response = await fetch(window.getApiUrl('/super-admin/business-types'), {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setBusinessTypes(data.data || []);
                } else {
                    throw new Error('Failed to load business types');
                }
            } catch (error) {
                console.error('Error loading business types:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load business types'
                });
            }
        };

        const loadTemplates = async () => {
            try {
                const response = await fetch(window.getApiUrl('/super-admin/business-type-templates'), {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setTemplates(data.data || []);
                }
            } catch (error) {
                console.error('Error loading templates:', error);
            } finally {
                setLoading(false);
            }
        };

        const createBusinessType = async (typeData) => {
            try {
                const response = await fetch(window.getApiUrl('/super-admin/business-types'), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(typeData)
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: 'Business type created successfully'
                    });
                    loadBusinessTypes();
                } else {
                    throw new Error('Failed to create business type');
                }
            } catch (error) {
                console.error('Error creating business type:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to create business type'
                });
            }
        };

        const updateBusinessType = async (id, typeData) => {
            try {
                const response = await fetch(window.getApiUrl(`/super-admin/business-types/${id}`), {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(typeData)
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: 'Business type updated successfully'
                    });
                    loadBusinessTypes();
                } else {
                    throw new Error('Failed to update business type');
                }
            } catch (error) {
                console.error('Error updating business type:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to update business type'
                });
            }
        };

        const createTemplate = async (templateData) => {
            try {
                const response = await fetch(window.getApiUrl('/super-admin/business-type-templates'), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(templateData)
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: 'Template created successfully'
                    });
                    loadTemplates();
                    setShowTemplateModal(false);
                } else {
                    throw new Error('Failed to create template');
                }
            } catch (error) {
                console.error('Error creating template:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to create template'
                });
            }
        };

        const BusinessTypeForm = ({ type, onSave, onCancel }) => {
            const [formData, setFormData] = React.useState({
                name: type?.name || '',
                description: type?.description || '',
                icon: type?.icon || 'fas fa-building',
                is_active: type?.is_active !== undefined ? type.is_active : true
            });

            const handleSubmit = (e) => {
                e.preventDefault();
                onSave(formData);
            };

            return (
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Business Type Name
                        </label>
                        <input
                            type="text"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description
                        </label>
                        <textarea
                            value={formData.description}
                            onChange={(e) => setFormData({...formData, description: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            rows="3"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Icon Class
                        </label>
                        <input
                            type="text"
                            value={formData.icon}
                            onChange={(e) => setFormData({...formData, icon: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="fas fa-building"
                        />
                    </div>

                    <div className="flex items-center">
                        <input
                            type="checkbox"
                            id="is_active"
                            checked={formData.is_active}
                            onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                            className="mr-2"
                        />
                        <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                            Active
                        </label>
                    </div>

                    <div className="flex justify-end space-x-3">
                        <Button
                            type="button"
                            variant="secondary"
                            onClick={onCancel}
                        >
                            Cancel
                        </Button>
                        <Button type="submit">
                            {type ? 'Update' : 'Create'} Business Type
                        </Button>
                    </div>
                </form>
            );
        };

        const TemplateModal = ({ businessType, onClose, onSave }) => {
            const [templateData, setTemplateData] = React.useState({
                business_type_id: businessType?.id || '',
                template_data: '',
                version: '1.0'
            });

            const handleSubmit = (e) => {
                e.preventDefault();
                onSave(templateData);
            };

            return (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                        <h3 className="text-lg font-semibold mb-4">
                            Create Template for {businessType?.name}
                        </h3>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Template Data (JSON)
                                </label>
                                <textarea
                                    value={templateData.template_data}
                                    onChange={(e) => setTemplateData({...templateData, template_data: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows="15"
                                    placeholder="Enter template JSON data..."
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Version
                                </label>
                                <input
                                    type="text"
                                    value={templateData.version}
                                    onChange={(e) => setTemplateData({...templateData, version: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div className="flex justify-end space-x-3">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={onClose}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit">
                                    Create Template
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        };

        if (loading) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            );
        }

        return (
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold text-gray-900">Business Types Management</h1>
                    <Button
                        onClick={() => setSelectedType({})}
                        icon="fas fa-plus"
                    >
                        Add Business Type
                    </Button>
                </div>

                {/* Business Types List */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Business Types</h2>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Name
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Description
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Icon
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Templates
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {businessTypes.map((type) => {
                                    const typeTemplates = templates.filter(t => t.business_type_id === type.id);
                                    
                                    return (
                                        <tr key={type.id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <i className={`${type.icon} mr-2 text-gray-400`}></i>
                                                    <span className="text-sm font-medium text-gray-900">
                                                        {type.name}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="text-sm text-gray-900">
                                                    {type.description}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {type.icon}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    type.is_active 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {type.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {typeTemplates.length} template(s)
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex space-x-2">
                                                    <button
                                                        onClick={() => setSelectedType(type)}
                                                        className="text-blue-600 hover:text-blue-900"
                                                    >
                                                        Edit
                                                    </button>
                                                    <button
                                                        onClick={() => {
                                                            setSelectedType(type);
                                                            setShowTemplateModal(true);
                                                        }}
                                                        className="text-green-600 hover:text-green-900"
                                                    >
                                                        Add Template
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Business Type Form Modal */}
                {selectedType && !showTemplateModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 w-full max-w-md">
                            <h3 className="text-lg font-semibold mb-4">
                                {selectedType.id ? 'Edit' : 'Create'} Business Type
                            </h3>
                            <BusinessTypeForm
                                type={selectedType}
                                onSave={(data) => {
                                    if (selectedType.id) {
                                        updateBusinessType(selectedType.id, data);
                                    } else {
                                        createBusinessType(data);
                                    }
                                    setSelectedType(null);
                                }}
                                onCancel={() => setSelectedType(null)}
                            />
                        </div>
                    </div>
                )}

                {/* Template Modal */}
                {showTemplateModal && selectedType && (
                    <TemplateModal
                        businessType={selectedType}
                        onClose={() => {
                            setShowTemplateModal(false);
                            setSelectedType(null);
                        }}
                        onSave={createTemplate}
                    />
                )}

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('SuperAdminBusinessTypes component error:', error);
        reportError(error);
        return (
            <div className="p-6 bg-red-50 border border-red-200 rounded-md">
                <h3 className="text-red-800 font-medium">Error Loading Business Types</h3>
                <p className="text-red-600 text-sm mt-1">
                    There was an error loading the business types management page. Please refresh and try again.
                </p>
            </div>
        );
    }
}
