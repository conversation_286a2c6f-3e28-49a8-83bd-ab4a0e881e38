<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Login Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; }
        button:hover { background: #0056b3; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; }
        .step h3 { margin-top: 0; color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Complete Login Flow Test</h1>
        <p>This test simulates the complete login flow: Login → Store Token → Verify AuthContext</p>
        
        <div class="step">
            <h3>Step 1: Clear Previous Session</h3>
            <button onclick="clearSession()">Clear Session</button>
            <div id="clearStatus"></div>
        </div>

        <div class="step">
            <h3>Step 2: Perform Login</h3>
            <button onclick="performLogin()">Login with Enhanced Auth</button>
            <div id="loginStatus"></div>
        </div>

        <div class="step">
            <h3>Step 3: Test AuthContext Verification</h3>
            <button onclick="testAuthContext()">Test AuthContext</button>
            <div id="authStatus"></div>
        </div>

        <div class="step">
            <h3>Step 4: Simulate App.html Load</h3>
            <button onclick="simulateAppLoad()">Simulate App Load</button>
            <div id="appStatus"></div>
        </div>

        <div class="step">
            <h3>Step 5: Instructions</h3>
            <div class="info">
                <strong>If all steps pass:</strong>
                <ol>
                    <li>The login flow is working correctly</li>
                    <li>Tokens are being stored properly</li>
                    <li>AuthContext can verify tokens</li>
                    <li>The "Invalid token" error should be resolved</li>
                </ol>
                <p><strong>Next:</strong> Try logging in through the actual login page and accessing app.html</p>
            </div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearSession() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('rememberMe');
            sessionStorage.clear();
            
            showStatus('clearStatus', '✅ Session cleared. All tokens removed from storage.', 'success');
        }

        async function performLogin() {
            showStatus('loginStatus', '🔄 Performing login...', 'info');
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123',
                        rememberMe: false
                    })
                });

                const data = await response.json();
                
                if (data.success && data.tokens && data.tokens.access_token) {
                    // Store token exactly like LoginForm does
                    localStorage.setItem('authToken', data.tokens.access_token);
                    
                    const tokenData = JSON.parse(atob(data.tokens.access_token));
                    const expiryTime = new Date(tokenData.exp * 1000);
                    
                    showStatus('loginStatus', `
                        ✅ <strong>Login successful!</strong><br>
                        User: ${data.user.name}<br>
                        Email: ${data.user.email}<br>
                        Role: ${data.user.role}<br>
                        Token expires: ${expiryTime.toLocaleString()}<br>
                        Token stored in localStorage: ✅
                    `, 'success');
                } else {
                    showStatus('loginStatus', `❌ Login failed: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testAuthContext() {
            showStatus('authStatus', '🧪 Testing AuthContext verification...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('authStatus', '❌ No token found. Please login first.', 'error');
                return;
            }

            try {
                // Test exactly what AuthContext does
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                if (response.ok) {
                    const responseText = await response.text();
                    
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        showStatus('authStatus', `❌ JSON parse error: ${parseError.message}`, 'error');
                        return;
                    }

                    if (data.success && data.user) {
                        showStatus('authStatus', `
                            ✅ <strong>AuthContext verification successful!</strong><br>
                            User: ${data.user.name} (${data.user.email})<br>
                            Role: ${data.user.role}<br>
                            Company: ${data.user.company_name}<br><br>
                            <em>🎉 AuthContext should work correctly now!</em>
                        `, 'success');
                    } else if (data.success === false) {
                        showStatus('authStatus', `❌ Verification failed: ${data.error}`, 'error');
                    } else {
                        showStatus('authStatus', `❌ Invalid response structure`, 'error');
                    }
                } else {
                    showStatus('authStatus', `❌ HTTP error: ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('authStatus', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function simulateAppLoad() {
            showStatus('appStatus', '🏠 Simulating app.html load...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('appStatus', '❌ No token found. App would redirect to login.', 'error');
                return;
            }

            try {
                // Simulate what happens when app.html loads and AuthContext initializes
                const tokenData = JSON.parse(atob(token));
                const isExpired = tokenData.exp < (Date.now() / 1000);
                
                if (isExpired) {
                    showStatus('appStatus', '❌ Token is expired. App would show login.', 'error');
                    return;
                }

                // Test the verification that AuthContext would do
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                const data = await response.json();
                
                if (data.success && data.user) {
                    showStatus('appStatus', `
                        ✅ <strong>App load simulation successful!</strong><br>
                        AuthContext would authenticate user: ${data.user.name}<br>
                        Dashboard would load normally<br>
                        No "Invalid token" errors<br><br>
                        <strong>🚀 Ready to test actual app.html!</strong>
                    `, 'success');
                } else {
                    showStatus('appStatus', `❌ App load would fail: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus('appStatus', `❌ App load simulation error: ${error.message}`, 'error');
            }
        }

        // Auto-clear session on page load for clean testing
        window.onload = function() {
            clearSession();
        };
    </script>
</body>
</html>