<?php
echo "<h1>Direct Verify Test</h1>";

// Get a fresh token first
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

$loginContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => json_encode($loginData),
        'timeout' => 10
    ]
]);

$loginResponse = @file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=login", false, $loginContext);

if ($loginResponse === false) {
    echo "❌ Failed to login<br>";
    exit;
}

$loginResult = json_decode($loginResponse, true);

if (!$loginResult['success']) {
    echo "❌ Login failed: " . $loginResult['error'] . "<br>";
    exit;
}

$token = $loginResult['tokens']['access_token'];
echo "✅ Login successful, token: " . substr($token, 0, 30) . "...<br><br>";

// Now test verify
echo "<h2>Testing Verify Endpoint</h2>";

$verifyContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\nAuthorization: Bearer $token\r\n",
        'content' => json_encode(['token' => $token]),
        'timeout' => 10
    ]
]);

$verifyResponse = @file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=verify", false, $verifyContext);

if ($verifyResponse === false) {
    echo "❌ Failed to call verify endpoint<br>";
    echo "Error: " . error_get_last()['message'] . "<br>";
} else {
    echo "Raw verify response:<br>";
    echo "<pre>" . htmlspecialchars($verifyResponse) . "</pre><br>";
    
    $verifyResult = json_decode($verifyResponse, true);
    
    if ($verifyResult === null) {
        echo "❌ Failed to parse verify response as JSON<br>";
        echo "JSON Error: " . json_last_error_msg() . "<br>";
    } else {
        echo "Parsed verify response:<br>";
        echo "<pre>" . print_r($verifyResult, true) . "</pre>";
        
        if ($verifyResult['success']) {
            echo "✅ Verify successful!<br>";
        } else {
            echo "❌ Verify failed: " . $verifyResult['error'] . "<br>";
        }
    }
}
?>