function LeadNotes({ leadId }) {
    try {
        const [notes, setNotes] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [newNote, setNewNote] = React.useState('');

        React.useEffect(() => {
            fetchNotes();
        }, [leadId]);

        const fetchNotes = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl(`/note:${leadId}`), {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
                
                if (!response.ok) {
                    throw new Error('Failed to fetch notes');
                }
                
                const data = await response.json();
                setNotes(data.items || []);
            } catch (error) {
                console.error('Error fetching notes:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleAddNote = async (e) => {
            e.preventDefault();
            if (!newNote.trim()) return;

            try {
                const token = localStorage.getItem('authToken');
                // Create note
                const noteResponse = await fetch(window.getApiUrl(`/note`), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: newNote,
                        lead_id: leadId
                    })
                });

                if (!noteResponse.ok) {
                    throw new Error('Failed to create note');
                }

                // Create activity for the note
                await fetch(window.getApiUrl(`/activity`), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'note_created',
                        description: 'Added a new note',
                        lead_id: leadId
                    })
                });

                setNewNote('');
                fetchNotes();
            } catch (error) {
                console.error('Error adding note:', error);
            }
        };

        const handleEditNote = async (noteId, updatedContent) => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl(`/note/${noteId}`), {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: updatedContent,
                        updatedAt: new Date().toISOString()
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to update note');
                }

                // Create activity for note edit
                await fetch(window.getApiUrl(`/lead/${leadId}/activities`), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'activity',
                        action: 'note_updated',
                        title: 'Note Updated',
                        description: 'Updated a note',
                        createdAt: new Date().toISOString(),
                        status: 'updated'
                    })
                });

                fetchNotes();
            } catch (error) {
                console.error('Error updating note:', error);
            }
        };

        const handleDeleteNote = async (noteId) => {
            if (window.confirm('Are you sure you want to delete this note?')) {
                try {
                    const token = localStorage.getItem('authToken');
                    const response = await fetch(window.getApiUrl(`/note/${noteId}`), {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete note');
                    }
                    
                    // Create activity for note deletion
                    await fetch(window.getApiUrl(`/activity`), {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'note_deleted',
                            description: 'Deleted a note',
                            lead_id: leadId
                        })
                    });

                    fetchNotes();
                } catch (error) {
                    console.error('Error deleting note:', error);
                }
            }
        };

        try {
            return (
                <div data-name="lead-notes" className="space-y-4">
                    <form onSubmit={handleAddNote}>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Add Note</label>
                                <textarea
                                    value={newNote}
                                    onChange={(e) => setNewNote(e.target.value)}
                                    rows={4}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="Enter your note..."
                                    required
                                />
                            </div>
                            <div>
                                <button
                                    type="submit"
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    Add Note
                                </button>
                            </div>
                        </div>
                    </form>

                    <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                        {loading ? (
                            <div className="text-center py-4">
                                <i className="fas fa-spinner fa-spin text-gray-400"></i>
                                <p className="text-gray-500 mt-2">Loading notes...</p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {notes.length === 0 ? (
                                    <p className="text-gray-500">No notes yet</p>
                                ) : (
                                    notes.map((note, index) => (
                                        <NoteItem 
                                            key={note.id || note.objectId || index} 
                                            note={note} 
                                            onEdit={handleEditNote}
                                            onDelete={handleDeleteNote}
                                        />
                                    ))
                                )}
                            </div>
                        )}
                    </div>
                </div>
            );
        } catch (error) {
            console.error('LeadNotes component error:', error);
            return (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600">Error loading notes</p>
                </div>
            );
        }
    } catch (error) {
        console.error('LeadNotes component error:', error);
        return (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">Error loading notes component</p>
            </div>
        );
    }
}
