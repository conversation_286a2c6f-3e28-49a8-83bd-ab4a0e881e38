function Leads() {
    try {
        const [view, setView] = React.useState('list'); // list, form, details, pipeline
        const [selectedLead, setSelectedLead] = React.useState(null);
        const [selectedLeads, setSelectedLeads] = React.useState([]); // For bulk operations
        const [notification, setNotification] = React.useState(null);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [showBulkActions, setShowBulkActions] = React.useState(false);
        const [refreshKey, setRefreshKey] = React.useState(0);
        const [loading, setLoading] = React.useState(false);
        const [error, setError] = React.useState(null);

        // Auto-refresh leads every 30 seconds when in list view
        React.useEffect(() => {
            let interval;
            if (view === 'list') {
                interval = setInterval(() => {
                    setRefreshKey(prev => prev + 1);
                }, 30000);
            }
            return () => {
                if (interval) clearInterval(interval);
            };
        }, [view]);

        const handleCreateLead = () => {
            setSelectedLead(null);
            setError(null);
            setView('form');
        };

        const handleEditLead = (lead) => {
            setSelectedLead(lead);
            setError(null);
            setView('form');
        };

        const handleLeadClick = (lead) => {
            setSelectedLead(lead);
            setError(null);
            setView('details');
        };

        const handleViewChange = (newView) => {
            setView(newView);
            setError(null);
            if (newView === 'list') {
                setSelectedLead(null);
                setSelectedLeads([]);
            }
        };

        const handleFormSubmit = () => {
            setView('list');
            setSelectedLead(null);
            setNotification({
                type: 'success',
                message: selectedLead ? 'Lead updated successfully' : 'Lead created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the lead list instead of reloading the page
            setRefreshKey(prev => prev + 1);
        };

        const handleFormCancel = () => {
            setView('list');
            setSelectedLead(null);
        };

        const handleDetailsClose = () => {
            setView('list');
            setSelectedLead(null);
        };

        const handleDeleteLead = (lead) => {
            setSelectedLead(lead);
            setShowDeleteConfirm(true);
        };

        const handleBulkDelete = () => {
            if (selectedLeads.length === 0) return;
            setShowDeleteConfirm(true);
        };

        const handleBulkStatusUpdate = async (newStatus) => {
            if (selectedLeads.length === 0) return;

            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');

                const updatePromises = selectedLeads.map(leadId =>
                    fetch(`${window.APP_CONFIG.API_BASE_URL}/lead/${leadId}`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ status: newStatus })
                    })
                );

                const results = await Promise.all(updatePromises);
                const successCount = results.filter(r => r.ok).length;

                setNotification({
                    type: successCount === selectedLeads.length ? 'success' : 'warning',
                    message: `Updated ${successCount} of ${selectedLeads.length} leads`
                });

                setSelectedLeads([]);
                setRefreshKey(prev => prev + 1);

            } catch (error) {
                setNotification({
                    type: 'error',
                    message: 'Failed to update leads'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleConfirmDelete = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');

                if (selectedLeads.length > 0) {
                    // Bulk delete
                    const deletePromises = selectedLeads.map(leadId =>
                        fetch(`${window.APP_CONFIG.API_BASE_URL}/lead/${leadId}`, {
                            method: 'DELETE',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        })
                    );

                    const results = await Promise.all(deletePromises);
                    const successCount = results.filter(r => r.ok).length;

                    setNotification({
                        type: successCount === selectedLeads.length ? 'success' : 'warning',
                        message: `Deleted ${successCount} of ${selectedLeads.length} leads`
                    });

                    setSelectedLeads([]);
                } else if (selectedLead) {
                    // Single delete
                    const response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/lead/${selectedLead.objectId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        setNotification({
                            type: 'success',
                            message: 'Lead deleted successfully'
                        });
                    } else {
                        throw new Error('Failed to delete lead');
                    }
                }

                setShowDeleteConfirm(false);
                setSelectedLead(null);
                setView('list');
                setRefreshKey(prev => prev + 1);

            } catch (error) {
                console.error('Error deleting lead:', error);
                setNotification({
                    type: 'error',
                    message: error.message || 'Failed to delete lead(s)'
                });
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="leads-page">
                {/* Header with view switcher and actions */}
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center space-x-4">
                        <h1 className="text-2xl font-bold">Leads</h1>

                        {/* View Switcher */}
                        <div className="flex bg-gray-100 rounded-lg p-1">
                            <button
                                onClick={() => handleViewChange('list')}
                                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                                    view === 'list'
                                        ? 'bg-white text-blue-600 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                }`}
                            >
                                <i className="fas fa-list mr-1"></i>
                                List
                            </button>
                            <button
                                onClick={() => handleViewChange('pipeline')}
                                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                                    view === 'pipeline'
                                        ? 'bg-white text-blue-600 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                }`}
                            >
                                <i className="fas fa-columns mr-1"></i>
                                Pipeline
                            </button>
                        </div>
                    </div>

                    <div className="flex items-center space-x-3">
                        {/* Bulk Actions */}
                        {selectedLeads.length > 0 && (
                            <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg">
                                <span className="text-sm text-blue-700">
                                    {selectedLeads.length} selected
                                </span>
                                <div className="flex space-x-1">
                                    <button
                                        onClick={() => handleBulkStatusUpdate('qualified')}
                                        className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                                        disabled={loading}
                                    >
                                        Mark Qualified
                                    </button>
                                    <button
                                        onClick={() => handleBulkStatusUpdate('contacted')}
                                        className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                                        disabled={loading}
                                    >
                                        Mark Contacted
                                    </button>
                                    <button
                                        onClick={handleBulkDelete}
                                        className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                                        disabled={loading}
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        )}

                        <Button
                            onClick={handleCreateLead}
                            icon="fas fa-plus"
                            disabled={loading}
                        >
                            Add Lead
                        </Button>
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {view === 'form' ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedLead ? 'Edit Lead' : 'New Lead'}
                        </h2>
                        <LeadForm
                            lead={selectedLead}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : view === 'details' ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <LeadDetails
                            lead={selectedLead}
                            onEdit={() => handleEditLead(selectedLead)}
                            onDelete={() => handleDeleteLead(selectedLead)}
                            onClose={handleDetailsClose}
                        />
                    </div>
                ) : view === 'pipeline' ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="text-center py-12">
                            <i className="fas fa-columns text-4xl text-gray-400 mb-4"></i>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Pipeline View</h3>
                            <p className="text-gray-500 mb-4">Kanban-style lead pipeline coming soon!</p>
                            <Button
                                onClick={() => handleViewChange('list')}
                                variant="secondary"
                            >
                                Back to List View
                            </Button>
                        </div>
                    </div>
                ) : (
                    <LeadList
                        onLeadClick={handleLeadClick}
                        onDeleteLead={handleDeleteLead}
                        selectedLeads={selectedLeads}
                        onLeadSelect={setSelectedLeads}
                        key={refreshKey}
                    />
                )}

                {showDeleteConfirm && (
                    <Modal
                        isOpen={showDeleteConfirm}
                        onClose={() => setShowDeleteConfirm(false)}
                        title={selectedLeads.length > 0 ? `Delete ${selectedLeads.length} Leads` : "Delete Lead"}
                    >
                        <div className="p-4">
                            <p className="mb-4">
                                {selectedLeads.length > 0
                                    ? `Are you sure you want to delete ${selectedLeads.length} selected leads? This action cannot be undone.`
                                    : "Are you sure you want to delete this lead? This action cannot be undone."
                                }
                            </p>
                            <div className="flex justify-end space-x-3">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                    disabled={loading}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleConfirmDelete}
                                    loading={loading}
                                    disabled={loading}
                                >
                                    {loading ? 'Deleting...' : 'Delete'}
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}

                {/* Error Display */}
                {error && (
                    <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-lg">
                        <div className="flex items-center">
                            <i className="fas fa-exclamation-triangle mr-2"></i>
                            <span>{error}</span>
                            <button
                                onClick={() => setError(null)}
                                className="ml-3 text-red-500 hover:text-red-700"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('Leads page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
