function TaskItem({ task, onUpdate, onDelete }) {
    const [isEditing, setIsEditing] = React.useState(false);
    const [editData, setEditData] = React.useState({
        title: task.objectData.title,
        description: task.objectData.description || '',
        dueDate: task.objectData.dueDate || '',
        priority: task.objectData.priority,
        status: task.objectData.status
    });

    // Rest of your TaskItem component code...
}
