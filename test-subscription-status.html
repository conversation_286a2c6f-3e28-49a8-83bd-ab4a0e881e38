<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SubscriptionStatus Component Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script>
        // Mock API URL function
        window.getApiUrl = (endpoint) => `http://localhost/biz/api/api.php${endpoint}`;
    </script>

    <!-- Load the SubscriptionStatus component -->
    <script type="text/babel" src="components/SubscriptionStatus.js"></script>

    <script type="text/babel">
        function TestApp() {
            const [testCase, setTestCase] = React.useState('no-data');
            
            // Mock auth context
            const mockAuthContext = {
                isAuthenticated: true,
                token: 'mock-token',
                user: { id: 1, name: 'Test User' }
            };

            // Mock subscription data for different test cases
            const mockSubscriptions = {
                'no-data': null,
                'trial': {
                    plan_name: 'Free Trial',
                    status: 'trial',
                    is_trial: true,
                    trial_end_date: '2025-08-15',
                    features: [
                        { name: 'Basic CRM', enabled: true },
                        { name: 'Email Support', enabled: true },
                        { name: 'Advanced Analytics', enabled: false }
                    ],
                    limits: {
                        leads: 100,
                        invoices: 50,
                        storage: 1000
                    }
                },
                'active': {
                    plan_name: 'Business Plan',
                    status: 'active',
                    amount: 500,
                    billing_cycle: 'monthly',
                    features: [
                        { name: 'Full CRM', enabled: true },
                        { name: 'Priority Support', enabled: true },
                        { name: 'Advanced Analytics', enabled: true }
                    ],
                    limits: {
                        leads: -1,
                        invoices: -1,
                        storage: 10000
                    }
                },
                'json-string': {
                    plan_name: 'Test Plan',
                    status: 'trial',
                    features: JSON.stringify([
                        { name: 'Feature 1', enabled: true },
                        { name: 'Feature 2', enabled: false }
                    ]),
                    limits: JSON.stringify({
                        leads: 50,
                        invoices: 25
                    })
                },
                'malformed': {
                    plan_name: 'Broken Plan',
                    status: 'trial',
                    features: [null, undefined, { name: 'Valid Feature', enabled: true }],
                    limits: { valid_limit: 100, null_limit: null }
                }
            };

            const handleUpgrade = () => {
                alert('Upgrade clicked!');
            };

            const handleManage = () => {
                alert('Manage clicked!');
            };

            return (
                <div className="min-h-screen p-8">
                    <div className="max-w-4xl mx-auto">
                        <h1 className="text-3xl font-bold text-gray-900 mb-6">
                            SubscriptionStatus Component Test
                        </h1>

                        {/* Test Case Selector */}
                        <div className="mb-6 p-4 bg-white rounded-lg shadow">
                            <h2 className="text-lg font-semibold mb-3">Test Cases:</h2>
                            <div className="flex flex-wrap gap-2">
                                {Object.keys(mockSubscriptions).map(key => (
                                    <button
                                        key={key}
                                        onClick={() => setTestCase(key)}
                                        className={`px-3 py-1 rounded text-sm ${
                                            testCase === key 
                                                ? 'bg-blue-600 text-white' 
                                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                        }`}
                                    >
                                        {key}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {/* Component Test */}
                        <div className="space-y-6">
                            <div>
                                <h3 className="text-lg font-medium mb-3">
                                    Current Test: <span className="text-blue-600">{testCase}</span>
                                </h3>
                                
                                <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
                                    <SubscriptionStatus
                                        authContext={mockAuthContext}
                                        subscription={mockSubscriptions[testCase]}
                                        onUpgrade={handleUpgrade}
                                        onManage={handleManage}
                                    />
                                </div>
                            </div>

                            {/* Debug Info */}
                            <div className="bg-gray-50 p-4 rounded-lg">
                                <h4 className="font-medium mb-2">Debug Info:</h4>
                                <pre className="text-xs text-gray-600 overflow-auto">
                                    {JSON.stringify(mockSubscriptions[testCase], null, 2)}
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>