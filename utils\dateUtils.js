function formatDate(date) {
    try {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    } catch (error) {
        console.error('formatDate error:', error);
        return '';
    }
}

function formatDateTime(date) {
    try {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('formatDateTime error:', error);
        return '';
    }
}

function isDateValid(date) {
    try {
        const d = new Date(date);
        return !isNaN(d.getTime());
    } catch (error) {
        console.error('isDateValid error:', error);
        return false;
    }
}

function addDays(date, days) {
    try {
        const result = new Date(date);
        result.setDate(result.getDate() + days);
        return result;
    } catch (error) {
        console.error('addDays error:', error);
        return date;
    }
}

function getDaysBetween(startDate, endDate) {
    try {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch (error) {
        console.error('getDaysBetween error:', error);
        return 0;
    }
}
