// Security utilities for the application

// Session timeout management
class SessionManager {
    constructor() {
        this.timeoutDuration = 2 * 60 * 60 * 1000; // 2 hours (more reasonable for business app)
        this.warningDuration = 10 * 60 * 1000; // 10 minutes before timeout
        this.timeoutId = null;
        this.warningId = null;
        this.isWarningShown = false;
    }

    startSession() {
        this.resetTimeout();
        this.setupActivityListeners();
    }

    resetTimeout() {
        // Clear existing timeouts
        if (this.timeoutId) clearTimeout(this.timeoutId);
        if (this.warningId) clearTimeout(this.warningId);
        
        this.isWarningShown = false;

        // Set warning timeout
        this.warningId = setTimeout(() => {
            this.showTimeoutWarning();
        }, this.timeoutDuration - this.warningDuration);

        // Set session timeout
        this.timeoutId = setTimeout(() => {
            this.handleSessionTimeout();
        }, this.timeoutDuration);
    }

    showTimeoutWarning() {
        if (this.isWarningShown) return;
        this.isWarningShown = true;

        if (window.toast) {
            window.toast.warning('Your session will expire in 5 minutes. Click anywhere to extend your session.', 10000);
        }

        // Auto-extend session if user interacts
        const extendSession = () => {
            this.resetTimeout();
            document.removeEventListener('click', extendSession);
            document.removeEventListener('keypress', extendSession);
            document.removeEventListener('mousemove', extendSession);
        };

        document.addEventListener('click', extendSession);
        document.addEventListener('keypress', extendSession);
        document.addEventListener('mousemove', extendSession);
    }

    handleSessionTimeout() {
        console.log('SessionManager - Session timeout triggered');

        // Clear auth token
        localStorage.removeItem('authToken');
        localStorage.removeItem('rememberMe');

        // Show timeout message
        if (window.toast) {
            window.toast.error('Your session has expired. Please log in again.');
        }

        // Use app navigation instead of reload
        setTimeout(() => {
            window.dispatchEvent(new CustomEvent('app-navigate', {
                detail: {
                    page: 'login',
                    id: null,
                    action: null,
                    params: { reason: 'session-timeout' }
                }
            }));
        }, 2000);
    }

    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        const resetOnActivity = () => {
            this.resetTimeout();
        };

        events.forEach(event => {
            document.addEventListener(event, resetOnActivity, true);
        });
    }

    endSession() {
        if (this.timeoutId) clearTimeout(this.timeoutId);
        if (this.warningId) clearTimeout(this.warningId);
    }
}

// Password strength validation
function validatePasswordStrength(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const criteria = {
        length: password.length >= minLength,
        upperCase: hasUpperCase,
        lowerCase: hasLowerCase,
        numbers: hasNumbers,
        specialChar: hasSpecialChar
    };

    const score = Object.values(criteria).filter(Boolean).length;
    
    let strength = 'weak';
    if (score >= 4) strength = 'strong';
    else if (score >= 3) strength = 'medium';

    return {
        score,
        strength,
        criteria,
        isValid: score >= 3 // Require at least 3 criteria
    };
}

// Input sanitization
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    return input
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .trim();
}

// XSS protection
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// CSRF token management
function generateCSRFToken() {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
}

// Secure API request wrapper
async function secureApiRequest(url, options = {}) {
    const token = localStorage.getItem('authToken');
    
    const defaultHeaders = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (token) {
        defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    const secureOptions = {
        ...options,
        headers: {
            ...defaultHeaders,
            ...options.headers
        },
        credentials: 'same-origin' // Prevent CSRF
    };

    try {
        const response = await fetch(url, secureOptions);
        
        // Handle unauthorized responses
        if (response.status === 401) {
            localStorage.removeItem('authToken');
            window.location.reload();
            return null;
        }

        return response;
    } catch (error) {
        console.error('Secure API request failed:', error);
        throw error;
    }
}

// Rate limiting for client-side actions
class RateLimiter {
    constructor() {
        this.attempts = new Map();
    }

    isAllowed(key, maxAttempts = 5, windowMs = 60000) {
        const now = Date.now();
        const windowStart = now - windowMs;

        if (!this.attempts.has(key)) {
            this.attempts.set(key, []);
        }

        const attempts = this.attempts.get(key);
        
        // Remove old attempts outside the window
        const recentAttempts = attempts.filter(time => time > windowStart);
        this.attempts.set(key, recentAttempts);

        if (recentAttempts.length >= maxAttempts) {
            return false;
        }

        // Add current attempt
        recentAttempts.push(now);
        return true;
    }

    reset(key) {
        this.attempts.delete(key);
    }
}

// Initialize global security features
const sessionManager = new SessionManager();
const rateLimiter = new RateLimiter();

// Export for global use
window.SecurityUtils = {
    SessionManager: sessionManager,
    validatePasswordStrength,
    sanitizeInput,
    escapeHtml,
    generateCSRFToken,
    secureApiRequest,
    RateLimiter: rateLimiter
};

// Auto-start session management when authenticated
document.addEventListener('DOMContentLoaded', () => {
    if (localStorage.getItem('authToken')) {
        console.log('SecurityUtils - Auth token found, but session timeout disabled for debugging');
        // sessionManager.startSession(); // Disabled for now to prevent unwanted logouts
    }
});
