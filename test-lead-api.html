<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Lead API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input, select, textarea { width: 200px; padding: 5px; margin: 5px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; }
    </style>
</head>
<body>
    <h1>Lead API Test</h1>
    
    <div class="test-section">
        <h3>Create Lead Test</h3>
        <div class="form-group">
            <label>Name:</label>
            <input type="text" id="leadName" value="Test Lead" />
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="leadEmail" value="<EMAIL>" />
        </div>
        <div class="form-group">
            <label>Phone:</label>
            <input type="text" id="leadPhone" value="+1234567890" />
        </div>
        <div class="form-group">
            <label>Company:</label>
            <input type="text" id="leadCompany" value="Test Company" />
        </div>
        <div class="form-group">
            <label>Source:</label>
            <select id="leadSource">
                <option value="website">Website</option>
                <option value="referral">Referral</option>
                <option value="social">Social Media</option>
            </select>
        </div>
        <div class="form-group">
            <label>Status:</label>
            <select id="leadStatus">
                <option value="new">New</option>
                <option value="contacted">Contacted</option>
                <option value="qualified">Qualified</option>
            </select>
        </div>
        <div class="form-group">
            <label>Value:</label>
            <input type="number" id="leadValue" value="1000" />
        </div>
        <div class="form-group">
            <label>Notes:</label>
            <textarea id="leadNotes">Test lead notes</textarea>
        </div>
        <button onclick="testCreateLead()">Create Lead</button>
        <div id="create-result"></div>
    </div>
    
    <div class="test-section">
        <h3>List Leads Test</h3>
        <button onclick="testListLeads()">List Leads</button>
        <div id="list-result"></div>
    </div>

    <script>
        // Test token for super admin (generated by create-super-admin.php)
        const testToken = 'test_super_admin_super_admin_001';
        
        function getApiUrl(endpoint) {
            return `/biz/api/api.php${endpoint}`;
        }
        
        function showResult(elementId, status, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `test-section ${status}`;
            element.innerHTML = `
                <strong>${status.toUpperCase()}:</strong> ${message}
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }
        
        function showLoading(elementId) {
            showResult(elementId, 'loading', 'Testing...');
        }
        
        async function testCreateLead() {
            showLoading('create-result');
            try {
                const leadData = {
                    name: document.getElementById('leadName').value,
                    email: document.getElementById('leadEmail').value,
                    phone: document.getElementById('leadPhone').value,
                    company: document.getElementById('leadCompany').value,
                    source: document.getElementById('leadSource').value,
                    status: document.getElementById('leadStatus').value,
                    value: parseFloat(document.getElementById('leadValue').value) || 0,
                    notes: document.getElementById('leadNotes').value,
                    priority: 'medium',
                    tags: ['test', 'api'],
                    assignedTo: '',
                    followUpDate: ''
                };
                
                console.log('Creating lead with data:', leadData);
                
                const response = await fetch(getApiUrl('/object/lead'), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(leadData)
                });
                
                console.log('Response status:', response.status);
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    console.error('Failed to parse response as JSON:', e);
                    showResult('create-result', 'error', `Invalid response format: ${responseText}`);
                    return;
                }
                
                if (response.ok) {
                    showResult('create-result', 'success', 'Lead created successfully', data);
                } else {
                    const errorMessage = data.error || data.message || 'Unknown error';
                    showResult('create-result', 'error', `Create failed (${response.status}): ${errorMessage}`, data);
                }
            } catch (error) {
                console.error('Create lead error:', error);
                const errorMessage = error.message || 'Network or system error';
                showResult('create-result', 'error', `Create error: ${errorMessage}`);
                // Clear loading state
                document.getElementById('create-result').classList.remove('loading');
            }
        }
        
        async function testListLeads() {
            showLoading('list-result');
            try {
                const response = await fetch(getApiUrl('/object/lead'), {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('list-result', 'success', 'Leads listed successfully', data);
                } else {
                    const errorText = await response.text();
                    showResult('list-result', 'error', `List failed: ${response.status}`, errorText);
                }
            } catch (error) {
                showResult('list-result', 'error', `List error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
