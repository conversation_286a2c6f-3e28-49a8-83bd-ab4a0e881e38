// ErrorMessage Component
function ErrorMessage({ error, onRetry }) {
        const errorMessage = typeof error === 'string' ? error : (error?.message || 'Something went wrong. Please try again.');

        const container = document.createElement('div');
        container.className = 'bg-red-50 border border-red-200 rounded-md p-4';

        const flexDiv = document.createElement('div');
        flexDiv.className = 'flex';

        const iconDiv = document.createElement('div');
        iconDiv.className = 'flex-shrink-0';
        const icon = document.createElement('i');
        icon.className = 'fas fa-exclamation-circle text-red-500';
        iconDiv.appendChild(icon);

        const contentDiv = document.createElement('div');
        contentDiv.className = 'ml-3';

        const title = document.createElement('h3');
        title.className = 'text-sm font-medium text-red-800';
        title.textContent = 'An error occurred';

        const messageDiv = document.createElement('div');
        messageDiv.className = 'mt-2 text-sm text-red-700';
        const messageP = document.createElement('p');
        messageP.textContent = errorMessage;
        messageDiv.appendChild(messageP);

        contentDiv.appendChild(title);
        contentDiv.appendChild(messageDiv);

        if (onRetry) {
            const buttonDiv = document.createElement('div');
            buttonDiv.className = 'mt-4';
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500';
            button.textContent = 'Try again';
            button.onclick = onRetry;
            buttonDiv.appendChild(button);
            contentDiv.appendChild(buttonDiv);
        }

        flexDiv.appendChild(iconDiv);
        flexDiv.appendChild(contentDiv);
        container.appendChild(flexDiv);

        return container;
}

// Make ErrorMessage globally available
window.ErrorMessage = ErrorMessage;