function AccountSettings() {
    try {
        const [activeTab, setActiveTab] = React.useState('profile');
        const [loading, setLoading] = React.useState(false);
        const [notification, setNotification] = React.useState(null);
        const [userProfile, setUserProfile] = React.useState({});
        const [companyProfile, setCompanyProfile] = React.useState({});

        React.useEffect(() => {
            loadUserProfile();
            loadCompanyProfile();
        }, []);

        const loadUserProfile = async () => {
            try {
                const response = await fetch(window.getApiUrl('/auth/profile'), {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setUserProfile(data.user || {});
                }
            } catch (error) {
                console.error('Error loading user profile:', error);
            }
        };

        const loadCompanyProfile = async () => {
            try {
                const response = await fetch(window.getApiUrl('/company/profile'), {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setCompanyProfile(data.company || {});
                }
            } catch (error) {
                console.error('Error loading company profile:', error);
            }
        };

        const ProfileTab = () => {
            const [profileData, setProfileData] = React.useState({
                name: userProfile.name || '',
                email: userProfile.email || '',
                phone: userProfile.phone || '',
                current_password: '',
                new_password: '',
                confirm_password: ''
            });

            const handleProfileUpdate = async (e) => {
                e.preventDefault();
                setLoading(true);

                try {
                    const updateData = {
                        name: profileData.name,
                        email: profileData.email,
                        phone: profileData.phone
                    };

                    // Add password change if provided
                    if (profileData.new_password) {
                        if (profileData.new_password !== profileData.confirm_password) {
                            throw new Error('New passwords do not match');
                        }
                        updateData.current_password = profileData.current_password;
                        updateData.new_password = profileData.new_password;
                    }

                    const response = await fetch(window.getApiUrl('/auth/update-profile'), {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(updateData)
                    });

                    if (response.ok) {
                        setNotification({
                            type: 'success',
                            message: 'Profile updated successfully'
                        });
                        loadUserProfile();
                        // Clear password fields
                        setProfileData({
                            ...profileData,
                            current_password: '',
                            new_password: '',
                            confirm_password: ''
                        });
                    } else {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to update profile');
                    }
                } catch (error) {
                    setNotification({
                        type: 'error',
                        message: error.message
                    });
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-6">Profile Information</h3>
                    
                    <form onSubmit={handleProfileUpdate} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Full Name
                                </label>
                                <input
                                    type="text"
                                    value={profileData.name}
                                    onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Email Address
                                </label>
                                <input
                                    type="email"
                                    value={profileData.email}
                                    onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <input
                                    type="tel"
                                    value={profileData.phone}
                                    onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        <div className="border-t pt-6">
                            <h4 className="text-md font-medium text-gray-900 mb-4">Change Password</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Current Password
                                    </label>
                                    <input
                                        type="password"
                                        value={profileData.current_password}
                                        onChange={(e) => setProfileData({...profileData, current_password: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        New Password
                                    </label>
                                    <input
                                        type="password"
                                        value={profileData.new_password}
                                        onChange={(e) => setProfileData({...profileData, new_password: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Confirm New Password
                                    </label>
                                    <input
                                        type="password"
                                        value={profileData.confirm_password}
                                        onChange={(e) => setProfileData({...profileData, confirm_password: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-end">
                            <Button
                                type="submit"
                                loading={loading}
                                disabled={loading}
                            >
                                Update Profile
                            </Button>
                        </div>
                    </form>
                </div>
            );
        };

        const CompanyTab = () => {
            const [companyData, setCompanyData] = React.useState({
                name: companyProfile.name || '',
                email: companyProfile.email || '',
                phone: companyProfile.phone || '',
                address: companyProfile.address || '',
                website: companyProfile.website || '',
                business_type: companyProfile.business_type || ''
            });

            const handleCompanyUpdate = async (e) => {
                e.preventDefault();
                setLoading(true);

                try {
                    const response = await fetch(window.getApiUrl('/company/update'), {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(companyData)
                    });

                    if (response.ok) {
                        setNotification({
                            type: 'success',
                            message: 'Company information updated successfully'
                        });
                        loadCompanyProfile();
                    } else {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to update company information');
                    }
                } catch (error) {
                    setNotification({
                        type: 'error',
                        message: error.message
                    });
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-6">Company Information</h3>
                    
                    <form onSubmit={handleCompanyUpdate} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Company Name
                                </label>
                                <input
                                    type="text"
                                    value={companyData.name}
                                    onChange={(e) => setCompanyData({...companyData, name: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Business Type
                                </label>
                                <select
                                    value={companyData.business_type}
                                    onChange={(e) => setCompanyData({...companyData, business_type: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Business Type</option>
                                    <option value="digital_marketing">Digital Marketing</option>
                                    <option value="restaurant">Restaurant</option>
                                    <option value="retail">Retail</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="consulting">Consulting</option>
                                    <option value="manufacturing">Manufacturing</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Email Address
                                </label>
                                <input
                                    type="email"
                                    value={companyData.email}
                                    onChange={(e) => setCompanyData({...companyData, email: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <input
                                    type="tel"
                                    value={companyData.phone}
                                    onChange={(e) => setCompanyData({...companyData, phone: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Website
                                </label>
                                <input
                                    type="url"
                                    value={companyData.website}
                                    onChange={(e) => setCompanyData({...companyData, website: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="https://example.com"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Address
                            </label>
                            <textarea
                                value={companyData.address}
                                onChange={(e) => setCompanyData({...companyData, address: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                rows="3"
                                placeholder="Enter company address"
                            />
                        </div>

                        <div className="flex justify-end">
                            <Button
                                type="submit"
                                loading={loading}
                                disabled={loading}
                            >
                                Update Company Information
                            </Button>
                        </div>
                    </form>
                </div>
            );
        };

        const tabs = [
            { id: 'profile', name: 'Profile', icon: 'fas fa-user' },
            { id: 'company', name: 'Company', icon: 'fas fa-building' },
            { id: 'subscription', name: 'Subscription', icon: 'fas fa-credit-card' },
            { id: 'notifications', name: 'Notifications', icon: 'fas fa-bell' }
        ];

        return (
            <div className="space-y-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Account Settings</h1>
                    <p className="text-gray-600">Manage your account and company settings</p>
                </div>

                {/* Tabs */}
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        {tabs.map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === tab.id
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <i className={`${tab.icon} mr-2`}></i>
                                {tab.name}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Tab Content */}
                <div>
                    {activeTab === 'profile' && <ProfileTab />}
                    {activeTab === 'company' && <CompanyTab />}
                    {activeTab === 'subscription' && (
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Management</h3>
                            <p className="text-gray-600">Subscription management features will be available here.</p>
                        </div>
                    )}
                    {activeTab === 'notifications' && (
                        <div className="bg-white p-6 rounded-lg shadow">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
                            <p className="text-gray-600">Notification settings will be available here.</p>
                        </div>
                    )}
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('AccountSettings component error:', error);
        reportError(error);
        return (
            <div className="p-6 bg-red-50 border border-red-200 rounded-md">
                <h3 className="text-red-800 font-medium">Error Loading Account Settings</h3>
                <p className="text-red-600 text-sm mt-1">
                    There was an error loading the account settings page. Please refresh and try again.
                </p>
            </div>
        );
    }
}
