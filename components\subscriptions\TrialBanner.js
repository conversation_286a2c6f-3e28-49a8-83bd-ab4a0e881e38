// TrialBanner Component
function TrialBanner({ trialData, onUpgrade, onExtend }) {
    if (!trialData || !trialData.is_trial) {
        return null;
    }
    
    const daysRemaining = trialData.trial_days_remaining || 0;
    const isExpired = trialData.expired || daysRemaining <= 0;
    
    // Create banner element
    const banner = document.createElement('div');
    
    if (isExpired) {
        banner.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded-r-md shadow-sm';
        
        const content = document.createElement('div');
        content.className = 'flex flex-col md:flex-row md:items-center';
        
        const leftSection = document.createElement('div');
        leftSection.className = 'flex items-center';
        
        const iconDiv = document.createElement('div');
        iconDiv.className = 'py-1';
        const icon = document.createElement('i');
        icon.className = 'fas fa-exclamation-circle text-red-500 mr-2 text-lg';
        iconDiv.appendChild(icon);
        
        const textDiv = document.createElement('div');
        const title = document.createElement('p');
        title.className = 'font-bold';
        title.textContent = 'Trial Expired';
        const subtitle = document.createElement('p');
        subtitle.className = 'text-sm';
        subtitle.textContent = 'Your free trial has ended. Please upgrade to continue using all features.';
        textDiv.appendChild(title);
        textDiv.appendChild(subtitle);
        
        leftSection.appendChild(iconDiv);
        leftSection.appendChild(textDiv);
        
        const buttonDiv = document.createElement('div');
        buttonDiv.className = 'mt-3 md:mt-0 md:ml-auto';
        const upgradeBtn = document.createElement('button');
        upgradeBtn.className = 'bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200';
        upgradeBtn.textContent = 'Upgrade Now';
        upgradeBtn.onclick = onUpgrade;
        buttonDiv.appendChild(upgradeBtn);
        
        content.appendChild(leftSection);
        content.appendChild(buttonDiv);
        banner.appendChild(content);
        
    } else {
        // Active trial
        const urgencyClass = daysRemaining <= 3 ? 'bg-orange-100 border-orange-500 text-orange-700' : 'bg-blue-100 border-blue-500 text-blue-700';
        banner.className = `${urgencyClass} border-l-4 p-4 mb-4 rounded-r-md shadow-sm`;
        
        const content = document.createElement('div');
        content.className = 'flex flex-col md:flex-row md:items-center';
        
        const leftSection = document.createElement('div');
        leftSection.className = 'flex items-center';
        
        const iconDiv = document.createElement('div');
        iconDiv.className = 'py-1';
        const icon = document.createElement('i');
        icon.className = daysRemaining <= 3 ? 'fas fa-clock text-orange-500 mr-2 text-lg' : 'fas fa-info-circle text-blue-500 mr-2 text-lg';
        iconDiv.appendChild(icon);
        
        const textDiv = document.createElement('div');
        const title = document.createElement('p');
        title.className = 'font-bold';
        title.textContent = `${daysRemaining} day${daysRemaining !== 1 ? 's' : ''} left in your free trial`;
        const subtitle = document.createElement('p');
        subtitle.className = 'text-sm';
        subtitle.textContent = 'Upgrade now to continue enjoying all features without interruption.';
        textDiv.appendChild(title);
        textDiv.appendChild(subtitle);
        
        leftSection.appendChild(iconDiv);
        leftSection.appendChild(textDiv);
        
        const buttonDiv = document.createElement('div');
        buttonDiv.className = 'mt-3 md:mt-0 md:ml-auto flex space-x-2';
        
        const upgradeBtn = document.createElement('button');
        upgradeBtn.className = daysRemaining <= 3 ? 'bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded transition duration-200' : 'bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200';
        upgradeBtn.textContent = 'Upgrade Now';
        upgradeBtn.onclick = onUpgrade;
        buttonDiv.appendChild(upgradeBtn);
        
        if (trialData.can_extend && onExtend) {
            const extendBtn = document.createElement('button');
            extendBtn.className = 'bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-200';
            extendBtn.textContent = 'Extend Trial';
            extendBtn.onclick = onExtend;
            buttonDiv.appendChild(extendBtn);
        }
        
        content.appendChild(leftSection);
        content.appendChild(buttonDiv);
        banner.appendChild(content);
    }
    
    return banner;
}

// Make TrialBanner globally available
window.TrialBanner = TrialBanner;
