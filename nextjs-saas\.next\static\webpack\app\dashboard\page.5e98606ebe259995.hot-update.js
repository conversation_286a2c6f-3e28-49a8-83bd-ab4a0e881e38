"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CreditCard,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    var _session_user, _session_user1, _session_user_company, _session_user2;\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this);\n    }\n    // For now, let's make the dashboard accessible without authentication to debug\n    // if (status === 'unauthenticated') {\n    //   redirect('/auth/signin')\n    // }\n    const stats = [\n        {\n            title: \"Total Customers\",\n            value: \"2\",\n            change: \"+2 from last month\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-blue-600\"\n        },\n        {\n            title: \"Active Leads\",\n            value: \"0\",\n            change: \"No new leads\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-green-600\"\n        },\n        {\n            title: \"Invoices\",\n            value: \"0\",\n            change: \"No invoices yet\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"text-orange-600\"\n        },\n        {\n            title: \"Revenue\",\n            value: \"$0\",\n            change: \"No revenue yet\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"text-purple-600\"\n        }\n    ];\n    const quickActions = [\n        {\n            title: \"Add Customer\",\n            description: \"Create a new customer profile\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/dashboard/customers/new\"\n        },\n        {\n            title: \"Create Invoice\",\n            description: \"Generate a new invoice\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/dashboard/invoices/new\"\n        },\n        {\n            title: \"Add Lead\",\n            description: \"Track a new business lead\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/dashboard/leads/new\"\n        },\n        {\n            title: \"View Reports\",\n            description: \"Analyze your business data\",\n            icon: _barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/dashboard/reports\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CreditCard_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Welcome back, \",\n                                                    (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.name) || (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_company = _session_user2.company) === null || _session_user_company === void 0 ? void 0 : _session_user_company.name) || \"Demo Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: stats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: stat.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"h-4 w-4 \".concat(stat.color)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: stat.change\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, stat.title, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Common tasks to get you started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                            children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"h-auto p-4 flex flex-col items-start space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: action.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 text-left\",\n                                                            children: action.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, action.title, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Your latest business activities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-600 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Account created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Welcome to Business SaaS!\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Just now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-600 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Sample data loaded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"Demo customers and items added\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Just now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Start using the platform to see more activities here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Getting Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Complete these steps to set up your business\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-blue-600\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Set up your company profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Add your company details, logo, and branding\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"link\",\n                                                            className: \"p-0 h-auto mt-2\",\n                                                            children: \"Complete setup →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Add your first customer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Start building your customer database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"link\",\n                                                            className: \"p-0 h-auto mt-2\",\n                                                            children: \"Add customer →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Create your first invoice\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Generate professional invoices for your customers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"link\",\n                                                            className: \"p-0 h-auto mt-2\",\n                                                            children: \"Create invoice →\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\biz\\\\nextjs-saas\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"ujwIunAD3hlHFoJLG3BNiDLiMqM=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});