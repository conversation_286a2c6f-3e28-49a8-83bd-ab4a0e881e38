<?php
/**
 * Direct Lead Creation Test
 * Test lead creation directly without going through the full API stack
 */

require_once 'api/db-config.php';
require_once 'api/handlers/crud-handler.php';
require_once 'api/handlers/validation-handler.php';
require_once 'api/handlers/field-handler.php';
require_once 'api/handlers/object-handler.php';

echo "🧪 Testing Lead Creation Directly...\n\n";

try {
    // Simulate a super admin user
    $_SESSION['current_user'] = [
        'object_id' => 'super_admin_001',
        'name' => 'Super Administrator',
        'email' => '<EMAIL>',
        'role' => 'super_admin',
        'company_id' => 'company_1751611520_782'
    ];
    
    // Test data
    $leadData = [
        'name' => 'Test Lead Direct',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'company' => 'Test Company Direct',
        'source' => 'website',
        'status' => 'new',
        'value' => 1500.00,
        'notes' => 'Direct test lead',
        'priority' => 'medium',
        'tags' => ['test', 'direct'],
        'assignedTo' => '',
        'followUpDate' => ''
    ];
    
    echo "📝 Lead Data:\n";
    print_r($leadData);
    echo "\n";
    
    // Test validation
    echo "🔍 Testing validation...\n";
    $validation = ValidationHandler::processObjectData('lead', $leadData);
    if (!$validation['valid']) {
        echo "❌ Validation failed:\n";
        print_r($validation['errors']);
        exit;
    }
    echo "✅ Validation passed\n\n";
    
    // Test field extraction
    echo "🔍 Testing field extraction...\n";
    $fields = extractFields('lead', $leadData);
    echo "Extracted fields:\n";
    print_r($fields);
    echo "\n";
    
    // Test table mapping
    echo "🔍 Testing table mapping...\n";
    $table = mapObjectTypeToTable('lead');
    echo "Table: $table\n\n";
    
    // Test direct database insertion
    echo "🔍 Testing direct database insertion...\n";
    
    $objectId = generateId();
    $fields['object_id'] = $objectId;
    $fields['company_id'] = 'company_1751611520_782';
    
    // Handle tags as JSON
    if (isset($fields['tags']) && is_array($fields['tags'])) {
        $fields['tags'] = json_encode($fields['tags']);
    }
    
    // Build SQL
    $columns = implode(', ', array_keys($fields));
    $placeholders = implode(', ', array_fill(0, count($fields), '?'));
    $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
    
    echo "SQL: $sql\n";
    echo "Values: " . print_r(array_values($fields), true) . "\n";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        echo "❌ Failed to prepare statement: " . $conn->error . "\n";
        exit;
    }
    
    $types = str_repeat('s', count($fields));
    $stmt->bind_param($types, ...array_values($fields));
    
    if ($stmt->execute()) {
        echo "✅ Lead created successfully with ID: $objectId\n";
        
        // Verify by reading back
        $checkSql = "SELECT * FROM $table WHERE object_id = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("s", $objectId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            echo "\n📋 Created lead data:\n";
            print_r($row);
        }
        
    } else {
        echo "❌ Failed to create lead: " . $stmt->error . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
