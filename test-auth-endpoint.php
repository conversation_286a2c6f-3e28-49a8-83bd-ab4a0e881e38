<?php
echo "<h1>Auth Endpoint Test</h1>";

// Step 1: Login to get a fresh token
echo "<h2>Step 1: Login</h2>";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

$loginContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => json_encode($loginData),
        'timeout' => 10
    ]
]);

$loginResponse = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=login", false, $loginContext);
$loginResult = json_decode($loginResponse, true);

if ($loginResult['success']) {
    $token = $loginResult['tokens']['access_token'];
    echo "✅ Login successful<br>";
    echo "Token: " . substr($token, 0, 20) . "...<br><br>";
    
    // Step 2: Test auth verification
    echo "<h2>Step 2: Token Verification</h2>";
    
    // Test with Authorization header
    echo "<h3>Test with Authorization Header:</h3>";
    $verifyContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\nAuthorization: Bearer $token\r\n",
            'content' => json_encode(['token' => $token]),
            'timeout' => 10
        ]
    ]);
    
    $verifyResponse = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=verify", false, $verifyContext);
    $verifyResult = json_decode($verifyResponse, true);
    
    if ($verifyResult['success']) {
        echo "✅ Token verification successful<br>";
        echo "User: " . $verifyResult['user']['name'] . " (" . $verifyResult['user']['email'] . ")<br>";
        echo "Role: " . $verifyResult['user']['role'] . "<br>";
    } else {
        echo "❌ Token verification failed: " . $verifyResult['error'] . "<br>";
    }
    
    // Test with POST body only
    echo "<h3>Test with POST Body Only:</h3>";
    $verifyContext2 = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode(['token' => $token]),
            'timeout' => 10
        ]
    ]);
    
    $verifyResponse2 = file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=verify", false, $verifyContext2);
    $verifyResult2 = json_decode($verifyResponse2, true);
    
    if ($verifyResult2['success']) {
        echo "✅ Token verification successful (POST body)<br>";
        echo "User: " . $verifyResult2['user']['name'] . " (" . $verifyResult2['user']['email'] . ")<br>";
    } else {
        echo "❌ Token verification failed (POST body): " . $verifyResult2['error'] . "<br>";
    }
    
} else {
    echo "❌ Login failed: " . $loginResult['error'] . "<br>";
}
?>