<?php
require_once 'api/db-config.php';

echo "<h1>Leads Table Structure</h1>\n";

// Check notes table structure
$result = $conn->query("DESCRIBE notes");
echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>\n";
}
echo "</table>\n";

$conn->close();
?>