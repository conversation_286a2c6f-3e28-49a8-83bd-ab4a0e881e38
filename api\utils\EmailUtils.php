<?php
/**
 * Email Utility Class
 * Handles sending emails for password reset, verification, etc.
 */

class EmailUtils {
    
    /**
     * Send password reset email
     */
    public static function sendPasswordResetEmail($email, $name, $resetLink) {
        $subject = "Password Reset Request - Bizma";
        
        $message = "
        <html>
        <head>
            <title>Password Reset Request</title>
        </head>
        <body>
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                <h2 style='color: #3b82f6;'>Password Reset Request</h2>
                
                <p>Hello " . htmlspecialchars($name) . ",</p>
                
                <p>We received a request to reset your password for your Bizma account.</p>
                
                <p>Click the button below to reset your password:</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='" . htmlspecialchars($resetLink) . "' 
                       style='background-color: #3b82f6; color: white; padding: 12px 24px; 
                              text-decoration: none; border-radius: 6px; display: inline-block;'>
                        Reset Password
                    </a>
                </div>
                
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; color: #6b7280;'>" . htmlspecialchars($resetLink) . "</p>
                
                <p><strong>This link will expire in 1 hour.</strong></p>
                
                <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;'>
                
                <p style='color: #6b7280; font-size: 14px;'>
                    Best regards,<br>
                    The Bizma Team
                </p>
            </div>
        </body>
        </html>
        ";
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Send verification email
     */
    public static function sendVerificationEmail($email, $name, $verificationLink) {
        $subject = "Verify Your Email - Bizma";
        
        $message = "
        <html>
        <head>
            <title>Email Verification</title>
        </head>
        <body>
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                <h2 style='color: #3b82f6;'>Welcome to Bizma!</h2>
                
                <p>Hello " . htmlspecialchars($name) . ",</p>
                
                <p>Thank you for registering with Bizma. Please verify your email address to complete your registration.</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='" . htmlspecialchars($verificationLink) . "' 
                       style='background-color: #10b981; color: white; padding: 12px 24px; 
                              text-decoration: none; border-radius: 6px; display: inline-block;'>
                        Verify Email
                    </a>
                </div>
                
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; color: #6b7280;'>" . htmlspecialchars($verificationLink) . "</p>
                
                <hr style='margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;'>
                
                <p style='color: #6b7280; font-size: 14px;'>
                    Best regards,<br>
                    The Bizma Team
                </p>
            </div>
        </body>
        </html>
        ";
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Core email sending function
     */
    private static function sendEmail($to, $subject, $message) {
        // For development/testing - log emails instead of sending
        if (self::isDevelopmentMode()) {
            error_log("=== EMAIL WOULD BE SENT ===");
            error_log("To: " . $to);
            error_log("Subject: " . $subject);
            error_log("Message: " . strip_tags($message));
            error_log("=== END EMAIL ===");
            return true;
        }
        
        // Production email sending
        $headers = array(
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Bizma <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        );
        
        return mail($to, $subject, $message, implode("\r\n", $headers));
    }
    
    /**
     * Check if we're in development mode
     */
    private static function isDevelopmentMode() {
        // Check if we're on localhost or development environment
        return (
            $_SERVER['HTTP_HOST'] === 'localhost' ||
            strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
            strpos($_SERVER['HTTP_HOST'], 'dev.') === 0 ||
            strpos($_SERVER['HTTP_HOST'], 'test.') === 0
        );
    }
    
    /**
     * Configure SMTP settings (for production)
     */
    public static function configureSMTP($host, $port, $username, $password, $encryption = 'tls') {
        // This would be used with PHPMailer or similar library in production
        // For now, we'll use PHP's built-in mail() function
        
        // Store SMTP settings for future use
        $_SESSION['smtp_config'] = [
            'host' => $host,
            'port' => $port,
            'username' => $username,
            'password' => $password,
            'encryption' => $encryption
        ];
    }
}
?>
