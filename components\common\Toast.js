function Toast({ message, type = 'info', onClose, duration = 5000 }) {
    React.useEffect(() => {
        if (duration > 0) {
            const timer = setTimeout(() => {
                onClose();
            }, duration);
            return () => clearTimeout(timer);
        }
    }, [duration, onClose]);

    const getToastStyles = () => {
        const baseStyles = "fixed top-4 right-4 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden z-50 transform transition-all duration-300 ease-in-out";
        
        switch (type) {
            case 'success':
                return `${baseStyles} border-l-4 border-green-500`;
            case 'error':
                return `${baseStyles} border-l-4 border-red-500`;
            case 'warning':
                return `${baseStyles} border-l-4 border-yellow-500`;
            case 'info':
            default:
                return `${baseStyles} border-l-4 border-blue-500`;
        }
    };

    const getIconClass = () => {
        switch (type) {
            case 'success':
                return 'fas fa-check-circle text-green-500';
            case 'error':
                return 'fas fa-exclamation-circle text-red-500';
            case 'warning':
                return 'fas fa-exclamation-triangle text-yellow-500';
            case 'info':
            default:
                return 'fas fa-info-circle text-blue-500';
        }
    };

    return (
        <div className={getToastStyles()}>
            <div className="p-4">
                <div className="flex items-start">
                    <div className="flex-shrink-0">
                        <i className={getIconClass()}></i>
                    </div>
                    <div className="ml-3 w-0 flex-1 pt-0.5">
                        <p className="text-sm font-medium text-gray-900">
                            {message}
                        </p>
                    </div>
                    <div className="ml-4 flex-shrink-0 flex">
                        <button
                            className="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onClick={onClose}
                        >
                            <span className="sr-only">Close</span>
                            <i className="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Toast Manager Component
window.ToastManager = function ToastManager() {
    const [toasts, setToasts] = React.useState([]);

    const addToast = React.useCallback((message, type = 'info', duration = 5000) => {
        const id = Date.now() + Math.random();
        const newToast = { id, message, type, duration };
        setToasts(prev => [...prev, newToast]);
    }, []);

    const removeToast = React.useCallback((id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    }, []);

    // Expose addToast globally
    React.useEffect(() => {
        window.showToast = addToast;
        return () => {
            delete window.showToast;
        };
    }, [addToast]);

    return (
        <div className="toast-container">
            {toasts.map((toast, index) => (
                <div
                    key={toast.id}
                    style={{ top: `${1 + index * 5}rem` }}
                    className="fixed right-4 z-50"
                >
                    <Toast
                        message={toast.message}
                        type={toast.type}
                        duration={toast.duration}
                        onClose={() => removeToast(toast.id)}
                    />
                </div>
            ))}
        </div>
    );
}

// Global toast functions
window.toast = {
    success: (message, duration) => window.showToast && window.showToast(message, 'success', duration),
    error: (message, duration) => window.showToast && window.showToast(message, 'error', duration),
    warning: (message, duration) => window.showToast && window.showToast(message, 'warning', duration),
    info: (message, duration) => window.showToast && window.showToast(message, 'info', duration)
};
