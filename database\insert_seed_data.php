<?php
/**
 * Insert Seed Data for Enhanced SaaS
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🌱 Inserting seed data...\n\n";

try {
    // Insert business types
    echo "📋 Inserting business types...\n";
    
    $businessTypes = [
        [
            'id' => 'jewellery',
            'name' => 'Jewellery Business',
            'description' => 'Specialized for jewellery stores, designers, and manufacturers',
            'icon' => 'fas fa-gem',
            'default_modules' => '["inventory", "customers", "orders", "repairs", "appraisals"]',
            'default_categories' => '["rings", "necklaces", "earrings", "bracelets", "watches", "custom_designs"]',
            'default_features' => '["precious_metals_tracking", "stone_certification", "custom_design_tools", "repair_tracking"]',
            'default_templates' => '["jewellery_invoice", "appraisal_certificate", "repair_receipt"]',
            'sort_order' => 1
        ],
        [
            'id' => 'retail',
            'name' => 'Retail Business',
            'description' => 'Perfect for retail stores, shops, and e-commerce businesses',
            'icon' => 'fas fa-store',
            'default_modules' => '["inventory", "pos", "customers", "orders", "suppliers"]',
            'default_categories' => '["electronics", "clothing", "home_goods", "books", "sports", "beauty"]',
            'default_features' => '["barcode_scanning", "multi_location", "loyalty_programs", "discount_management"]',
            'default_templates' => '["retail_invoice", "receipt", "purchase_order"]',
            'sort_order' => 2
        ],
        [
            'id' => 'education',
            'name' => 'Education Services',
            'description' => 'Designed for schools, training centers, and educational institutions',
            'icon' => 'fas fa-graduation-cap',
            'default_modules' => '["students", "courses", "instructors", "schedules", "assessments"]',
            'default_categories' => '["academic_courses", "vocational_training", "online_courses", "certifications"]',
            'default_features' => '["student_portal", "grade_management", "attendance_tracking", "course_materials"]',
            'default_templates' => '["student_invoice", "certificate", "progress_report"]',
            'sort_order' => 3
        ],
        [
            'id' => 'healthcare',
            'name' => 'Healthcare Services',
            'description' => 'Tailored for clinics, hospitals, and healthcare providers',
            'icon' => 'fas fa-heartbeat',
            'default_modules' => '["patients", "appointments", "treatments", "billing", "records"]',
            'default_categories' => '["general_medicine", "dental", "physiotherapy", "diagnostics", "pharmacy"]',
            'default_features' => '["patient_records", "appointment_scheduling", "prescription_management", "insurance_billing"]',
            'default_templates' => '["medical_invoice", "prescription", "treatment_plan"]',
            'sort_order' => 4
        ],
        [
            'id' => 'consulting',
            'name' => 'Consulting Services',
            'description' => 'Ideal for consultants, agencies, and professional services',
            'icon' => 'fas fa-briefcase',
            'default_modules' => '["clients", "projects", "time_tracking", "proposals", "contracts"]',
            'default_categories' => '["business_consulting", "it_services", "marketing", "legal", "financial"]',
            'default_features' => '["project_management", "time_billing", "proposal_generation", "client_portal"]',
            'default_templates' => '["consulting_proposal", "project_invoice", "service_agreement"]',
            'sort_order' => 5
        ],
        [
            'id' => 'manufacturing',
            'name' => 'Manufacturing',
            'description' => 'Built for manufacturers, suppliers, and production companies',
            'icon' => 'fas fa-industry',
            'default_modules' => '["production", "inventory", "suppliers", "quality", "orders"]',
            'default_categories' => '["raw_materials", "finished_goods", "machinery", "tools", "packaging"]',
            'default_features' => '["production_planning", "quality_control", "supplier_management", "batch_tracking"]',
            'default_templates' => '["production_order", "quality_report", "supplier_invoice"]',
            'sort_order' => 6
        ]
    ];
    
    $stmt = $conn->prepare("INSERT INTO business_types (id, name, description, icon, default_modules, default_categories, default_features, default_templates, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, TRUE, ?) ON DUPLICATE KEY UPDATE name = VALUES(name), description = VALUES(description), icon = VALUES(icon), default_modules = VALUES(default_modules), default_categories = VALUES(default_categories), default_features = VALUES(default_features), default_templates = VALUES(default_templates), sort_order = VALUES(sort_order)");
    
    foreach ($businessTypes as $type) {
        $stmt->bind_param("ssssssssi", 
            $type['id'], 
            $type['name'], 
            $type['description'], 
            $type['icon'], 
            $type['default_modules'], 
            $type['default_categories'], 
            $type['default_features'], 
            $type['default_templates'], 
            $type['sort_order']
        );
        
        if ($stmt->execute()) {
            echo "✅ Inserted business type: {$type['name']}\n";
        } else {
            echo "❌ Failed to insert business type: {$type['name']} - " . $stmt->error . "\n";
        }
    }
    
    // Insert pricing plans
    echo "\n💰 Inserting pricing plans...\n";
    
    $pricingPlans = [
        [
            'id' => 'trial',
            'name' => 'Free Trial',
            'description' => 'Try all features free for 14 days',
            'short_description' => 'Full access trial',
            'price_monthly' => 0.00,
            'price_yearly' => 0.00,
            'trial_days' => 14,
            'features' => '["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Basic Analytics", "Email Notifications", "Data Export"]',
            'limits_data' => '{"max_customers": 50, "max_invoices": 20, "max_quotations": 20, "max_users": 2, "storage_gb": 1}',
            'business_types' => '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
            'is_trial_available' => false,
            'is_visible' => false,
            'is_popular' => false,
            'sort_order' => 0
        ],
        [
            'id' => 'basic',
            'name' => 'Business Plan',
            'description' => 'Complete business management solution with all essential features',
            'short_description' => 'Everything you need to run your business',
            'price_monthly' => 500.00,
            'price_yearly' => 5000.00,
            'trial_days' => 14,
            'features' => '["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Business Analytics", "Email Notifications", "Data Export", "Multi-user Access", "24/7 Support", "Custom Branding", "API Access"]',
            'limits_data' => '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": 10, "storage_gb": 50}',
            'business_types' => '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
            'is_trial_available' => true,
            'is_visible' => true,
            'is_popular' => true,
            'sort_order' => 1
        ],
        [
            'id' => 'premium',
            'name' => 'Premium Plan',
            'description' => 'Advanced features for growing businesses',
            'short_description' => 'Scale your business with premium tools',
            'price_monthly' => 1000.00,
            'price_yearly' => 10000.00,
            'trial_days' => 14,
            'features' => '["Everything in Business Plan", "Advanced Analytics", "Custom Reports", "Priority Support", "White Label", "Advanced Integrations", "Bulk Operations", "Advanced Security"]',
            'limits_data' => '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": 25, "storage_gb": 100}',
            'business_types' => '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
            'is_trial_available' => true,
            'is_visible' => true,
            'is_popular' => false,
            'sort_order' => 2
        ]
    ];
    
    $stmt = $conn->prepare("INSERT INTO pricing_plans (id, name, description, short_description, price_monthly, price_yearly, trial_days, features, limits_data, business_types, is_trial_available, is_visible, is_popular, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name), description = VALUES(description), short_description = VALUES(short_description), price_monthly = VALUES(price_monthly), price_yearly = VALUES(price_yearly), trial_days = VALUES(trial_days), features = VALUES(features), limits_data = VALUES(limits_data), business_types = VALUES(business_types), is_trial_available = VALUES(is_trial_available), is_visible = VALUES(is_visible), is_popular = VALUES(is_popular), sort_order = VALUES(sort_order)");
    
    foreach ($pricingPlans as $plan) {
        $stmt->bind_param("ssssddisssiiiii", 
            $plan['id'], 
            $plan['name'], 
            $plan['description'], 
            $plan['short_description'], 
            $plan['price_monthly'], 
            $plan['price_yearly'], 
            $plan['trial_days'], 
            $plan['features'], 
            $plan['limits_data'], 
            $plan['business_types'], 
            $plan['is_trial_available'], 
            $plan['is_visible'], 
            $plan['is_popular'], 
            $plan['sort_order']
        );
        
        if ($stmt->execute()) {
            echo "✅ Inserted pricing plan: {$plan['name']}\n";
        } else {
            echo "❌ Failed to insert pricing plan: {$plan['name']} - " . $stmt->error . "\n";
        }
    }
    
    echo "\n🎉 Seed data inserted successfully!\n";
    
} catch (Exception $e) {
    echo "💥 Seed data insertion failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Seed data insertion completed.\n";
?>
