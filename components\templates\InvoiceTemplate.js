function InvoiceTemplate({ invoice, companyInfo }) {
    try {
        // Check for customer data and handle if it's just an ID
        const [customerData, setCustomerData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [qrCodeData, setQrCodeData] = React.useState(null);

        // Extract template settings from companyInfo with enhanced A4 defaults
        const templates = companyInfo && companyInfo.templates ? companyInfo.templates : {
            paperSize: 'a4',
            orientation: 'portrait',
            margins: { top: 15, right: 15, bottom: 15, left: 15 },
            invoiceTemplate: 'modern',
            headerColor: '#3b82f6',
            accentColor: '#1e3a8a',
            fontFamily: 'Segoe UI, sans-serif',
            fontSize: 'medium',
            showLogo: true,
            showSignature: true,
            showWatermark: true,
            // Enhanced A4 specific settings
            lineHeight: 'normal',
            headerStyle: 'modern',
            footerText: '',
            showPageNumbers: true,
            showCompanyDetails: true,
            showBankDetails: true,
            logoPosition: 'top-right',
            logoSize: 'medium',
            borderStyle: 'minimal',
            tableStyle: 'striped'
        };

        // Generate CSS classes for paper size and styling
        const getPaperClasses = () => {
            const paperSize = templates.paperSize || 'a4';
            const classes = [`${paperSize}-page`];

            // Add margin class
            if (templates.margins) {
                const { top, right, bottom, left } = templates.margins;
                if (top === 10 && right === 10 && bottom === 10 && left === 10) {
                    classes.push('margin-minimal');
                } else if (top === 20 && right === 20 && bottom === 20 && left === 20) {
                    classes.push('margin-comfortable');
                } else if (right === 25 && left === 25) {
                    classes.push('margin-wide');
                } else {
                    classes.push('margin-standard');
                }
            }

            // Add font size class
            classes.push(`font-${templates.fontSize || 'medium'}`);

            // Add line height class
            classes.push(`line-height-${templates.lineHeight || 'normal'}`);

            // Add border style class
            classes.push(`border-${templates.borderStyle || 'minimal'}`);

            // Add page numbers class
            if (templates.showPageNumbers !== false) {
                classes.push('show-page-numbers');
            }

            return classes.join(' ');
        };

        React.useEffect(() => {
            async function fetchCustomerData() {
                try {
                    const invoiceData = invoice.objectData || invoice;
                    if (typeof invoiceData.customer === 'string' || invoiceData.customer instanceof String) {
                        // If customer is just an ID, fetch the customer data
                        const customer = await trickleGetObject('customer', invoiceData.customer);
                        setCustomerData(customer.objectData);
                    } else {
                        // If customer is already an object
                        setCustomerData(invoiceData.customer);
                    }
                } catch (error) {
                    console.error('Error fetching customer data:', error);
                } finally {
                    setLoading(false);
                }
            }
            
            fetchCustomerData();
        }, [invoice]);

        React.useEffect(() => {
            // Generate QR code for UPI payments if applicable
            if (!loading && companyInfo && companyInfo.upiId && invoice && invoice.objectData && invoice.objectData.status !== 'paid') {
                const invoiceData = invoice.objectData || invoice;
                const paymentInfo = {
                    pa: companyInfo.upiId,
                    pn: companyInfo.companyName,
                    am: calculateBalanceDue().toString(),
                    cu: 'INR',
                    tn: `Invoice ${invoiceData.invoiceNumber || invoice.objectId}`
                };
                
                // Format as UPI URI
                const upiString = `upi://pay?${Object.entries(paymentInfo)
                    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
                    .join('&')}`;
                
                generateQrCode(upiString);
            }
        }, [loading, companyInfo, invoice]);

        // Generate QR code using the library
        const generateQrCode = (data) => {
            try {
                if (typeof QRCode !== 'undefined') {
                    // Create a container element
                    const container = document.createElement('div');
                    
                    // Create QR code in the container
                    new QRCode(container, {
                        text: data,
                        width: 128,
                        height: 128,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.H
                    });
                    
                    // Extract the generated image
                    const qrImage = container.querySelector('img');
                    if (qrImage && qrImage.src) {
                        setQrCodeData(qrImage.src);
                    }
                }
            } catch (error) {
                console.error('Error generating QR code:', error);
            }
        };

        // Format status text with first letter capitalized
        const getStatusText = (status) => {
            if (!status) return 'Draft';
            return status.charAt(0).toUpperCase() + status.slice(1);
        };

        // Calculate all amounts to ensure they're displayed correctly
        const calculateSubtotal = () => {
            const invoiceData = invoice.objectData || invoice;
            if (invoiceData.subtotal) return invoiceData.subtotal;
            
            // Calculate from items if subtotal not provided
            if (invoiceData.items && Array.isArray(invoiceData.items)) {
                return invoiceData.items.reduce((total, item) => 
                    total + (item.quantity * item.price), 0
                );
            }
            
            return 0;
        };

        const calculateTax = () => {
            const invoiceData = invoice.objectData || invoice;
            if (invoiceData.tax) return invoiceData.tax;
            
            const subtotal = calculateSubtotal();
            const discount = parseFloat(invoiceData.discount) || 0;
            const taxRate = parseFloat(invoiceData.taxRate) || 0;
            
            return (subtotal - discount) * (taxRate / 100);
        };

        const calculateTotal = () => {
            const invoiceData = invoice.objectData || invoice;
            if (invoiceData.total) return invoiceData.total;
            
            const subtotal = calculateSubtotal();
            const discount = parseFloat(invoiceData.discount) || 0;
            const tax = calculateTax();
            
            return subtotal - discount + tax;
        };

        const calculateBalanceDue = () => {
            const invoiceData = invoice.objectData || invoice;
            const total = calculateTotal();
            const amountPaid = parseFloat(invoiceData.amountPaid) || 0;
            return total - amountPaid;
        };

        // Format payment method for display
        const getPaymentMethodText = (method) => {
            if (!method) return 'Not specified';
            
            const methodMap = {
                'bank_transfer': 'Bank Transfer',
                'credit_card': 'Credit Card',
                'cash': 'Cash',
                'upi': 'UPI',
                'cheque': 'Cheque',
                'other': 'Other'
            };
            
            return methodMap[method.toLowerCase()] || method;
        };

        // Get payment method icon
        const getPaymentMethodIcon = (method) => {
            if (!method) return 'fas fa-money-bill';
            
            const iconMap = {
                'bank_transfer': 'fas fa-university',
                'credit_card': 'fas fa-credit-card',
                'cash': 'fas fa-money-bill-wave',
                'upi': 'fas fa-mobile-alt',
                'cheque': 'fas fa-money-check',
                'other': 'fas fa-money-bill'
            };
            
            return iconMap[method.toLowerCase()] || 'fas fa-money-bill';
        };

        // Apply template settings to style
        const getTemplateStyles = () => {
            let style = {};
            
            // Font family
            if (templates.fontFamily) {
                style.fontFamily = templates.fontFamily;
            }
            
            // Font size
            const fontSizeMap = {
                'small': '0.875rem',
                'medium': '1rem',
                'large': '1.125rem'
            };
            style.fontSize = fontSizeMap[templates.fontSize] || '1rem';
            
            // Colors
            style.headerColor = templates.headerColor || '#3b82f6';
            style.accentColor = templates.accentColor || '#1e3a8a';
            
            // Paper size
            const paperSizeClass = templates.paperSize || 'a4';
            const orientationClass = templates.orientation || 'portrait';
            
            return {
                style,
                paperSizeClass,
                orientationClass
            };
        };

        const { style, paperSizeClass, orientationClass } = getTemplateStyles();

        if (loading) {
            return (
                <div className="flex justify-center items-center p-8">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        const invoiceData = invoice.objectData || invoice;
        
        // Get status for watermark
        const isDraft = !invoiceData.status || invoiceData.status.toLowerCase() === 'draft';
        const isPaid = invoiceData.status && invoiceData.status.toLowerCase() === 'paid';
        
        // Get days overdue if applicable
        const getDaysOverdue = () => {
            if (!invoiceData.dueDate || invoiceData.status === 'paid') return null;
            
            const dueDate = new Date(invoiceData.dueDate);
            const today = new Date();
            
            if (today > dueDate) {
                const diffTime = Math.abs(today - dueDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return diffDays;
            }
            
            return null;
        };
        
        const daysOverdue = getDaysOverdue();
        
        return (
            <div
                data-name="invoice-template"
                className={`template-container ${getPaperClasses()} ${orientationClass}`}
                style={style}
            >
                {templates.showWatermark && isDraft && <div className="watermark">DRAFT</div>}
                {templates.showWatermark && isPaid && <div className="watermark">PAID</div>}
                
                {/* Enhanced A4 Header */}
                <div className={`template-header style-${templates.headerStyle || 'modern'} flex justify-between items-start mb-8`}>
                    <div>
                        {templates.showLogo && companyInfo.logo && (
                            <img
                                src={companyInfo.logo}
                                alt="Company Logo"
                                className={`company-logo position-${templates.logoPosition || 'top-right'} size-${templates.logoSize || 'medium'} h-16 mb-4`}
                            />
                        )}
                        {templates.showCompanyDetails !== false && (
                            <div className="text-gray-600">
                                <p className="font-bold text-lg text-gray-800">{companyInfo.companyName}</p>
                                <p className="text-sm">{companyInfo.companyAddress}</p>
                                <p className="text-sm">{companyInfo.companyPhone}</p>
                                <p className="text-sm">{companyInfo.companyEmail}</p>
                                {companyInfo.companyGST && <p className="text-sm">GSTIN: {companyInfo.companyGST}</p>}
                            </div>
                        )}
                    </div>
                    
                    <div className="text-right">
                        <h1 
                            className="text-4xl font-bold mb-2"
                            style={{ color: style.headerColor }}
                        >
                            INVOICE
                        </h1>
                        <p className="text-gray-600 text-lg font-semibold">#{invoiceData.invoiceNumber || invoice.objectId.substring(0, 8)}</p>
                        
                        <div className="mt-4 bg-gray-100 px-4 py-2 rounded-lg">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-xs text-gray-500">Issue Date</p>
                                    <p className="font-medium">{formatDate(invoice.createdAt)}</p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500">Due Date</p>
                                    <p className="font-medium">{formatDate(invoiceData.dueDate)}</p>
                                </div>
                            </div>
                            
                            <div className="mt-2">
                                <p className="text-xs text-gray-500">Status</p>
                                <p>
                                    <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium status-${invoiceData.status || 'draft'}`}>
                                        {getStatusText(invoiceData.status)}
                                        {daysOverdue && invoiceData.status === 'overdue' && ` (${daysOverdue} days)`}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Client Info */}
                <div className="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h2 className="text-sm font-semibold uppercase text-gray-500 mb-2">Bill To</h2>
                    {customerData ? (
                        <div>
                            <h3 className="text-lg font-bold text-gray-800 mb-1">{customerData.name}</h3>
                            {customerData.company && <p className="text-gray-700">{customerData.company}</p>}
                            {customerData.address && <p className="text-gray-600 whitespace-pre-line text-sm">{customerData.address}</p>}
                            {customerData.email && <p className="text-gray-600 text-sm">{customerData.email}</p>}
                            {customerData.phone && <p className="text-gray-600 text-sm">{customerData.phone}</p>}
                            {customerData.gst && <p className="text-gray-600 text-sm">GSTIN: {customerData.gst}</p>}
                        </div>
                    ) : (
                        <p className="text-gray-600">Customer information not available</p>
                    )}
                </div>

                {/* Enhanced A4 Items Table */}
                <div className="mb-8">
                    <table className={`invoice-table style-${templates.tableStyle || 'striped'} w-full border-collapse`}>
                        <thead>
                            <tr style={{ backgroundColor: `${style.headerColor}15` }}>
                                <th className="py-3 px-4 text-left font-semibold text-gray-600 border-b border-gray-200">Item & Description</th>
                                <th className="py-3 px-4 text-center font-semibold text-gray-600 border-b border-gray-200">Qty</th>
                                <th className="py-3 px-4 text-right font-semibold text-gray-600 border-b border-gray-200">Rate</th>
                                <th className="py-3 px-4 text-right font-semibold text-gray-600 border-b border-gray-200">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            {invoiceData.items && invoiceData.items.map((item, index) => (
                                <tr key={index} className="border-b border-gray-100">
                                    <td className="py-3 px-4">
                                        <div className="font-medium">{item.description}</div>
                                        {item.details && <div className="text-gray-500 text-xs mt-1">{item.details}</div>}
                                    </td>
                                    <td className="py-3 px-4 text-center">{item.quantity}</td>
                                    <td className="py-3 px-4 text-right">{formatCurrency(item.price)}</td>
                                    <td className="py-3 px-4 text-right font-medium">{formatCurrency(item.quantity * item.price)}</td>
                                </tr>
                            ))}
                            
                            {/* Empty rows to make the table look better */}
                            {invoiceData.items && invoiceData.items.length < 3 && (
                                Array(3 - invoiceData.items.length).fill().map((_, index) => (
                                    <tr key={`empty-${index}`} className="border-b border-gray-100">
                                        <td className="py-3 px-4">&nbsp;</td>
                                        <td className="py-3 px-4">&nbsp;</td>
                                        <td className="py-3 px-4">&nbsp;</td>
                                        <td className="py-3 px-4">&nbsp;</td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>

                {/* Totals and Notes */}
                <div className="flex flex-col md:flex-row gap-8">
                    {/* Notes & Terms */}
                    <div className="md:w-1/2">
                        {invoiceData.notes && (
                            <div className="mb-4">
                                <h3 className="text-sm font-semibold uppercase text-gray-500 mb-2">Notes</h3>
                                <p className="text-gray-600 text-sm">{invoiceData.notes}</p>
                            </div>
                        )}
                        
                        {invoiceData.terms && (
                            <div className="mb-4">
                                <h3 className="text-sm font-semibold uppercase text-gray-500 mb-2">Terms & Conditions</h3>
                                <p className="text-gray-600 text-sm">{invoiceData.terms}</p>
                            </div>
                        )}
                        
                        {/* Payment Method */}
                        <div className="mt-4">
                            <h3 className="text-sm font-semibold uppercase text-gray-500 mb-2">Payment Method</h3>
                            <div className="flex items-center text-gray-600">
                                <i className={`${getPaymentMethodIcon(invoiceData.paymentMethod)} mr-2`}></i>
                                <span>{getPaymentMethodText(invoiceData.paymentMethod)}</span>
                            </div>
                        </div>
                        
                        {/* Payment Status */}
                        {invoiceData.status === 'paid' && invoiceData.paidAt && (
                            <div className="mt-4 p-2 bg-green-50 border border-green-200 rounded-md">
                                <h3 className="text-sm font-semibold text-green-800 mb-1">Payment Received</h3>
                                <p className="text-xs text-green-700">
                                    <i className="fas fa-check-circle mr-1"></i>
                                    Paid on {formatDate(invoiceData.paidAt)}
                                </p>
                            </div>
                        )}
                        
                        {invoiceData.status === 'overdue' && (
                            <div className="mt-4 p-2 bg-red-50 border border-red-200 rounded-md">
                                <h3 className="text-sm font-semibold text-red-800 mb-1">Payment Overdue</h3>
                                <p className="text-xs text-red-700">
                                    <i className="fas fa-exclamation-circle mr-1"></i>
                                    {daysOverdue ? `Overdue by ${daysOverdue} days` : 'Payment is past due'}
                                </p>
                            </div>
                        )}
                    </div>
                    
                    {/* Totals */}
                    <div className="md:w-1/2">
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <div className="flex justify-between py-2">
                                <span className="text-gray-600">Subtotal</span>
                                <span className="font-medium">{formatCurrency(calculateSubtotal())}</span>
                            </div>
                            
                            {(invoiceData.discount > 0) && (
                                <div className="flex justify-between py-2 border-t border-gray-200">
                                    <span className="text-gray-600">Discount</span>
                                    <span className="font-medium">-{formatCurrency(invoiceData.discount)}</span>
                                </div>
                            )}
                            
                            {(invoiceData.taxRate > 0 || invoiceData.tax > 0) && (
                                <div className="flex justify-between py-2 border-t border-gray-200">
                                    <span className="text-gray-600">Tax ({invoiceData.taxRate || 0}%)</span>
                                    <span className="font-medium">{formatCurrency(calculateTax())}</span>
                                </div>
                            )}
                            
                            <div className="flex justify-between py-2 border-t border-gray-200 font-bold text-lg">
                                <span>Total</span>
                                <span style={{ color: style.accentColor }}>{formatCurrency(calculateTotal())}</span>
                            </div>
                            
                            {invoiceData.amountPaid > 0 && (
                                <React.Fragment>
                                    <div className="flex justify-between py-2 border-t border-gray-200">
                                        <span className="text-gray-600">Amount Paid</span>
                                        <span className="font-medium">-{formatCurrency(invoiceData.amountPaid)}</span>
                                    </div>
                                    <div className="flex justify-between py-2 border-t border-gray-200 font-bold">
                                        <span>Balance Due</span>
                                        <span style={{ color: style.accentColor }}>{formatCurrency(calculateBalanceDue())}</span>
                                    </div>
                                </React.Fragment>
                            )}
                        </div>
                        
                        {/* QR Code for UPI Payment */}
                        {invoiceData.status !== 'paid' && invoiceData.paymentMethod === 'upi' && qrCodeData && (
                            <div className="mt-4 flex flex-col items-center p-3 border border-gray-200 rounded-lg bg-white">
                                <p className="text-sm font-medium text-gray-700 mb-2">Scan to Pay via UPI</p>
                                <img 
                                    src={qrCodeData} 
                                    alt="UPI QR Code"
                                    className="w-32 h-32 mb-2"
                                />
                                <p className="text-xs text-gray-500">{companyInfo.upiId}</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Payment Instructions */}
                {companyInfo.bankDetails && (
                    <div className="mt-8 p-4 border border-dashed border-gray-300 rounded-lg">
                        <h3 className="text-sm font-semibold uppercase text-gray-500 mb-2">Payment Instructions</h3>
                        <div className="text-gray-600 text-sm whitespace-pre-line">
                            {companyInfo.bankDetails}
                        </div>
                    </div>
                )}

                {/* Signature */}
                {templates.showSignature && (
                    <div className="mt-8 flex justify-end">
                        <div className="text-center">
                            <div className="w-48 border-b border-gray-300 mb-2">
                                {companyInfo.signature && (
                                    <img 
                                        src={companyInfo.signature} 
                                        alt="Authorized Signature" 
                                        className="h-12 mx-auto mb-1"
                                    />
                                )}
                            </div>
                            <p className="font-semibold text-sm">{companyInfo.authorizedName || companyInfo.companyName}</p>
                            <p className="text-xs text-gray-500">Authorized Signatory</p>
                        </div>
                    </div>
                )}

                {/* Enhanced A4 Footer */}
                <div className="template-footer mt-8 pt-4 border-t border-gray-200 text-center">
                    {templates.footerText ? (
                        <p className="font-medium text-gray-700 mb-1">{templates.footerText}</p>
                    ) : (
                        <p className="font-medium text-gray-700 mb-1">Thank you for your business!</p>
                    )}
                    <p className="text-sm text-gray-500">If you have any questions concerning this invoice, please contact us.</p>
                    {companyInfo.companyWebsite && <p className="text-sm text-gray-500 mt-1">{companyInfo.companyWebsite}</p>}
                </div>
            </div>
        );
    } catch (error) {
        console.error('InvoiceTemplate component error:', error);
        reportError(error);
        return (
            <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded">
                Error rendering invoice template. Please try again.
            </div>
        );
    }
}
