<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/utils/SecurityUtils.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Check if superadmin account exists
    $email = '<EMAIL>';
    $stmt = $conn->prepare("SELECT id, object_id, name, email, password, role, status, created_at, last_login FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'status' => 'not_found',
            'message' => 'Superadmin account not found',
            'email' => $email
        ]);
    } else {
        $user = $result->fetch_assoc();
        
        // Don't expose the actual password hash for security
        $userInfo = $user;
        $userInfo['password'] = '[HIDDEN - Length: ' . strlen($user['password']) . ']';
        $userInfo['password_starts_with'] = substr($user['password'], 0, 10) . '...';
        
        echo json_encode([
            'status' => 'found',
            'message' => 'Superadmin account found',
            'user_info' => $userInfo,
            'password_verification' => [
                'is_bcrypt' => password_get_info($user['password']),
                'password_length' => strlen($user['password'])
            ]
        ]);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
