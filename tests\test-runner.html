<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bizma Test Suite</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-pass { color: #10b981; }
        .test-fail { color: #ef4444; }
        .test-pending { color: #f59e0b; }
        .test-running { color: #3b82f6; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-vial mr-2 text-blue-600"></i>
                    Bizma Test Suite
                </h1>
                <div class="flex space-x-3">
                    <button id="runAllTests" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-play mr-2"></i>
                        Run All Tests
                    </button>
                    <button id="clearResults" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                        <i class="fas fa-trash mr-2"></i>
                        Clear Results
                    </button>
                </div>
            </div>

            <!-- Test Summary -->
            <div id="testSummary" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 hidden">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                        <div>
                            <p class="text-sm text-green-600">Passed</p>
                            <p id="passedCount" class="text-2xl font-bold text-green-700">0</p>
                        </div>
                    </div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-times-circle text-red-500 text-xl mr-3"></i>
                        <div>
                            <p class="text-sm text-red-600">Failed</p>
                            <p id="failedCount" class="text-2xl font-bold text-red-700">0</p>
                        </div>
                    </div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-clock text-yellow-500 text-xl mr-3"></i>
                        <div>
                            <p class="text-sm text-yellow-600">Pending</p>
                            <p id="pendingCount" class="text-2xl font-bold text-yellow-700">0</p>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-list text-blue-500 text-xl mr-3"></i>
                        <div>
                            <p class="text-sm text-blue-600">Total</p>
                            <p id="totalCount" class="text-2xl font-bold text-blue-700">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Categories -->
            <div class="space-y-6">
                <!-- Authentication Tests -->
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-user-shield mr-2"></i>
                            Authentication & Registration Tests
                        </h3>
                    </div>
                    <div id="authTests" class="p-4 space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>

                <!-- Subscription Tests -->
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-credit-card mr-2"></i>
                            Subscription & Trial Tests
                        </h3>
                    </div>
                    <div id="subscriptionTests" class="p-4 space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>

                <!-- Plan Management Tests -->
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-cog mr-2"></i>
                            Plan Management Tests
                        </h3>
                    </div>
                    <div id="planTests" class="p-4 space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>

                <!-- Payment Integration Tests -->
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-money-bill mr-2"></i>
                            Payment Integration Tests
                        </h3>
                    </div>
                    <div id="paymentTests" class="p-4 space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>

                <!-- Business Type Tests -->
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-building mr-2"></i>
                            Business Type Configuration Tests
                        </h3>
                    </div>
                    <div id="businessTypeTests" class="p-4 space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>

                <!-- API Integration Tests -->
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-plug mr-2"></i>
                            API Integration Tests
                        </h3>
                    </div>
                    <div id="apiTests" class="p-4 space-y-2">
                        <!-- Test results will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Test Progress -->
            <div id="testProgress" class="mt-6 hidden">
                <div class="bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p id="progressText" class="text-sm text-gray-600 mt-2">Ready to run tests...</p>
            </div>
        </div>
    </div>

    <!-- Test Framework -->
    <script src="test-framework.js"></script>
    <script src="auth-tests.js"></script>
    <script src="subscription-tests.js"></script>
    <script src="plan-tests.js"></script>
    <script src="payment-tests.js"></script>
    <script src="business-type-tests.js"></script>
    <script src="api-tests.js"></script>

    <script>
        // Initialize test runner
        document.addEventListener('DOMContentLoaded', function() {
            const testRunner = new TestRunner();
            
            document.getElementById('runAllTests').addEventListener('click', () => {
                testRunner.runAllTests();
            });
            
            document.getElementById('clearResults').addEventListener('click', () => {
                testRunner.clearResults();
            });
        });
    </script>
</body>
</html>
