import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()
    
    console.log('Testing auth for:', email)
    
    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        company: true,
        ownedCompany: true
      }
    })
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }
    
    console.log('User found:', { id: user.id, email: user.email, hasPassword: !!user.password })
    
    if (!user.password) {
      return NextResponse.json({ error: 'No password set' }, { status: 400 })
    }
    
    // Check password
    const isValid = await bcrypt.compare(password, user.password)
    console.log('Password valid:', isValid)
    
    return NextResponse.json({ 
      success: true, 
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    })
    
  } catch (error) {
    console.error('Auth test error:', error)
    return NextResponse.json({ error: 'Server error' }, { status: 500 })
  }
}
