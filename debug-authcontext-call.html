<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug AuthContext Call</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Debug AuthContext Call</h1>
    
    <button onclick="getAndTestToken()">Get Fresh Token & Test</button>
    <button onclick="testCurrentToken()">Test Current Token</button>
    <button onclick="simulateAuthContextCall()">Simulate AuthContext Call</button>
    <button onclick="clearLogs()">Clear Logs</button>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'log') {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log ${type}`;
            logEntry.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsDiv.appendChild(logEntry);
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function getAndTestToken() {
            log('🔄 Getting fresh token...');
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('authToken', data.tokens.access_token);
                    log(`✅ Fresh token obtained: ${data.tokens.access_token.substring(0, 30)}...`, 'success');
                    
                    // Test the token immediately
                    await testToken(data.tokens.access_token);
                } else {
                    log(`❌ Login failed: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testCurrentToken() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                log('❌ No token in localStorage', 'error');
                return;
            }
            
            log(`🔍 Testing current token: ${token.substring(0, 30)}...`);
            await testToken(token);
        }

        async function testToken(token) {
            log(`📤 Testing token verification...`);
            
            try {
                // Test exactly what AuthContext does
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                log(`📥 Response status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const responseText = await response.text();
                    log(`📄 Raw response: ${responseText}`);
                    
                    try {
                        const data = JSON.parse(responseText);
                        log(`📊 Parsed response: ${JSON.stringify(data, null, 2)}`);
                        
                        if (data.success && data.user) {
                            log(`✅ Token verification successful! User: ${data.user.name}`, 'success');
                        } else if (data.success === false) {
                            log(`❌ Token verification failed: ${data.error}`, 'error');
                        } else {
                            log(`❌ Invalid response structure`, 'error');
                        }
                    } catch (parseError) {
                        log(`❌ JSON parse error: ${parseError.message}`, 'error');
                    }
                } else {
                    log(`❌ HTTP error: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function simulateAuthContextCall() {
            log('🎭 Simulating exact AuthContext call...');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                log('❌ No token in localStorage', 'error');
                return;
            }

            try {
                // Simulate exactly what AuthContext does
                const authUrl = '/biz/api/enhanced-auth-handler.php?action=verify';
                log(`📍 Auth URL: ${authUrl}`);
                log(`🔑 Token: ${token.substring(0, 30)}...`);
                
                const response = await fetch(authUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });

                if (response.ok) {
                    const responseText = await response.text();
                    log(`📄 Response text: ${responseText}`);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        log(`❌ Failed to parse JSON: ${parseError.message}`, 'error');
                        log(`❌ Response text: ${responseText}`, 'error');
                        throw new Error('Server returned invalid JSON response');
                    }

                    if (data.success && data.user) {
                        log(`✅ AuthContext simulation successful!`, 'success');
                        log(`👤 User: ${data.user.name} (${data.user.email})`, 'success');
                    } else if (data.success === false) {
                        log(`❌ AuthContext would fail: ${data.error}`, 'error');
                        throw new Error(data.error || 'Authentication failed');
                    } else {
                        log(`❌ Invalid user data received`, 'error');
                        throw new Error('Invalid user data received');
                    }
                } else {
                    log(`❌ HTTP error: ${response.status}`, 'error');
                    if (response.status === 401 || response.status === 403) {
                        throw new Error('Authentication token is invalid or expired');
                    } else {
                        throw new Error(`Server error: ${response.status}`);
                    }
                }
            } catch (error) {
                log(`❌ AuthContext would throw: ${error.message}`, 'error');
            }
        }

        // Check current token on load
        window.onload = function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                log(`🔍 Found token in localStorage: ${token.substring(0, 30)}...`);
            } else {
                log('ℹ️ No token found in localStorage');
            }
        };
    </script>
</body>
</html>