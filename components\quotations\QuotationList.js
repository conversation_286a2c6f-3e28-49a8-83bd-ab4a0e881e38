function QuotationList({ onQuotationClick }) {
    try {
        const [quotations, setQuotations] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [customersMap, setCustomersMap] = React.useState({});

        React.useEffect(() => {
            fetchQuotations();
            fetchCustomers();
        }, []);

        const fetchQuotations = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/quotation'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setQuotations(data.items || []);
                } else {
                    throw new Error('Failed to fetch quotations');
                }
            } catch (error) {
                console.error('Error fetching quotations:', error);
                setQuotations([]);
            } finally {
                setLoading(false);
            }
        };

        const fetchCustomers = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/customer'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const customerMap = {};
                    (data.items || []).forEach(customer => {
                        customerMap[customer.objectId] = customer.objectData;
                    });
                    setCustomersMap(customerMap);
                } else {
                    throw new Error('Failed to fetch customers');
                }
            } catch (error) {
                console.error('Error fetching customers:', error);
                setCustomersMap({});
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters && filters.status ? filters.status : '');
        };

        const filteredQuotations = React.useMemo(() => {
            return quotations.filter(quotation => {
                const customerName = customersMap[quotation.objectData.customer] && customersMap[quotation.objectData.customer].name ? customersMap[quotation.objectData.customer].name : '';
                const quotationNumber = quotation.objectData.quotationNumber || '';
                
                const matchesSearch = !searchQuery || 
                    customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    quotationNumber.toLowerCase().includes(searchQuery.toLowerCase());
                
                const matchesStatus = !selectedStatus || quotation.objectData.status === selectedStatus;

                return matchesSearch && matchesStatus;
            });
        }, [quotations, searchQuery, selectedStatus, customersMap]);

        const quotationStatusFilters = [
            { id: 'status', label: 'Status', type: 'select', options: [
                { label: 'Draft', value: 'draft' },
                { label: 'Sent', value: 'sent' },
                { label: 'Accepted', value: 'accepted' },
                { label: 'Rejected', value: 'rejected' }
            ]}
        ];

        const columns = [
            { 
                key: 'quotationNumber', 
                label: 'Quotation #',
                render: (row) => row.objectData.quotationNumber || `#${row.objectId.substring(0, 8)}`
            },
            { 
                key: 'customer', 
                label: 'Customer',
                render: (row) => customersMap[row.objectData.customer] && customersMap[row.objectData.customer].name ? customersMap[row.objectData.customer].name : 'Unknown Customer'
            },
            { 
                key: 'total', 
                label: 'Total',
                render: (row) => formatCurrency(row.objectData.total)
            },
            {
                key: 'validUntil',
                label: 'Valid Until',
                render: (row) => formatDate(row.objectData.validUntil)
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`quotation-status ${row.objectData.status}`}>
                        {row.objectData.status ? row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1) : 'Unknown'}
                    </span>
                )
            }
        ];

        return (
            <div data-name="quotation-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search quotations..."
                        filters={quotationStatusFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredQuotations}
                    loading={loading}
                    onRowClick={onQuotationClick}
                    emptyMessage="No quotations found"
                />
            </div>
        );
    } catch (error) {
        console.error('QuotationList component error:', error);
        reportError(error);
        return null;
    }
}
