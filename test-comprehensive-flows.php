<?php
/**
 * Comprehensive Flow Testing
 * Tests all major user flows and system functionality
 */

require_once 'api/db-config.php';

echo "<h1>🧪 Comprehensive Flow Testing</h1>\n";

$testResults = [];

// Test 1: Authentication Flow
echo "<h2>1. Authentication Flow Test</h2>\n";

$authTests = [
    'Enhanced Auth Handler' => '/api/enhanced-auth-handler.php?action=login',
    'Simple Auth Handler' => '/api/simple-auth.php',
    'Token Verification' => '/api/enhanced-auth-handler.php?action=verify'
];

foreach ($authTests as $testName => $endpoint) {
    $testData = [
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'rememberMe' => true
    ];
    
    // Add action for simple auth handler
    if ($testName === 'Simple Auth Handler') {
        $testData['action'] = 'login';
    }
    
    // Special handling for token verification
    if ($testName === 'Token Verification') {
        // Get a fresh token first
        $loginResponse = @file_get_contents("http://localhost/biz/api/enhanced-auth-handler.php?action=login", false, stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\n",
                'content' => json_encode(['email' => '<EMAIL>', 'password' => 'admin123']),
                'timeout' => 10
            ]
        ]));
        
        $loginData = json_decode($loginResponse, true);
        $testToken = $loginData['tokens']['access_token'] ?? 'invalid_token';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\nAuthorization: Bearer $testToken\r\n",
                'content' => json_encode(['token' => $testToken]),
                'timeout' => 10
            ]
        ]);
    } else {
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => "Content-Type: application/json\r\n",
                'content' => json_encode($testData),
                'timeout' => 10
            ]
        ]);
    }
    
    $response = @file_get_contents("http://localhost/biz$endpoint", false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $status = $data['success'] ?? false ? '✅' : '❌';
            echo "<p>$status $testName: " . ($data['success'] ?? false ? 'Working' : ($data['error'] ?? 'Failed')) . "</p>\n";
            $testResults[$testName] = $data['success'] ?? false;
        } else {
            echo "<p>❌ $testName: Invalid JSON response</p>\n";
            $testResults[$testName] = false;
        }
    } else {
        echo "<p>❌ $testName: No response</p>\n";
        $testResults[$testName] = false;
    }
}

// Test 2: Subscription Management
echo "<h2>2. Subscription Management Test</h2>\n";

$subscriptionTests = [
    'Current Subscription' => '/api/enhanced-subscription-handler.php?action=current',
    'Usage Statistics' => '/api/enhanced-subscription-handler.php?action=usage',
    'Available Plans' => '/api/enhanced-subscription-handler.php?action=plans'
];

foreach ($subscriptionTests as $testName => $endpoint) {
    $response = @file_get_contents("http://localhost/biz$endpoint");
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $status = $data['success'] ?? false ? '✅' : '❌';
            echo "<p>$status $testName: " . ($data['success'] ?? false ? 'Working' : ($data['error'] ?? 'Failed')) . "</p>\n";
            $testResults[$testName] = $data['success'] ?? false;
        } else {
            echo "<p>❌ $testName: Invalid JSON response</p>\n";
            $testResults[$testName] = false;
        }
    } else {
        echo "<p>❌ $testName: No response</p>\n";
        $testResults[$testName] = false;
    }
}

// Test 3: Dashboard APIs
echo "<h2>3. Dashboard APIs Test</h2>\n";

$dashboardTests = [
    'Dashboard Stats' => '/api/dashboard-stats.php',
    'Recent Activity' => '/api/recent-activity.php',
    'Business Types' => '/api/business-types.php',
    'Pricing Plans' => '/api/pricing-plans.php'
];

foreach ($dashboardTests as $testName => $endpoint) {
    $response = @file_get_contents("http://localhost/biz$endpoint");
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $status = $data['success'] ?? false ? '✅' : '❌';
            echo "<p>$status $testName: " . ($data['success'] ?? false ? 'Working' : ($data['error'] ?? 'Failed')) . "</p>\n";
            $testResults[$testName] = $data['success'] ?? false;
        } else {
            echo "<p>❌ $testName: Invalid JSON response</p>\n";
            $testResults[$testName] = false;
        }
    } else {
        echo "<p>❌ $testName: No response</p>\n";
        $testResults[$testName] = false;
    }
}

// Test 4: CRUD Operations
echo "<h2>4. CRUD Operations Test</h2>\n";

$crudTests = [
    'Create Lead' => ['method' => 'POST', 'endpoint' => '/api/api.php/lead', 'data' => ['name' => 'Test Lead', 'email' => '<EMAIL>', 'phone' => '1234567890']],
    'Create Task' => ['method' => 'POST', 'endpoint' => '/api/api.php/task', 'data' => ['title' => 'Test Task', 'description' => 'Test Description', 'priority' => 'medium']],
    'Create Note' => ['method' => 'POST', 'endpoint' => '/api/api.php/note', 'data' => ['content' => 'Test Note Content']]
];

foreach ($crudTests as $testName => $testConfig) {
    $testData = array_merge($testConfig['data'], [
        'company_id' => 'super_admin_001',
        'user_id' => 'super_admin_001'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => $testConfig['method'],
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode($testData),
            'timeout' => 30,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents("http://localhost/biz{$testConfig['endpoint']}", false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $status = $data['success'] ?? false ? '✅' : '❌';
            echo "<p>$status $testName: " . ($data['success'] ?? false ? 'Working' : ($data['error'] ?? 'Failed')) . "</p>\n";
            $testResults[$testName] = $data['success'] ?? false;
        } else {
            echo "<p>❌ $testName: Invalid JSON response - " . substr($response, 0, 100) . "</p>\n";
            $testResults[$testName] = false;
        }
    } else {
        $error = error_get_last();
        echo "<p>❌ $testName: No response" . ($error ? " - " . $error['message'] : "") . "</p>\n";
        $testResults[$testName] = false;
    }
}

// Test 5: Database Integrity
echo "<h2>5. Database Integrity Test</h2>\n";

$tables = ['users', 'companies', 'subscriptions', 'pricing_plans', 'business_types', 'leads', 'activities', 'tasks', 'notes', 'refresh_tokens'];

foreach ($tables as $table) {
    try {
        $sql = "SELECT COUNT(*) as count FROM $table";
        $result = $conn->query($sql);
        
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p>✅ Table '$table': $count records</p>\n";
            $testResults["Table $table"] = true;
        } else {
            echo "<p>❌ Table '$table': Query failed - " . $conn->error . "</p>\n";
            $testResults["Table $table"] = false;
        }
    } catch (Exception $e) {
        echo "<p>❌ Table '$table': " . $e->getMessage() . "</p>\n";
        $testResults["Table $table"] = false;
    }
}

// Test 6: Frontend Components
echo "<h2>6. Frontend Components Test</h2>\n";

$frontendFiles = [
    'Enhanced Dashboard' => 'components/dashboard/EnhancedDashboard.js',
    'Enhanced Super Admin' => 'components/admin/EnhancedSuperAdminDashboard.js',
    'Enhanced Business Templates' => 'components/templates/EnhancedBusinessTemplates.js',
    'Enhanced Error Boundary' => 'components/common/EnhancedErrorBoundary.js',
    'Enhanced Auth Context' => 'components/auth/AuthContext.js'
];

foreach ($frontendFiles as $testName => $filePath) {
    if (file_exists($filePath)) {
        $size = filesize($filePath);
        echo "<p>✅ $testName: File exists (" . number_format($size) . " bytes)</p>\n";
        $testResults[$testName] = true;
    } else {
        echo "<p>❌ $testName: File missing</p>\n";
        $testResults[$testName] = false;
    }
}

// Test Summary
echo "<h2>📊 Test Summary</h2>\n";

$totalTests = count($testResults);
$passedTests = array_sum($testResults);
$failedTests = $totalTests - $passedTests;
$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 1) : 0;

echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc; margin: 10px 0;'>\n";
echo "<h3>Overall Results:</h3>\n";
echo "<ul>\n";
echo "<li><strong>Total Tests:</strong> $totalTests</li>\n";
echo "<li><strong>Passed:</strong> <span style='color: green;'>$passedTests</span></li>\n";
echo "<li><strong>Failed:</strong> <span style='color: red;'>$failedTests</span></li>\n";
echo "<li><strong>Success Rate:</strong> $successRate%</li>\n";
echo "</ul>\n";

if ($successRate >= 90) {
    echo "<p style='color: green; font-weight: bold;'>🎉 Excellent! System is working well.</p>\n";
} elseif ($successRate >= 70) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Good, but some improvements needed.</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Critical issues found. Immediate attention required.</p>\n";
}

echo "</div>\n";

// Recommendations
echo "<h2>🎯 Recommendations</h2>\n";
echo "<div style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin: 10px 0;'>\n";

if ($failedTests > 0) {
    echo "<h3>Priority Fixes:</h3>\n";
    echo "<ul>\n";
    foreach ($testResults as $testName => $result) {
        if (!$result) {
            echo "<li>Fix: $testName</li>\n";
        }
    }
    echo "</ul>\n";
}

echo "<h3>Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li>Fix any failed tests above</li>\n";
echo "<li>Implement user acceptance testing</li>\n";
echo "<li>Set up monitoring and alerting</li>\n";
echo "<li>Create backup and recovery procedures</li>\n";
echo "<li>Implement security hardening</li>\n";
echo "<li>Optimize performance and caching</li>\n";
echo "</ol>\n";
echo "</div>\n";

$conn->close();
?>