function ItemList({ onItemClick }) {
    try {
        const [items, setItems] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedCategory, setSelectedCategory] = React.useState('');
        const [categories, setCategories] = React.useState([]);
        const [showInactive, setShowInactive] = React.useState(false);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [itemToDelete, setItemToDelete] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const tableRef = React.useRef(null);

        React.useEffect(() => {
            fetchItems();
            fetchCategories();
        }, []);

        const fetchItems = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/item'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setItems(data.items || []);
                } else {
                    throw new Error('Failed to fetch items');
                }
            } catch (error) {
                console.error('Error fetching items:', error);
                setItems([]);
            } finally {
                setLoading(false);
            }
        };

        const fetchCategories = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/item_category'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setCategories(data.items || []);
                } else {
                    throw new Error('Failed to fetch categories');
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
                setCategories([]);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedCategory(filters && filters.category ? filters.category : '');
            setShowInactive(filters && filters.showInactive ? filters.showInactive : false);
        };

        const getCategoryName = (categoryId) => {
            const category = categories.find(cat => cat.objectId === categoryId);
            return category ? category.objectData.name : 'Uncategorized';
        };

        const handlePrintItems = () => {
            if (tableRef.current) {
                printFormattedTable(tableRef.current, 'Items List');
            }
        };

        const handleDeleteItem = (item, e) => {
            e.stopPropagation(); // Prevent row click
            setItemToDelete(item);
            setShowDeleteConfirm(true);
        };

        const confirmDeleteItem = async () => {
            if (!itemToDelete) return;

            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(`${window.getApiUrl('/item')}/${itemToDelete.objectId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: 'Item deleted successfully'
                    });

                    // Remove from local state
                    setItems(items.filter(item => item.objectId !== itemToDelete.objectId));
                } else {
                    throw new Error('Failed to delete item');
                }
            } catch (error) {
                console.error('Error deleting item:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete item'
                });
            } finally {
                setShowDeleteConfirm(false);
                setItemToDelete(null);
            }
        };

        const cancelDeleteItem = () => {
            setShowDeleteConfirm(false);
            setItemToDelete(null);
        };

        const filteredItems = React.useMemo(() => {
            return items.filter(item => {
                const matchesSearch = !searchQuery || 
                    item.objectData.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (item.objectData.sku && item.objectData.sku.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (item.objectData.description && item.objectData.description.toLowerCase().includes(searchQuery.toLowerCase()));
                
                const matchesCategory = !selectedCategory || item.objectData.category === selectedCategory;
                
                const matchesStatus = showInactive || item.objectData.isActive !== false;

                return matchesSearch && matchesCategory && matchesStatus;
            });
        }, [items, searchQuery, selectedCategory, showInactive]);

        const itemFilters = [
            { 
                id: 'category', 
                label: 'Category', 
                type: 'select', 
                options: [
                    { label: 'All Categories', value: '' },
                    ...categories.map(category => ({
                        label: category.objectData.name,
                        value: category.objectId
                    }))
                ]
            },
            { 
                id: 'showInactive', 
                label: 'Show Inactive Items', 
                type: 'checkbox' 
            }
        ];

        const columns = [
            { key: 'name', label: 'Item Name' },
            { 
                key: 'category', 
                label: 'Category',
                render: (row) => getCategoryName(row.objectData.category)
            },
            { 
                key: 'type', 
                label: 'Type',
                render: (row) => {
                    const type = row.objectData.itemType || 'product';
                    const isRecurring = row.objectData.isRecurring;
                    return (
                        <div>
                            <span className="capitalize">{type}</span>
                            {isRecurring && (
                                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    Recurring
                                </span>
                            )}
                        </div>
                    );
                }
            },
            { key: 'sku', label: 'SKU' },
            { 
                key: 'price', 
                label: 'Price',
                render: (row) => formatCurrency(row.objectData.price)
            },
            { 
                key: 'stockQuantity', 
                label: 'Stock',
                render: (row) => {
                    if (row.objectData.itemType === 'service') {
                        return 'N/A';
                    }
                    return `${row.objectData.stockQuantity || 0} ${row.objectData.unit || 'piece'}`;
                }
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`px-2 py-1 text-xs rounded-full ${row.objectData.isActive !== false ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {row.objectData.isActive !== false ? 'Active' : 'Inactive'}
                    </span>
                )
            },
            {
                key: 'actions',
                label: 'Actions',
                render: (row) => (
                    <button
                        onClick={(e) => handleDeleteItem(row, e)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete Item"
                    >
                        <i className="fas fa-trash"></i>
                    </button>
                )
            }
        ];

        return (
            <div data-name="item-list">
                <div className="mb-6 flex justify-between items-center">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search items..."
                        filters={itemFilters}
                    />
                    <Button
                        variant="secondary"
                        icon="fas fa-print"
                        onClick={handlePrintItems}
                    >
                        Print List
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                <div ref={tableRef}>
                    <Table
                        columns={columns}
                        data={filteredItems}
                        loading={loading}
                        onRowClick={onItemClick}
                        emptyMessage="No items found"
                    />
                </div>

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to delete "{itemToDelete && itemToDelete.objectData && itemToDelete.objectData.name ? itemToDelete.objectData.name : 'this item'}"? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={cancelDeleteItem}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={confirmDeleteItem}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('ItemList component error:', error);
        reportError(error);
        return null;
    }
}
