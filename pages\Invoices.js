function Invoices() {
    try {
        const [showForm, setShowForm] = React.useState(false);
        const [showViewer, setShowViewer] = React.useState(false);
        const [selectedInvoice, setSelectedInvoice] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [companyInfo, setCompanyInfo] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [refreshKey, setRefreshKey] = React.useState(0);
        
        // Enhanced state for better UX
        const [searchTerm, setSearchTerm] = React.useState('');
        const [filterStatus, setFilterStatus] = React.useState('all');
        const [sortBy, setSortBy] = React.useState('created_date');
        const [sortOrder, setSortOrder] = React.useState('desc');
        const [selectedInvoices, setSelectedInvoices] = React.useState([]);
        const [showBulkActions, setShowBulkActions] = React.useState(false);
        const [isExporting, setIsExporting] = React.useState(false);
        const [viewMode, setViewMode] = React.useState('grid');
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [invoiceToDelete, setInvoiceToDelete] = React.useState(null);
        
        // Get authentication context
        const authContext = React.useContext(window.AuthContext);
        const { isAuthenticated, token, user } = authContext || {};
        
        // Navigation function using the improved routing system
        const navigate = (page, id = null, action = null, params = {}) => {
            window.dispatchEvent(new CustomEvent('app-navigate', { 
                detail: { 
                    page,
                    id,
                    action,
                    params
                } 
            }));
        };

        React.useEffect(() => {
            loadInitialData();
        }, []);

        const loadInitialData = async () => {
            try {
                setLoading(true);
                
                // Use token from context if available, fallback to localStorage
                const authToken = token || localStorage.getItem('authToken');
                
                if (!authToken) {
                    throw new Error('Authentication required');
                }
                
                // Use the API utility for better error handling
                const settingsUrl = window.getApiUrl('/settings');
                
                const response = await fetch(settingsUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || `Failed to fetch settings (${response.status})`);
                }
                
                const data = await response.json();
                if (data.items && data.items.length > 0) {
                    setCompanyInfo(data.items[0].objectData);
                } else {
                    console.warn('No company settings found');
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
                
                // Handle authentication errors
                if (error.message.includes('Authentication') || error.message.includes('401')) {
                    localStorage.removeItem('authToken');
                    // Redirect to login
                    navigate('login', null, null, { redirect: 'invoices' });
                    return;
                }
                
                setNotification({
                    type: 'error',
                    message: error.message || 'Failed to load company information',
                    duration: 5000
                });
            } finally {
                setLoading(false);
            }
        };

        const handleCreateInvoice = () => {
            setSelectedInvoice(null);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleEditInvoice = (invoice) => {
            setSelectedInvoice(invoice);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleViewInvoice = (invoice) => {
            setSelectedInvoice(invoice);
            setShowViewer(true);
            setShowForm(false);
        };

        const handleInvoiceClick = (invoice) => {
            handleViewInvoice(invoice);
        };

        const handleFormSubmit = async () => {
            setShowForm(false);
            setSelectedInvoice(null);
            setNotification({
                type: 'success',
                message: selectedInvoice 
                    ? 'Invoice updated successfully' 
                    : 'Invoice created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the invoice list instead of reloading the page
            setRefreshKey(prev => prev + 1);
        };

        const handleFormCancel = () => {
            setShowForm(false);
            setSelectedInvoice(null);
        };

        const handleViewerClose = () => {
            setShowViewer(false);
            setSelectedInvoice(null);
            // Don't reload the page, just refresh the invoice list
            setRefreshKey(prev => prev + 1);
        };

        const handleInvoiceDelete = () => {
            setShowViewer(false);
            setSelectedInvoice(null);
            setNotification({
                type: 'success',
                message: 'Invoice deleted successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            // Refresh the invoice list
            setRefreshKey(prev => prev + 1);
        };

        // Enhanced functions for better UX
        const handleSearch = (term) => {
            setSearchTerm(term);
        };

        const handleFilterChange = (status) => {
            setFilterStatus(status);
        };

        const handleSort = (field) => {
            if (sortBy === field) {
                setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
            } else {
                setSortBy(field);
                setSortOrder('asc');
            }
        };

        const handleSelectInvoice = (invoiceId, isSelected) => {
            if (isSelected) {
                setSelectedInvoices(prev => [...prev, invoiceId]);
            } else {
                setSelectedInvoices(prev => prev.filter(id => id !== invoiceId));
            }
        };

        const handleSelectAll = (invoices, isSelected) => {
            if (isSelected) {
                setSelectedInvoices(invoices.map(i => i.objectId));
            } else {
                setSelectedInvoices([]);
            }
        };

        const handleBulkDelete = async () => {
            if (selectedInvoices.length === 0) return;

            try {
                const token = localStorage.getItem('authToken');
                const deletePromises = selectedInvoices.map(invoiceId =>
                    fetch(`/api/api.php/invoice/${invoiceId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    })
                );

                await Promise.all(deletePromises);
                
                setNotification({
                    type: 'success',
                    message: `${selectedInvoices.length} invoices deleted successfully`
                });
                setSelectedInvoices([]);
                setShowBulkActions(false);
                setRefreshKey(prev => prev + 1);
            } catch (error) {
                console.error('Bulk delete error:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete some invoices'
                });
            }
        };

        const handleBulkStatusUpdate = async (newStatus) => {
            if (selectedInvoices.length === 0) return;

            try {
                const token = localStorage.getItem('authToken');
                const updatePromises = selectedInvoices.map(invoiceId =>
                    fetch(`/api/api.php/invoice/${invoiceId}`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ status: newStatus })
                    })
                );

                await Promise.all(updatePromises);
                
                setNotification({
                    type: 'success',
                    message: `${selectedInvoices.length} invoices updated to ${newStatus}`
                });
                setSelectedInvoices([]);
                setShowBulkActions(false);
                setRefreshKey(prev => prev + 1);
            } catch (error) {
                console.error('Bulk update error:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to update some invoices'
                });
            }
        };

        const handleExportInvoices = async () => {
            setIsExporting(true);
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch('/api/api.php/invoice/export', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `invoices_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    setNotification({
                        type: 'success',
                        message: 'Invoices exported successfully'
                    });
                } else {
                    throw new Error('Export failed');
                }
            } catch (error) {
                console.error('Export error:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to export invoices'
                });
            } finally {
                setIsExporting(false);
            }
        };

        const handleDeleteInvoice = (invoice) => {
            setInvoiceToDelete(invoice);
            setShowDeleteConfirm(true);
        };

        const confirmDeleteInvoice = async () => {
            if (!invoiceToDelete) return;

            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(`/api/api.php/invoice/${invoiceToDelete.objectId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: 'Invoice deleted successfully'
                    });
                    setRefreshKey(prev => prev + 1);
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Failed to delete invoice');
                }
            } catch (error) {
                console.error('Delete invoice error:', error);
                setNotification({
                    type: 'error',
                    message: error.message || 'Failed to delete invoice'
                });
            } finally {
                setShowDeleteConfirm(false);
                setInvoiceToDelete(null);
            }
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="invoices-page" className="p-6">
                {/* Enhanced Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
                        <p className="text-gray-600 mt-1">Manage your billing and payments</p>
                    </div>
                    <div className="flex items-center space-x-3">
                        {selectedInvoices.length > 0 && (
                            <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-600">
                                    {selectedInvoices.length} selected
                                </span>
                                <Button
                                    variant="secondary"
                                    size="sm"
                                    onClick={() => setShowBulkActions(true)}
                                    icon="fas fa-cog"
                                >
                                    Bulk Actions
                                </Button>
                            </div>
                        )}
                        <Button
                            variant="secondary"
                            onClick={handleExportInvoices}
                            loading={isExporting}
                            icon="fas fa-download"
                        >
                            Export
                        </Button>
                        <Button
                            onClick={handleCreateInvoice}
                            icon="fas fa-plus"
                        >
                            Create Invoice
                        </Button>
                    </div>
                </div>

                {/* Enhanced Search and Filter Bar */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
                        <div className="flex-1 max-w-md">
                            <SearchBar
                                placeholder="Search invoices by number, customer, or amount..."
                                onSearch={handleSearch}
                                value={searchTerm}
                            />
                        </div>
                        <div className="flex items-center space-x-4">
                            <select
                                value={filterStatus}
                                onChange={(e) => handleFilterChange(e.target.value)}
                                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="all">All Status</option>
                                <option value="draft">Draft</option>
                                <option value="sent">Sent</option>
                                <option value="paid">Paid</option>
                                <option value="overdue">Overdue</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                            <select
                                value={`${sortBy}-${sortOrder}`}
                                onChange={(e) => {
                                    const [field, order] = e.target.value.split('-');
                                    setSortBy(field);
                                    setSortOrder(order);
                                }}
                                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="created_date-desc">Newest First</option>
                                <option value="created_date-asc">Oldest First</option>
                                <option value="due_date-asc">Due Date (Earliest)</option>
                                <option value="due_date-desc">Due Date (Latest)</option>
                                <option value="total_amount-desc">Amount (High to Low)</option>
                                <option value="total_amount-asc">Amount (Low to High)</option>
                            </select>
                            <div className="flex items-center border border-gray-300 rounded-md">
                                <button
                                    onClick={() => setViewMode('grid')}
                                    className={`px-3 py-2 text-sm ${
                                        viewMode === 'grid' 
                                            ? 'bg-blue-50 text-blue-600' 
                                            : 'text-gray-600 hover:text-gray-900'
                                    }`}
                                >
                                    <i className="fas fa-th-large"></i>
                                </button>
                                <button
                                    onClick={() => setViewMode('list')}
                                    className={`px-3 py-2 text-sm border-l border-gray-300 ${
                                        viewMode === 'list' 
                                            ? 'bg-blue-50 text-blue-600' 
                                            : 'text-gray-600 hover:text-gray-900'
                                    }`}
                                >
                                    <i className="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedInvoice ? 'Edit Invoice' : 'New Invoice'}
                        </h2>
                        <InvoiceForm
                            invoice={selectedInvoice}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : showViewer && selectedInvoice && companyInfo ? (
                    <DocumentViewer
                        type="invoice"
                        data={selectedInvoice}
                        companyInfo={companyInfo}
                        onEdit={() => handleEditInvoice(selectedInvoice)}
                        onDelete={handleInvoiceDelete}
                        onClose={handleViewerClose}
                    />
                ) : (
                    <InvoiceList 
                        onInvoiceClick={handleInvoiceClick}
                        onEditInvoice={handleEditInvoice}
                        onDeleteInvoice={handleDeleteInvoice}
                        refreshKey={refreshKey}
                        searchTerm={searchTerm}
                        filterStatus={filterStatus}
                        sortBy={sortBy}
                        sortOrder={sortOrder}
                        viewMode={viewMode}
                        selectedInvoices={selectedInvoices}
                        onSelectInvoice={handleSelectInvoice}
                        onSelectAll={handleSelectAll}
                    />
                )}

                {/* Bulk Actions Modal */}
                {showBulkActions && (
                    <Modal
                        title="Bulk Actions"
                        onClose={() => setShowBulkActions(false)}
                    >
                        <div className="space-y-4">
                            <p className="text-gray-600">
                                {selectedInvoices.length} invoices selected
                            </p>
                            <div className="grid grid-cols-2 gap-3">
                                <Button
                                    variant="secondary"
                                    onClick={() => handleBulkStatusUpdate('sent')}
                                    icon="fas fa-paper-plane"
                                    size="sm"
                                >
                                    Mark as Sent
                                </Button>
                                <Button
                                    variant="secondary"
                                    onClick={() => handleBulkStatusUpdate('paid')}
                                    icon="fas fa-check-circle"
                                    size="sm"
                                >
                                    Mark as Paid
                                </Button>
                                <Button
                                    variant="secondary"
                                    onClick={() => handleBulkStatusUpdate('overdue')}
                                    icon="fas fa-exclamation-triangle"
                                    size="sm"
                                >
                                    Mark as Overdue
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleBulkDelete}
                                    icon="fas fa-trash"
                                    size="sm"
                                >
                                    Delete Selected
                                </Button>
                            </div>
                            <div className="flex justify-end space-x-3 pt-4 border-t">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowBulkActions(false)}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && invoiceToDelete && (
                    <Modal
                        title="Delete Invoice"
                        onClose={() => setShowDeleteConfirm(false)}
                    >
                        <div className="space-y-4">
                            <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                    <i className="fas fa-exclamation-triangle text-red-500 text-xl"></i>
                                </div>
                                <div>
                                    <p className="text-gray-900 font-medium">
                                        Are you sure you want to delete this invoice?
                                    </p>
                                    <p className="text-gray-600 text-sm mt-1">
                                        Invoice #{invoiceToDelete.objectId && invoiceToDelete.objectId.substring(0, 8)} will be permanently deleted.
                                        This action cannot be undone.
                                    </p>
                                </div>
                            </div>
                            <div className="flex justify-end space-x-3 pt-4 border-t">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={confirmDeleteInvoice}
                                    icon="fas fa-trash"
                                >
                                    Delete Invoice
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Invoices page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
