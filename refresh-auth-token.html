<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refresh Auth Token</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔄 Refresh Authentication Token</h1>
    <p>This page will get a fresh authentication token and store it in localStorage.</p>
    
    <button onclick="refreshToken()">Get Fresh Token</button>
    <button onclick="clearToken()">Clear Token</button>
    <button onclick="checkCurrentToken()">Check Current Token</button>
    
    <div id="status"></div>

    <script>
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function refreshToken() {
            showStatus('Getting fresh token...', 'info');
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('authToken', data.tokens.access_token);
                    
                    // Decode token to show expiry
                    const tokenData = JSON.parse(atob(data.tokens.access_token));
                    const expiryTime = new Date(tokenData.exp * 1000);
                    
                    showStatus(`
                        ✅ Fresh token obtained and stored!<br>
                        <strong>User:</strong> ${data.user.name}<br>
                        <strong>Expires:</strong> ${expiryTime.toLocaleString()}<br>
                        <strong>Token:</strong> ${data.tokens.access_token.substring(0, 30)}...<br><br>
                        <em>You can now test the AuthContext in other pages.</em>
                    `, 'success');
                } else {
                    showStatus(`❌ Login failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        function clearToken() {
            localStorage.removeItem('authToken');
            showStatus('🗑️ Token cleared from localStorage', 'success');
        }

        function checkCurrentToken() {
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                showStatus('❌ No token found in localStorage', 'error');
                return;
            }

            try {
                const tokenData = JSON.parse(atob(token));
                const expiryTime = new Date(tokenData.exp * 1000);
                const isExpired = tokenData.exp < (Date.now() / 1000);
                
                showStatus(`
                    📋 Current Token Info:<br>
                    <strong>User:</strong> ${tokenData.email}<br>
                    <strong>Role:</strong> ${tokenData.role}<br>
                    <strong>Expires:</strong> ${expiryTime.toLocaleString()}<br>
                    <strong>Status:</strong> ${isExpired ? '❌ EXPIRED' : '✅ Valid'}<br>
                    <strong>Token:</strong> ${token.substring(0, 30)}...
                `, isExpired ? 'error' : 'success');
            } catch (error) {
                showStatus(`❌ Invalid token format: ${error.message}`, 'error');
            }
        }

        // Check current token on page load
        window.onload = function() {
            checkCurrentToken();
        };
    </script>
</body>
</html>