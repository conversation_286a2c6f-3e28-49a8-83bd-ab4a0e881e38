// Business Type Template Mapping Utility
function getBusinessTypeTemplate(businessType) {
    const templateMap = {
        'Digital Marketing': 'DigitalMarketingItems',
        'Marketing': 'DigitalMarketingItems',
        'Technology': 'DigitalMarketingItems',
        'Real Estate': 'RealEstateItems',
        'Healthcare': 'HealthcareItems',
        'Restaurant': 'RestaurantItems',
        'Food & Beverage': 'RestaurantItems',
        'Retail': 'RetailItems',
        'E-commerce': 'RetailItems',
        'Manufacturing': 'RetailItems',
        'Finance': 'DigitalMarketingItems',
        'Education': 'DigitalMarketingItems',
        'Consulting': 'DigitalMarketingItems',
        'Other': 'DigitalMarketingItems'
    };

    return templateMap[businessType] || 'DigitalMarketingItems';
}

// Get user's business type from various sources
async function getUserBusinessType() {
    try {
        // First try to get from AuthContext
        const authContext = window.AuthContext?.getAuthContext?.() || 
                           JSON.parse(localStorage.getItem('authContext') || '{}');
        
        if (authContext.user?.industry) {
            return authContext.user.industry;
        }

        if (authContext.currentCompany?.industry) {
            return authContext.currentCompany.industry;
        }

        // If not available in context, try to fetch from API
        if (authContext.token) {
            const response = await fetch('/api/user/profile.php', {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.user?.industry) {
                    return data.user.industry;
                }
                if (data.success && data.company?.industry) {
                    return data.company.industry;
                }
            }
        }

        // Default fallback
        return 'Digital Marketing';
    } catch (error) {
        console.error('Error getting user business type:', error);
        return 'Digital Marketing';
    }
}

// Get template component name for user's business type
async function getTemplateForUser() {
    const businessType = await getUserBusinessType();
    return getBusinessTypeTemplate(businessType);
}

// Get template display information
function getTemplateInfo(templateName) {
    const templateInfo = {
        'DigitalMarketingItems': {
            name: 'Digital Marketing',
            description: 'Services and packages for digital marketing agencies',
            icon: 'fa-bullhorn',
            color: 'blue'
        },
        'RealEstateItems': {
            name: 'Real Estate',
            description: 'Property services and real estate solutions',
            icon: 'fa-home',
            color: 'green'
        },
        'HealthcareItems': {
            name: 'Healthcare',
            description: 'Medical services and healthcare solutions',
            icon: 'fa-heartbeat',
            color: 'red'
        },
        'RestaurantItems': {
            name: 'Restaurant & Food',
            description: 'Food service and restaurant management',
            icon: 'fa-utensils',
            color: 'orange'
        },
        'RetailItems': {
            name: 'Retail & E-commerce',
            description: 'Retail products and e-commerce solutions',
            icon: 'fa-shopping-cart',
            color: 'purple'
        }
    };

    return templateInfo[templateName] || templateInfo['DigitalMarketingItems'];
}

// Check if template component is available
function isTemplateAvailable(templateName) {
    return window[templateName] && typeof window[templateName] === 'function';
}

// Render template component
function renderTemplate(templateName, props = {}) {
    if (!isTemplateAvailable(templateName)) {
        console.warn(`Template ${templateName} not available, falling back to DigitalMarketingItems`);
        templateName = 'DigitalMarketingItems';
    }

    const TemplateComponent = window[templateName];
    return React.createElement(TemplateComponent, props);
}

// Make functions globally available
window.getBusinessTypeTemplate = getBusinessTypeTemplate;
window.getUserBusinessType = getUserBusinessType;
window.getTemplateForUser = getTemplateForUser;
window.getTemplateInfo = getTemplateInfo;
window.isTemplateAvailable = isTemplateAvailable;
window.renderTemplate = renderTemplate;