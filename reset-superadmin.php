<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/utils/SecurityUtils.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    $email = '<EMAIL>';
    $newPassword = 'admin123'; // Default password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Check if superadmin exists
    $stmt = $conn->prepare("SELECT id, name, email, role, status FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // Create superadmin account
        $name = 'Super Administrator';
        $role = 'super_admin';
        $status = 'active';
        $object_id = 'user_' . time() . '_' . rand(100, 999);
        
        $stmt = $conn->prepare("INSERT INTO users (object_id, name, email, password_hash, role, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("ssssss", $object_id, $name, $email, $hashedPassword, $role, $status);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'action' => 'created',
                'message' => 'Superadmin account created successfully',
                'credentials' => [
                    'email' => $email,
                    'password' => $newPassword
                ],
                'user_id' => $conn->insert_id
            ]);
        } else {
            throw new Exception('Failed to create superadmin account: ' . $stmt->error);
        }
    } else {
        // Update existing superadmin password
        $user = $result->fetch_assoc();
        
        $stmt = $conn->prepare("UPDATE users SET password_hash = ?, status = 'active' WHERE email = ?");
        $stmt->bind_param("ss", $hashedPassword, $email);
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'action' => 'updated',
                'message' => 'Superadmin password reset successfully',
                'credentials' => [
                    'email' => $email,
                    'password' => $newPassword
                ],
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'status' => $user['status']
                ]
            ]);
        } else {
            throw new Exception('Failed to update superadmin password: ' . $stmt->error);
        }
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
