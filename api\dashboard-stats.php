<?php
/**
 * Dashboard Statistics API
 * Provides comprehensive dashboard statistics for users and admins
 */

require_once 'db-config.php';
require_once 'utils/ErrorHandler.php';

header('Content-Type: application/json');

// Get request method and parameters
$method = $_SERVER['REQUEST_METHOD'];
$days = isset($_GET['days']) ? (int)$_GET['days'] : 30;

// For now, use a default company ID - in production, get from authenticated user
$companyId = 'super_admin_001';

if ($method === 'GET') {
    try {
        // Calculate date range
        $startDate = date('Y-m-d', strtotime("-$days days"));
        $endDate = date('Y-m-d');
        
        $stats = [];
        
        // Get customer statistics (using leads table as customers)
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_count,
                COUNT(CASE WHEN created_at >= DATE_SUB(?, INTERVAL ? DAY) AND created_at < ? THEN 1 END) as prev_count
            FROM leads 
            WHERE company_id = ?
        ");
        
        if ($stmt) {
            $stmt->bind_param("ssiis", $startDate, $startDate, $days, $startDate, $companyId);
            $stmt->execute();
            $result = $stmt->get_result();
            $customerData = $result->fetch_assoc();
        } else {
            // Fallback if leads table doesn't exist
            $customerData = ['total' => 0, 'new_count' => 0, 'prev_count' => 0];
        }
        
        $stats['customers'] = [
            'total' => (int)$customerData['total'],
            'change' => $customerData['prev_count'] > 0 ? 
                round((($customerData['new_count'] - $customerData['prev_count']) / $customerData['prev_count']) * 100, 1) : 
                ($customerData['new_count'] > 0 ? 100 : 0)
        ];
        
        // Get invoice statistics (with fallback)
        try {
            $stmt = $conn->prepare("
                SELECT 
                    COUNT(*) as total,
                    COALESCE(SUM(total_amount), 0) as amount,
                    COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_count,
                    COUNT(CASE WHEN created_at >= DATE_SUB(?, INTERVAL ? DAY) AND created_at < ? THEN 1 END) as prev_count
                FROM invoices 
                WHERE company_id = ?
            ");
            
            if ($stmt) {
                $stmt->bind_param("ssiis", $startDate, $startDate, $days, $startDate, $companyId);
                $stmt->execute();
                $result = $stmt->get_result();
                $invoiceData = $result->fetch_assoc();
            } else {
                throw new Exception("Invoice table not found");
            }
        } catch (Exception $e) {
            // Fallback data if invoices table doesn't exist
            $invoiceData = ['total' => 0, 'amount' => 0, 'new_count' => 0, 'prev_count' => 0];
        }
        
        $stats['invoices'] = [
            'total' => (int)$invoiceData['total'],
            'amount' => (float)$invoiceData['amount'],
            'change' => $invoiceData['prev_count'] > 0 ? 
                round((($invoiceData['new_count'] - $invoiceData['prev_count']) / $invoiceData['prev_count']) * 100, 1) : 
                ($invoiceData['new_count'] > 0 ? 100 : 0)
        ];
        
        // Get quotation statistics (with fallback)
        try {
            $stmt = $conn->prepare("
                SELECT 
                    COUNT(*) as total,
                    COALESCE(SUM(total_amount), 0) as amount,
                    COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_count
                FROM quotations 
                WHERE company_id = ?
            ");
            
            if ($stmt) {
                $stmt->bind_param("ss", $startDate, $companyId);
                $stmt->execute();
                $result = $stmt->get_result();
                $quotationData = $result->fetch_assoc();
            } else {
                throw new Exception("Quotations table not found");
            }
        } catch (Exception $e) {
            // Fallback data if quotations table doesn't exist
            $quotationData = ['total' => 0, 'amount' => 0, 'new_count' => 0];
        }
        
        $stats['quotations'] = [
            'total' => (int)$quotationData['total'],
            'amount' => (float)$quotationData['amount'],
            'change' => 0 // Simplified for now
        ];
        
        // Get leads statistics (with fallback)
        try {
            $stmt = $conn->prepare("
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'converted' THEN 1 END) as converted,
                    COUNT(CASE WHEN created_at >= ? THEN 1 END) as new_count
                FROM leads 
                WHERE company_id = ?
            ");
            
            if ($stmt) {
                $stmt->bind_param("ss", $startDate, $companyId);
                $stmt->execute();
                $result = $stmt->get_result();
                $leadsData = $result->fetch_assoc();
            } else {
                throw new Exception("Leads table not found");
            }
        } catch (Exception $e) {
            // Fallback data if leads table doesn't exist
            $leadsData = ['total' => 0, 'converted' => 0, 'new_count' => 0];
        }
        
        $stats['leads'] = [
            'total' => (int)$leadsData['total'],
            'converted' => (int)$leadsData['converted'],
            'change' => 0 // Simplified for now
        ];
        
        // Calculate revenue
        $stats['revenue'] = [
            'total' => $stats['invoices']['amount'],
            'change' => $stats['invoices']['change']
        ];
        
        // Get pending payments (with fallback)
        try {
            $stmt = $conn->prepare("
                SELECT 
                    COUNT(*) as total,
                    COALESCE(SUM(total_amount - COALESCE(paid_amount, 0)), 0) as amount
                FROM invoices 
                WHERE company_id = ? AND status != 'paid' AND total_amount > COALESCE(paid_amount, 0)
            ");
            
            if ($stmt) {
                $stmt->bind_param("s", $companyId);
                $stmt->execute();
                $result = $stmt->get_result();
                $pendingData = $result->fetch_assoc();
            } else {
                throw new Exception("Invoices table not found");
            }
        } catch (Exception $e) {
            // Fallback data if invoices table doesn't exist
            $pendingData = ['total' => 0, 'amount' => 0];
        }
        
        $stats['pending_payments'] = [
            'total' => (int)$pendingData['total'],
            'amount' => (float)$pendingData['amount']
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $stats,
            'period' => [
                'days' => $days,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Dashboard Stats Error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Failed to get dashboard statistics'
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

$conn->close();
?>