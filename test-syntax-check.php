<?php
echo "<h1>Syntax Check</h1>";

// Test if the enhanced auth handler can be loaded
try {
    require_once 'api/enhanced-auth-handler.php';
    echo "✅ Enhanced auth handler loaded successfully<br>";
} catch (ParseError $e) {
    echo "❌ Parse error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
}
?>