.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.item-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.item-icon {
    width: 48px;
    height: 48px;
    border-radius: 0.5rem;
    background-color: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.item-details-header {
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

.item-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.category-list .category-item {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.category-list .category-header {
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem 0.5rem 0 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-list .subcategory-list {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
}

.category-list .subcategory-item {
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
}

.category-list .subcategory-item:hover {
    background-color: #f3f4f6;
}

@media (max-width: 768px) {
    .item-info-grid {
        grid-template-columns: 1fr;
    }
}
