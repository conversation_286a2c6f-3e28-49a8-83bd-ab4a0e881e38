<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Routing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test API Routing</h1>
        
        <div class="test-section">
            <h2>Test 1: Direct API File Access</h2>
            <button onclick="testDirectAccess()">Test /biz/api/api.php</button>
            <div id="direct-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: API with Path Info</h2>
            <button onclick="testPathInfo()">Test /biz/api/api.php/auth/login</button>
            <div id="pathinfo-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Enhanced Auth Handler Direct</h2>
            <button onclick="testEnhancedAuth()">Test /biz/api/enhanced-auth-handler.php</button>
            <div id="enhanced-result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 4: Login with Credentials</h2>
            <button onclick="testLogin()">Test Login</button>
            <div id="login-result"></div>
        </div>
    </div>

    <script>
        async function testDirectAccess() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<p>Testing direct API access...</p>';
            
            try {
                const response = await fetch('/biz/api/api.php', {
                    method: 'GET'
                });
                
                const text = await response.text();
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testPathInfo() {
            const resultDiv = document.getElementById('pathinfo-result');
            resultDiv.innerHTML = '<p>Testing path info routing...</p>';
            
            try {
                const response = await fetch('/biz/api/api.php/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123',
                        rememberMe: false
                    })
                });
                
                const text = await response.text();
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'info');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testEnhancedAuth() {
            const resultDiv = document.getElementById('enhanced-result');
            resultDiv.innerHTML = '<p>Testing enhanced auth handler...</p>';
            
            try {
                const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123',
                        rememberMe: false
                    })
                });
                
                const text = await response.text();
                resultDiv.className = 'test-section ' + (response.ok ? 'success' : 'info');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <pre>${text}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>Testing login with real credentials...</p>';
            
            try {
                const response = await fetch('/biz/api/api.php/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'BhaviGani@56',
                        rememberMe: false
                    })
                });
                
                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = null;
                }
                
                resultDiv.className = 'test-section ' + (data && data.success ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <h3>Status: ${response.status}</h3>
                    <h4>Response:</h4>
                    <pre>${text}</pre>
                    ${data && data.success ? '<p><strong>✅ Login Successful!</strong></p>' : '<p><strong>❌ Login Failed</strong></p>'}
                `;
            } catch (error) {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
