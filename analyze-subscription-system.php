<?php
require_once 'api/db-config.php';

echo "=== SaaS Subscription System Analysis ===\n\n";

// Check existing tables related to subscriptions
$subscriptionTables = [
    'pricing_plans',
    'subscriptions', 
    'active_subscriptions',
    'companies',
    'users',
    'payment_transactions',
    'payments'
];

echo "1. Database Tables Analysis:\n";
foreach ($subscriptionTables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "   ✓ $table exists\n";
        
        // Skip structure for views that might be broken
        if ($table !== 'active_subscriptions') {
            // Show table structure
            $structure = $conn->query("DESCRIBE $table");
            if ($structure) {
                echo "     Columns: ";
                $columns = [];
                while ($row = $structure->fetch_assoc()) {
                    $columns[] = $row['Field'];
                }
                echo implode(', ', $columns) . "\n";
            }
            
            // Show row count
            $count = $conn->query("SELECT COUNT(*) as count FROM $table");
            if ($count) {
                $countRow = $count->fetch_assoc();
                echo "     Records: " . $countRow['count'] . "\n";
            }
        } else {
            echo "     (View - skipping structure check due to potential issues)\n";
        }
    } else {
        echo "   ✗ $table missing\n";
    }
    echo "\n";
}

// Check pricing plans data
echo "2. Pricing Plans Data:\n";
$plansResult = $conn->query("SELECT * FROM pricing_plans ORDER BY price_monthly ASC");
if ($plansResult && $plansResult->num_rows > 0) {
    while ($plan = $plansResult->fetch_assoc()) {
        $monthlyPrice = $plan['price_monthly'] ?? 0;
        $yearlyPrice = $plan['price_yearly'] ?? 0;
        $status = $plan['is_active'] ? 'Active' : 'Inactive';
        echo "   - {$plan['name']}: \${monthlyPrice}/month, \${yearlyPrice}/year (Status: {$status})\n";
        if (!empty($plan['features'])) {
            $features = json_decode($plan['features'], true);
            if ($features) {
                echo "     Features: " . implode(', ', array_keys($features)) . "\n";
            }
        }
    }
} else {
    echo "   No pricing plans found\n";
}

// Check active subscriptions
echo "\n3. Active Subscriptions:\n";
$subsResult = $conn->query("
    SELECT s.*, c.name as company_name, pp.name as plan_name 
    FROM subscriptions s 
    LEFT JOIN companies c ON s.company_id = c.object_id 
    LEFT JOIN pricing_plans pp ON s.plan_id = pp.id 
    ORDER BY s.created_at DESC 
    LIMIT 10
");
if ($subsResult && $subsResult->num_rows > 0) {
    while ($sub = $subsResult->fetch_assoc()) {
        echo "   - {$sub['company_name']}: {$sub['plan_name']} (Status: {$sub['status']}, Expires: {$sub['expires_at']})\n";
    }
} else {
    echo "   No subscriptions found\n";
}

// Check API endpoints
echo "\n4. API Endpoints Check:\n";
$apiFiles = [
    'api/super-admin/plans.php' => 'Super Admin Plans Management',
    'api/super-admin/subscriptions.php' => 'Super Admin Subscriptions Management',
    'api/subscription-management.php' => 'User Subscription Management',
    'api/subscription-management/current.php' => 'Current Subscription Info',
    'api/subscription-management/trial-status.php' => 'Trial Status',
    'api/payment/payment-api.php' => 'Payment Processing'
];

foreach ($apiFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✓ $description ($file)\n";
    } else {
        echo "   ✗ $description ($file) - MISSING\n";
    }
}

$conn->close();
?>