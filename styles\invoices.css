.invoice-form {
    max-width: 800px;
    margin: 0 auto;
}

.invoice-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.invoice-items {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.invoice-item {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr auto;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.invoice-item:last-child {
    border-bottom: none;
}

.invoice-totals {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e5e7eb;
}

.invoice-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.invoice-status.draft {
    background-color: #f3f4f6;
    color: #4b5563;
}

.invoice-status.sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.invoice-status.paid {
    background-color: #d1fae5;
    color: #065f46;
}

.invoice-status.overdue {
    background-color: #fee2e2;
    color: #991b1b;
}

.payment-history {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.payment-record {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.payment-record:last-child {
    border-bottom: none;
}

/* Enhanced invoice design */
.enhanced-invoice {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.enhanced-invoice-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #3b82f6;
    margin-bottom: 0.25rem;
}

.enhanced-invoice-number {
    font-size: 1.25rem;
    color: #6b7280;
    font-weight: 500;
}

.enhanced-invoice-meta {
    background-color: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.enhanced-invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
}

.enhanced-invoice-table th {
    background-color: #f1f5f9;
    padding: 0.75rem 1rem;
    text-align: left;
    color: #64748b;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e2e8f0;
}

.enhanced-invoice-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.enhanced-invoice-table tbody tr:last-child td {
    border-bottom: none;
}

.enhanced-invoice-totals {
    background-color: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.enhanced-invoice-total-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.enhanced-invoice-total-row:last-child {
    border-bottom: none;
    padding-top: 1rem;
    font-weight: 700;
    font-size: 1.125rem;
}

.enhanced-invoice-notes {
    margin-bottom: 2rem;
}

.enhanced-invoice-signature {
    display: flex;
    justify-content: flex-end;
    margin-top: 3rem;
}

.enhanced-invoice-signature-box {
    text-align: center;
}

.enhanced-invoice-signature-line {
    width: 12rem;
    border-bottom: 1px solid #9ca3af;
    margin-bottom: 0.5rem;
    min-height: 3rem;
}

.enhanced-invoice-footer {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

/* Payment status badges */
.payment-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.payment-status-badge.paid {
    background-color: #d1fae5;
    color: #065f46;
}

.payment-status-badge.overdue {
    background-color: #fee2e2;
    color: #991b1b;
}

.payment-status-badge.pending {
    background-color: #fef3c7;
    color: #92400e;
}

/* UPI QR code */
.upi-qr-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-top: 1.5rem;
}

.upi-qr-code {
    width: 120px;
    height: 120px;
    margin-bottom: 0.5rem;
}

.upi-id {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Due date indicator */
.due-date-indicator {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
}

.due-date-indicator.overdue {
    color: #ef4444;
}

.due-date-indicator.upcoming {
    color: #f59e0b;
}

.due-date-indicator.paid {
    color: #10b981;
}

@media (max-width: 768px) {
    .invoice-header {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .invoice-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .enhanced-invoice-header {
        flex-direction: column;
    }
    
    .enhanced-invoice-meta {
        padding: 1rem;
    }
    
    .enhanced-invoice-table th, 
    .enhanced-invoice-table td {
        padding: 0.75rem 0.5rem;
    }
}
