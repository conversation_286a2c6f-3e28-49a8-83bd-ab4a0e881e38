'use client'

import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  FileText, 
  CreditCard, 
  TrendingUp,
  Building2,
  Plus,
  BarChart3
} from 'lucide-react'

export default function DashboardPage() {
  const { data: session, status } = useSession()

  // Show loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const stats = [
    {
      title: 'Total Customers',
      value: '2',
      change: '+2 from last month',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Active Leads',
      value: '0',
      change: 'No new leads',
      icon: TrendingUp,
      color: 'text-green-600'
    },
    {
      title: 'Invoices',
      value: '0',
      change: 'No invoices yet',
      icon: FileText,
      color: 'text-orange-600'
    },
    {
      title: 'Revenue',
      value: '$0',
      change: 'No revenue yet',
      icon: CreditCard,
      color: 'text-purple-600'
    }
  ]

  const quickActions = [
    {
      title: 'Add Customer',
      description: 'Create a new customer profile',
      icon: Users,
      href: '/dashboard/customers/new'
    },
    {
      title: 'Create Invoice',
      description: 'Generate a new invoice',
      icon: FileText,
      href: '/dashboard/invoices/new'
    },
    {
      title: 'Add Lead',
      description: 'Track a new business lead',
      icon: TrendingUp,
      href: '/dashboard/leads/new'
    },
    {
      title: 'View Reports',
      description: 'Analyze your business data',
      icon: BarChart3,
      href: '/dashboard/reports'
    }
  ]

  return (
    <div className="min-h-screen bg-red-500 p-8">
      <div className="bg-white p-4 rounded-lg shadow-lg mb-4">
        <h1 className="text-2xl font-bold text-blue-600">STYLE TEST</h1>
        <p className="text-green-600">If you can see colors and styling, Tailwind is working!</p>
      </div>
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-sm text-gray-600">
                  Welcome back, {session?.user?.name || session?.user?.email || 'User'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {session?.user?.company?.name || 'Demo Company'}
              </span>
              <Button variant="outline" size="sm">
                Settings
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks to get you started
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <Button
                    key={action.title}
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-start space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <action.icon className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">{action.title}</span>
                    </div>
                    <span className="text-sm text-gray-600 text-left">
                      {action.description}
                    </span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest business activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Account created</p>
                    <p className="text-xs text-gray-600">Welcome to Business SaaS!</p>
                  </div>
                  <span className="text-xs text-gray-500">Just now</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Sample data loaded</p>
                    <p className="text-xs text-gray-600">Demo customers and items added</p>
                  </div>
                  <span className="text-xs text-gray-500">Just now</span>
                </div>
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500">
                    Start using the platform to see more activities here
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Getting Started */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>
              Complete these steps to set up your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">1</span>
                </div>
                <div>
                  <h3 className="font-medium">Set up your company profile</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Add your company details, logo, and branding
                  </p>
                  <Button variant="link" className="p-0 h-auto mt-2">
                    Complete setup →
                  </Button>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600">2</span>
                </div>
                <div>
                  <h3 className="font-medium">Add your first customer</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Start building your customer database
                  </p>
                  <Button variant="link" className="p-0 h-auto mt-2">
                    Add customer →
                  </Button>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600">3</span>
                </div>
                <div>
                  <h3 className="font-medium">Create your first invoice</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Generate professional invoices for your customers
                  </p>
                  <Button variant="link" className="p-0 h-auto mt-2">
                    Create invoice →
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
      </div>
    </div>
  )
}
