/**
 * Error reporting utility - this is a mock implementation for local development
 * In a production environment, this would send errors to a logging service
 */

function reportError(error) {
    console.error('Application Error:', error);
    
    // In a real environment, you might send this to a logging service
    // For local development, we'll just log to console
    
    // You can add more sophisticated error handling here if needed
    const errorInfo = {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
    };
    
    console.log('Error details:', errorInfo);
    
    // In a real app, you might show a user-friendly error message or notification
}

// Make available globally
window.reportError = reportError;
