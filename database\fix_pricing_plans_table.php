<?php
/**
 * Fix pricing_plans table structure
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🔧 Fixing pricing_plans table structure...\n\n";

try {
    // Check current table structure
    $result = $conn->query("DESCRIBE pricing_plans");
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    echo "Current columns: " . implode(', ', $columns) . "\n\n";
    
    // Add missing columns
    $alterations = [
        "ADD COLUMN IF NOT EXISTS short_description VARCHAR(255) AFTER description",
        "ADD COLUMN IF NOT EXISTS trial_days INT DEFAULT 14 AFTER price_yearly",
        "ADD COLUMN IF NOT EXISTS limits_data JSON AFTER features",
        "ADD COLUMN IF NOT EXISTS business_types JSON AFTER limits_data",
        "ADD COLUMN IF NOT EXISTS is_trial_available BOOLEAN DEFAULT TRUE AFTER business_types",
        "ADD COLUMN IF NOT EXISTS is_visible BOOLEAN DEFAULT TRUE AFTER is_trial_available",
        "ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE AFTER is_visible",
        "ADD COLUMN IF NOT EXISTS sort_order INT DEFAULT 0 AFTER is_popular"
    ];
    
    foreach ($alterations as $alteration) {
        echo "Executing: ALTER TABLE pricing_plans $alteration\n";
        if ($conn->query("ALTER TABLE pricing_plans $alteration") === TRUE) {
            echo "✅ Success\n";
        } else {
            echo "❌ Error: " . $conn->error . "\n";
        }
    }
    
    // Now insert the basic plan that was missing
    echo "\n💰 Inserting basic pricing plan...\n";
    
    $stmt = $conn->prepare("INSERT INTO pricing_plans (id, name, description, short_description, price_monthly, price_yearly, trial_days, features, limits_data, business_types, is_trial_available, is_visible, is_popular, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE name = VALUES(name), description = VALUES(description), short_description = VALUES(short_description), price_monthly = VALUES(price_monthly), price_yearly = VALUES(price_yearly), trial_days = VALUES(trial_days), features = VALUES(features), limits_data = VALUES(limits_data), business_types = VALUES(business_types), is_trial_available = VALUES(is_trial_available), is_visible = VALUES(is_visible), is_popular = VALUES(is_popular), sort_order = VALUES(sort_order)");
    
    $plans = [
        [
            'id' => 'trial',
            'name' => 'Free Trial',
            'description' => 'Try all features free for 14 days',
            'short_description' => 'Full access trial',
            'price_monthly' => 0.00,
            'price_yearly' => 0.00,
            'trial_days' => 14,
            'features' => '["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Basic Analytics", "Email Notifications", "Data Export"]',
            'limits_data' => '{"max_customers": 50, "max_invoices": 20, "max_quotations": 20, "max_users": 2, "storage_gb": 1}',
            'business_types' => '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
            'is_trial_available' => 0,
            'is_visible' => 0,
            'is_popular' => 0,
            'sort_order' => 0
        ],
        [
            'id' => 'basic',
            'name' => 'Business Plan',
            'description' => 'Complete business management solution with all essential features',
            'short_description' => 'Everything you need to run your business',
            'price_monthly' => 500.00,
            'price_yearly' => 5000.00,
            'trial_days' => 14,
            'features' => '["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Business Analytics", "Email Notifications", "Data Export", "Multi-user Access", "24/7 Support", "Custom Branding", "API Access"]',
            'limits_data' => '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": 10, "storage_gb": 50}',
            'business_types' => '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
            'is_trial_available' => 1,
            'is_visible' => 1,
            'is_popular' => 1,
            'sort_order' => 1
        ],
        [
            'id' => 'premium',
            'name' => 'Premium Plan',
            'description' => 'Advanced features for growing businesses',
            'short_description' => 'Scale your business with premium tools',
            'price_monthly' => 1000.00,
            'price_yearly' => 10000.00,
            'trial_days' => 14,
            'features' => '["Everything in Business Plan", "Advanced Analytics", "Custom Reports", "Priority Support", "White Label", "Advanced Integrations", "Bulk Operations", "Advanced Security"]',
            'limits_data' => '{"max_customers": -1, "max_invoices": -1, "max_quotations": -1, "max_users": 25, "storage_gb": 100}',
            'business_types' => '["jewellery", "retail", "education", "healthcare", "consulting", "manufacturing"]',
            'is_trial_available' => 1,
            'is_visible' => 1,
            'is_popular' => 0,
            'sort_order' => 2
        ]
    ];
    
    foreach ($plans as $plan) {
        $stmt->bind_param("ssssddisssiiiii",
            $plan['id'],
            $plan['name'],
            $plan['description'],
            $plan['short_description'],
            $plan['price_monthly'],
            $plan['price_yearly'],
            $plan['trial_days'],
            $plan['features'],
            $plan['limits_data'],
            $plan['business_types'],
            $plan['is_trial_available'],
            $plan['is_visible'],
            $plan['is_popular'],
            $plan['sort_order']
        );
        
        if ($stmt->execute()) {
            echo "✅ Inserted pricing plan: {$plan['name']}\n";
        } else {
            echo "❌ Failed to insert pricing plan: {$plan['name']} - " . $stmt->error . "\n";
        }
    }
    
    echo "\n🎉 Pricing plans table fixed and data inserted!\n";
    
} catch (Exception $e) {
    echo "💥 Fix failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Fix completed.\n";
?>
