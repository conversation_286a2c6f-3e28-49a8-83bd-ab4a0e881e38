.plan-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.plan-card.popular {
    transform: scale(1.05);
    border: 2px solid #3b82f6;
}

.plan-card.popular:hover {
    transform: scale(1.05) translateY(-2px);
}

.plan-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.plan-features {
    padding: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.feature-icon {
    color: #10b981;
    margin-right: 0.75rem;
}

.usage-stats {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 2rem;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.progress-fill.blue {
    background-color: #3b82f6;
}

.progress-fill.green {
    background-color: #10b981;
}

.upgrade-modal {
    max-width: 28rem;
    margin: 2rem auto;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.current-plan {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 2rem;
}

.plan-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.plan-status.active {
    background-color: #d1fae5;
    color: #065f46;
}

.plan-status.trial {
    background-color: #dbeafe;
    color: #1e40af;
}

.plan-status.expired {
    background-color: #fee2e2;
    color: #991b1b;
}

@media (max-width: 768px) {
    .plan-card.popular {
        transform: none;
    }

    .plan-card.popular:hover {
        transform: translateY(-2px);
    }
}
