<?php
// <PERSON>ript to check if all utility files exist
$basePath = __DIR__ . '/..';

// List of utility scripts that should be loaded
$utilityScripts = [
    '/utils/ValidationUtils.js',
    '/utils/dataCache.js',
    '/utils/NotificationManager.js',
    '/utils/NetworkStatus.js',
    '/utils/ApiClient.js',
    '/components/common/LoadingSpinner.js',
    '/components/common/ErrorMessage.js',
    '/components/common/NotificationContainer.js',
    '/components/subscriptions/TrialBanner.js',
    '/components/subscriptions/ExtendTrialModal.js'
];

echo "Checking utility files...\n\n";

$missingFiles = [];
$existingFiles = [];

foreach ($utilityScripts as $script) {
    $filePath = $basePath . $script;
    if (file_exists($filePath)) {
        $existingFiles[] = $script;
        echo "✅ Found: $script\n";
    } else {
        $missingFiles[] = $script;
        echo "❌ Missing: $script\n";
    }
}

echo "\nSummary:\n";
echo "- Total files: " . count($utilityScripts) . "\n";
echo "- Existing files: " . count($existingFiles) . "\n";
echo "- Missing files: " . count($missingFiles) . "\n";

if (count($missingFiles) > 0) {
    echo "\nMissing files:\n";
    foreach ($missingFiles as $file) {
        echo "- $file\n";
    }
}
?>