# Production Environment Configuration
# Copy this file to .env and update values for production deployment

# Application Settings
APP_NAME="Bizma Business Management SaaS"
APP_VERSION="1.0.0"
APP_ENV="production"
APP_DEBUG="false"
APP_URL="https://yourdomain.com"

# Database Configuration
DB_HOST="localhost"
DB_NAME="bizma_production"
DB_USERNAME="bizma_user"
DB_PASSWORD="your_secure_database_password"
DB_CHARSET="utf8mb4"

# Security Settings
JWT_SECRET="your_very_secure_jwt_secret_key_here"
ENCRYPTION_KEY="your_32_character_encryption_key"
SESSION_LIFETIME="86400"
PASSWORD_RESET_EXPIRY="3600"

# Email Configuration
MAIL_DRIVER="smtp"
MAIL_HOST="smtp.gmail.com"
MAIL_PORT="587"
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your-app-password"
MAIL_ENCRYPTION="tls"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Bizma Support"

# Payment Gateway (Razorpay/Stripe)
RAZORPAY_KEY_ID="your_razorpay_key_id"
RAZORPAY_KEY_SECRET="your_razorpay_key_secret"
STRIPE_PUBLISHABLE_KEY="your_stripe_publishable_key"
STRIPE_SECRET_KEY="your_stripe_secret_key"

# File Upload Settings
MAX_UPLOAD_SIZE="10485760"
ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"
UPLOAD_PATH="/uploads"

# Cache Settings
CACHE_DRIVER="file"
CACHE_LIFETIME="3600"

# Session Settings
SESSION_DRIVER="database"
SESSION_SECURE="true"
SESSION_HTTP_ONLY="true"
SESSION_SAME_SITE="strict"

# CORS Settings
CORS_ALLOWED_ORIGINS="https://yourdomain.com"
CORS_ALLOWED_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_ALLOWED_HEADERS="Content-Type,Authorization,X-Requested-With"

# Rate Limiting
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="3600"

# Logging
LOG_LEVEL="error"
LOG_FILE="/var/log/bizma/app.log"
ERROR_REPORTING="false"

# Backup Settings
BACKUP_ENABLED="true"
BACKUP_FREQUENCY="daily"
BACKUP_RETENTION="30"

# Monitoring
HEALTH_CHECK_ENABLED="true"
METRICS_ENABLED="true"

# CDN Settings
CDN_ENABLED="false"
CDN_URL=""

# Analytics
GOOGLE_ANALYTICS_ID=""
FACEBOOK_PIXEL_ID=""

# Social Login (Optional)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
FACEBOOK_APP_ID=""
FACEBOOK_APP_SECRET=""

# API Settings
API_RATE_LIMIT="1000"
API_RATE_WINDOW="3600"
API_VERSION="v1"

# Feature Flags
FEATURE_SUPER_ADMIN="true"
FEATURE_SUBSCRIPTIONS="true"
FEATURE_PAYMENTS="true"
FEATURE_REPORTS="true"
FEATURE_EMAIL_VERIFICATION="true"
FEATURE_TWO_FACTOR_AUTH="false"

# Business Settings
DEFAULT_CURRENCY="INR"
DEFAULT_TIMEZONE="Asia/Kolkata"
DEFAULT_LANGUAGE="en"
TRIAL_DURATION_DAYS="14"
MAX_COMPANIES_PER_USER="5"

# Notification Settings
NOTIFICATION_CHANNELS="email,database"
SLACK_WEBHOOK_URL=""
DISCORD_WEBHOOK_URL=""

# Maintenance Mode
MAINTENANCE_MODE="false"
MAINTENANCE_MESSAGE="We're performing scheduled maintenance. Please check back soon."

# SSL/TLS Settings
FORCE_HTTPS="true"
HSTS_MAX_AGE="31536000"

# Content Security Policy
CSP_ENABLED="true"
CSP_REPORT_URI="/api/csp-report"
