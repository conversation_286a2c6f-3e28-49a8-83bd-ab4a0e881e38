function PlanFeatures({ plan, isCurrentPlan }) {
    try {
        const features = {
            free: [
                { name: 'Up to 100 leads', included: true },
                { name: '3 team members', included: true },
                { name: 'Basic reporting', included: true },
                { name: 'Email support', included: true },
                { name: '5GB storage', included: true },
                { name: 'API access', included: false },
                { name: 'Custom fields', included: false },
                { name: 'Advanced reporting', included: false },
                { name: 'Priority support', included: false },
                { name: 'Custom integrations', included: false }
            ],
            starter: [
                { name: 'Up to 1,000 leads', included: true },
                { name: '10 team members', included: true },
                { name: 'Advanced reporting', included: true },
                { name: 'Priority support', included: true },
                { name: '25GB storage', included: true },
                { name: 'API access', included: true },
                { name: 'Custom fields', included: true },
                { name: 'Custom integrations', included: false },
                { name: 'White-label options', included: false },
                { name: 'Dedicated account manager', included: false }
            ],
            professional: [
                { name: 'Up to 10,000 leads', included: true },
                { name: 'Unlimited team members', included: true },
                { name: 'Custom reporting', included: true },
                { name: '24/7 phone support', included: true },
                { name: 'Unlimited storage', included: true },
                { name: 'API access', included: true },
                { name: 'Custom fields', included: true },
                { name: 'Custom integrations', included: true },
                { name: 'White-label options', included: true },
                { name: 'Dedicated account manager', included: true }
            ]
        };

        return (
            <div data-name="plan-features" className="mt-8">
                <h3 className="text-lg font-medium text-gray-900">What's included</h3>
                <ul className="mt-6 space-y-4">
                    {features[plan].map((feature, index) => (
                        <li key={index} className="flex">
                            <i className={`flex-shrink-0 w-5 h-5 ${
                                feature.included ? 'text-green-500 fas fa-check' : 'text-gray-400 fas fa-minus'
                            }`}></i>
                            <span className={`ml-3 text-sm ${
                                feature.included ? 'text-gray-700' : 'text-gray-500 line-through'
                            }`}>
                                {feature.name}
                            </span>
                        </li>
                    ))}
                </ul>
            </div>
        );
    } catch (error) {
        console.error('PlanFeatures component error:', error);
        reportError(error);
        return null;
    }
}
