function SearchBar({
    value,
    onChange,
    placeholder = 'Search...',
    onSearch,
    filters = [],
    className = ''
}) {
    try {
        const [showFilters, setShowFilters] = React.useState(false);
        const [selectedFilters, setSelectedFilters] = React.useState({});

        const handleKeyPress = (e) => {
            if (e.key === 'Enter' && onSearch) {
                onSearch(value, selectedFilters);
            }
        };

        const handleFilterChange = (filterId, value) => {
            setSelectedFilters(prev => ({
                ...prev,
                [filterId]: value
            }));
        };

        const handleSearch = () => {
            if (onSearch) {
                onSearch(value, selectedFilters);
            }
        };

        const clearFilters = () => {
            setSelectedFilters({});
        };

        return (
            <div data-name="search-bar" className={`w-full ${className}`}>
                <div className="relative">
                    <div className="flex">
                        <div className="relative flex-grow">
                            <input
                                data-name="search-input"
                                type="text"
                                value={value}
                                onChange={(e) => onChange(e.target.value)}
                                onKeyPress={handleKeyPress}
                                placeholder={placeholder}
                                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500"
                            />
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i className="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                        
                        {filters.length > 0 && (
                            <button
                                data-name="filter-button"
                                type="button"
                                onClick={() => setShowFilters(!showFilters)}
                                className="ml-2 px-4 py-2 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none"
                            >
                                <i className="fas fa-filter"></i>
                            </button>
                        )}
                        
                        <button
                            data-name="search-button"
                            type="button"
                            onClick={handleSearch}
                            className="ml-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none"
                        >
                            Search
                        </button>
                    </div>

                    {showFilters && filters.length > 0 && (
                        <div data-name="filters-dropdown" className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 z-10">
                            <div className="p-4">
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg font-semibold">Filters</h3>
                                    <button
                                        onClick={clearFilters}
                                        className="text-sm text-blue-600 hover:text-blue-800"
                                    >
                                        Clear all
                                    </button>
                                </div>
                                {filters.map((filter) => (
                                    <div key={filter.id} className="mb-4">
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            {filter.label}
                                        </label>
                                        {filter.type === 'select' ? (
                                            <select
                                                value={selectedFilters[filter.id] || ''}
                                                onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                                                className="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            >
                                                <option value="">All</option>
                                                {filter.options.map((option) => (
                                                    <option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </option>
                                                ))}
                                            </select>
                                        ) : filter.type === 'checkbox' ? (
                                            <input
                                                type="checkbox"
                                                checked={selectedFilters[filter.id] || false}
                                                onChange={(e) => handleFilterChange(filter.id, e.target.checked)}
                                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            />
                                        ) : (
                                            <input
                                                type="text"
                                                value={selectedFilters[filter.id] || ''}
                                                onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                                                className="w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            />
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('SearchBar component error:', error);
        reportError(error);
        return null;
    }
}
