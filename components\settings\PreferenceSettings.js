function PreferenceSettings({ formData, handleInputChange }) {
    try {
        return (
            <div className="space-y-6">
                <h2 className="text-lg font-medium mb-4">General Preferences</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Default Currency
                        </label>
                        <select
                            name="currency"
                            value={formData.currency}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="INR">Indian Rupee (₹)</option>
                            <option value="USD">US Dollar ($)</option>
                            <option value="EUR">Euro (€)</option>
                            <option value="GBP">British Pound (£)</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Date Format
                        </label>
                        <select
                            name="dateFormat"
                            value={formData.dateFormat}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Default Tax Rate (%)
                        </label>
                        <input
                            type="number"
                            name="taxRate"
                            value={formData.taxRate}
                            onChange={handleInputChange}
                            min="0"
                            max="100"
                            step="0.01"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Theme
                        </label>
                        <select
                            name="theme"
                            value={formData.theme}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                            <option value="system">System Default</option>
                        </select>
                    </div>
                </div>

                <div className="mt-8">
                    <h2 className="text-lg font-medium mb-4">Document Settings</h2>
                    
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Default Payment Terms
                            </label>
                            <textarea
                                name="defaultPaymentTerms"
                                value={formData.defaultPaymentTerms}
                                onChange={handleInputChange}
                                rows={3}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="Enter your default payment terms..."
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Default Notes
                            </label>
                            <textarea
                                name="defaultNotes"
                                value={formData.defaultNotes}
                                onChange={handleInputChange}
                                rows={3}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="Enter your default notes..."
                            />
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('PreferenceSettings component error:', error);
        reportError(error);
        return null;
    }
}
