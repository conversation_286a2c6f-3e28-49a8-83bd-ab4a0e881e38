function PlanSelector({ onPlanSelect }) {
    try {
        const [billingCycle, setBillingCycle] = React.useState('monthly');
        const [selectedPlan, setSelectedPlan] = React.useState(null);

        const plans = {
            monthly: [
                {
                    id: 'free',
                    name: 'Free',
                    description: 'Perfect for getting started',
                    price: 0,
                    features: ['Up to 100 leads', '3 team members', 'Basic reporting', 'Email support']
                },
                {
                    id: 'starter',
                    name: 'Starter',
                    description: 'For growing businesses',
                    price: 29,
                    features: ['Up to 1,000 leads', '10 team members', 'Advanced reporting', 'Priority support']
                },
                {
                    id: 'professional',
                    name: 'Professional',
                    description: 'For larger organizations',
                    price: 99,
                    features: ['Unlimited leads', 'Unlimited team members', 'Custom reporting', '24/7 phone support']
                }
            ],
            annual: [
                {
                    id: 'free',
                    name: 'Free',
                    description: 'Perfect for getting started',
                    price: 0,
                    features: ['Up to 100 leads', '3 team members', 'Basic reporting', 'Email support']
                },
                {
                    id: 'starter',
                    name: 'Starter',
                    description: 'For growing businesses',
                    price: 290,
                    features: ['Up to 1,000 leads', '10 team members', 'Advanced reporting', 'Priority support']
                },
                {
                    id: 'professional',
                    name: 'Professional',
                    description: 'For larger organizations',
                    price: 990,
                    features: ['Unlimited leads', 'Unlimited team members', 'Custom reporting', '24/7 phone support']
                }
            ]
        };

        const handlePlanSelect = (plan) => {
            setSelectedPlan(plan);
            onPlanSelect({
                ...plan,
                billingCycle,
                annualPrice: billingCycle === 'annual'
            });
        };

        return (
            <div data-name="plan-selector" className="space-y-8">
                <div className="flex justify-center">
                    <div className="relative bg-gray-100 p-0.5 rounded-lg inline-flex">
                        <button
                            type="button"
                            className={`relative py-2 px-6 rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:z-10 ${
                                billingCycle === 'monthly'
                                    ? 'bg-white shadow-sm text-gray-900'
                                    : 'text-gray-700 hover:text-gray-900'
                            }`}
                            onClick={() => setBillingCycle('monthly')}
                        >
                            Monthly billing
                        </button>
                        <button
                            type="button"
                            className={`relative py-2 px-6 rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:z-10 ${
                                billingCycle === 'annual'
                                    ? 'bg-white shadow-sm text-gray-900'
                                    : 'text-gray-700 hover:text-gray-900'
                            }`}
                            onClick={() => setBillingCycle('annual')}
                        >
                            Annual billing
                            <span className="absolute -top-2 -right-2 px-2 py-0.5 text-xs font-semibold bg-green-100 text-green-800 rounded-full">
                                Save 17%
                            </span>
                        </button>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    {plans[billingCycle].map((plan) => (
                        <div
                            key={plan.id}
                            className={`relative rounded-lg border p-8 shadow-sm ${
                                selectedPlan && selectedPlan.id === plan.id
                                    ? 'border-blue-500 ring-2 ring-blue-500'
                                    : 'border-gray-300'
                            }`}
                        >
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {plan.name}
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    {plan.description}
                                </p>
                            </div>

                            <p className="mt-4">
                                <span className="text-4xl font-bold tracking-tight text-gray-900">
                                    ${plan.price}
                                </span>
                                <span className="text-base font-medium text-gray-500">
                                    /{billingCycle === 'annual' ? 'year' : 'month'}
                                </span>
                            </p>

                            <ul className="mt-6 space-y-4">
                                {plan.features.map((feature, index) => (
                                    <li key={index} className="flex">
                                        <i className="fas fa-check flex-shrink-0 text-green-500"></i>
                                        <span className="ml-3 text-sm text-gray-700">
                                            {feature}
                                        </span>
                                    </li>
                                ))}
                            </ul>

                            <Button
                                onClick={() => handlePlanSelect(plan)}
                                variant={selectedPlan && selectedPlan.id === plan.id ? 'primary' : 'secondary'}
                                className="mt-8 w-full"
                            >
                                {plan.price === 0 ? 'Get started' : 'Select plan'}
                            </Button>
                        </div>
                    ))}
                </div>
            </div>
        );
    } catch (error) {
        console.error('PlanSelector component error:', error);
        reportError(error);
        return null;
    }
}
