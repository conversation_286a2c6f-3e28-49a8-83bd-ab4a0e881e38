<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Bizma - The all-in-one business management platform that helps you manage customers, create invoices, track leads, and grow your business efficiently.">
    <meta name="keywords" content="business management, CRM, customer management, invoice, quotation, contract management, Bizma">
    <meta property="og:title" content="Bizma - Grow Your Business with Smart Management">
    <meta property="og:description" content="The all-in-one business management platform that helps you manage customers, create invoices, track leads, and grow your business efficiently.">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Bizma - Grow Your Business with Smart Management">
    <meta name="twitter:description" content="The all-in-one business management platform that helps you manage customers, create invoices, track leads, and grow your business efficiently.">
    <title>Bizma - Grow Your Business with Smart Management</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    
    <!-- React -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone@7/babel.min.js"></script>

    <!-- Utility Scripts -->
    <script src="utils/api-utils.js"></script>
    <script type="text/babel" src="components/auth/AuthContext.js"></script>

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Root element for React -->
    <div id="root"></div>

    <!-- Load Configuration and Utilities -->
    <script src="config.js"></script>
    <script src="utils/main.js?v=4"></script>
    
    <!-- Emergency fix for component loading issues -->
    <script src="utils/fix-loading.js"></script>
    
    <!-- Load Components -->
    <script type="text/babel" src="components/common/Button.js"></script>
    <script type="text/babel" src="components/common/Input.js"></script>
    <script type="text/babel" src="components/common/Modal.js"></script>
    <script type="text/babel" src="components/common/Notification.js"></script>
    <script type="text/babel" src="components/SubscriptionStatus.js"></script>
    <script type="text/babel" src="components/SimpleSubscriptionStatus.js"></script>
    <script type="text/babel" src="components/layout/WebsiteHeader.js"></script>
    <script type="text/babel" src="components/layout/WebsiteFooter.js"></script>
    <script type="text/babel" src="components/auth/LoginForm.js?v=2"></script>
    <script type="text/babel" src="components/auth/EnhancedRegisterForm.js"></script>
    <script type="text/babel" src="components/auth/ForgotPasswordForm.js"></script>
    <script type="text/babel" src="components/pricing/SimplePricingPlans.js"></script>
    <script type="text/babel" src="components/pricing/EnhancedPricingPlans.js"></script>
    <script type="text/babel" src="components/pricing/PricingCalculator.js"></script>
    <script type="text/babel" src="components/pricing/PricingToggle.js"></script>
    <script type="text/babel" src="components/pricing/PricingSummary.js"></script>
    
    <!-- Item Templates -->
    <script type="text/babel" src="components/items/RealEstateItems.js"></script>
    
    <script type="text/babel" src="pages/LandingPage.js"></script>
    <script type="text/babel" src="pages/SuperAdminDashboard.js"></script>
    <script type="text/babel" src="pages/SuperAdminPlans.js"></script>
    <script type="text/babel" src="pages/SuperAdminPaymentGateways.js"></script>
    <script type="text/babel" src="pages/SuperAdminBusinessTypes.js"></script>

    <!-- Utility Scripts (must load first) -->
    <script type="text/babel" src="utils/business-type-templates.js"></script>
    <script type="text/babel" src="utils/update-plans.js"></script>

    <!-- Lead Components (load child components first) -->
    <script type="text/babel" src="components/leads/ActivityItem.js"></script>
    <script type="text/babel" src="components/leads/TaskItem.js"></script>
    <script type="text/babel" src="components/leads/NoteItem.js"></script>
    <script type="text/babel" src="components/leads/TagInput.js"></script>
    <script type="text/babel" src="components/leads/LeadActivity.js"></script>
    <script type="text/babel" src="components/leads/LeadDetails.js"></script>
    <script type="text/babel" src="components/leads/LeadForm.js"></script>
    <script type="text/babel" src="components/leads/LeadList.js"></script>
    <script type="text/babel" src="components/leads/LeadNotes.js"></script>
    <script type="text/babel" src="components/leads/LeadTasks.js"></script>

    <!-- Admin Components -->
    <script type="text/babel" src="components/admin/PlanModal.js"></script>
    <script type="text/babel" src="components/admin/BusinessTypeModal.js"></script>
    <script type="text/babel" src="components/admin/BusinessTypeDetailModal.js"></script>
    <script type="text/babel" src="components/admin/PolicyPagesManager.js?v=3"></script>
    <script type="text/babel" src="components/admin/AnalyticsDashboard.js"></script>

    <!-- Initialize Page -->
    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            return (
                <div className="min-h-screen bg-gray-50">
                    <WebsiteHeader />
                    <LandingPage />
                    <WebsiteFooter />
                </div>
            );
        }

        // Render the app using React 18 createRoot
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
