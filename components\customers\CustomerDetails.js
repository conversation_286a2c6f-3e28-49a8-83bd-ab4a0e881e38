function CustomerDetails({ customer, onEdit, onDelete, onClose }) {
    try {
        const [activities, setActivities] = React.useState([]);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            fetchCustomerActivities();
        }, [customer.objectId]);

        const fetchCustomerActivities = async () => {
            try {
                setLoading(true);
                // For now, we'll use mock activities since activity tracking isn't implemented yet
                // TODO: Implement activity tracking API endpoint
                const mockActivities = [
                    {
                        objectId: 'activity_1',
                        objectData: {
                            type: 'created',
                            description: 'Customer created',
                            timestamp: new Date().toISOString()
                        }
                    }
                ];
                setActivities(mockActivities);
            } catch (error) {
                console.error('Error fetching customer activities:', error);
                setActivities([]);
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="customer-details">
                <div className="customer-details-header">
                    <div className="flex items-start justify-between">
                        <div className="flex items-center">
                            <div className="customer-avatar">
                                {customer.objectData.name[0].toUpperCase()}
                            </div>
                            <div className="ml-4">
                                <h2 className="text-2xl font-bold">{customer.objectData.name}</h2>
                                <p className="text-gray-600">{customer.objectData.company || 'Individual Customer'}</p>
                            </div>
                        </div>
                        <div className="flex space-x-4">
                            <Button
                                variant="secondary"
                                icon="fas fa-edit"
                                onClick={onEdit}
                            >
                                Edit
                            </Button>
                            <Button
                                variant="danger"
                                icon="fas fa-trash"
                                onClick={onDelete}
                            >
                                Delete
                            </Button>
                            <Button
                                variant="secondary"
                                icon="fas fa-times"
                                onClick={onClose}
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="customer-info-grid">
                    <div className="space-y-6">
                        <div>
                            <h3 className="text-lg font-medium mb-4">Contact Information</h3>
                            <div className="space-y-3">
                                <div className="flex items-center text-gray-600">
                                    <i className="fas fa-envelope w-6"></i>
                                    <span>{customer.objectData.email}</span>
                                </div>
                                {customer.objectData.phone && (
                                    <div className="flex items-center text-gray-600">
                                        <i className="fas fa-phone w-6"></i>
                                        <span>{customer.objectData.phone}</span>
                                    </div>
                                )}
                                {customer.objectData.address && (
                                    <div className="flex items-start text-gray-600">
                                        <i className="fas fa-map-marker-alt w-6 mt-1"></i>
                                        <span>{customer.objectData.address}</span>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div>
                            <h3 className="text-lg font-medium mb-4">Customer Details</h3>
                            <div className="space-y-3">
                                <div>
                                    <span className="text-gray-500">Customer Type</span>
                                    <p className="font-medium">
                                        {customer.objectData.type.charAt(0).toUpperCase() + customer.objectData.type.slice(1)}
                                    </p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Created Date</span>
                                    <p className="font-medium">{formatDate(customer.createdAt)}</p>
                                </div>
                                {customer.objectData.notes && (
                                    <div>
                                        <span className="text-gray-500">Notes</span>
                                        <p className="font-medium whitespace-pre-line">
                                            {customer.objectData.notes}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 className="text-lg font-medium mb-4">Recent Activity</h3>
                        {loading ? (
                            <div className="flex justify-center items-center h-32">
                                <i className="fas fa-spinner fa-spin text-blue-500"></i>
                            </div>
                        ) : (
                            <div className="customer-activity-timeline">
                                {activities.length === 0 ? (
                                    <p className="text-gray-500">No recent activities</p>
                                ) : (
                                    activities.map((activity, index) => (
                                        <div key={index} className="customer-activity-item">
                                            <p className="font-medium">{activity.objectData.description}</p>
                                            <p className="text-sm text-gray-500">
                                                {formatDateTime(activity.createdAt)}
                                            </p>
                                        </div>
                                    ))
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('CustomerDetails component error:', error);
        reportError(error);
        return null;
    }
}
