# Database Configuration
DB_HOST=localhost
DB_NAME=business_management
DB_USER=dbuser
DB_PASSWORD=strong_password_here
DB_PORT=3306

# Application Settings
APP_NAME="Business Management"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
APP_TIMEZONE=UTC

# Security
JWT_SECRET=change_this_to_a_random_string_of_at_least_32_characters
ENCRYPTION_KEY=change_this_to_another_random_string_of_32_characters

# PhonePe Payment Gateway (Production)
PHONEPE_MERCHANT_ID=your_merchant_id
PHONEPE_SALT_KEY=your_salt_key
PHONEPE_SALT_INDEX=1
PHONEPE_ENV=prod

# Email Configuration
MAIL_DRIVER=smtp
MAIL_HOST=smtp.yourdomain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Business Management"

# File Storage
UPLOAD_MAX_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
UPLOAD_PATH=uploads

# Rate Limiting
THROTTLE_ENABLED=true
THROTTLE_MAX_ATTEMPTS=60
THROTTLE_DECAY_MINUTES=1

# Session
SESSION_LIFETIME=120  # minutes
SESSION_SECURE_COOKIE=true

# CORS Settings
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# Logging
LOG_CHANNEL=file
LOG_LEVEL=error
LOG_MAX_FILES=30