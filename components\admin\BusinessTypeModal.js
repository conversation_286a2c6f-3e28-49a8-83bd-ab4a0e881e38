// Business Type Modal Component for Creating/Editing Business Types
function BusinessTypeModal({ isOpen, onClose, businessType, onSave }) {
    const [formData, setFormData] = React.useState({
        id: '',
        name: '',
        description: '',
        icon: 'fas fa-building',
        color: 'blue',
        status: 'active',
        categories: [],
        default_features: [],
        sort_order: 0
    });
    const [newCategory, setNewCategory] = React.useState('');
    const [newFeature, setNewFeature] = React.useState('');
    const [saving, setSaving] = React.useState(false);

    React.useEffect(() => {
        if (businessType) {
            setFormData({
                ...businessType,
                categories: Array.isArray(businessType.categories) ? businessType.categories : [],
                default_features: Array.isArray(businessType.default_features) ? businessType.default_features : []
            });
        } else {
            setFormData({
                id: '',
                name: '',
                description: '',
                icon: 'fas fa-building',
                color: 'blue',
                status: 'active',
                categories: [],
                default_features: [],
                sort_order: 0
            });
        }
    }, [businessType]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const addCategory = () => {
        if (newCategory.trim() && !formData.categories.includes(newCategory.trim())) {
            setFormData(prev => ({
                ...prev,
                categories: [...prev.categories, newCategory.trim()]
            }));
            setNewCategory('');
        }
    };

    const removeCategory = (index) => {
        setFormData(prev => ({
            ...prev,
            categories: prev.categories.filter((_, i) => i !== index)
        }));
    };

    const addFeature = () => {
        if (newFeature.trim() && !formData.default_features.includes(newFeature.trim())) {
            setFormData(prev => ({
                ...prev,
                default_features: [...prev.default_features, newFeature.trim()]
            }));
            setNewFeature('');
        }
    };

    const removeFeature = (index) => {
        setFormData(prev => ({
            ...prev,
            default_features: prev.default_features.filter((_, i) => i !== index)
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);
        
        try {
            await onSave(formData);
        } catch (error) {
            console.error('Error saving business type:', error);
        } finally {
            setSaving(false);
        }
    };

    if (!isOpen) return null;

    const iconOptions = [
        'fas fa-building', 'fas fa-store', 'fas fa-gem', 'fas fa-utensils',
        'fas fa-laptop', 'fas fa-car', 'fas fa-home', 'fas fa-heart',
        'fas fa-graduation-cap', 'fas fa-briefcase', 'fas fa-tools', 'fas fa-paint-brush'
    ];

    const colorOptions = [
        'blue', 'green', 'purple', 'red', 'yellow', 'indigo', 'pink', 'gray'
    ];

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            {businessType ? 'Edit Business Type' : 'Create New Business Type'}
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Basic Information */}
                            <div className="space-y-4">
                                <h4 className="text-md font-medium text-gray-900">Basic Information</h4>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Business Type ID *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.id}
                                        onChange={(e) => handleInputChange('id', e.target.value)}
                                        disabled={!!businessType}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                                        required
                                    />
                                    {!!businessType && (
                                        <p className="text-xs text-gray-500 mt-1">ID cannot be changed</p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Name *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange('name', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Description
                                    </label>
                                    <textarea
                                        value={formData.description}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Icon
                                    </label>
                                    <select
                                        value={formData.icon}
                                        onChange={(e) => handleInputChange('icon', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        {iconOptions.map(icon => (
                                            <option key={icon} value={icon}>
                                                {icon.replace('fas fa-', '').replace('-', ' ')}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Color
                                    </label>
                                    <select
                                        value={formData.color}
                                        onChange={(e) => handleInputChange('color', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        {colorOptions.map(color => (
                                            <option key={color} value={color}>
                                                {color.charAt(0).toUpperCase() + color.slice(1)}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            {/* Categories and Features */}
                            <div className="space-y-4">
                                <h4 className="text-md font-medium text-gray-900">Categories & Features</h4>
                                
                                {/* Categories */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Categories
                                    </label>
                                    <div className="flex mb-2">
                                        <input
                                            type="text"
                                            value={newCategory}
                                            onChange={(e) => setNewCategory(e.target.value)}
                                            placeholder="Add category"
                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCategory())}
                                        />
                                        <button
                                            type="button"
                                            onClick={addCategory}
                                            className="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
                                        >
                                            Add
                                        </button>
                                    </div>
                                    <div className="space-y-1 max-h-32 overflow-y-auto">
                                        {formData.categories.map((category, index) => (
                                            <div key={index} className="flex items-center justify-between bg-gray-100 px-3 py-1 rounded">
                                                <span className="text-sm">{category}</span>
                                                <button
                                                    type="button"
                                                    onClick={() => removeCategory(index)}
                                                    className="text-red-600 hover:text-red-800"
                                                >
                                                    <i className="fas fa-times"></i>
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Default Features */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Default Features
                                    </label>
                                    <div className="flex mb-2">
                                        <input
                                            type="text"
                                            value={newFeature}
                                            onChange={(e) => setNewFeature(e.target.value)}
                                            placeholder="Add feature"
                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                                        />
                                        <button
                                            type="button"
                                            onClick={addFeature}
                                            className="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
                                        >
                                            Add
                                        </button>
                                    </div>
                                    <div className="space-y-1 max-h-32 overflow-y-auto">
                                        {formData.default_features.map((feature, index) => (
                                            <div key={index} className="flex items-center justify-between bg-gray-100 px-3 py-1 rounded">
                                                <span className="text-sm">{feature}</span>
                                                <button
                                                    type="button"
                                                    onClick={() => removeFeature(index)}
                                                    className="text-red-600 hover:text-red-800"
                                                >
                                                    <i className="fas fa-times"></i>
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Status and Sort Order */}
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Status
                                        </label>
                                        <select
                                            value={formData.status}
                                            onChange={(e) => handleInputChange('status', e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Sort Order
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.sort_order}
                                            onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 0)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                disabled={saving}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                disabled={saving}
                            >
                                {saving ? (
                                    <>
                                        <i className="fas fa-spinner fa-spin mr-2"></i>
                                        Saving...
                                    </>
                                ) : (
                                    businessType ? 'Update Business Type' : 'Create Business Type'
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.BusinessTypeModal = BusinessTypeModal;
