// Subscription and Trial Tests
document.addEventListener('DOMContentLoaded', function() {
    if (typeof TestRunner !== 'undefined') {
        const testRunner = window.testRunner || new TestRunner();
        window.testRunner = testRunner;

        const subscriptionTests = [
            {
                name: 'Subscription management API should be accessible',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management.php', {
                        method: 'OPTIONS'
                    });
                    testRunner.expect(response.status).toBe(200);
                }
            },
            {
                name: 'Trial status API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/trial-status');
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Current subscription API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/current');
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Usage stats API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/usage');
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Trial start API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/start-trial', {
                        method: 'POST',
                        body: JSON.stringify({
                            plan_id: 'basic'
                        })
                    });
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Plan upgrade API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/upgrade', {
                        method: 'POST',
                        body: JSON.stringify({
                            plan_id: 'premium',
                            billing_cycle: 'monthly'
                        })
                    });
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Trial extension API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/extend-trial', {
                        method: 'POST'
                    });
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Subscription cancellation API should require authentication',
                test: async function() {
                    const response = await testRunner.makeApiCall('/subscription-management/cancel', {
                        method: 'POST'
                    });
                    testRunner.expect(response.status).toBe(401);
                }
            },
            {
                name: 'Trial status banner component should exist',
                test: async function() {
                    testRunner.expect(typeof window.TrialStatusBanner).toBe('function');
                }
            },
            {
                name: 'Subscription status component should exist',
                test: async function() {
                    testRunner.expect(typeof window.SubscriptionStatus).toBe('function');
                }
            },
            {
                name: 'Subscription management modal should exist',
                test: async function() {
                    testRunner.expect(typeof window.SubscriptionManagementModal).toBe('function');
                }
            },
            {
                name: 'Trial banner should show correct trial information',
                test: async function() {
                    // Mock trial data
                    const mockTrialData = {
                        is_trial: true,
                        trial_days_remaining: 7,
                        expired: false,
                        can_extend: true
                    };
                    
                    // Test that trial data is processed correctly
                    testRunner.expect(mockTrialData.is_trial).toBeTruthy();
                    testRunner.expect(mockTrialData.trial_days_remaining).toBeGreaterThan(0);
                    testRunner.expect(mockTrialData.expired).toBeFalsy();
                }
            },
            {
                name: 'Trial expiry calculation should work correctly',
                test: async function() {
                    const now = new Date();
                    const trialEnd = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 days from now
                    
                    const daysRemaining = Math.ceil((trialEnd - now) / (24 * 60 * 60 * 1000));
                    testRunner.expect(daysRemaining).toBe(7);
                    
                    const expiredTrial = new Date(now.getTime() - (1 * 24 * 60 * 60 * 1000)); // 1 day ago
                    const isExpired = expiredTrial < now;
                    testRunner.expect(isExpired).toBeTruthy();
                }
            },
            {
                name: 'Subscription status should handle different states',
                test: async function() {
                    const validStates = ['trial', 'active', 'expired', 'cancelled'];
                    
                    validStates.forEach(state => {
                        testRunner.expect(validStates).toContain(state);
                    });
                }
            },
            {
                name: 'Usage statistics should calculate percentages correctly',
                test: async function() {
                    const mockUsage = {
                        customers: { current: 50, limit: 100 },
                        invoices: { current: 200, limit: 500 },
                        users: { current: 5, limit: 10 }
                    };
                    
                    Object.values(mockUsage).forEach(usage => {
                        const percentage = (usage.current / usage.limit) * 100;
                        testRunner.expect(percentage).toBeGreaterThan(0);
                        testRunner.expect(percentage).toBeLessThan(100);
                    });
                }
            }
        ];

        testRunner.addTestSuite('Subscription', subscriptionTests);
    }
});
