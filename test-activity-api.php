<?php
/**
 * Test script for activity API without authentication
 */

// Include required files
require_once 'api/db-config.php';
require_once 'api/handlers/field-handler.php';
require_once 'api/handlers/object-handler.php';
require_once 'api/handlers/items-handler.php';
require_once 'api/handlers/crud-handler.php';
require_once 'api/handlers/validation-handler.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Mock current user for testing
$currentUser = [
    'object_id' => 'test_user_001',
    'company_id' => 'super_admin_001'
];

try {
    // Test task data with due_date
    $testData = [
        'title' => 'Test task with due date ' . date('Y-m-d H:i:s'),
        'description' => 'Test task description',
        'dueDate' => '2025-07-15',
        'priority' => 'high',
        'status' => 'pending',
        'lead_id' => 'id_687245355524c'
    ];

    echo "<h2>Testing Task Creation</h2>\n";
    echo "<h3>Input Data:</h3>\n";
    echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>\n";

    // Test field extraction
    echo "<h3>Testing extractFields function:</h3>\n";
    $fields = extractFields('task', $testData);
    echo "<pre>" . json_encode($fields, JSON_PRETTY_PRINT) . "</pre>\n";

    // Test validation
    echo "<h3>Testing validation:</h3>\n";
    $validation = ValidationHandler::processObjectData('task', $testData);
    echo "<pre>" . json_encode($validation, JSON_PRETTY_PRINT) . "</pre>\n";

    if ($validation['valid']) {
        echo "<h3>Attempting to create task:</h3>\n";
        
        // Simulate the createObject function logic
        $objectType = 'task';
        $table = mapObjectTypeToTable($objectType);
        echo "<p>Table: $table</p>\n";
        
        $objectId = generateId();
        echo "<p>Generated Object ID: $objectId</p>\n";
        
        $fields['object_id'] = $objectId;
        $fields['company_id'] = $currentUser['company_id'];
        $fields['user_id'] = $currentUser['object_id'];
        
        echo "<h4>Final fields for insertion:</h4>\n";
        echo "<pre>" . json_encode($fields, JSON_PRETTY_PRINT) . "</pre>\n";
        
        // Build SQL
        $columns = implode(', ', array_keys($fields));
        $placeholders = implode(', ', array_fill(0, count($fields), '?'));
        $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        
        echo "<h4>SQL Query:</h4>\n";
        echo "<pre>$sql</pre>\n";
        
        echo "<h4>Values:</h4>\n";
        echo "<pre>" . json_encode(array_values($fields), JSON_PRETTY_PRINT) . "</pre>\n";
        
        // Try to execute
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $values = array_values($fields);
            $types = str_repeat('s', count($values));
            $stmt->bind_param($types, ...$values);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ Task created successfully!</p>\n";
                echo "<p>Inserted ID: " . $conn->insert_id . "</p>\n";
            } else {
                echo "<p style='color: red;'>❌ SQL Error: " . $stmt->error . "</p>\n";
            }
            $stmt->close();
        } else {
            echo "<p style='color: red;'>❌ Prepare Error: " . $conn->error . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Validation failed</p>\n";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

$conn->close();
?>