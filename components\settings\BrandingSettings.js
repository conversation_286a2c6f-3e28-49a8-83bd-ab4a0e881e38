function BrandingSettings({ formData, logoPreview, signaturePreview, handleFileUpload, setLogoPreview, setSignaturePreview, setFormData }) {
    try {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Company Logo
                        </label>
                        <div className="flex items-center space-x-4">
                            {logoPreview && (
                                <div className="relative w-32 h-16 border rounded-md overflow-hidden">
                                    <img 
                                        src={logoPreview} 
                                        alt="Company Logo" 
                                        className="object-contain w-full h-full"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setLogoPreview('');
                                            setFormData(prev => ({ ...prev, logo: '' }));
                                        }}
                                        className="absolute top-0 right-0 bg-red-500 text-white p-1 rounded-bl-md"
                                    >
                                        <i className="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            )}
                            <label className="flex flex-col items-center justify-center w-32 h-16 border-2 border-gray-300 border-dashed rounded-md cursor-pointer hover:bg-gray-50">
                                <div className="space-y-1 text-center">
                                    <i className="fas fa-upload text-gray-400"></i>
                                    <div className="text-xs text-gray-500">
                                        {logoPreview ? 'Change logo' : 'Upload logo'}
                                    </div>
                                </div>
                                <input 
                                    type="file" 
                                    className="hidden" 
                                    accept="image/*" 
                                    onChange={(e) => handleFileUpload(e, 'logo')}
                                />
                            </label>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                            Recommended size: 200x80 pixels. PNG or JPG format.
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Signature
                        </label>
                        <div className="flex items-center space-x-4">
                            {signaturePreview && (
                                <div className="relative w-32 h-16 border rounded-md overflow-hidden">
                                    <img 
                                        src={signaturePreview} 
                                        alt="Signature" 
                                        className="object-contain w-full h-full"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setSignaturePreview('');
                                            setFormData(prev => ({ ...prev, signature: '' }));
                                        }}
                                        className="absolute top-0 right-0 bg-red-500 text-white p-1 rounded-bl-md"
                                    >
                                        <i className="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            )}
                            <label className="flex flex-col items-center justify-center w-32 h-16 border-2 border-gray-300 border-dashed rounded-md cursor-pointer hover:bg-gray-50">
                                <div className="space-y-1 text-center">
                                    <i className="fas fa-upload text-gray-400"></i>
                                    <div className="text-xs text-gray-500">
                                        {signaturePreview ? 'Change signature' : 'Upload signature'}
                                    </div>
                                </div>
                                <input 
                                    type="file" 
                                    className="hidden" 
                                    accept="image/*" 
                                    onChange={(e) => handleFileUpload(e, 'signature')}
                                />
                            </label>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                            Recommended size: 200x80 pixels. PNG with transparent background preferred.
                        </p>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('BrandingSettings component error:', error);
        reportError(error);
        return null;
    }
}
