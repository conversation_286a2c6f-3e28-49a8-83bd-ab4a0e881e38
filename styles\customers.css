.customer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.customer-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.customer-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.customer-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.customer-details-header {
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

.customer-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.customer-activity-timeline {
    border-left: 2px solid #e5e7eb;
    margin-left: 1rem;
    padding-left: 2rem;
}

.customer-activity-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.customer-activity-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: #3b82f6;
    border: 2px solid white;
}

@media (max-width: 768px) {
    .customer-info-grid {
        grid-template-columns: 1fr;
    }
}
