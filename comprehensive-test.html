<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Application Test - Bizma SaaS</title>
    <script src="config.js"></script>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3b82f6;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status.pending { background: #fef3c7; color: #92400e; }
        .status.testing { background: #dbeafe; color: #1e40af; }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fed7aa; color: #9a3412; }
        
        button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        
        pre {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
            margin: 10px 0;
        }
        .log {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            transition: width 0.3s ease;
        }
        .metric {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 8px;
            margin: 10px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        h1 { color: #1e293b; margin-bottom: 10px; }
        h2 { color: #334155; margin-bottom: 15px; }
        h3 { color: #475569; margin-bottom: 10px; }
        .icon { margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Bizma SaaS - Comprehensive Application Test Suite</h1>
        <p>Complete testing framework for production readiness verification</p>
        
        <div class="test-grid">
            <div class="metric">
                <div class="metric-value" id="totalTests">0</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="passedTests">0</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="failedTests">0</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="testProgress">0%</div>
                <div class="metric-label">Progress</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Test Controls</h2>
        <button onclick="runAllTests()" id="runAllBtn">
            <span class="icon">🚀</span>Run All Tests
        </button>
        <button onclick="runCriticalTests()" class="btn-secondary">
            <span class="icon">⚡</span>Critical Tests Only
        </button>
        <button onclick="clearResults()" class="btn-danger">
            <span class="icon">🗑️</span>Clear Results
        </button>
        <button onclick="exportResults()" class="btn-success">
            <span class="icon">📊</span>Export Report
        </button>
    </div>

    <div class="test-grid">
        <!-- Authentication Tests -->
        <div class="test-card">
            <h3><span class="icon">🔐</span>Authentication System</h3>
            <div class="status pending" id="auth-status">Pending</div>
            <p>Tests login, registration, token validation, and session management</p>
            <button onclick="testAuthentication()">Test Authentication</button>
            <div id="auth-results" class="log" style="display: none;"></div>
        </div>

        <!-- User Flow Tests -->
        <div class="test-card">
            <h3><span class="icon">👤</span>User Flow</h3>
            <div class="status pending" id="userflow-status">Pending</div>
            <p>Complete user journey from registration to dashboard usage</p>
            <button onclick="testUserFlow()">Test User Flow</button>
            <div id="userflow-results" class="log" style="display: none;"></div>
        </div>

        <!-- Super Admin Tests -->
        <div class="test-card">
            <h3><span class="icon">👑</span>Super Admin</h3>
            <div class="status pending" id="superadmin-status">Pending</div>
            <p>Super admin dashboard, CMS, and management features</p>
            <button onclick="testSuperAdmin()">Test Super Admin</button>
            <div id="superadmin-results" class="log" style="display: none;"></div>
        </div>

        <!-- Trial Plan Tests -->
        <div class="test-card">
            <h3><span class="icon">⏰</span>Trial Plans</h3>
            <div class="status pending" id="trial-status">Pending</div>
            <p>Trial plan creation, management, and upgrade flows</p>
            <button onclick="testTrialPlans()">Test Trial Plans</button>
            <div id="trial-results" class="log" style="display: none;"></div>
        </div>

        <!-- API Tests -->
        <div class="test-card">
            <h3><span class="icon">🔌</span>API Endpoints</h3>
            <div class="status pending" id="api-status">Pending</div>
            <p>All API endpoints, error handling, and response validation</p>
            <button onclick="testAPIEndpoints()">Test APIs</button>
            <div id="api-results" class="log" style="display: none;"></div>
        </div>

        <!-- Security Tests -->
        <div class="test-card">
            <h3><span class="icon">🛡️</span>Security</h3>
            <div class="status pending" id="security-status">Pending</div>
            <p>Security headers, input validation, and vulnerability checks</p>
            <button onclick="testSecurity()">Test Security</button>
            <div id="security-results" class="log" style="display: none;"></div>
        </div>

        <!-- Performance Tests -->
        <div class="test-card">
            <h3><span class="icon">⚡</span>Performance</h3>
            <div class="status pending" id="performance-status">Pending</div>
            <p>Load times, responsiveness, and optimization checks</p>
            <button onclick="testPerformance()">Test Performance</button>
            <div id="performance-results" class="log" style="display: none;"></div>
        </div>

        <!-- UI/UX Tests -->
        <div class="test-card">
            <h3><span class="icon">🎨</span>UI/UX</h3>
            <div class="status pending" id="ui-status">Pending</div>
            <p>Responsive design, accessibility, and user experience</p>
            <button onclick="testUIUX()">Test UI/UX</button>
            <div id="ui-results" class="log" style="display: none;"></div>
        </div>

        <!-- Database Tests -->
        <div class="test-card">
            <h3><span class="icon">🗄️</span>Database</h3>
            <div class="status pending" id="database-status">Pending</div>
            <p>Database connectivity, queries, and data integrity</p>
            <button onclick="testDatabase()">Test Database</button>
            <div id="database-results" class="log" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📋 Test Results Summary</h2>
        <div id="testSummary" class="log">
            No tests run yet. Click "Run All Tests" to begin comprehensive testing.
        </div>
    </div>

    <script src="comprehensive-test.js"></script>
</body>
</html>
