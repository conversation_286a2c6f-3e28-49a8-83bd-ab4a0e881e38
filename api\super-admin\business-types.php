<?php
require_once '../config/database.php';
require_once '../utils/SecurityUtils.php';
require_once '../utils/AuthUtils.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication and super admin role
    $authResult = AuthUtils::verifyToken();
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    $user = $authResult['user'];
    if ($user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        exit;
    }

    $conn = Database::getConnection();
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetBusinessTypes($conn);
            break;
        case 'POST':
            handleCreateBusinessType($conn);
            break;
        case 'PUT':
            handleUpdateBusinessType($conn);
            break;
        case 'DELETE':
            handleDeleteBusinessType($conn);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Business Types API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetBusinessTypes($conn) {
    try {
        $stmt = $conn->prepare("
            SELECT id, name, description, icon, color, default_modules, 
                   default_categories, default_features, default_templates, 
                   is_active, created_at, updated_at
            FROM business_types 
            ORDER BY name ASC
        ");
        $stmt->execute();
        $result = $stmt->get_result();
        
        $businessTypes = [];
        while ($row = $result->fetch_assoc()) {
            $businessTypes[] = $row;
        }
        
        echo json_encode(['success' => true, 'data' => $businessTypes]);
    } catch (Exception $e) {
        error_log("Error fetching business types: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to fetch business types']);
    }
}

function handleCreateBusinessType($conn) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $id = SecurityUtils::sanitizeInput($input['id'] ?? '');
        $name = SecurityUtils::sanitizeInput($input['name'] ?? '');
        $description = SecurityUtils::sanitizeInput($input['description'] ?? '');
        $icon = SecurityUtils::sanitizeInput($input['icon'] ?? 'fas fa-building');
        $color = SecurityUtils::sanitizeInput($input['color'] ?? 'blue');
        $default_modules = SecurityUtils::sanitizeInput($input['default_modules'] ?? '');
        $default_categories = SecurityUtils::sanitizeInput($input['default_categories'] ?? '');
        $default_features = SecurityUtils::sanitizeInput($input['default_features'] ?? '');
        $default_templates = SecurityUtils::sanitizeInput($input['default_templates'] ?? '');
        $is_active = isset($input['is_active']) ? (bool)$input['is_active'] : true;
        
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'Name is required']);
            return;
        }
        
        // Generate ID if not provided
        if (empty($id)) {
            $id = strtolower(str_replace(' ', '_', $name));
            $id = preg_replace('/[^a-z0-9_]/', '', $id);
        }
        
        $stmt = $conn->prepare("
            INSERT INTO business_types (id, name, description, icon, color, default_modules, 
                                      default_categories, default_features, default_templates, 
                                      is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->bind_param("sssssssssi", $id, $name, $description, $icon, $color, 
                         $default_modules, $default_categories, $default_features, 
                         $default_templates, $is_active);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Business type created successfully', 'id' => $id]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to create business type']);
        }
    } catch (Exception $e) {
        error_log("Error creating business type: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to create business type']);
    }
}

function handleUpdateBusinessType($conn) {
    try {
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $id = end($pathParts);
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        $name = SecurityUtils::sanitizeInput($input['name'] ?? '');
        $description = SecurityUtils::sanitizeInput($input['description'] ?? '');
        $icon = SecurityUtils::sanitizeInput($input['icon'] ?? 'fas fa-building');
        $color = SecurityUtils::sanitizeInput($input['color'] ?? 'blue');
        $default_modules = SecurityUtils::sanitizeInput($input['default_modules'] ?? '');
        $default_categories = SecurityUtils::sanitizeInput($input['default_categories'] ?? '');
        $default_features = SecurityUtils::sanitizeInput($input['default_features'] ?? '');
        $default_templates = SecurityUtils::sanitizeInput($input['default_templates'] ?? '');
        $is_active = isset($input['is_active']) ? (bool)$input['is_active'] : true;
        
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'Name is required']);
            return;
        }
        
        $stmt = $conn->prepare("
            UPDATE business_types 
            SET name = ?, description = ?, icon = ?, color = ?, default_modules = ?, 
                default_categories = ?, default_features = ?, default_templates = ?, 
                is_active = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->bind_param("ssssssssss", $name, $description, $icon, $color, 
                         $default_modules, $default_categories, $default_features, 
                         $default_templates, $is_active, $id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Business type updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update business type']);
        }
    } catch (Exception $e) {
        error_log("Error updating business type: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to update business type']);
    }
}

function handleDeleteBusinessType($conn) {
    try {
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $id = end($pathParts);
        
        // Check if business type is in use
        $checkStmt = $conn->prepare("SELECT COUNT(*) as count FROM companies WHERE business_type_id = ?");
        $checkStmt->bind_param("s", $id);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        $row = $result->fetch_assoc();
        
        if ($row['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Cannot delete business type that is in use']);
            return;
        }
        
        $stmt = $conn->prepare("DELETE FROM business_types WHERE id = ?");
        $stmt->bind_param("s", $id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Business type deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete business type']);
        }
    } catch (Exception $e) {
        error_log("Error deleting business type: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to delete business type']);
    }
}
?>
