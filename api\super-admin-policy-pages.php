<?php
/**
 * Super Admin Policy Pages API
 * Handles CRUD operations for policy pages
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/db-config.php';

try {
    // Verify super admin access
    $user = getCurrentUser();
    if (!$user || $user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Super admin access required']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    // Extract policy ID if present
    $policyId = $pathParts[3] ?? '';

    switch ($method) {
        case 'GET':
            if ($policyId) {
                getPolicyPage($policyId);
            } else {
                getAllPolicyPages();
            }
            break;
            
        case 'POST':
            createPolicyPage();
            break;
            
        case 'PUT':
            if ($policyId) {
                updatePolicyPage($policyId);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Policy ID required for update']);
            }
            break;
            
        case 'DELETE':
            if ($policyId) {
                deletePolicyPage($policyId);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Policy ID required for deletion']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log('Policy Pages API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function getAllPolicyPages() {
    global $conn;
    
    $sql = "SELECT object_id, page_type, title, content, version, is_active, 
                   template_variables, created_at, updated_at 
            FROM policy_pages 
            ORDER BY page_type ASC, created_at DESC";
    
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . $conn->error);
    }
    
    $policies = [];
    while ($row = $result->fetch_assoc()) {
        // Parse JSON fields
        $row['template_variables'] = json_decode($row['template_variables'] ?? '{}', true);
        $row['is_active'] = (bool)$row['is_active'];
        
        $policies[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $policies,
        'count' => count($policies)
    ]);
}

function getPolicyPage($policyId) {
    global $conn;
    
    $stmt = $conn->prepare("
        SELECT object_id, page_type, title, content, version, is_active, 
               template_variables, created_at, updated_at 
        FROM policy_pages 
        WHERE object_id = ?
    ");
    $stmt->bind_param("s", $policyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($policy = $result->fetch_assoc()) {
        // Parse JSON fields
        $policy['template_variables'] = json_decode($policy['template_variables'] ?? '{}', true);
        $policy['is_active'] = (bool)$policy['is_active'];
        
        echo json_encode([
            'success' => true,
            'data' => $policy
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Policy page not found'
        ]);
    }
}

function createPolicyPage() {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        return;
    }
    
    $pageType = $input['page_type'] ?? '';
    $title = $input['title'] ?? '';
    $content = $input['content'] ?? '';
    $version = $input['version'] ?? '1.0';
    $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : true;
    $templateVariables = json_encode($input['template_variables'] ?? []);
    
    if (!$pageType || !$title || !$content) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Page type, title, and content are required']);
        return;
    }
    
    try {
        $policyId = 'policy_' . time() . '_' . rand(100, 999);
        
        $stmt = $conn->prepare("
            INSERT INTO policy_pages (
                object_id, page_type, title, content, version, is_active, 
                template_variables, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->bind_param("sssssss",
            $policyId,
            $pageType,
            $title,
            $content,
            $version,
            $isActive,
            $templateVariables
        );
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Policy page created successfully',
                'data' => [
                    'object_id' => $policyId,
                    'page_type' => $pageType,
                    'title' => $title
                ]
            ]);
        } else {
            throw new Exception('Failed to create policy page: ' . $stmt->error);
        }
        
    } catch (Exception $e) {
        error_log('Policy creation error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to create policy page']);
    }
}

function updatePolicyPage($policyId) {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        return;
    }
    
    $pageType = $input['page_type'] ?? '';
    $title = $input['title'] ?? '';
    $content = $input['content'] ?? '';
    $version = $input['version'] ?? '1.0';
    $isActive = isset($input['is_active']) ? (bool)$input['is_active'] : true;
    $templateVariables = json_encode($input['template_variables'] ?? []);
    
    if (!$pageType || !$title || !$content) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Page type, title, and content are required']);
        return;
    }
    
    try {
        $stmt = $conn->prepare("
            UPDATE policy_pages SET 
                page_type = ?, title = ?, content = ?, version = ?, 
                is_active = ?, template_variables = ?, updated_at = NOW()
            WHERE object_id = ?
        ");
        
        $stmt->bind_param("sssssss",
            $pageType,
            $title,
            $content,
            $version,
            $isActive,
            $templateVariables,
            $policyId
        );
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Policy page updated successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Policy page not found']);
            }
        } else {
            throw new Exception('Failed to update policy page: ' . $stmt->error);
        }
        
    } catch (Exception $e) {
        error_log('Policy update error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update policy page']);
    }
}

function deletePolicyPage($policyId) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("DELETE FROM policy_pages WHERE object_id = ?");
        $stmt->bind_param("s", $policyId);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Policy page deleted successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Policy page not found']);
            }
        } else {
            throw new Exception('Failed to delete policy page: ' . $stmt->error);
        }
        
    } catch (Exception $e) {
        error_log('Policy deletion error: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to delete policy page']);
    }
}
?>
