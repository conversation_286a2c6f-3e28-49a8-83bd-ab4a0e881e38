<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AuthContext Final Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div id="root"></div>

    <script>
        // Mock getApiUrl function
        window.getApiUrl = function(path) {
            return '/biz/api/api.php' + path;
        };
    </script>

    <script src="/biz/components/auth/AuthContext.js"></script>

    <script type="text/babel">
        function TestApp() {
            const [step, setStep] = React.useState(1);
            const [loginStatus, setLoginStatus] = React.useState('');
            const [authStatus, setAuthStatus] = React.useState('');

            const performLogin = async () => {
                setLoginStatus('Logging in...');
                try {
                    const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'admin123'
                        })
                    });

                    const data = await response.json();
                    if (data.success) {
                        localStorage.setItem('authToken', data.tokens.access_token);
                        setLoginStatus('✅ Login successful! Token stored in localStorage.');
                        setStep(2);
                    } else {
                        setLoginStatus('❌ Login failed: ' + data.error);
                    }
                } catch (error) {
                    setLoginStatus('❌ Login error: ' + error.message);
                }
            };

            const clearToken = () => {
                localStorage.removeItem('authToken');
                setAuthStatus('Token cleared from localStorage');
                setStep(1);
            };

            return (
                <div>
                    <h1>🔐 AuthContext Final Test</h1>
                    
                    {step === 1 && (
                        <div>
                            <h2>Step 1: Login and Store Token</h2>
                            <button onClick={performLogin}>Login and Get Fresh Token</button>
                            {loginStatus && <div className="status">{loginStatus}</div>}
                        </div>
                    )}

                    {step === 2 && (
                        <div>
                            <h2>Step 2: Test AuthContext</h2>
                            <p>Fresh token stored. Testing AuthContext...</p>
                            <button onClick={clearToken}>Clear Token and Restart</button>
                            
                            <AuthProvider>
                                <AuthTest />
                            </AuthProvider>
                        </div>
                    )}
                </div>
            );
        }

        function AuthTest() {
            const authContext = React.useContext(AuthContext);
            
            if (!authContext) {
                return <div className="status error">❌ AuthContext not available</div>;
            }

            const { user, loading, error } = authContext;

            if (loading) {
                return <div className="status loading">🔄 Loading authentication...</div>;
            }

            if (error) {
                return <div className="status error">❌ Auth Error: {error}</div>;
            }

            if (user) {
                return (
                    <div className="status success">
                        <h3>✅ Authentication Successful!</h3>
                        <p><strong>User:</strong> {user.name} ({user.email})</p>
                        <p><strong>Role:</strong> {user.role}</p>
                        <p><strong>Company:</strong> {user.company_name}</p>
                        <p><strong>Industry:</strong> {user.industry}</p>
                    </div>
                );
            }

            return <div className="status error">❌ No user authenticated</div>;
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>