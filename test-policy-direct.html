<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Policy Pages Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .policy-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .policy-title { font-weight: bold; color: #333; }
        .policy-content { margin-top: 10px; color: #666; }
        .loading { text-align: center; color: #666; }
        .error { background: #ffebee; color: #c62828; padding: 10px; border-radius: 5px; }
        .success { background: #e8f5e8; color: #2e7d32; padding: 10px; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Policy Pages Test</h1>
        
        <div id="authStatus"></div>
        
        <button onclick="loadPolicyPages()">Load Policy Pages</button>
        <button onclick="checkAuth()">Check Auth</button>
        
        <div id="policyPages"></div>
    </div>

    <script src="config.js"></script>
    <script>
        function showMessage(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            document.getElementById('authStatus').appendChild(div);
        }

        function checkAuth() {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('user');
            
            document.getElementById('authStatus').innerHTML = '';
            
            if (token && user) {
                const userData = JSON.parse(user);
                showMessage(`Logged in as: ${userData.name} (${userData.role})`, 'success');
                showMessage(`Token: ${token.substring(0, 20)}...`, 'info');
            } else {
                showMessage('Not authenticated', 'error');
            }
        }

        async function loadPolicyPages() {
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                showMessage('Please authenticate first', 'error');
                return;
            }

            document.getElementById('policyPages').innerHTML = '<div class="loading">Loading policy pages...</div>';

            try {
                const response = await fetch(window.getApiUrl('/super-admin/policy-pages'), {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok && data.success) {
                    displayPolicyPages(data.data);
                } else {
                    document.getElementById('policyPages').innerHTML = 
                        `<div class="error">Failed to load policy pages: ${data.message || data.error}</div>`;
                }
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('policyPages').innerHTML = 
                    `<div class="error">Error loading policy pages: ${error.message}</div>`;
            }
        }

        function displayPolicyPages(policyData) {
            const container = document.getElementById('policyPages');
            container.innerHTML = '<h2>Policy Pages</h2>';
            
            Object.keys(policyData).forEach(key => {
                const policy = policyData[key];
                const policyDiv = document.createElement('div');
                policyDiv.className = 'policy-item';
                policyDiv.innerHTML = `
                    <div class="policy-title">${policy.title}</div>
                    <div class="policy-content">${policy.content}</div>
                `;
                container.appendChild(policyDiv);
            });
        }

        // Check auth on page load
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
