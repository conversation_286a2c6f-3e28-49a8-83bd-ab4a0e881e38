<?php
/**
 * Database Schema Verification and Auto-Creation Script
 * Ensures all required tables exist with proper structure
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/db-config.php';

class DatabaseSchemaManager {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Check and create all required tables
     */
    public function verifyAndCreateTables() {
        $tables = $this->getRequiredTables();
        $results = [];
        
        foreach ($tables as $tableName => $schema) {
            $exists = $this->tableExists($tableName);
            if (!$exists) {
                $created = $this->createTable($tableName, $schema);
                $results[$tableName] = $created ? 'CREATED' : 'FAILED';
            } else {
                $results[$tableName] = 'EXISTS';
            }
        }
        
        return $results;
    }
    
    /**
     * Check if table exists
     */
    private function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE '$tableName'";
        $result = $this->conn->query($sql);
        return $result && $result->num_rows > 0;
    }
    
    /**
     * Create table with given schema
     */
    private function createTable($tableName, $schema) {
        try {
            $sql = "CREATE TABLE `$tableName` ($schema)";
            return $this->conn->query($sql);
        } catch (Exception $e) {
            error_log("Failed to create table $tableName: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all required table schemas
     */
    private function getRequiredTables() {
        return [
            'companies' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) DEFAULT NULL,
                `phone` varchar(50) DEFAULT NULL,
                `address` text DEFAULT NULL,
                `website` varchar(255) DEFAULT NULL,
                `owner_id` varchar(255) DEFAULT NULL,
                `members` text DEFAULT NULL,
                `status` enum('active','inactive','suspended') DEFAULT 'active',
                `subscription_plan` varchar(100) DEFAULT 'free',
                `subscription_expires` datetime DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `owner_id` (`owner_id`),
                KEY `status` (`status`)
            ",
            
            'users' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) NOT NULL,
                `password_hash` varchar(255) DEFAULT NULL,
                `company_id` varchar(255) DEFAULT NULL,
                `role` enum('super_admin','admin','user','manager','viewer') DEFAULT 'user',
                `permissions` json DEFAULT NULL,
                `auth_token` varchar(255) DEFAULT NULL,
                `token_expires` datetime DEFAULT NULL,
                `status` enum('active','inactive','pending') DEFAULT 'active',
                `last_login` datetime DEFAULT NULL,
                `login_count` int(11) DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                UNIQUE KEY `email` (`email`),
                KEY `company_id` (`company_id`),
                KEY `auth_token` (`auth_token`)
            ",
            
            'customers' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) DEFAULT NULL,
                `phone` varchar(50) DEFAULT NULL,
                `company` varchar(255) DEFAULT NULL,
                `address` text DEFAULT NULL,
                `type` enum('individual','business') DEFAULT 'individual',
                `notes` text DEFAULT NULL,
                `status` enum('active','inactive') DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `company_id` (`company_id`),
                KEY `email` (`email`)
            ",
            
            'leads' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) DEFAULT NULL,
                `phone` varchar(50) DEFAULT NULL,
                `company` varchar(255) DEFAULT NULL,
                `position` varchar(255) DEFAULT NULL,
                `source` varchar(100) DEFAULT NULL,
                `status` enum('new','contacted','qualified','proposal','negotiation','closed_won','closed_lost') DEFAULT 'new',
                `priority` enum('low','medium','high') DEFAULT 'medium',
                `value` decimal(10,2) DEFAULT NULL,
                `notes` text DEFAULT NULL,
                `assigned_to` varchar(255) DEFAULT NULL,
                `next_follow_up` date DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `company_id` (`company_id`),
                KEY `status` (`status`),
                KEY `assigned_to` (`assigned_to`)
            ",
            
            'invoices' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `customer_id` varchar(255) NOT NULL,
                `invoice_number` varchar(100) NOT NULL,
                `subtotal` decimal(10,2) DEFAULT 0.00,
                `tax` decimal(10,2) DEFAULT 0.00,
                `tax_rate` decimal(5,2) DEFAULT 0.00,
                `discount` decimal(10,2) DEFAULT 0.00,
                `total` decimal(10,2) NOT NULL,
                `amount_paid` decimal(10,2) DEFAULT 0.00,
                `balance` decimal(10,2) DEFAULT 0.00,
                `due_date` date NOT NULL,
                `status` enum('draft','sent','paid','overdue','cancelled') DEFAULT 'draft',
                `payment_method` varchar(100) DEFAULT NULL,
                `paid_at` datetime DEFAULT NULL,
                `notes` text DEFAULT NULL,
                `terms` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                UNIQUE KEY `invoice_number` (`invoice_number`),
                KEY `company_id` (`company_id`),
                KEY `customer_id` (`customer_id`),
                KEY `status` (`status`)
            ",
            
            'quotations' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `customer_id` varchar(255) NOT NULL,
                `quotation_number` varchar(100) NOT NULL,
                `project_name` varchar(255) DEFAULT NULL,
                `subtotal` decimal(10,2) DEFAULT 0.00,
                `tax` decimal(10,2) DEFAULT 0.00,
                `tax_rate` decimal(5,2) DEFAULT 0.00,
                `discount` decimal(10,2) DEFAULT 0.00,
                `total` decimal(10,2) NOT NULL,
                `valid_until` date NOT NULL,
                `status` enum('draft','sent','approved','rejected','expired') DEFAULT 'draft',
                `notes` text DEFAULT NULL,
                `terms` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                UNIQUE KEY `quotation_number` (`quotation_number`),
                KEY `company_id` (`company_id`),
                KEY `customer_id` (`customer_id`),
                KEY `status` (`status`)
            ",
            
            'contracts' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `customer_id` varchar(255) NOT NULL,
                `contract_number` varchar(100) NOT NULL,
                `title` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `value` decimal(10,2) NOT NULL,
                `status` enum('draft','active','completed','cancelled','expired') DEFAULT 'draft',
                `start_date` date NOT NULL,
                `end_date` date NOT NULL,
                `type` varchar(100) DEFAULT NULL,
                `scope` text DEFAULT NULL,
                `payment_terms` text DEFAULT NULL,
                `terms` text DEFAULT NULL,
                `renewal_terms` text DEFAULT NULL,
                `is_recurring` tinyint(1) DEFAULT 0,
                `billing_cycle` enum('monthly','quarterly','yearly') DEFAULT NULL,
                `customer_signature` text DEFAULT NULL,
                `signed_at1` datetime DEFAULT NULL,
                `signed_at2` datetime DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                UNIQUE KEY `contract_number` (`contract_number`),
                KEY `customer_id` (`customer_id`),
                KEY `status` (`status`)
            ",
            
            'items' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `category_id` varchar(255) DEFAULT NULL,
                `subcategory_id` varchar(255) DEFAULT NULL,
                `sku` varchar(100) DEFAULT NULL,
                `price` decimal(10,2) NOT NULL,
                `cost_price` decimal(10,2) DEFAULT NULL,
                `tax` decimal(5,2) DEFAULT 0.00,
                `stock_quantity` int(11) DEFAULT 0,
                `unit` varchar(50) DEFAULT 'piece',
                `is_active` tinyint(1) DEFAULT 1,
                `is_recurring` tinyint(1) DEFAULT 0,
                `recurring_period` enum('monthly','quarterly','yearly') DEFAULT NULL,
                `item_type` enum('product','service','digital') DEFAULT 'product',
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `company_id` (`company_id`),
                KEY `category_id` (`category_id`),
                KEY `sku` (`sku`)
            ",
            
            'item_categories' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `company_id` (`company_id`)
            ",
            
            'item_subcategories' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `parent_id` varchar(255) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `company_id` (`company_id`),
                KEY `parent_id` (`parent_id`)
            ",
            
            'settings' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `object_id` varchar(255) NOT NULL,
                `company_id` varchar(255) NOT NULL,
                `company_name` varchar(255) DEFAULT NULL,
                `company_email` varchar(255) DEFAULT NULL,
                `company_phone` varchar(50) DEFAULT NULL,
                `company_address` text DEFAULT NULL,
                `company_website` varchar(255) DEFAULT NULL,
                `tax_rate` decimal(5,2) DEFAULT 0.00,
                `currency` varchar(10) DEFAULT 'USD',
                `date_format` varchar(20) DEFAULT 'Y-m-d',
                `theme` varchar(50) DEFAULT 'default',
                `logo` varchar(255) DEFAULT NULL,
                `signature` text DEFAULT NULL,
                `company_gst` varchar(50) DEFAULT NULL,
                `authorized_name` varchar(255) DEFAULT NULL,
                `bank_details` text DEFAULT NULL,
                `upi_id` varchar(100) DEFAULT NULL,
                `default_payment_terms` text DEFAULT NULL,
                `default_notes` text DEFAULT NULL,
                `notifications` json DEFAULT NULL,
                `templates` json DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `object_id` (`object_id`),
                KEY `company_id` (`company_id`)
            ",
            
            'admin_audit_log' => "
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `admin_user_id` varchar(255) NOT NULL,
                `action` varchar(50) NOT NULL,
                `target_type` varchar(50) DEFAULT NULL,
                `target_id` varchar(255) DEFAULT NULL,
                `details` text DEFAULT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `user_agent` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                PRIMARY KEY (`id`),
                KEY `admin_user_id` (`admin_user_id`),
                KEY `action` (`action`),
                KEY `target_type` (`target_type`),
                KEY `target_id` (`target_id`)
            "
        ];
    }
}

// Run the schema check if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $schemaManager = new DatabaseSchemaManager($conn);
    $results = $schemaManager->verifyAndCreateTables();
    
    echo "<h2>Database Schema Verification Results</h2>\n";
    echo "<table border='1' cellpadding='5'>\n";
    echo "<tr><th>Table Name</th><th>Status</th></tr>\n";
    
    foreach ($results as $table => $status) {
        $color = $status === 'EXISTS' ? 'green' : ($status === 'CREATED' ? 'blue' : 'red');
        echo "<tr><td>$table</td><td style='color: $color'>$status</td></tr>\n";
    }
    
    echo "</table>\n";
    
    // Check if all tables are ready
    $allReady = !in_array('FAILED', $results);
    if ($allReady) {
        echo "<p style='color: green; font-weight: bold;'>✅ All database tables are ready!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some tables failed to create. Check error logs.</p>";
    }
}
?>