<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Check superadmin user data
    $stmt = $conn->prepare("SELECT id, object_id, name, email, role, status, auth_token, token_expires FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Superadmin user not found'
        ]);
        exit;
    }
    
    $user = $result->fetch_assoc();
    
    // Check if token is valid
    $tokenValid = false;
    if ($user['auth_token'] && $user['token_expires']) {
        $tokenValid = strtotime($user['token_expires']) > time();
    }
    
    echo json_encode([
        'success' => true,
        'user' => [
            'id' => $user['id'],
            'object_id' => $user['object_id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'status' => $user['status'],
            'has_token' => !empty($user['auth_token']),
            'token_valid' => $tokenValid,
            'token_expires' => $user['token_expires']
        ],
        'role_check' => [
            'is_super_admin' => $user['role'] === 'super_admin',
            'role_value' => $user['role'],
            'role_type' => gettype($user['role']),
            'role_length' => strlen($user['role'])
        ]
    ]);
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
