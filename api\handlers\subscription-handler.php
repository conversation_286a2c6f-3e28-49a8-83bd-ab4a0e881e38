<?php
/**
 * Subscription Handler
 * Special handler for subscriptions table which has a different structure
 * than the generic object system
 */

/**
 * Convert subscription table row to object format
 */
function subscriptionRowToObject($row) {
    // Extract object_id and other metadata
    $objectId = $row['object_id'];
    $createdAt = $row['created_at'];
    $updatedAt = $row['updated_at'];
    
    // Build object data from individual columns
    $objectData = [
        'planId' => $row['plan_id'],
        'planName' => $row['plan_name'],
        'status' => $row['status'],
        'startDate' => $row['start_date'],
        'endDate' => $row['end_date'],
        'trialEndDate' => $row['trial_end_date'],
        'price' => $row['price'],
        'billingCycle' => $row['billing_cycle'],
        'features' => $row['features'] ? json_decode($row['features'], true) : null,
        'limitsData' => $row['limits_data'] ? json_decode($row['limits_data'], true) : null,
        'paymentMethod' => $row['payment_method'],
        'lastPaymentDate' => $row['last_payment_date'],
        'nextPaymentDate' => $row['next_payment_date']
    ];
    
    return [
        'objectId' => $objectId,
        'objectType' => 'subscription',
        'objectData' => $objectData,
        'createdAt' => $createdAt,
        'updatedAt' => $updatedAt
    ];
}

/**
 * Convert object data to subscription table columns
 */
function objectToSubscriptionData($objectData, $currentUser) {
    return [
        'company_id' => $currentUser['company_id'],
        'user_id' => $currentUser['object_id'],
        'plan_id' => $objectData['planId'] ?? '',
        'plan_name' => $objectData['planName'] ?? '',
        'status' => $objectData['status'] ?? 'inactive',
        'start_date' => $objectData['startDate'] ?? date('Y-m-d H:i:s'),
        'end_date' => $objectData['endDate'] ?? null,
        'trial_end_date' => $objectData['trialEndDate'] ?? null,
        'price' => $objectData['price'] ?? 0,
        'billing_cycle' => $objectData['billingCycle'] ?? $objectData['billingPeriod'] ?? 'monthly',
        'features' => isset($objectData['features']) ? json_encode($objectData['features']) : null,
        'limits_data' => isset($objectData['limitsData']) ? json_encode($objectData['limitsData']) : null,
        'payment_method' => $objectData['paymentMethod'] ?? null,
        'last_payment_date' => $objectData['lastPaymentDate'] ?? null,
        'next_payment_date' => $objectData['nextPaymentDate'] ?? null
    ];
}

/**
 * Create subscription
 */
function createSubscription($objectData) {
    global $conn;
    
    // Get current user
    $currentUser = getCurrentUser();
    error_log("Current user from token: " . json_encode($currentUser));

    if (!$currentUser) {
        error_log("Authentication failed - no current user found");
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }

    error_log("User authenticated: " . $currentUser['object_id'] . " with company: " . $currentUser['company_id']);
    
    // Validate company exists
    if (empty($currentUser['company_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'User has no company assigned']);
        return;
    }

    // Check if company exists in companies table
    $checkCompanySql = "SELECT object_id FROM companies WHERE object_id = ?";
    $checkStmt = $conn->prepare($checkCompanySql);
    $checkStmt->bind_param("s", $currentUser['company_id']);
    $checkStmt->execute();
    $companyResult = $checkStmt->get_result();

    if ($companyResult->num_rows == 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Company not found: ' . $currentUser['company_id']]);
        return;
    }

    // Convert object data to table format
    $subscriptionData = objectToSubscriptionData($objectData, $currentUser);

    // Debug logging
    error_log("Creating subscription with data: " . json_encode($subscriptionData));

    // Generate object ID
    $objectId = 'sub_' . uniqid();
    
    // Prepare SQL
    $sql = "INSERT INTO subscriptions (object_id, company_id, user_id, plan_id, plan_name, status, start_date, end_date, trial_end_date, price, billing_cycle, features, limits_data, payment_method, last_payment_date, next_payment_date, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    $stmt->bind_param("sssssssssdssssss", 
        $objectId,
        $subscriptionData['company_id'],
        $subscriptionData['user_id'],
        $subscriptionData['plan_id'],
        $subscriptionData['plan_name'],
        $subscriptionData['status'],
        $subscriptionData['start_date'],
        $subscriptionData['end_date'],
        $subscriptionData['trial_end_date'],
        $subscriptionData['price'],
        $subscriptionData['billing_cycle'],
        $subscriptionData['features'],
        $subscriptionData['limits_data'],
        $subscriptionData['payment_method'],
        $subscriptionData['last_payment_date'],
        $subscriptionData['next_payment_date']
    );
    
    if ($stmt->execute()) {
        // Return created object in standard format
        $createdObject = [
            'objectId' => $objectId,
            'objectType' => 'subscription',
            'objectData' => $objectData,
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ];

        error_log("Subscription created successfully: " . json_encode($createdObject));

        http_response_code(201);
        echo json_encode($createdObject);
    } else {
        $error = 'Failed to create subscription: ' . $stmt->error;
        error_log("Subscription creation failed: " . $error);
        http_response_code(500);
        echo json_encode(['error' => $error]);
    }
    
    $stmt->close();
}

/**
 * Get subscriptions list
 */
function getSubscriptions() {
    global $conn;
    
    // Get current user
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    // Get query parameters
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
    
    // Prepare SQL to get subscriptions for current company
    $sql = "SELECT * FROM subscriptions WHERE company_id = ? ORDER BY created_at DESC LIMIT ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    $stmt->bind_param("si", $currentUser['company_id'], $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $subscriptions = [];
    while ($row = $result->fetch_assoc()) {
        $subscriptions[] = subscriptionRowToObject($row);
    }
    
    echo json_encode([
        'items' => $subscriptions,
        'nextPageToken' => null
    ]);
    
    $stmt->close();
}

/**
 * Get single subscription
 */
function getSubscription($objectId) {
    global $conn;
    
    // Get current user
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    // Prepare SQL
    $sql = "SELECT * FROM subscriptions WHERE object_id = ? AND company_id = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    $stmt->bind_param("ss", $objectId, $currentUser['company_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Subscription not found']);
        return;
    }
    
    $row = $result->fetch_assoc();
    echo json_encode(subscriptionRowToObject($row));
    
    $stmt->close();
}

/**
 * Update subscription
 */
function updateSubscription($objectId, $objectData) {
    global $conn;
    
    // Get current user
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    // Convert object data to table format
    $subscriptionData = objectToSubscriptionData($objectData, $currentUser);
    
    // Prepare SQL
    $sql = "UPDATE subscriptions SET plan_id = ?, plan_name = ?, status = ?, start_date = ?, end_date = ?, trial_end_date = ?, price = ?, billing_cycle = ?, features = ?, limits_data = ?, payment_method = ?, last_payment_date = ?, next_payment_date = ?, updated_at = NOW() WHERE object_id = ? AND company_id = ?";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    $stmt->bind_param("ssssssdssssssss",
        $subscriptionData['plan_id'],
        $subscriptionData['plan_name'],
        $subscriptionData['status'],
        $subscriptionData['start_date'],
        $subscriptionData['end_date'],
        $subscriptionData['trial_end_date'],
        $subscriptionData['price'],
        $subscriptionData['billing_cycle'],
        $subscriptionData['features'],
        $subscriptionData['limits_data'],
        $subscriptionData['payment_method'],
        $subscriptionData['last_payment_date'],
        $subscriptionData['next_payment_date'],
        $objectId,
        $currentUser['company_id']
    );
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Subscription not found']);
            return;
        }
        
        // Return updated object
        $updatedObject = [
            'objectId' => $objectId,
            'objectType' => 'subscription',
            'objectData' => $objectData,
            'updatedAt' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($updatedObject);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update subscription: ' . $stmt->error]);
    }
    
    $stmt->close();
}

/**
 * Delete subscription
 */
function deleteSubscription($objectId) {
    global $conn;
    
    // Get current user
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    // Prepare SQL
    $sql = "DELETE FROM subscriptions WHERE object_id = ? AND company_id = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
        return;
    }
    
    $stmt->bind_param("ss", $objectId, $currentUser['company_id']);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Subscription not found']);
            return;
        }
        
        echo json_encode(['message' => 'Subscription deleted successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete subscription: ' . $stmt->error]);
    }
    
    $stmt->close();
}
?>
