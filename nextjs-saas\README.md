# Business SaaS - Complete Next.js Application

A comprehensive SaaS application built with Next.js 14, TypeScript, Prisma, and MySQL, featuring all the functionality from the original PHP application.

## 🚀 Features

### 🔐 Authentication & User Management
- Multi-tenant authentication with NextAuth.js
- Role-based access control (SUPER_ADMIN, ADMIN, MANAGER, USER, VIEWER)
- Email verification & password reset
- Social login (Google, GitHub)
- Session management with JWT
- User invitation system

### 🏢 Multi-Tenant Architecture
- Company/Organization management
- Data isolation between tenants
- User invitations and team management
- Company settings and branding

### 💳 Subscription & Billing System
- Multiple pricing plans with trial support
- Usage tracking and limits enforcement
- Stripe payment integration
- Invoice generation and billing history
- Trial management with extensions
- Subscription upgrades/downgrades

### 📋 Business Management (CRM)
- **Customers**: Full customer management with contact details
- **Leads**: Lead tracking with status, source, value, notes
- **Quotations**: Quote generation with items and pricing
- **Invoices**: Invoice creation with payment tracking
- **Contracts**: Contract management and tracking
- **Items**: Product/service catalog with pricing

### 🎯 Business Type Templates
- Pre-configured business types:
  - Jewellery Business
  - Retail Business  
  - Healthcare Services
  - Consulting Services
  - Manufacturing
  - Education Services
  - Restaurant & Food Service
  - Automotive Services
- Auto-configuration of modules, categories, and templates

### 📊 Dashboard & Analytics
- Real-time business metrics
- Revenue tracking and forecasting
- Activity feeds and notifications
- Usage statistics
- Custom reports

### ⚙️ Super Admin Features
- System-wide analytics
- User and company management
- Subscription monitoring
- Business type management
- Pricing plan configuration
- Payment gateway settings
- Policy page management
- Audit logs

## 🛠 Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Database**: MySQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: Radix UI + Tailwind CSS
- **State Management**: Zustand + React Query
- **Payments**: Stripe
- **Email**: Resend
- **Styling**: Tailwind CSS
- **Forms**: React Hook Form + Zod

## 📁 Project Structure

```
nextjs-saas/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Protected dashboard pages
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── dashboard/        # Dashboard components
│   └── providers.tsx     # App providers
├── lib/                  # Utility libraries
│   ├── auth.ts          # NextAuth configuration
│   ├── prisma.ts        # Prisma client
│   └── utils.ts         # Utility functions
├── prisma/              # Database schema and migrations
│   ├── schema.prisma    # Database schema
│   └── seed.ts          # Database seeding
├── types/               # TypeScript type definitions
├── hooks/               # Custom React hooks
├── store/               # Zustand stores
└── utils/               # Utility functions
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- MySQL database
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd nextjs-saas
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
DATABASE_URL="mysql://username:password@localhost:3306/saas_db"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
RESEND_API_KEY="re_..."
```

4. **Set up the database**
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed the database
npm run db:seed
```

5. **Start the development server**
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 📊 Database Schema

The application uses a comprehensive database schema with the following main entities:

- **Users**: User accounts with roles and permissions
- **Companies**: Multi-tenant organizations
- **Subscriptions**: Subscription and billing management
- **Customers**: Customer relationship management
- **Leads**: Lead tracking and conversion
- **Items**: Product/service catalog
- **Quotations**: Quote generation and management
- **Invoices**: Invoice creation and payment tracking
- **Contracts**: Contract management
- **Activities**: Activity tracking and logging
- **Tasks**: Task management
- **Notes**: Note-taking system

## 🔧 API Routes

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout

### Business Management
- `GET/POST /api/customers` - Customer management
- `GET/POST /api/leads` - Lead management
- `GET/POST /api/quotations` - Quotation management
- `GET/POST /api/invoices` - Invoice management
- `GET/POST /api/contracts` - Contract management
- `GET/POST /api/items` - Item catalog management

### Subscription Management
- `GET /api/subscriptions/current` - Current subscription
- `POST /api/subscriptions/upgrade` - Upgrade subscription
- `GET /api/subscriptions/usage` - Usage statistics

### Super Admin
- `GET /api/admin/users` - User management
- `GET /api/admin/companies` - Company management
- `GET /api/admin/subscriptions` - Subscription monitoring
- `GET/POST /api/admin/plans` - Pricing plan management

## 🎨 UI Components

The application uses a comprehensive set of UI components built with Radix UI and Tailwind CSS:

- **Forms**: Input, Select, Checkbox, Radio, Textarea
- **Navigation**: Sidebar, Header, Breadcrumbs
- **Data Display**: Tables, Cards, Lists, Charts
- **Feedback**: Toasts, Alerts, Loading states
- **Overlays**: Modals, Dropdowns, Tooltips

## 🔒 Security Features

- **Authentication**: Secure JWT-based authentication
- **Authorization**: Role-based access control
- **Data Isolation**: Multi-tenant data separation
- **Input Validation**: Zod schema validation
- **CSRF Protection**: Built-in CSRF protection
- **Rate Limiting**: API rate limiting
- **Audit Logging**: Comprehensive audit trails

## 📱 Responsive Design

The application is fully responsive and works seamlessly across:
- Desktop computers
- Tablets
- Mobile phones
- Different screen sizes and orientations

## 🧪 Testing

```bash
# Run type checking
npm run type-check

# Run linting
npm run lint

# Format code
npm run format
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically on push

### Docker

```bash
# Build Docker image
docker build -t nextjs-saas .

# Run container
docker run -p 3000:3000 nextjs-saas
```

## 📈 Performance

- **Server-Side Rendering**: Fast initial page loads
- **Static Generation**: Optimized static pages
- **Image Optimization**: Automatic image optimization
- **Code Splitting**: Automatic code splitting
- **Caching**: Intelligent caching strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or join our Discord community.

---

**Built with ❤️ using Next.js, TypeScript, and modern web technologies.**
