<?php
require_once '../db-config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication and super admin role
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    if ($currentUser['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetPlans();
            break;
        case 'POST':
            handleCreatePlan();
            break;
        case 'PUT':
            handleUpdatePlan();
            break;
        case 'DELETE':
            handleDeletePlan();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Plans API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

function handleGetPlans() {
    global $conn;
    
    try {
        $sql = "
            SELECT p.*, 
                   COUNT(s.id) as subscriber_count,
                   SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_subscribers
            FROM pricing_plans p
            LEFT JOIN subscriptions s ON p.id = s.plan_id
            GROUP BY p.id
            ORDER BY p.sort_order ASC, p.created_at DESC
        ";
        
        $result = $conn->query($sql);
        $plans = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // Parse JSON fields
                $row['features'] = $row['features'] ? json_decode($row['features'], true) : [];
                $row['limits_data'] = $row['limits_data'] ? json_decode($row['limits_data'], true) : [];
                $row['business_types'] = $row['business_types'] ? json_decode($row['business_types'], true) : [];
                
                $plans[] = $row;
            }
        }
        
        echo json_encode(['success' => true, 'data' => $plans]);
    } catch (Exception $e) {
        error_log("Error fetching plans: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to fetch plans']);
    }
}

function handleCreatePlan() {
    global $conn;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        $required = ['name', 'price_monthly', 'price_yearly'];
        foreach ($required as $field) {
            if (!isset($input[$field])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => "Field '$field' is required"]);
                return;
            }
        }
        
        // Sanitize inputs
        $name = sanitizeInput($input['name']);
        $description = sanitizeInput($input['description'] ?? '');
        $short_description = sanitizeInput($input['short_description'] ?? '');
        $price_monthly = floatval($input['price_monthly']);
        $price_yearly = floatval($input['price_yearly']);
        $trial_days = intval($input['trial_days'] ?? 14);
        $features = json_encode($input['features'] ?? []);
        $limits_data = json_encode($input['limits_data'] ?? []);
        $business_types = json_encode($input['business_types'] ?? []);
        $is_trial_available = isset($input['is_trial_available']) ? (bool)$input['is_trial_available'] : true;
        $is_visible = isset($input['is_visible']) ? (bool)$input['is_visible'] : true;
        $is_popular = isset($input['is_popular']) ? (bool)$input['is_popular'] : false;
        $sort_order = intval($input['sort_order'] ?? 0);
        $is_active = isset($input['is_active']) ? (bool)$input['is_active'] : true;
        
        $stmt = $conn->prepare("
            INSERT INTO pricing_plans (
                name, description, short_description, price_monthly, price_yearly, 
                trial_days, features, limits_data, business_types, is_trial_available, 
                is_visible, is_popular, sort_order, is_active, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->bind_param("sssddiissiiiii", 
            $name, $description, $short_description, $price_monthly, $price_yearly,
            $trial_days, $features, $limits_data, $business_types, $is_trial_available,
            $is_visible, $is_popular, $sort_order, $is_active
        );
        
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Plan created successfully',
                'data' => ['id' => $conn->insert_id]
            ]);
        } else {
            throw new Exception('Failed to create plan');
        }
        
    } catch (Exception $e) {
        error_log("Error creating plan: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create plan',
            'error' => $e->getMessage()
        ]);
    }
}

function handleUpdatePlan() {
    global $conn;
    
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $planId = trim($pathInfo, '/');
        
        if (empty($planId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Plan ID required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Build update query dynamically
        $updateFields = [];
        $params = [];
        $types = '';
        
        $allowedFields = [
            'name' => 's',
            'description' => 's',
            'short_description' => 's',
            'price_monthly' => 'd',
            'price_yearly' => 'd',
            'trial_days' => 'i',
            'features' => 's',
            'limits_data' => 's',
            'business_types' => 's',
            'is_trial_available' => 'i',
            'is_visible' => 'i',
            'is_popular' => 'i',
            'sort_order' => 'i',
            'is_active' => 'i'
        ];
        
        foreach ($allowedFields as $field => $type) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                
                if (in_array($field, ['features', 'limits_data', 'business_types'])) {
                    $params[] = json_encode($input[$field]);
                } elseif (in_array($field, ['is_trial_available', 'is_visible', 'is_popular', 'is_active'])) {
                    $params[] = (bool)$input[$field] ? 1 : 0;
                } else {
                    $params[] = $input[$field];
                }
                $types .= $type;
            }
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
            return;
        }
        
        $params[] = $planId;
        $types .= 'i';
        
        $sql = "UPDATE pricing_plans SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Plan updated successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Plan not found']);
            }
        } else {
            throw new Exception('Failed to update plan');
        }
        
    } catch (Exception $e) {
        error_log("Error updating plan: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update plan',
            'error' => $e->getMessage()
        ]);
    }
}

function handleDeletePlan() {
    global $conn;
    
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $planId = trim($pathInfo, '/');
        
        if (empty($planId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Plan ID required']);
            return;
        }
        
        // Check if plan has active subscriptions
        $checkStmt = $conn->prepare("SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = ? AND status = 'active'");
        $checkStmt->bind_param("i", $planId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $checkRow = $checkResult->fetch_assoc();
        
        if ($checkRow['count'] > 0) {
            http_response_code(400);
            echo json_encode([
                'success' => false, 
                'message' => 'Cannot delete plan with active subscriptions. Deactivate the plan instead.'
            ]);
            return;
        }
        
        // Soft delete - set is_active to false
        $stmt = $conn->prepare("UPDATE pricing_plans SET is_active = 0 WHERE id = ?");
        $stmt->bind_param("i", $planId);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Plan deactivated successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Plan not found']);
            }
        } else {
            throw new Exception('Failed to deactivate plan');
        }
        
    } catch (Exception $e) {
        error_log("Error deactivating plan: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to deactivate plan',
            'error' => $e->getMessage()
        ]);
    }
}
?>