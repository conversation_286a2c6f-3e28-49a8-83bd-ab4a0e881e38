function UserManagement() {
    try {
        const { currentCompany } = useAuth();
        const [users, setUsers] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [showInviteModal, setShowInviteModal] = React.useState(false);
        const [inviteForm, setInviteForm] = React.useState({
            email: '',
            role: 'member',
            permissions: []
        });
        const [errors, setErrors] = React.useState({});

        React.useEffect(() => {
            fetchUsers();
        }, [currentCompany]);

        const fetchUsers = async () => {
            try {
                setLoading(true);
                const response = await trickleListObjects(`company_users:${currentCompany.objectId}`, 100, true);
                setUsers(response.items);
            } catch (error) {
                console.error('Error fetching users:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleInviteUser = async (e) => {
            e.preventDefault();
            if (!validateInviteForm()) return;

            try {
                setLoading(true);
                await trickleCreateObject(`company_users:${currentCompany.objectId}`, {
                    ...inviteForm,
                    status: 'invited',
                    invitedAt: new Date().toISOString()
                });
                setShowInviteModal(false);
                setInviteForm({
                    email: '',
                    role: 'member',
                    permissions: []
                });
                fetchUsers();
            } catch (error) {
                console.error('Error inviting user:', error);
                setErrors({ submit: 'Failed to invite user' });
            } finally {
                setLoading(false);
            }
        };

        const handleRemoveUser = async (userId) => {
            try {
                await trickleDeleteObject(`company_users:${currentCompany.objectId}`, userId);
                fetchUsers();
            } catch (error) {
                console.error('Error removing user:', error);
            }
        };

        const handleUpdateUserRole = async (userId, role) => {
            try {
                await trickleUpdateObject(`company_users:${currentCompany.objectId}`, userId, { role });
                fetchUsers();
            } catch (error) {
                console.error('Error updating user role:', error);
            }
        };

        const validateInviteForm = () => {
            const newErrors = {};
            if (!inviteForm.email) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(inviteForm.email)) {
                newErrors.email = 'Invalid email format';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const roleOptions = [
            { value: 'owner', label: 'Owner', description: 'Full access to all features and settings' },
            { value: 'admin', label: 'Admin', description: 'Can manage users and most settings' },
            { value: 'member', label: 'Member', description: 'Basic access to features' },
            { value: 'viewer', label: 'Viewer', description: 'View-only access' }
        ];

        return (
            <div data-name="user-management" className="space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h2 className="text-xl font-bold">User Management</h2>
                        <p className="text-gray-500">Manage team members and their roles</p>
                    </div>
                    <Button
                        onClick={() => setShowInviteModal(true)}
                        icon="fas fa-user-plus"
                    >
                        Invite User
                    </Button>
                </div>

                {loading ? (
                    <div className="flex justify-center items-center h-32">
                        <i className="fas fa-spinner fa-spin text-blue-500"></i>
                    </div>
                ) : (
                    <div className="bg-white shadow rounded-lg overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        User
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Role
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Last Active
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {users.map((user) => (
                                    <tr key={user.objectId}>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <div className="flex-shrink-0 h-10 w-10">
                                                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                        <span className="text-gray-500 font-medium">
                                                            {user.objectData.name && user.objectData.name[0] ? user.objectData.name[0] : user.objectData.email[0].toUpperCase()}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="ml-4">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {user.objectData.name || user.objectData.email}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {user.objectData.email}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <select
                                                value={user.objectData.role}
                                                onChange={(e) => handleUpdateUserRole(user.objectId, e.target.value)}
                                                className="text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                                disabled={user.objectData.role === 'owner'}
                                            >
                                                {roleOptions.map(role => (
                                                    <option key={role.value} value={role.value}>
                                                        {role.label}
                                                    </option>
                                                ))}
                                            </select>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                user.objectData.status === 'active' ? 'bg-green-100 text-green-800' :
                                                user.objectData.status === 'invited' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                                {user.objectData.status.charAt(0).toUpperCase() + user.objectData.status.slice(1)}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {user.objectData.lastActiveAt ? formatDateTime(user.objectData.lastActiveAt) : 'Never'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            {user.objectData.role !== 'owner' && (
                                                <button
                                                    onClick={() => handleRemoveUser(user.objectId)}
                                                    className="text-red-600 hover:text-red-900"
                                                >
                                                    Remove
                                                </button>
                                            )}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}

                {showInviteModal && (
                    <Modal
                        isOpen={showInviteModal}
                        onClose={() => setShowInviteModal(false)}
                        title="Invite User"
                    >
                        <form onSubmit={handleInviteUser} className="space-y-6 p-6">
                            <Input
                                label="Email Address"
                                name="email"
                                type="email"
                                value={inviteForm.email}
                                onChange={(e) => setInviteForm(prev => ({ ...prev, email: e.target.value }))}
                                error={errors.email}
                                required
                            />

                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Role
                                </label>
                                <select
                                    name="role"
                                    value={inviteForm.role}
                                    onChange={(e) => setInviteForm(prev => ({ ...prev, role: e.target.value }))}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    {roleOptions.map(role => (
                                        <option key={role.value} value={role.value}>
                                            {role.label}
                                        </option>
                                    ))}
                                </select>
                                <p className="mt-1 text-sm text-gray-500">
                                    {roleOptions.find(r => r.value === inviteForm.role) && roleOptions.find(r => r.value === inviteForm.role).description ? roleOptions.find(r => r.value === inviteForm.role).description : ''}
                                </p>
                            </div>

                            {errors.submit && (
                                <div className="text-red-600 text-sm">
                                    {errors.submit}
                                </div>
                            )}

                            <div className="flex justify-end space-x-4">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={() => setShowInviteModal(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    loading={loading}
                                >
                                    Send Invitation
                                </Button>
                            </div>
                        </form>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('UserManagement component error:', error);
        reportError(error);
        return null;
    }
}
