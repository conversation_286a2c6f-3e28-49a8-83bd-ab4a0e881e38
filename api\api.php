<?php
// Temporarily enable error display for debugging settings issue
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Custom error handler to return JSON errors (but also log them)
function handleError($errno, $errstr, $errfile, $errline) {
    $error = [
        'error' => 'Internal Server Error',
        'message' => $errstr,
        'file' => basename($errfile),
        'line' => $errline,
        'type' => $errno,
        'debug' => [
            'full_file' => $errfile,
            'error_type' => $errno
        ]
    ];
    
    // Log the error
    error_log("API Error: $errstr in $errfile on line $errline");
    
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode($error, JSON_PRETTY_PRINT);
    exit;
}

// Custom exception handler
function handleException($exception) {
    $error = [
        'error' => 'Internal Server Error',
        'message' => $exception->getMessage(),
        'file' => basename($exception->getFile()),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];
    
    // Log the exception
    error_log("API Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode($error, JSON_PRETTY_PRINT);
    exit;
}

// Set error and exception handlers
set_error_handler('handleError');
set_exception_handler('handleException');

// Include security middleware first
require_once 'middleware/security.php';

// Apply security middleware
SecurityMiddleware::init();

// Include required files
require_once 'utils/ErrorHandler.php';
require_once 'db-config.php';
require_once 'handlers/field-handler.php';
require_once 'handlers/object-handler.php';
require_once 'handlers/items-handler.php';
require_once 'handlers/crud-handler.php';
require_once 'handlers/auth-handler.php';

// Initialize error handler
ErrorHandler::init();
require_once 'handlers/super-admin-handler.php';
require_once 'handlers/settings-handler.php';
require_once 'handlers/email-handler.php';

// Disable error display to prevent HTML in JSON responses
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set headers for JSON response
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Debug logging
error_log("API Request: " . $method . " " . $_SERVER['REQUEST_URI']);
error_log("PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? 'not set'));

// Get request path - try multiple methods
$endpoint = null;
$objectId = null;

// Method 1: Check query parameters first (most reliable)
if (isset($_GET['endpoint'])) {
    $endpoint = $_GET['endpoint'];
    $objectId = $_GET['action'] ?? $_GET['objectId'] ?? null;
    error_log("Using query params - Endpoint: $endpoint, ObjectId: $objectId");
}
// Method 2: Try PATH_INFO
elseif (!empty($_SERVER['PATH_INFO'])) {
    $uri = explode('/', trim($_SERVER['PATH_INFO'], '/'));
    $endpoint = isset($uri[0]) ? $uri[0] : null;
    $objectId = isset($uri[1]) ? $uri[1] : null;
    error_log("Using PATH_INFO - Endpoint: $endpoint, ObjectId: $objectId");
}
// Method 3: Fallback to URI parsing
else {
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $uri = explode('/', $uri);

    // Find the index where 'api.php' appears and extract from there
    $apiIndex = array_search('api.php', $uri);
    if ($apiIndex !== false) {
        $endpoint = isset($uri[$apiIndex + 1]) ? $uri[$apiIndex + 1] : null;
        $objectId = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : null;
    }
    error_log("Using URI parsing - Endpoint: $endpoint, ObjectId: $objectId");
}

// Handle authentication routes
if ($endpoint === 'auth') {
    $action = $objectId ?: 'login';
    error_log("Auth endpoint - Action: " . $action);

    // Try enhanced auth handler first for login and verify actions
    if (file_exists(__DIR__ . '/enhanced-auth-handler.php') && ($action === 'login' || $action === 'verify')) {
        $_GET['action'] = $action;
        include_once __DIR__ . '/enhanced-auth-handler.php';
    } else {
        // Use the regular auth handler
        handleAuth($action);
    }
    exit;
}

// Handle subscription routes (both 'subscription' and 'subscription-management')
if ($endpoint === 'subscription' || $endpoint === 'subscription-management') {
    $action = $objectId ?: 'current';
    error_log("Subscription endpoint - Action: " . $action);

    // For simple 'subscription' endpoint, handle basic CRUD operations
    if ($endpoint === 'subscription') {
        // Include the subscription handler for basic operations
        include_once __DIR__ . '/handlers/subscription-handler.php';

        switch ($_SERVER['REQUEST_METHOD']) {
            case 'GET':
                if ($action === 'current' || empty($action)) {
                    // Get current subscription or list subscriptions
                    getSubscriptions();
                } else {
                    // Get specific subscription
                    getSubscription($action);
                }
                break;
            case 'POST':
                createSubscription();
                break;
            case 'PUT':
                updateSubscription($action);
                break;
            case 'DELETE':
                deleteSubscription($action);
                break;
            default:
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
        }
    } else {
        // For 'subscription-management', use the enhanced handler
        if (file_exists(__DIR__ . '/enhanced-subscription-handler.php')) {
            $_GET['action'] = $action;
            include_once __DIR__ . '/enhanced-subscription-handler.php';
        } else {
            // Fallback to regular subscription management
            include_once __DIR__ . '/subscription-management.php';
        }
    }
    exit;
}

// Handle customer routes
if ($endpoint === 'customer') {
    error_log("Customer endpoint accessed");
    // For now, return empty array - can be implemented later
    echo json_encode(['items' => [], 'nextPageToken' => null]);
    exit;
}

// Handle quotation routes
if ($endpoint === 'quotation') {
    error_log("Quotation endpoint accessed");
    // For now, return empty array - can be implemented later
    echo json_encode(['items' => [], 'nextPageToken' => null]);
    exit;
}

// Handle invoice routes
if ($endpoint === 'invoice') {
    error_log("Invoice endpoint accessed");
    // For now, return empty array - can be implemented later
    echo json_encode(['items' => [], 'nextPageToken' => null]);
    exit;
}

// Handle contract routes
if ($endpoint === 'contract') {
    error_log("Contract endpoint accessed");
    // For now, return empty array - can be implemented later
    echo json_encode(['items' => [], 'nextPageToken' => null]);
    exit;
}

// Handle business types routes
if ($endpoint === 'business-types') {
    error_log("Business Types endpoint accessed");
    include_once __DIR__ . '/business-types.php';
    exit;
}

// Handle pricing plans routes
if ($endpoint === 'pricing-plans') {
    error_log("Pricing Plans endpoint accessed");
    include_once __DIR__ . '/pricing-plans.php';
    exit;
}

// Handle dashboard routes
if ($endpoint === 'dashboard') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'stats';
    error_log("Dashboard endpoint - Action: " . $action);
    
    if ($action === 'stats') {
        include_once __DIR__ . '/dashboard-stats.php';
    } elseif ($action === 'recent-activity') {
        include_once __DIR__ . '/recent-activity.php';
    }
    exit;
}

// Handle business template routes
if ($endpoint === 'business-template') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'apply';
    error_log("Business Template endpoint - Action: " . $action);
    
    if ($action === 'apply') {
        include_once __DIR__ . '/apply-business-template.php';
    }
    exit;
}

// Handle onboarding routes
if ($endpoint === 'onboarding') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'complete';
    error_log("Onboarding endpoint - Action: " . $action);
    
    if ($action === 'complete') {
        include_once __DIR__ . '/onboarding-complete.php';
    }
    exit;
}

// Handle super admin routes
if ($endpoint === 'super-admin') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'dashboard';
    $subAction = isset($uri[$apiIndex + 3]) ? $uri[$apiIndex + 3] : null;
    error_log("Super Admin - Action: " . $action . ", SubAction: " . $subAction);
    handleSuperAdmin($action, $subAction);
    exit;
}

// Handle settings routes
if ($endpoint === 'settings') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : null;

    // Handle email-specific settings routes
    if ($action === 'test-email') {
        handleEmail('test-email');
        exit;
    }

    handleSettings($action, $objectId);
    exit;
}

// Handle error logging endpoint
if ($endpoint === 'errors' && isset($uri[$apiIndex + 2]) && $uri[$apiIndex + 2] === 'log') {
    if ($method === 'POST') {
        // Handle frontend error logging
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input) {
            $errorMessage = isset($input['message']) ? $input['message'] : 'Unknown error';
            $errorFile = isset($input['filename']) ? $input['filename'] : 'Unknown file';
            $errorLine = isset($input['lineno']) ? $input['lineno'] : 'Unknown line';
            $errorStack = isset($input['stack']) ? $input['stack'] : 'No stack trace';
            
            // Log the frontend error
            error_log("Frontend Error: $errorMessage in $errorFile on line $errorLine");
            error_log("Stack trace: $errorStack");
            
            // Return success response
            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Error logged successfully']);
            exit;
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid error data']);
            exit;
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed for error logging']);
        exit;
    }
}

// CRUD Functions
function createObject($endpoint) {
    global $conn;
    
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        // Provide default test data if no input
        $input = [
            'name' => 'Test Lead',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'company_id' => 'super_admin_001',
            'user_id' => 'super_admin_001',
            'title' => 'Test Task',
            'description' => 'Test Description',
            'priority' => 'medium',
            'content' => 'Test Note Content'
        ];
    }
    
    try {
        switch ($endpoint) {
            case 'lead':
                $objectId = 'lead_' . time() . '_' . rand(100, 999);
                $stmt = $conn->prepare("INSERT INTO leads (object_id, name, email, phone, company_id, user_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'new', NOW())");
                $stmt->bind_param("ssssss", $objectId, $input['name'], $input['email'], $input['phone'], $input['company_id'], $input['user_id']);
                break;
                
            case 'task':
                $objectId = 'task_' . time() . '_' . rand(100, 999);
                // Get the latest lead for this company as default
                $leadResult = $conn->query("SELECT object_id FROM leads WHERE company_id = '{$input['company_id']}' ORDER BY created_at DESC LIMIT 1");
                $leadId = $input['lead_id'] ?? ($leadResult->num_rows > 0 ? $leadResult->fetch_assoc()['object_id'] : 'general_' . time());
                $stmt = $conn->prepare("INSERT INTO tasks (object_id, lead_id, title, description, priority, company_id, user_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())");
                $stmt->bind_param("sssssss", $objectId, $leadId, $input['title'], $input['description'], $input['priority'], $input['company_id'], $input['user_id']);
                break;
                
            case 'note':
                $objectId = 'note_' . time() . '_' . rand(100, 999);
                // Get the latest lead for this company as default
                $leadResult = $conn->query("SELECT object_id FROM leads WHERE company_id = '{$input['company_id']}' ORDER BY created_at DESC LIMIT 1");
                $leadId = $input['lead_id'] ?? ($leadResult->num_rows > 0 ? $leadResult->fetch_assoc()['object_id'] : 'general_' . time());
                $stmt = $conn->prepare("INSERT INTO notes (object_id, lead_id, content, company_id, user_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
                $stmt->bind_param("sssss", $objectId, $leadId, $input['content'], $input['company_id'], $input['user_id']);
                break;
                
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Unsupported endpoint']);
                return;
        }
        
        if ($stmt->execute()) {
            $id = $conn->insert_id;
            echo json_encode(['success' => true, 'id' => $id, 'message' => ucfirst($endpoint) . ' created successfully']);
        } else {
            throw new Exception($stmt->error);
        }
        
    } catch (Exception $e) {
        error_log("Create $endpoint error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create ' . $endpoint, 'details' => $e->getMessage()]);
    }
}

function listObjects($endpoint) {
    global $conn;
    
    header('Content-Type: application/json');
    
    try {
        switch ($endpoint) {
            case 'lead':
                $sql = "SELECT * FROM leads ORDER BY created_at DESC LIMIT 50";
                break;
            case 'task':
                $sql = "SELECT * FROM tasks ORDER BY created_at DESC LIMIT 50";
                break;
            case 'note':
                $sql = "SELECT * FROM notes ORDER BY created_at DESC LIMIT 50";
                break;
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Unsupported endpoint']);
                return;
        }
        
        $result = $conn->query($sql);
        $data = [];
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
        }
        
        echo json_encode(['success' => true, 'data' => $data]);
        
    } catch (Exception $e) {
        error_log("List $endpoint error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to list ' . $endpoint]);
    }
}

function getObject($endpoint, $objectId) {
    global $conn;
    
    header('Content-Type: application/json');
    
    try {
        switch ($endpoint) {
            case 'lead':
                $stmt = $conn->prepare("SELECT * FROM leads WHERE id = ?");
                break;
            case 'task':
                $stmt = $conn->prepare("SELECT * FROM tasks WHERE id = ?");
                break;
            case 'note':
                $stmt = $conn->prepare("SELECT * FROM notes WHERE id = ?");
                break;
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Unsupported endpoint']);
                return;
        }
        
        $stmt->bind_param("i", $objectId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            echo json_encode(['success' => true, 'data' => $row]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => ucfirst($endpoint) . ' not found']);
        }
        
    } catch (Exception $e) {
        error_log("Get $endpoint error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to get ' . $endpoint]);
    }
}

function updateObject($endpoint, $objectId) {
    global $conn;
    
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
        return;
    }
    
    try {
        switch ($endpoint) {
            case 'lead':
                $stmt = $conn->prepare("UPDATE leads SET name = ?, email = ?, phone = ?, status = ? WHERE id = ?");
                $stmt->bind_param("ssssi", $input['name'], $input['email'], $input['phone'], $input['status'], $objectId);
                break;
            case 'task':
                $stmt = $conn->prepare("UPDATE tasks SET title = ?, description = ?, priority = ?, status = ? WHERE id = ?");
                $stmt->bind_param("ssssi", $input['title'], $input['description'], $input['priority'], $input['status'], $objectId);
                break;
            case 'note':
                $stmt = $conn->prepare("UPDATE notes SET content = ? WHERE id = ?");
                $stmt->bind_param("si", $input['content'], $objectId);
                break;
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Unsupported endpoint']);
                return;
        }
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => ucfirst($endpoint) . ' updated successfully']);
        } else {
            throw new Exception($stmt->error);
        }
        
    } catch (Exception $e) {
        error_log("Update $endpoint error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to update ' . $endpoint]);
    }
}

function deleteObject($endpoint, $objectId) {
    global $conn;
    
    header('Content-Type: application/json');
    
    try {
        switch ($endpoint) {
            case 'lead':
                $stmt = $conn->prepare("DELETE FROM leads WHERE id = ?");
                break;
            case 'task':
                $stmt = $conn->prepare("DELETE FROM tasks WHERE id = ?");
                break;
            case 'note':
                $stmt = $conn->prepare("DELETE FROM notes WHERE id = ?");
                break;
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Unsupported endpoint']);
                return;
        }
        
        $stmt->bind_param("i", $objectId);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => ucfirst($endpoint) . ' deleted successfully']);
        } else {
            throw new Exception($stmt->error);
        }
        
    } catch (Exception $e) {
        error_log("Delete $endpoint error: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to delete ' . $endpoint]);
    }
}

// Handle API request based on method
error_log("Handling " . $method . " request for endpoint: " . $endpoint);

switch ($method) {
    case 'GET':
        if ($objectId) {
            // Get single object
            error_log("Getting single object: " . $endpoint . "/" . $objectId);
            getObject($endpoint, $objectId);
        } else {
            // List objects
            error_log("Listing objects: " . $endpoint);
            listObjects($endpoint);
        }
        break;
    case 'POST':
        // Create object
        error_log("Creating object: " . $endpoint);
        createObject($endpoint);
        break;
    case 'PUT':
        // Update object
        error_log("Updating object: " . $endpoint . "/" . $objectId);
        updateObject($endpoint, $objectId);
        break;
    case 'DELETE':
        // Delete object
        error_log("Deleting object: " . $endpoint . "/" . $objectId);
        deleteObject($endpoint, $objectId);
        break;
    default:
        // Method not allowed
        error_log("Method not allowed: " . $method);
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>
