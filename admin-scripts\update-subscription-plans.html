<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Subscription Plans - Admin Tool</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        function PlanUpdater() {
            const [status, setStatus] = useState('ready');
            const [logs, setLogs] = useState([]);
            const [authToken, setAuthToken] = useState('');

            const addLog = (message, type = 'info') => {
                const timestamp = new Date().toLocaleTimeString();
                setLogs(prev => [...prev, { message, type, timestamp }]);
            };

            const updatePlans = async () => {
                if (!authToken.trim()) {
                    addLog('Please enter your admin auth token', 'error');
                    return;
                }

                setStatus('updating');
                setLogs([]);
                addLog('Starting plan update process...', 'info');

                try {
                    // Define the new plan structure
                    const newPlans = [
                        {
                            id: 'trial',
                            name: 'Trial Plan',
                            description: 'Free trial for 5 days with full access to all features',
                            short_description: 'Free for 5 Days',
                            price_monthly: 0,
                            price_yearly: 0,
                            trial_days: 5,
                            features: [
                                'Customer Management',
                                'Invoice Generation',
                                'Quotation Management',
                                'Contract Management',
                                'Leads Management',
                                'Inventory Management',
                                'Email Notifications',
                                'Custom Branding'
                            ],
                            limits_data: {
                                customers: 50,
                                invoices: 25,
                                quotations: 25,
                                contracts: 10,
                                leads: 50,
                                inventory_items: 100,
                                storage_mb: 500
                            },
                            business_types: [],
                            is_trial_available: true,
                            is_visible: true,
                            is_popular: false,
                            sort_order: 1
                        },
                        {
                            id: 'business',
                            name: 'Business Plan',
                            description: 'Complete business management solution with unlimited access to all features',
                            short_description: 'Full Business Solution',
                            price_monthly: 500,
                            price_yearly: 5000,
                            trial_days: 5,
                            features: [
                                'Customer Management',
                                'Invoice Generation',
                                'Quotation Management',
                                'Contract Management',
                                'Leads Management',
                                'Inventory Management',
                                'Email Notifications',
                                'Custom Branding',
                                'Priority Support',
                                'Advanced Analytics',
                                'Data Export',
                                'API Access'
                            ],
                            limits_data: {
                                customers: -1,
                                invoices: -1,
                                quotations: -1,
                                contracts: -1,
                                leads: -1,
                                inventory_items: -1,
                                storage_mb: -1
                            },
                            business_types: [],
                            is_trial_available: true,
                            is_visible: true,
                            is_popular: true,
                            sort_order: 2
                        }
                    ];

                    // Update each plan
                    for (const plan of newPlans) {
                        addLog(`Updating plan: ${plan.name}`, 'info');
                        
                        const response = await fetch('/api/super-admin/plans.php', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${authToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                action: 'create_or_update',
                                plan: plan
                            })
                        });

                        const result = await response.json();
                        
                        if (result.success) {
                            addLog(`✅ Successfully updated ${plan.name}`, 'success');
                        } else {
                            addLog(`❌ Failed to update ${plan.name}: ${result.message}`, 'error');
                        }
                    }

                    addLog('🎉 Plan update completed!', 'success');
                    setStatus('completed');

                } catch (error) {
                    addLog(`Error updating plans: ${error.message}`, 'error');
                    setStatus('error');
                }
            };

            const verifyPlans = async () => {
                if (!authToken.trim()) {
                    addLog('Please enter your admin auth token', 'error');
                    return;
                }

                try {
                    addLog('Fetching current plans...', 'info');
                    
                    const response = await fetch('/api/super-admin/plans.php', {
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        addLog('📋 Current Plans:', 'info');
                        result.plans.forEach(plan => {
                            addLog(`- ${plan.name}: ₹${plan.price_monthly}/month, ₹${plan.price_yearly}/year`, 'info');
                            addLog(`  Features: ${plan.features.length} features, Trial Days: ${plan.trial_days}`, 'info');
                        });
                    } else {
                        addLog(`Failed to fetch plans: ${result.message}`, 'error');
                    }
                } catch (error) {
                    addLog(`Error verifying plans: ${error.message}`, 'error');
                }
            };

            return (
                <div className="min-h-screen bg-gray-100 py-8">
                    <div className="max-w-4xl mx-auto px-4">
                        <div className="bg-white rounded-lg shadow-lg p-6">
                            <h1 className="text-2xl font-bold text-gray-900 mb-6">
                                <i className="fas fa-cog mr-2"></i>
                                Subscription Plans Updater
                            </h1>

                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Admin Auth Token
                                </label>
                                <input
                                    type="password"
                                    value={authToken}
                                    onChange={(e) => setAuthToken(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter your admin authentication token"
                                />
                            </div>

                            <div className="flex space-x-4 mb-6">
                                <button
                                    onClick={updatePlans}
                                    disabled={status === 'updating'}
                                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {status === 'updating' ? (
                                        <>
                                            <i className="fas fa-spinner fa-spin mr-2"></i>
                                            Updating Plans...
                                        </>
                                    ) : (
                                        <>
                                            <i className="fas fa-upload mr-2"></i>
                                            Update Plans
                                        </>
                                    )}
                                </button>

                                <button
                                    onClick={verifyPlans}
                                    className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                                >
                                    <i className="fas fa-check mr-2"></i>
                                    Verify Plans
                                </button>
                            </div>

                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">
                                    <i className="fas fa-list mr-2"></i>
                                    Update Log
                                </h3>
                                
                                <div className="space-y-2 max-h-96 overflow-y-auto">
                                    {logs.length === 0 ? (
                                        <p className="text-gray-500 italic">No logs yet. Click "Update Plans" to start.</p>
                                    ) : (
                                        logs.map((log, index) => (
                                            <div
                                                key={index}
                                                className={`p-2 rounded text-sm ${
                                                    log.type === 'error' 
                                                        ? 'bg-red-100 text-red-800' 
                                                        : log.type === 'success'
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-blue-100 text-blue-800'
                                                }`}
                                            >
                                                <span className="text-xs text-gray-500 mr-2">{log.timestamp}</span>
                                                {log.message}
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>

                            <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
                                <div className="flex items-start">
                                    <i className="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 className="text-sm font-medium text-yellow-900">Important Notes</h4>
                                        <ul className="text-sm text-yellow-700 mt-1 list-disc list-inside">
                                            <li>This will update the subscription plans to the new structure</li>
                                            <li>Trial Plan: Free for 5 Days</li>
                                            <li>Business Plan: ₹500 Monthly / ₹5000 Yearly</li>
                                            <li>Make sure to verify the changes after updating</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<PlanUpdater />);
    </script>
</body>
</html>