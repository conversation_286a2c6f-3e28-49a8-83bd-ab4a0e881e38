// About Us Page Component
function AboutUs() {
    React.useEffect(() => {
        document.title = 'About Us - Bizma';
    }, []);

    const stats = [
        { number: '1000+', label: 'Happy Customers' },
        { number: '50K+', label: 'Invoices Generated' },
        { number: '99.9%', label: 'Uptime' },
        { number: '24/7', label: 'Support' }
    ];

    const team = [
        {
            name: '<PERSON><PERSON>',
            role: 'CEO & Founder',
            image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
            description: 'Visionary leader with 15+ years in business technology.'
        },
        {
            name: '<PERSON><PERSON>',
            role: 'CTO',
            image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
            description: 'Tech expert passionate about building scalable solutions.'
        },
        {
            name: '<PERSON><PERSON>',
            role: 'Head of Product',
            image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
            description: 'Product strategist focused on user experience and innovation.'
        }
    ];

    const values = [
        {
            icon: '🎯',
            title: 'Customer First',
            description: 'Every decision we make is centered around delivering value to our customers and helping them succeed.'
        },
        {
            icon: '🚀',
            title: 'Innovation',
            description: 'We continuously innovate to provide cutting-edge solutions that keep our customers ahead of the curve.'
        },
        {
            icon: '🤝',
            title: 'Integrity',
            description: 'We operate with transparency, honesty, and ethical practices in everything we do.'
        },
        {
            icon: '💪',
            title: 'Excellence',
            description: 'We strive for excellence in our products, services, and customer relationships.'
        }
    ];

    return (
        <div className="min-h-screen bg-white">
            {/* Header */}
            <WebsiteHeader currentPage="about" />

            {/* Hero Section */}
            <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                        About Bizma
                    </h1>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                        We're on a mission to empower businesses with the tools they need to thrive in today's competitive landscape. 
                        Our comprehensive platform simplifies business management so you can focus on what matters most - growing your business.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <a 
                            href="contact.html" 
                            className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                        >
                            Get in Touch
                        </a>
                        <a 
                            href="register.html" 
                            className="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg hover:bg-blue-600 hover:text-white transition-colors font-semibold"
                        >
                            Start Free Trial
                        </a>
                    </div>
                </div>
            </section>

            {/* Stats Section */}
            <section className="py-16 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                        {stats.map((stat, index) => (
                            <div key={index} className="text-center">
                                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                                    {stat.number}
                                </div>
                                <div className="text-gray-600 font-medium">
                                    {stat.label}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Our Story Section */}
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                                Our Story
                            </h2>
                            <div className="space-y-4 text-gray-600 leading-relaxed">
                                <p>
                                    Founded in 2020, Bizma was born from the frustration of managing multiple business tools 
                                    that didn't work well together. Our founders, experienced entrepreneurs themselves, 
                                    understood the pain of juggling different platforms for customer management, invoicing, 
                                    and business analytics.
                                </p>
                                <p>
                                    We set out to create a unified platform that would bring all essential business functions 
                                    under one roof. Today, Bizma serves over 1,000 businesses across India, helping them 
                                    streamline operations and accelerate growth.
                                </p>
                                <p>
                                    Our commitment to innovation and customer success drives everything we do. We're not just 
                                    building software; we're building the foundation for business success in the digital age.
                                </p>
                            </div>
                        </div>
                        <div className="relative">
                            <img 
                                src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=600&h=400&fit=crop" 
                                alt="Team collaboration" 
                                className="rounded-lg shadow-xl"
                            />
                            <div className="absolute inset-0 bg-blue-600 opacity-10 rounded-lg"></div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Our Values Section */}
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Our Values
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            These core values guide our decisions and shape our culture as we work to empower businesses worldwide.
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {values.map((value, index) => (
                            <div key={index} className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                                <div className="text-4xl mb-4">{value.icon}</div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-3">{value.title}</h3>
                                <p className="text-gray-600">{value.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Team Section */}
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Meet Our Team
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            The passionate individuals behind Bizma who are dedicated to your business success.
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {team.map((member, index) => (
                            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                                <img 
                                    src={member.image} 
                                    alt={member.name}
                                    className="w-full h-64 object-cover"
                                />
                                <div className="p-6">
                                    <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                                    <p className="text-blue-600 font-medium mb-3">{member.role}</p>
                                    <p className="text-gray-600">{member.description}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-blue-600">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                        Ready to Transform Your Business?
                    </h2>
                    <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                        Join thousands of businesses that trust Bizma to manage their operations and drive growth.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <a 
                            href="register.html" 
                            className="bg-white text-blue-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors font-semibold"
                        >
                            Start Free Trial
                        </a>
                        <a 
                            href="contact.html" 
                            className="border border-white text-white px-8 py-3 rounded-lg hover:bg-white hover:text-blue-600 transition-colors font-semibold"
                        >
                            Contact Sales
                        </a>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <WebsiteFooter currentPage="about" />
        </div>
    );
}

// Make component globally available
window.AboutUs = AboutUs;
