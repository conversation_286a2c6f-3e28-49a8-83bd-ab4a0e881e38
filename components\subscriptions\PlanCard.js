function PlanCard({ plan, isCurrentPlan, onUpgrade }) {
    try {
        return (
            <div
                data-name={`plan-card-${plan.id}`}
                className={`bg-white rounded-lg shadow-lg overflow-hidden border-2 ${
                    plan.popular ? 'border-blue-500 transform scale-105' : 'border-transparent'
                }`}
            >
                {plan.popular && (
                    <div className="bg-blue-500 text-white text-center text-sm py-1">
                        Most Popular
                    </div>
                )}

                <div className="p-6">
                    <div className="flex items-center justify-between">
                        <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                        {plan.popular && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Recommended
                            </span>
                        )}
                    </div>

                    <div className="mt-4">
                        <div className="flex items-baseline">
                            <span className="text-4xl font-extrabold">₹{plan.price}</span>
                            <span className="text-gray-500 ml-1">/{plan.billingPeriod}</span>
                        </div>
                        <p className="mt-2 text-sm text-gray-500">
                            {plan.maxLeads === Infinity ? 'Unlimited' : `Up to ${plan.maxLeads}`} leads
                        </p>
                    </div>

                    <ul className="mt-6 space-y-4">
                        {plan.features.map((feature, index) => (
                            <li key={index} className="flex items-start">
                                <i className="fas fa-check text-green-500 mt-1 mr-2"></i>
                                <span className="text-gray-600">{feature}</span>
                            </li>
                        ))}
                    </ul>

                    <div className="mt-8">
                        <Button
                            onClick={() => onUpgrade(plan)}
                            disabled={isCurrentPlan}
                            variant={plan.popular ? 'primary' : 'secondary'}
                            className="w-full"
                        >
                            {isCurrentPlan ? 'Current Plan' : 'Upgrade to ' + plan.name}
                        </Button>
                    </div>

                    {plan.popular && (
                        <p className="mt-4 text-sm text-center text-gray-500">
                            Perfect for growing businesses
                        </p>
                    )}
                </div>

                {isCurrentPlan && (
                    <div className="bg-gray-50 px-6 py-4">
                        <div className="text-sm text-center text-gray-600">
                            <i className="fas fa-check-circle text-green-500 mr-1"></i>
                            Your current plan
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('PlanCard component error:', error);
        reportError(error);
        return null;
    }
}
