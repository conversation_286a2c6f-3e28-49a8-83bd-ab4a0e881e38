/**
 * Enhanced User Onboarding Component
 * Provides step-by-step onboarding with business template selection
 */

window.EnhancedOnboarding = function EnhancedOnboarding({ onComplete, user }) {
    const [currentStep, setCurrentStep] = React.useState(1);
    const [onboardingData, setOnboardingData] = React.useState({
        businessType: '',
        companyInfo: {
            name: '',
            industry: '',
            size: '',
            location: ''
        },
        preferences: {
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            dateFormat: 'DD/MM/YYYY'
        },
        goals: []
    });
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState(null);

    const steps = [
        {
            id: 1,
            title: 'Welcome to Your Business Management Platform',
            description: 'Let\'s set up your account to match your business needs',
            component: WelcomeStep
        },
        {
            id: 2,
            title: 'Tell Us About Your Business',
            description: 'Help us understand your business better',
            component: BusinessInfoStep
        },
        {
            id: 3,
            title: 'Choose Your Business Type',
            description: 'Select the template that best matches your business',
            component: BusinessTypeStep
        },
        {
            id: 4,
            title: 'Set Your Preferences',
            description: 'Configure your regional and display preferences',
            component: PreferencesStep
        },
        {
            id: 5,
            title: 'What Are Your Goals?',
            description: 'Help us customize your experience',
            component: GoalsStep
        },
        {
            id: 6,
            title: 'All Set!',
            description: 'Your account is ready to use',
            component: CompletionStep
        }
    ];

    const updateOnboardingData = (stepData) => {
        setOnboardingData(prev => ({ ...prev, ...stepData }));
    };

    const nextStep = () => {
        if (currentStep < steps.length) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const completeOnboarding = async () => {
        setLoading(true);
        setError(null);

        try {
            // Apply business template if selected
            if (onboardingData.businessType) {
                const templateResponse = await fetch('/api/api.php/business-template/apply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    },
                    body: JSON.stringify({
                        business_type: onboardingData.businessType,
                        company_id: user?.company_id
                    })
                });

                if (!templateResponse.ok) {
                    throw new Error('Failed to apply business template');
                }
            }

            // Save onboarding data
            const onboardingResponse = await fetch('/api/api.php/onboarding/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    ...onboardingData,
                    user_id: user?.id,
                    company_id: user?.company_id
                })
            });

            if (onboardingResponse.ok) {
                // Mark onboarding as complete
                localStorage.setItem('onboardingComplete', 'true');
                
                if (onComplete) {
                    onComplete(onboardingData);
                }
            } else {
                throw new Error('Failed to save onboarding data');
            }

        } catch (err) {
            console.error('Onboarding completion error:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const currentStepData = steps.find(step => step.id === currentStep);
    const CurrentStepComponent = currentStepData.component;

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="max-w-4xl w-full">
                {/* Progress Bar */}
                <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                        <h1 className="text-2xl font-bold text-gray-900">Getting Started</h1>
                        <span className="text-sm text-gray-600">
                            Step {currentStep} of {steps.length}
                        </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${(currentStep / steps.length) * 100}%` }}
                        ></div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="bg-white rounded-lg shadow-lg p-8">
                    <div className="text-center mb-8">
                        <h2 className="text-3xl font-bold text-gray-900 mb-2">
                            {currentStepData.title}
                        </h2>
                        <p className="text-gray-600">
                            {currentStepData.description}
                        </p>
                    </div>

                    {error && (
                        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-red-700">{error}</p>
                        </div>
                    )}

                    <CurrentStepComponent
                        data={onboardingData}
                        updateData={updateOnboardingData}
                        onNext={nextStep}
                        onPrev={prevStep}
                        onComplete={completeOnboarding}
                        loading={loading}
                        isLastStep={currentStep === steps.length}
                    />
                </div>
            </div>
        </div>
    );
};

// Step Components
const WelcomeStep = ({ onNext }) => (
    <div className="text-center">
        <div className="mb-8">
            <div className="mx-auto w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <i className="fas fa-rocket text-blue-600 text-3xl"></i>
            </div>
            <p className="text-lg text-gray-700 mb-6">
                Welcome! We're excited to help you manage your business more efficiently. 
                This quick setup will customize the platform to your specific needs.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="p-4 bg-blue-50 rounded-lg">
                    <i className="fas fa-chart-line text-blue-600 text-xl mb-2"></i>
                    <h3 className="font-semibold">Track Performance</h3>
                    <p className="text-gray-600">Monitor your business metrics</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                    <i className="fas fa-users text-green-600 text-xl mb-2"></i>
                    <h3 className="font-semibold">Manage Customers</h3>
                    <p className="text-gray-600">Keep track of all your clients</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                    <i className="fas fa-file-invoice text-purple-600 text-xl mb-2"></i>
                    <h3 className="font-semibold">Create Invoices</h3>
                    <p className="text-gray-600">Professional billing made easy</p>
                </div>
            </div>
        </div>
        <button
            onClick={onNext}
            className="px-8 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
        >
            Let's Get Started
        </button>
    </div>
);

const BusinessInfoStep = ({ data, updateData, onNext, onPrev }) => {
    const [formData, setFormData] = React.useState(data.companyInfo);

    const handleSubmit = (e) => {
        e.preventDefault();
        updateData({ companyInfo: formData });
        onNext();
    };

    return (
        <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company Name *
                    </label>
                    <input
                        type="text"
                        required
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Your Company Name"
                    />
                </div>
                
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Industry
                    </label>
                    <select
                        value={formData.industry}
                        onChange={(e) => setFormData({...formData, industry: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">Select Industry</option>
                        <option value="retail">Retail</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="consulting">Consulting</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="education">Education</option>
                        <option value="technology">Technology</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company Size
                    </label>
                    <select
                        value={formData.size}
                        onChange={(e) => setFormData({...formData, size: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">Select Size</option>
                        <option value="1-10">1-10 employees</option>
                        <option value="11-50">11-50 employees</option>
                        <option value="51-200">51-200 employees</option>
                        <option value="200+">200+ employees</option>
                    </select>
                </div>
                
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Location
                    </label>
                    <input
                        type="text"
                        value={formData.location}
                        onChange={(e) => setFormData({...formData, location: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="City, State"
                    />
                </div>
            </div>
            
            <div className="flex justify-between mt-8">
                <button
                    type="button"
                    onClick={onPrev}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                    Previous
                </button>
                <button
                    type="submit"
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                    Continue
                </button>
            </div>
        </form>
    );
};

const BusinessTypeStep = ({ data, updateData, onNext, onPrev }) => {
    const [selectedType, setSelectedType] = React.useState(data.businessType);

    const businessTypes = [
        {
            name: 'Retail Business',
            description: 'Perfect for retail stores, shops, and e-commerce businesses',
            icon: 'fas fa-store',
            features: ['Product management', 'Inventory tracking', 'Sales analytics']
        },
        {
            name: 'Healthcare Services',
            description: 'Tailored for clinics, hospitals, and healthcare providers',
            icon: 'fas fa-heartbeat',
            features: ['Patient management', 'Appointment scheduling', 'Medical billing']
        },
        {
            name: 'Consulting Services',
            description: 'Ideal for consultants, agencies, and professional services',
            icon: 'fas fa-briefcase',
            features: ['Project tracking', 'Time billing', 'Client management']
        },
        {
            name: 'Manufacturing',
            description: 'Built for manufacturers, suppliers, and production companies',
            icon: 'fas fa-industry',
            features: ['Production tracking', 'Quality control', 'Supply chain']
        },
        {
            name: 'Education Services',
            description: 'Designed for schools, training centers, and educational institutions',
            icon: 'fas fa-graduation-cap',
            features: ['Student management', 'Course tracking', 'Fee collection']
        },
        {
            name: 'Jewellery Business',
            description: 'Specialized for jewellery stores, designers, and manufacturers',
            icon: 'fas fa-gem',
            features: ['Inventory by metal type', 'Custom designs', 'Certification tracking']
        }
    ];

    const handleContinue = () => {
        updateData({ businessType: selectedType });
        onNext();
    };

    return (
        <div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                {businessTypes.map((type) => (
                    <div
                        key={type.name}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                            selectedType === type.name
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedType(type.name)}
                    >
                        <div className="text-center mb-3">
                            <i className={`${type.icon} text-2xl text-blue-600 mb-2`}></i>
                            <h3 className="font-semibold text-lg">{type.name}</h3>
                        </div>
                        <p className="text-gray-600 text-sm mb-3">{type.description}</p>
                        <ul className="text-xs text-gray-500 space-y-1">
                            {type.features.map((feature, index) => (
                                <li key={index} className="flex items-center">
                                    <i className="fas fa-check text-green-500 mr-2"></i>
                                    {feature}
                                </li>
                            ))}
                        </ul>
                    </div>
                ))}
            </div>

            <div className="text-center mb-6">
                <button
                    onClick={() => setSelectedType('')}
                    className={`px-4 py-2 border rounded-md ${
                        selectedType === '' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                    }`}
                >
                    Skip - I'll configure manually
                </button>
            </div>

            <div className="flex justify-between">
                <button
                    onClick={onPrev}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                    Previous
                </button>
                <button
                    onClick={handleContinue}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                    Continue
                </button>
            </div>
        </div>
    );
};

const PreferencesStep = ({ data, updateData, onNext, onPrev }) => {
    const [preferences, setPreferences] = React.useState(data.preferences);

    const handleContinue = () => {
        updateData({ preferences });
        onNext();
    };

    return (
        <div className="max-w-2xl mx-auto">
            <div className="space-y-6">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                    </label>
                    <select
                        value={preferences.currency}
                        onChange={(e) => setPreferences({...preferences, currency: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="INR">Indian Rupee (₹)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                        <option value="GBP">British Pound (£)</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Timezone
                    </label>
                    <select
                        value={preferences.timezone}
                        onChange={(e) => setPreferences({...preferences, timezone: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="Asia/Kolkata">India Standard Time</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Europe/London">GMT</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date Format
                    </label>
                    <select
                        value={preferences.dateFormat}
                        onChange={(e) => setPreferences({...preferences, dateFormat: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                </div>
            </div>

            <div className="flex justify-between mt-8">
                <button
                    onClick={onPrev}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                    Previous
                </button>
                <button
                    onClick={handleContinue}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                    Continue
                </button>
            </div>
        </div>
    );
};

const GoalsStep = ({ data, updateData, onNext, onPrev }) => {
    const [selectedGoals, setSelectedGoals] = React.useState(data.goals || []);

    const goals = [
        { id: 'increase_sales', label: 'Increase Sales', icon: 'fas fa-chart-line' },
        { id: 'improve_efficiency', label: 'Improve Efficiency', icon: 'fas fa-cogs' },
        { id: 'better_customer_service', label: 'Better Customer Service', icon: 'fas fa-smile' },
        { id: 'reduce_costs', label: 'Reduce Costs', icon: 'fas fa-piggy-bank' },
        { id: 'expand_business', label: 'Expand Business', icon: 'fas fa-expand-arrows-alt' },
        { id: 'digital_transformation', label: 'Digital Transformation', icon: 'fas fa-digital-tachograph' }
    ];

    const toggleGoal = (goalId) => {
        setSelectedGoals(prev => 
            prev.includes(goalId) 
                ? prev.filter(id => id !== goalId)
                : [...prev, goalId]
        );
    };

    const handleContinue = () => {
        updateData({ goals: selectedGoals });
        onNext();
    };

    return (
        <div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                {goals.map((goal) => (
                    <div
                        key={goal.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                            selectedGoals.includes(goal.id)
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => toggleGoal(goal.id)}
                    >
                        <div className="text-center">
                            <i className={`${goal.icon} text-2xl text-blue-600 mb-2`}></i>
                            <h3 className="font-medium">{goal.label}</h3>
                        </div>
                    </div>
                ))}
            </div>

            <div className="flex justify-between">
                <button
                    onClick={onPrev}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                    Previous
                </button>
                <button
                    onClick={handleContinue}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                    Continue
                </button>
            </div>
        </div>
    );
};

const CompletionStep = ({ onComplete, loading }) => (
    <div className="text-center">
        <div className="mb-8">
            <div className="mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <i className="fas fa-check text-green-600 text-3xl"></i>
            </div>
            <h3 className="text-xl font-semibold mb-4">You're All Set!</h3>
            <p className="text-gray-600 mb-6">
                Your account has been configured based on your preferences. 
                You can always change these settings later from your dashboard.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-8">
                <div className="p-4 bg-blue-50 rounded-lg">
                    <i className="fas fa-rocket text-blue-600 text-xl mb-2"></i>
                    <h4 className="font-semibold">Ready to Launch</h4>
                    <p className="text-gray-600">Your business tools are configured</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                    <i className="fas fa-shield-alt text-green-600 text-xl mb-2"></i>
                    <h4 className="font-semibold">Secure & Private</h4>
                    <p className="text-gray-600">Your data is protected</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                    <i className="fas fa-headset text-purple-600 text-xl mb-2"></i>
                    <h4 className="font-semibold">Support Available</h4>
                    <p className="text-gray-600">We're here to help you succeed</p>
                </div>
            </div>
        </div>
        <button
            onClick={onComplete}
            disabled={loading}
            className="px-8 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium disabled:opacity-50"
        >
            {loading ? (
                <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Setting up your account...
                </>
            ) : (
                'Start Using Your Dashboard'
            )}
        </button>
    </div>
);