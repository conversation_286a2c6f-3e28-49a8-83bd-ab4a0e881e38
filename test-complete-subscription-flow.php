<?php
require_once 'api/db-config.php';

echo "=== Complete SaaS Subscription System Test ===\n\n";

// Test 1: Database Structure
echo "1. Database Structure Test:\n";
$requiredTables = [
    'pricing_plans' => 'Subscription plans',
    'subscriptions' => 'Active subscriptions', 
    'companies' => 'Customer companies',
    'users' => 'System users',
    'usage_tracking' => 'Usage statistics',
    'payment_transactions' => 'Payment records'
];

foreach ($requiredTables as $table => $description) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        $count = $conn->query("SELECT COUNT(*) as count FROM $table");
        $countRow = $count->fetch_assoc();
        echo "   ✓ $description ($table): {$countRow['count']} records\n";
    } else {
        echo "   ✗ $description ($table): Missing\n";
    }
}

// Test 2: Pricing Plans
echo "\n2. Pricing Plans Test:\n";
$plansResult = $conn->query("
    SELECT id, name, price_monthly, price_yearly, is_active, 
           JSON_LENGTH(features) as feature_count,
           JSON_LENGTH(business_types) as business_type_count
    FROM pricing_plans 
    ORDER BY price_monthly ASC
");

if ($plansResult && $plansResult->num_rows > 0) {
    while ($plan = $plansResult->fetch_assoc()) {
        $status = $plan['is_active'] ? 'Active' : 'Inactive';
        echo "   ✓ {$plan['name']}: \${$plan['price_monthly']}/mo, \${$plan['price_yearly']}/yr ({$status})\n";
        echo "     Features: {$plan['feature_count']}, Business Types: {$plan['business_type_count']}\n";
    }
} else {
    echo "   ✗ No pricing plans found\n";
}

// Test 3: Active Subscriptions
echo "\n3. Active Subscriptions Test:\n";
$subsResult = $conn->query("
    SELECT s.*, c.name as company_name, pp.name as plan_name
    FROM subscriptions s
    JOIN companies c ON s.company_id = c.object_id
    JOIN pricing_plans pp ON s.plan_id = pp.id
    ORDER BY s.created_at DESC
");

if ($subsResult && $subsResult->num_rows > 0) {
    while ($sub = $subsResult->fetch_assoc()) {
        $trialInfo = $sub['status'] === 'trial' ? " (Trial ends: {$sub['trial_end_date']})" : '';
        echo "   ✓ {$sub['company_name']}: {$sub['plan_name']} - {$sub['status']}{$trialInfo}\n";
    }
} else {
    echo "   ✗ No subscriptions found\n";
}

// Test 4: Subscription Statistics
echo "\n4. Subscription Statistics:\n";
$statsResult = $conn->query("
    SELECT 
        COUNT(*) as total_subscriptions,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
        SUM(CASE WHEN status = 'trial' THEN 1 ELSE 0 END) as trial_subscriptions,
        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_subscriptions,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_subscriptions
    FROM subscriptions
");

if ($statsResult) {
    $stats = $statsResult->fetch_assoc();
    echo "   Total Subscriptions: {$stats['total_subscriptions']}\n";
    echo "   Active: {$stats['active_subscriptions']}\n";
    echo "   Trial: {$stats['trial_subscriptions']}\n";
    echo "   Expired: {$stats['expired_subscriptions']}\n";
    echo "   Cancelled: {$stats['cancelled_subscriptions']}\n";
}

// Test 5: Company-Subscription Mapping
echo "\n5. Company-Subscription Mapping Test:\n";
$mappingResult = $conn->query("
    SELECT c.name, c.subscription_id, s.status, s.plan_name
    FROM companies c
    LEFT JOIN subscriptions s ON c.object_id = s.company_id AND s.status IN ('active', 'trial')
    ORDER BY c.name
");

if ($mappingResult && $mappingResult->num_rows > 0) {
    while ($mapping = $mappingResult->fetch_assoc()) {
        if ($mapping['status']) {
            echo "   ✓ {$mapping['name']}: {$mapping['plan_name']} ({$mapping['status']})\n";
        } else {
            echo "   ✗ {$mapping['name']}: No active subscription\n";
        }
    }
}

// Test 6: Usage Tracking
echo "\n6. Usage Tracking Test:\n";
$usageResult = $conn->query("
    SELECT ut.*, c.name as company_name
    FROM usage_tracking ut
    JOIN companies c ON ut.company_id = c.object_id
    ORDER BY ut.month_year DESC, c.name
");

if ($usageResult && $usageResult->num_rows > 0) {
    while ($usage = $usageResult->fetch_assoc()) {
        echo "   ✓ {$usage['company_name']} ({$usage['month_year']}): ";
        echo "Leads: {$usage['leads_created']}, Invoices: {$usage['invoices_sent']}, Storage: " . formatBytes($usage['storage_used']) . "\n";
    }
} else {
    echo "   ✗ No usage tracking data found\n";
}

// Test 7: API Endpoints (Basic connectivity)
echo "\n7. API Endpoints Test:\n";
$endpoints = [
    '/super-admin/plans' => 'Super Admin Plans',
    '/super-admin/subscriptions' => 'Super Admin Subscriptions', 
    '/subscription-management/current' => 'User Current Subscription',
    '/subscription-management/trial-status' => 'User Trial Status'
];

foreach ($endpoints as $endpoint => $description) {
    $url = "http://localhost/biz/api/api.php$endpoint";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 401 || $httpCode == 403) {
        echo "   ✓ $description: Authentication required (expected)\n";
    } elseif ($httpCode == 200) {
        echo "   ✓ $description: Accessible\n";
    } else {
        echo "   ⚠ $description: HTTP $httpCode\n";
    }
}

// Test 8: Business Logic Validation
echo "\n8. Business Logic Validation:\n";

// Check for companies without subscriptions
$orphanCompanies = $conn->query("
    SELECT c.name FROM companies c
    LEFT JOIN subscriptions s ON c.object_id = s.company_id AND s.status IN ('active', 'trial')
    WHERE s.id IS NULL
");

if ($orphanCompanies && $orphanCompanies->num_rows > 0) {
    echo "   ⚠ Companies without subscriptions:\n";
    while ($company = $orphanCompanies->fetch_assoc()) {
        echo "     - {$company['name']}\n";
    }
} else {
    echo "   ✓ All companies have subscriptions\n";
}

// Check for expired trials
$expiredTrials = $conn->query("
    SELECT c.name, s.trial_end_date 
    FROM subscriptions s
    JOIN companies c ON s.company_id = c.object_id
    WHERE s.status = 'trial' AND s.trial_end_date < NOW()
");

if ($expiredTrials && $expiredTrials->num_rows > 0) {
    echo "   ⚠ Expired trials that need attention:\n";
    while ($trial = $expiredTrials->fetch_assoc()) {
        echo "     - {$trial['name']}: Expired on {$trial['trial_end_date']}\n";
    }
} else {
    echo "   ✓ No expired trials found\n";
}

echo "\n=== Test Complete ===\n";

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

$conn->close();
?>