<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Super Admin Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Super Admin Dashboard Test</h1>
    
    <div class="test-section">
        <h3>API Routing Test</h3>
        <button onclick="testRouting()">Test API Routing</button>
        <div id="routing-result"></div>
    </div>

    <div class="test-section">
        <h3>Authentication Test</h3>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="auth-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Dashboard Data Test</h3>
        <button onclick="testDashboard()">Test Dashboard API</button>
        <div id="dashboard-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Plans Management Test</h3>
        <button onclick="testPlans()">Test Plans API</button>
        <div id="plans-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Business Types Test</h3>
        <button onclick="testBusinessTypes()">Test Business Types API</button>
        <div id="business-types-result"></div>
    </div>

    <script>
        // Test token for super admin (generated by create-super-admin.php)
        const testToken = 'test_super_admin_super_admin_001';
        
        function getApiUrl(endpoint) {
            return `/biz/api/api.php${endpoint}`;
        }
        
        function showResult(elementId, status, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `test-section ${status}`;
            element.innerHTML = `
                <strong>${status.toUpperCase()}:</strong> ${message}
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }
        
        function showLoading(elementId) {
            showResult(elementId, 'loading', 'Testing...');
        }
        
        async function testRouting() {
            showLoading('routing-result');
            try {
                const response = await fetch('/biz/api/test-routing.php');

                if (response.ok) {
                    const data = await response.json();
                    showResult('routing-result', 'success', 'API routing working', data);
                } else {
                    showResult('routing-result', 'error', `Routing failed: ${response.status}`);
                }
            } catch (error) {
                showResult('routing-result', 'error', `Routing error: ${error.message}`);
            }
        }

        async function testAuth() {
            showLoading('auth-result');
            try {
                const response = await fetch(getApiUrl('/auth/verify'), {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('auth-result', 'success', 'Authentication working', data);
                } else {
                    showResult('auth-result', 'error', `Auth failed: ${response.status}`);
                }
            } catch (error) {
                showResult('auth-result', 'error', `Auth error: ${error.message}`);
            }
        }
        
        async function testDashboard() {
            showLoading('dashboard-result');
            try {
                const response = await fetch(getApiUrl('/super-admin/dashboard'), {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('dashboard-result', 'success', 'Dashboard API working', data);
                } else {
                    const errorText = await response.text();
                    showResult('dashboard-result', 'error', `Dashboard failed: ${response.status}`, errorText);
                }
            } catch (error) {
                showResult('dashboard-result', 'error', `Dashboard error: ${error.message}`);
            }
        }
        
        async function testPlans() {
            showLoading('plans-result');
            try {
                const response = await fetch(getApiUrl('/super-admin/plans'), {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('plans-result', 'success', 'Plans API working', data);
                } else {
                    const errorText = await response.text();
                    showResult('plans-result', 'error', `Plans failed: ${response.status}`, errorText);
                }
            } catch (error) {
                showResult('plans-result', 'error', `Plans error: ${error.message}`);
            }
        }
        
        async function testBusinessTypes() {
            showLoading('business-types-result');
            try {
                const response = await fetch(getApiUrl('/super-admin/business-types'), {
                    headers: {
                        'Authorization': `Bearer ${testToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('business-types-result', 'success', 'Business Types API working', data);
                } else {
                    const errorText = await response.text();
                    showResult('business-types-result', 'error', `Business Types failed: ${response.status}`, errorText);
                }
            } catch (error) {
                showResult('business-types-result', 'error', `Business Types error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
