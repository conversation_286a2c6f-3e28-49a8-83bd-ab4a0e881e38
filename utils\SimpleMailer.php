<?php
/**
 * Simple SMTP Mailer for Windows/XAMPP environments
 * Provides basic SMTP authentication support
 */

class SimpleMailer {
    
    /**
     * Send email using socket-based SMTP
     * 
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param array $config SMTP configuration
     * @return array Result with success status and message
     */
    public static function sendEmail($to, $subject, $message, $config = []) {
        try {
            // Default configuration
            $defaultConfig = [
                'host' => 'smtp.gmail.com',
                'port' => 587,
                'username' => '',
                'password' => '',
                'from_email' => '<EMAIL>',
                'from_name' => 'Bizma',
                'timeout' => 30
            ];
            
            $config = array_merge($defaultConfig, $config);
            
            // Validate required fields
            if (empty($config['username']) || empty($config['password'])) {
                return [
                    'success' => false,
                    'message' => 'SMTP username and password are required for authentication'
                ];
            }
            
            // Create socket connection
            $socket = fsockopen($config['host'], $config['port'], $errno, $errstr, $config['timeout']);
            
            if (!$socket) {
                return [
                    'success' => false,
                    'message' => "Failed to connect to SMTP server: {$errstr} ({$errno})"
                ];
            }
            
            // Set timeout
            stream_set_timeout($socket, $config['timeout']);
            
            // Read initial response
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '220') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "SMTP server not ready: {$response}"
                ];
            }
            
            // EHLO command
            fputs($socket, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
            $response = fgets($socket, 512);
            
            // STARTTLS for secure connection
            fputs($socket, "STARTTLS\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '220') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "STARTTLS failed: {$response}"
                ];
            }
            
            // Enable crypto
            if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "Failed to enable TLS encryption"
                ];
            }
            
            // EHLO again after TLS
            fputs($socket, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
            $response = fgets($socket, 512);
            
            // AUTH LOGIN
            fputs($socket, "AUTH LOGIN\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '334') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "AUTH LOGIN failed: {$response}"
                ];
            }
            
            // Send username
            fputs($socket, base64_encode($config['username']) . "\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '334') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "Username authentication failed: {$response}"
                ];
            }
            
            // Send password
            fputs($socket, base64_encode($config['password']) . "\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '235') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "Password authentication failed: {$response}"
                ];
            }
            
            // MAIL FROM
            fputs($socket, "MAIL FROM: <{$config['from_email']}>\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '250') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "MAIL FROM failed: {$response}"
                ];
            }
            
            // RCPT TO
            fputs($socket, "RCPT TO: <{$to}>\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '250') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "RCPT TO failed: {$response}"
                ];
            }
            
            // DATA
            fputs($socket, "DATA\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '354') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "DATA command failed: {$response}"
                ];
            }
            
            // Email headers and body
            $headers = "From: {$config['from_name']} <{$config['from_email']}>\r\n";
            $headers .= "To: {$to}\r\n";
            $headers .= "Subject: {$subject}\r\n";
            $headers .= "MIME-Version: 1.0\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "Date: " . date('r') . "\r\n";
            $headers .= "\r\n";
            
            fputs($socket, $headers . $message . "\r\n.\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) !== '250') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "Email sending failed: {$response}"
                ];
            }
            
            // QUIT
            fputs($socket, "QUIT\r\n");
            fclose($socket);
            
            return [
                'success' => true,
                'message' => 'Email sent successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get SMTP configuration from Config class or environment
     * 
     * @return array SMTP configuration
     */
    public static function getConfig() {
        if (class_exists('Config')) {
            return [
                'host' => Config::get('SMTP_HOST', 'smtp.gmail.com'),
                'port' => (int)Config::get('SMTP_PORT', 587),
                'username' => Config::get('SMTP_USERNAME', ''),
                'password' => Config::get('SMTP_PASSWORD', ''),
                'from_email' => Config::get('SMTP_FROM_EMAIL', '<EMAIL>'),
                'from_name' => Config::get('SMTP_FROM_NAME', 'Bizma')
            ];
        }
        
        return [
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'username' => '',
            'password' => '',
            'from_email' => '<EMAIL>',
            'from_name' => 'Bizma'
        ];
    }
}
?>
