<?php
/**
 * Direct Customer Creation Test
 * Test customer creation directly without going through the full API stack
 */

require_once 'api/db-config.php';
require_once 'api/handlers/crud-handler.php';
require_once 'api/handlers/validation-handler.php';
require_once 'api/handlers/field-handler.php';
require_once 'api/handlers/object-handler.php';

echo "🧪 Testing Customer Creation Directly...\n\n";

try {
    // Simulate a super admin user
    $_SESSION['current_user'] = [
        'object_id' => 'super_admin_001',
        'name' => 'Super Administrator',
        'email' => '<EMAIL>',
        'role' => 'super_admin',
        'company_id' => 'super_admin_001'
    ];
    
    // Test data
    $customerData = [
        'name' => 'Test Customer Direct',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'company' => 'Test Customer Company',
        'address' => '123 Test Street, Test City, TC 12345',
        'type' => 'business',
        'status' => 'active',
        'notes' => 'Direct test customer'
    ];
    
    echo "📝 Customer Data:\n";
    print_r($customerData);
    echo "\n";
    
    // Test validation
    echo "🔍 Testing validation...\n";
    $validation = ValidationHandler::processObjectData('customer', $customerData);
    if (!$validation['valid']) {
        echo "❌ Validation failed:\n";
        print_r($validation['errors']);
        exit;
    }
    echo "✅ Validation passed\n\n";
    
    // Test field extraction
    echo "🔍 Testing field extraction...\n";
    $fields = extractFields('customer', $customerData);
    echo "Extracted fields:\n";
    print_r($fields);
    echo "\n";
    
    // Test table mapping
    echo "🔍 Testing table mapping...\n";
    $table = mapObjectTypeToTable('customer');
    echo "Table: $table\n\n";
    
    // Test direct database insertion
    echo "🔍 Testing direct database insertion...\n";
    
    $objectId = generateId();
    $fields['object_id'] = $objectId;
    $fields['company_id'] = 'super_admin_001';
    
    // Build SQL
    $columns = implode(', ', array_keys($fields));
    $placeholders = implode(', ', array_fill(0, count($fields), '?'));
    $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
    
    echo "SQL: $sql\n";
    echo "Values: " . print_r(array_values($fields), true) . "\n";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        echo "❌ Failed to prepare statement: " . $conn->error . "\n";
        exit;
    }
    
    $types = str_repeat('s', count($fields));
    $stmt->bind_param($types, ...array_values($fields));
    
    if ($stmt->execute()) {
        echo "✅ Customer created successfully with ID: $objectId\n";
        
        // Verify by reading back
        $checkSql = "SELECT * FROM $table WHERE object_id = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("s", $objectId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            echo "\n📋 Created customer data:\n";
            print_r($row);
            
            // Test object conversion
            echo "\n🔄 Testing object conversion:\n";
            $objectData = rowToObject($row, 'customer');
            print_r($objectData);
        }
        
    } else {
        echo "❌ Failed to create customer: " . $stmt->error . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
