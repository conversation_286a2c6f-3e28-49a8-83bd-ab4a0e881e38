<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validate Updates - Admin Tool</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function ValidationDashboard() {
            const [validationResults, setValidationResults] = useState([]);
            const [loading, setLoading] = useState(false);
            const [authToken, setAuthToken] = useState('');

            const validationChecks = [
                {
                    id: 'leads-editing',
                    name: 'Leads Page - Edit Functionality',
                    description: 'Check if Activities, Tasks, and Notes can be edited',
                    category: 'Leads'
                },
                {
                    id: 'subscription-plans',
                    name: 'Subscription Plans Update',
                    description: 'Verify Trial Plan (5 days) and Business Plan (₹500/₹5000)',
                    category: 'Subscription'
                },
                {
                    id: 'user-dashboard-plans',
                    name: 'User Dashboard - Subscription Page',
                    description: 'Check if updated plans are reflected correctly',
                    category: 'Subscription'
                },
                {
                    id: 'payment-gateways',
                    name: 'Payment Gateway Options',
                    description: 'Verify PhonePe, Google Pay, and Net Banking options',
                    category: 'Payment'
                },
                {
                    id: 'frontend-plans',
                    name: 'Frontend Home Page Plans',
                    description: 'Check if updated plans are displayed on home page',
                    category: 'Frontend'
                },
                {
                    id: 'inventory-templates',
                    name: 'Dynamic Inventory Templates',
                    description: 'Verify business type-based inventory templates',
                    category: 'Inventory'
                }
            ];

            const runValidation = async () => {
                setLoading(true);
                const results = [];

                for (const check of validationChecks) {
                    try {
                        const result = await performValidationCheck(check);
                        results.push(result);
                    } catch (error) {
                        results.push({
                            ...check,
                            status: 'error',
                            message: `Validation failed: ${error.message}`,
                            timestamp: new Date().toISOString()
                        });
                    }
                }

                setValidationResults(results);
                setLoading(false);
            };

            const performValidationCheck = async (check) => {
                const baseResult = {
                    ...check,
                    timestamp: new Date().toISOString()
                };

                switch (check.id) {
                    case 'leads-editing':
                        return await validateLeadsEditing(baseResult);
                    
                    case 'subscription-plans':
                        return await validateSubscriptionPlans(baseResult);
                    
                    case 'user-dashboard-plans':
                        return await validateUserDashboardPlans(baseResult);
                    
                    case 'payment-gateways':
                        return await validatePaymentGateways(baseResult);
                    
                    case 'frontend-plans':
                        return await validateFrontendPlans(baseResult);
                    
                    case 'inventory-templates':
                        return await validateInventoryTemplates(baseResult);
                    
                    default:
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'Validation check not implemented'
                        };
                }
            };

            const validateLeadsEditing = async (baseResult) => {
                try {
                    // Check if the lead components exist and have edit functionality
                    const response = await fetch('/components/leads/ActivityItem.js');
                    const content = await response.text();
                    
                    const hasEditButton = content.includes('fa-edit') && content.includes('onClick');
                    const hasDeleteButton = content.includes('fa-trash') && content.includes('onClick');
                    const hasEditState = content.includes('isEditing') && content.includes('setIsEditing');

                    if (hasEditButton && hasDeleteButton && hasEditState) {
                        return {
                            ...baseResult,
                            status: 'success',
                            message: 'Edit and delete functionality implemented for Activities, Tasks, and Notes'
                        };
                    } else {
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'Edit functionality partially implemented'
                        };
                    }
                } catch (error) {
                    return {
                        ...baseResult,
                        status: 'error',
                        message: `Could not validate leads editing: ${error.message}`
                    };
                }
            };

            const validateSubscriptionPlans = async (baseResult) => {
                try {
                    if (!authToken) {
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'Auth token required for plan validation'
                        };
                    }

                    const response = await fetch('/api/super-admin/plans.php', {
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        const trialPlan = result.plans.find(p => p.name === 'Trial Plan');
                        const businessPlan = result.plans.find(p => p.name === 'Business Plan');

                        if (trialPlan && businessPlan) {
                            const trialValid = trialPlan.trial_days === 5 && trialPlan.price_monthly === 0;
                            const businessValid = businessPlan.price_monthly === 500 && businessPlan.price_yearly === 5000;

                            if (trialValid && businessValid) {
                                return {
                                    ...baseResult,
                                    status: 'success',
                                    message: 'Subscription plans updated correctly'
                                };
                            } else {
                                return {
                                    ...baseResult,
                                    status: 'warning',
                                    message: 'Plans exist but pricing may not be correct'
                                };
                            }
                        } else {
                            return {
                                ...baseResult,
                                status: 'error',
                                message: 'Required plans not found'
                            };
                        }
                    } else {
                        return {
                            ...baseResult,
                            status: 'error',
                            message: 'Could not fetch plans'
                        };
                    }
                } catch (error) {
                    return {
                        ...baseResult,
                        status: 'error',
                        message: `Plan validation failed: ${error.message}`
                    };
                }
            };

            const validateUserDashboardPlans = async (baseResult) => {
                try {
                    const response = await fetch('/pages/UserSubscriptionDashboard.js');
                    const content = await response.text();
                    
                    if (content.includes('subscription') || content.includes('plan')) {
                        return {
                            ...baseResult,
                            status: 'success',
                            message: 'User subscription dashboard exists'
                        };
                    } else {
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'User subscription dashboard needs verification'
                        };
                    }
                } catch (error) {
                    return {
                        ...baseResult,
                        status: 'error',
                        message: `Could not validate user dashboard: ${error.message}`
                    };
                }
            };

            const validatePaymentGateways = async (baseResult) => {
                try {
                    const response = await fetch('/pages/SuperAdminPaymentGateways.js');
                    const content = await response.text();
                    
                    const hasPhonePe = content.includes('phonepe');
                    const hasGooglePay = content.includes('googlepay');
                    const hasNetBanking = content.includes('netbanking');

                    if (hasPhonePe && hasGooglePay && hasNetBanking) {
                        return {
                            ...baseResult,
                            status: 'success',
                            message: 'New payment gateway options added successfully'
                        };
                    } else {
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'Some payment gateway options may be missing'
                        };
                    }
                } catch (error) {
                    return {
                        ...baseResult,
                        status: 'error',
                        message: `Could not validate payment gateways: ${error.message}`
                    };
                }
            };

            const validateFrontendPlans = async (baseResult) => {
                try {
                    const response = await fetch('/pages/LandingPage.js');
                    const content = await response.text();
                    
                    if (content.includes('pricing') || content.includes('plan')) {
                        return {
                            ...baseResult,
                            status: 'success',
                            message: 'Frontend pricing section exists'
                        };
                    } else {
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'Frontend pricing section needs verification'
                        };
                    }
                } catch (error) {
                    return {
                        ...baseResult,
                        status: 'error',
                        message: `Could not validate frontend plans: ${error.message}`
                    };
                }
            };

            const validateInventoryTemplates = async (baseResult) => {
                try {
                    const response = await fetch('/utils/business-type-templates.js');
                    const content = await response.text();
                    
                    const hasBusinessTypeMapping = content.includes('getBusinessTypeTemplate');
                    const hasRealEstateTemplate = content.includes('RealEstateItems');
                    const hasDynamicLoading = content.includes('getUserBusinessType');

                    if (hasBusinessTypeMapping && hasRealEstateTemplate && hasDynamicLoading) {
                        return {
                            ...baseResult,
                            status: 'success',
                            message: 'Dynamic inventory templates implemented successfully'
                        };
                    } else {
                        return {
                            ...baseResult,
                            status: 'warning',
                            message: 'Inventory template system partially implemented'
                        };
                    }
                } catch (error) {
                    return {
                        ...baseResult,
                        status: 'error',
                        message: `Could not validate inventory templates: ${error.message}`
                    };
                }
            };

            const getStatusIcon = (status) => {
                switch (status) {
                    case 'success':
                        return 'fa-check-circle text-green-500';
                    case 'warning':
                        return 'fa-exclamation-triangle text-yellow-500';
                    case 'error':
                        return 'fa-times-circle text-red-500';
                    default:
                        return 'fa-clock text-gray-500';
                }
            };

            const getStatusBg = (status) => {
                switch (status) {
                    case 'success':
                        return 'bg-green-50 border-green-200';
                    case 'warning':
                        return 'bg-yellow-50 border-yellow-200';
                    case 'error':
                        return 'bg-red-50 border-red-200';
                    default:
                        return 'bg-gray-50 border-gray-200';
                }
            };

            return (
                <div className="min-h-screen bg-gray-100 py-8">
                    <div className="max-w-6xl mx-auto px-4">
                        <div className="bg-white rounded-lg shadow-lg p-6">
                            <h1 className="text-2xl font-bold text-gray-900 mb-6">
                                <i className="fas fa-check-double mr-2"></i>
                                Update Validation Dashboard
                            </h1>

                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Admin Auth Token (Optional - for plan validation)
                                </label>
                                <input
                                    type="password"
                                    value={authToken}
                                    onChange={(e) => setAuthToken(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Enter your admin authentication token"
                                />
                            </div>

                            <div className="flex justify-between items-center mb-6">
                                <button
                                    onClick={runValidation}
                                    disabled={loading}
                                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {loading ? (
                                        <>
                                            <i className="fas fa-spinner fa-spin mr-2"></i>
                                            Running Validation...
                                        </>
                                    ) : (
                                        <>
                                            <i className="fas fa-play mr-2"></i>
                                            Run Validation
                                        </>
                                    )}
                                </button>

                                {validationResults.length > 0 && (
                                    <div className="text-sm text-gray-600">
                                        Last run: {new Date().toLocaleString()}
                                    </div>
                                )}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {validationChecks.map((check) => {
                                    const result = validationResults.find(r => r.id === check.id);
                                    
                                    return (
                                        <div
                                            key={check.id}
                                            className={`border rounded-lg p-4 ${
                                                result ? getStatusBg(result.status) : 'bg-gray-50 border-gray-200'
                                            }`}
                                        >
                                            <div className="flex items-start justify-between mb-2">
                                                <h3 className="font-medium text-gray-900">{check.name}</h3>
                                                {result && (
                                                    <i className={`fas ${getStatusIcon(result.status)}`}></i>
                                                )}
                                            </div>
                                            
                                            <p className="text-sm text-gray-600 mb-2">{check.description}</p>
                                            
                                            <div className="text-xs text-gray-500 mb-2">
                                                Category: {check.category}
                                            </div>

                                            {result && (
                                                <div className="text-sm">
                                                    <p className={`
                                                        ${result.status === 'success' ? 'text-green-700' : 
                                                          result.status === 'warning' ? 'text-yellow-700' : 'text-red-700'}
                                                    `}>
                                                        {result.message}
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>

                            {validationResults.length > 0 && (
                                <div className="mt-8">
                                    <h2 className="text-lg font-medium text-gray-900 mb-4">Summary</h2>
                                    <div className="grid grid-cols-3 gap-4">
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                                            <div className="text-2xl font-bold text-green-600">
                                                {validationResults.filter(r => r.status === 'success').length}
                                            </div>
                                            <div className="text-sm text-green-700">Passed</div>
                                        </div>
                                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                                            <div className="text-2xl font-bold text-yellow-600">
                                                {validationResults.filter(r => r.status === 'warning').length}
                                            </div>
                                            <div className="text-sm text-yellow-700">Warnings</div>
                                        </div>
                                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                                            <div className="text-2xl font-bold text-red-600">
                                                {validationResults.filter(r => r.status === 'error').length}
                                            </div>
                                            <div className="text-sm text-red-700">Failed</div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<ValidationDashboard />);
    </script>
</body>
</html>