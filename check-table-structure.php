<?php
require_once 'api/db-config.php';

echo "<h1>Table Structure Check</h1>\n";

// Check users table structure
$result = $conn->query("DESCRIBE users");
echo "<h2>Users Table Structure:</h2>\n";
echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>\n";
}
echo "</table>\n";

// Check if there are any users
$result = $conn->query("SELECT * FROM users LIMIT 3");
echo "<h2>Sample Users:</h2>\n";
if ($result->num_rows > 0) {
    echo "<table border='1'>\n";
    $first = true;
    while ($row = $result->fetch_assoc()) {
        if ($first) {
            echo "<tr>";
            foreach (array_keys($row) as $key) {
                echo "<th>$key</th>";
            }
            echo "</tr>\n";
            $first = false;
        }
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>No users found</p>\n";
}

$conn->close();
?>