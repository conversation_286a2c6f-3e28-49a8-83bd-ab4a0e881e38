<?php
require_once 'api/db-config.php';

echo "<h2>Activities in Database</h2>\n";

$sql = "SELECT a.*, l.name as lead_name FROM activities a LEFT JOIN leads l ON a.lead_id = l.object_id ORDER BY a.created_at DESC LIMIT 10";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Object ID</th><th>Lead Name</th><th>Type</th><th>Description</th><th>Created</th></tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['object_id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['lead_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['description']) . "</td>";
        echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>No activities found.</p>\n";
}

$conn->close();
?>