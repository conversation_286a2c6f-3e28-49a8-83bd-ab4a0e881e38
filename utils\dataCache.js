// Simple data caching utility
window.dataCache = {
    cache: {},
    
    /**
     * Get a cached item
     * @param {string} key - Cache key
     * @returns {any|null} - Cached data or null if not found or expired
     */
    get: function(key) {
        const item = this.cache[key];
        if (!item) return null;
        
        // Check if item is expired
        if (item.expiry && item.expiry < Date.now()) {
            delete this.cache[key];
            return null;
        }
        
        return item.data;
    },
    
    /**
     * Set a cached item
     * @param {string} key - Cache key
     * @param {any} data - Data to cache
     * @param {number} ttlSeconds - Time to live in seconds (default: 300s = 5min)
     */
    set: function(key, data, ttlSeconds = 300) {
        this.cache[key] = {
            data,
            expiry: ttlSeconds ? Date.now() + (ttlSeconds * 1000) : null
        };
    },
    
    /**
     * Check if a key exists in the cache and is not expired
     * @param {string} key - Cache key
     * @returns {boolean} - True if key exists and is not expired
     */
    has: function(key) {
        return this.get(key) !== null;
    },
    
    /**
     * Remove a specific item from the cache
     * @param {string} key - Cache key
     */
    remove: function(key) {
        delete this.cache[key];
    },
    
    /**
     * Clear all cached items
     */
    clear: function() {
        this.cache = {};
    },
    
    /**
     * Clear expired items from the cache
     */
    clearExpired: function() {
        const now = Date.now();
        Object.keys(this.cache).forEach(key => {
            const item = this.cache[key];
            if (item.expiry && item.expiry < now) {
                delete this.cache[key];
            }
        });
    }
};