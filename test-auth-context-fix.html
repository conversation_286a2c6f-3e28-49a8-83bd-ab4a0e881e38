<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auth Context Fix</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script>
        // Mock getApiUrl function
        window.getApiUrl = function(path) {
            return '/biz/api/api.php' + path;
        };
    </script>

    <script src="/biz/components/auth/AuthContext.js"></script>

    <script type="text/babel">
        function TestApp() {
            const [loginResult, setLoginResult] = React.useState(null);
            const [authStatus, setAuthStatus] = React.useState('checking...');

            // Test login first
            const testLogin = async () => {
                try {
                    const response = await fetch('/biz/api/enhanced-auth-handler.php?action=login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'admin123'
                        })
                    });

                    const data = await response.json();
                    if (data.success) {
                        localStorage.setItem('authToken', data.tokens.access_token);
                        setLoginResult('Login successful, token stored');
                        
                        // Force auth check
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        setLoginResult('Login failed: ' + data.error);
                    }
                } catch (error) {
                    setLoginResult('Login error: ' + error.message);
                }
            };

            return (
                <div style={{padding: '20px'}}>
                    <h1>Auth Context Test</h1>
                    
                    <div style={{marginBottom: '20px'}}>
                        <h3>Step 1: Login Test</h3>
                        <button onClick={testLogin}>Test Login</button>
                        {loginResult && <p>Result: {loginResult}</p>}
                    </div>

                    <div>
                        <h3>Step 2: Auth Context Test</h3>
                        <AuthProvider>
                            <AuthTest />
                        </AuthProvider>
                    </div>
                </div>
            );
        }

        function AuthTest() {
            const authContext = React.useContext(AuthContext);
            
            if (!authContext) {
                return <p>❌ AuthContext not available</p>;
            }

            const { user, loading, error } = authContext;

            if (loading) {
                return <p>🔄 Loading...</p>;
            }

            if (error) {
                return <p>❌ Error: {error}</p>;
            }

            if (user) {
                return (
                    <div>
                        <p>✅ User authenticated successfully!</p>
                        <p>User: {user.name} ({user.email})</p>
                        <p>Role: {user.role}</p>
                        <p>Company: {user.company_name}</p>
                    </div>
                );
            }

            return <p>❌ No user found</p>;
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>