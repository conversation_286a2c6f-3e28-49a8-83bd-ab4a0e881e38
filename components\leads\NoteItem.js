function NoteItem({ note, onEdit, onDelete }) {
    const [isEditing, setIsEditing] = React.useState(false);
    const [editContent, setEditContent] = React.useState(
        note.content || note.objectData?.content || ''
    );

    try {
        const handleSave = () => {
            if (onEdit && editContent.trim()) {
                onEdit(note.id || note.objectId, editContent.trim());
            }
            setIsEditing(false);
        };

        const handleCancel = () => {
            setEditContent(note.content || note.objectData?.content || '');
            setIsEditing(false);
        };

        const handleDelete = () => {
            if (onDelete) {
                onDelete(note.id || note.objectId);
            }
        };

        const noteData = note.objectData || note;

        return (
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                {isEditing ? (
                    <div className="space-y-3">
                        <textarea
                            value={editContent}
                            onChange={(e) => setEditContent(e.target.value)}
                            rows={4}
                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Enter your note..."
                        />
                        <div className="flex space-x-2">
                            <button
                                onClick={handleSave}
                                disabled={!editContent.trim()}
                                className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Save
                            </button>
                            <button
                                onClick={handleCancel}
                                className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                ) : (
                    <>
                        <div className="flex justify-between items-start">
                            <p className="text-gray-900 whitespace-pre-line flex-1">
                                {noteData.content}
                            </p>
                            <div className="flex space-x-1 ml-4">
                                <button
                                    onClick={() => setIsEditing(true)}
                                    className="text-gray-400 hover:text-blue-600 text-sm"
                                    title="Edit Note"
                                >
                                    <i className="fas fa-edit"></i>
                                </button>
                                <button
                                    onClick={handleDelete}
                                    className="text-gray-400 hover:text-red-600 text-sm"
                                    title="Delete Note"
                                >
                                    <i className="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                            {noteData.createdAt ? new Date(noteData.createdAt).toLocaleString() : 'No date'}
                            {noteData.updatedAt && noteData.updatedAt !== noteData.createdAt && (
                                <span className="ml-2">(edited)</span>
                            )}
                        </p>
                    </>
                )}
            </div>
        );
    } catch (error) {
        console.error('NoteItem component error:', error);
        return (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">Error loading note item</p>
            </div>
        );
    }
}

// Make component globally available
window.NoteItem = NoteItem;