// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Core business models
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  status        UserStatus @default(ACTIVE)
  
  // Company relationship
  companyId     String?
  company       Company?  @relation(fields: [companyId], references: [id])
  ownedCompany  Company?  @relation("CompanyOwner")
  
  // Permissions and settings
  permissions   Json?
  settings      Json?
  lastLoginAt   DateTime?
  loginCount    Int       @default(0)
  
  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // NextAuth.js relations
  accounts      Account[]
  sessions      Session[]
  
  // Business relations
  createdCustomers    Customer[]
  createdLeads        Lead[]
  createdQuotations   Quotation[]
  createdInvoices     Invoice[]
  createdContracts    Contract[]
  createdItems        Item[]
  activities          Activity[]
  tasks               Task[]
  notes               Note[]
  
  @@map("users")
}

model Company {
  id              String    @id @default(cuid())
  name            String
  email           String?
  phone           String?
  address         String?
  website         String?
  logo            String?
  
  // Business details
  industry        String?
  size            CompanySize?
  businessType    String?
  
  // Settings
  settings        Json?
  branding        Json?
  
  // Subscription
  subscriptionId  String?   @unique
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])
  
  // Owner and members
  ownerId         String    @unique
  owner           User      @relation("CompanyOwner", fields: [ownerId], references: [id])
  members         User[]
  
  // Status
  status          CompanyStatus @default(ACTIVE)
  
  // Timestamps
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Business relations
  customers       Customer[]
  leads           Lead[]
  quotations      Quotation[]
  invoices        Invoice[]
  contracts       Contract[]
  items           Item[]
  activities      Activity[]
  tasks           Task[]
  notes           Note[]
  
  @@map("companies")
}

model BusinessType {
  id                String   @id
  name              String
  description       String?
  icon              String?
  color             String?
  
  // Configuration
  defaultModules    Json?
  defaultCategories Json?
  defaultFeatures   Json?
  defaultTemplates  Json?
  
  // Status
  isActive          Boolean  @default(true)
  sortOrder         Int      @default(0)
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("business_types")
}

model PricingPlan {
  id                String   @id @default(cuid())
  name              String
  description       String?
  shortDescription  String?
  
  // Pricing
  priceMonthly      Decimal  @default(0)
  priceYearly       Decimal  @default(0)
  currency          String   @default("USD")
  
  // Trial
  trialDays         Int      @default(14)
  isTrialAvailable  Boolean  @default(true)
  
  // Features and limits
  features          Json?
  limits            Json?
  businessTypes     Json?
  
  // Display
  isVisible         Boolean  @default(true)
  isPopular         Boolean  @default(false)
  sortOrder         Int      @default(0)
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  subscriptions     Subscription[]
  
  @@map("pricing_plans")
}

model Subscription {
  id                String   @id @default(cuid())
  
  // Plan details
  planId            String
  plan              PricingPlan @relation(fields: [planId], references: [id])
  
  // Status and billing
  status            SubscriptionStatus @default(TRIAL)
  billingCycle      BillingCycle @default(MONTHLY)
  price             Decimal      @default(0)
  currency          String       @default("USD")
  
  // Trial management
  trialStartDate    DateTime?
  trialEndDate      DateTime?
  trialExtendedDays Int          @default(0)
  
  // Subscription dates
  startDate         DateTime?
  endDate           DateTime?
  nextBillingDate   DateTime?
  
  // Payment
  paymentMethod     String?
  paymentGateway    String?
  gatewaySubscriptionId String?
  lastPaymentDate   DateTime?
  lastPaymentAmount Decimal?
  
  // Usage and features
  features          Json?
  limits            Json?
  usage             Json?
  
  // Metadata
  notes             String?
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  company           Company?
  transactions      PaymentTransaction[]
  
  @@map("subscriptions")
}

model PaymentTransaction {
  id                String   @id @default(cuid())
  
  // Transaction details
  amount            Decimal
  currency          String   @default("USD")
  status            PaymentStatus @default(PENDING)
  type              PaymentType
  
  // Gateway details
  gateway           String
  gatewayTransactionId String?
  gatewayResponse   Json?
  
  // Subscription relation
  subscriptionId    String?
  subscription      Subscription? @relation(fields: [subscriptionId], references: [id])
  
  // Invoice relation
  invoiceId         String?
  invoice           Invoice? @relation(fields: [invoiceId], references: [id])
  
  // Metadata
  description       String?
  metadata          Json?
  
  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("payment_transactions")
}

// CRM Models
model Customer {
  id            String   @id @default(cuid())
  
  // Basic info
  name          String
  email         String?
  phone         String?
  company       String?
  
  // Address
  address       String?
  city          String?
  state         String?
  country       String?
  postalCode    String?
  
  // Business details
  industry      String?
  website       String?
  notes         String?
  tags          String[]
  
  // Status
  status        CustomerStatus @default(ACTIVE)
  
  // Company relation
  companyId     String
  companyRef    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById   String
  createdBy     User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  leads         Lead[]
  quotations    Quotation[]
  invoices      Invoice[]
  contracts     Contract[]
  activities    Activity[]
  
  @@map("customers")
}

model Lead {
  id            String   @id @default(cuid())
  
  // Basic info
  name          String
  email         String?
  phone         String?
  company       String?
  
  // Lead details
  source        String?
  status        LeadStatus @default(NEW)
  value         Decimal?   @default(0)
  probability   Int?       @default(0)
  
  // Additional info
  notes         String?
  tags          String[]
  
  // Customer relation
  customerId    String?
  customer      Customer? @relation(fields: [customerId], references: [id])
  
  // Company relation
  companyId     String
  companyRef    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById   String
  createdBy     User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  activities    Activity[]
  tasks         Task[]
  notes         Note[]
  quotations    Quotation[]
  
  @@map("leads")
}

model Item {
  id            String   @id @default(cuid())
  
  // Basic info
  name          String
  description   String?
  sku           String?
  
  // Pricing
  price         Decimal  @default(0)
  cost          Decimal  @default(0)
  
  // Categorization
  category      String?
  subcategory   String?
  
  // Status
  status        ItemStatus @default(ACTIVE)
  
  // Company relation
  companyId     String
  companyRef    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById   String
  createdBy     User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  quotationItems QuotationItem[]
  invoiceItems   InvoiceItem[]
  
  @@map("items")
}

model Quotation {
  id              String   @id @default(cuid())
  
  // Quotation details
  quotationNumber String   @unique
  title           String?
  
  // Customer relation
  customerId      String?
  customer        Customer? @relation(fields: [customerId], references: [id])
  
  // Lead relation
  leadId          String?
  lead            Lead?    @relation(fields: [leadId], references: [id])
  
  // Financial
  subtotal        Decimal  @default(0)
  taxRate         Decimal  @default(0)
  taxAmount       Decimal  @default(0)
  total           Decimal  @default(0)
  
  // Status and dates
  status          QuotationStatus @default(DRAFT)
  validUntil      DateTime?
  
  // Additional info
  notes           String?
  terms           String?
  
  // Company relation
  companyId       String
  companyRef      Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById     String
  createdBy       User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  items           QuotationItem[]
  invoices        Invoice[]
  
  @@map("quotations")
}

model QuotationItem {
  id            String   @id @default(cuid())
  
  // Item details
  itemId        String?
  item          Item?    @relation(fields: [itemId], references: [id])
  
  // Custom item details (if not from catalog)
  name          String
  description   String?
  
  // Pricing
  quantity      Decimal  @default(1)
  unitPrice     Decimal  @default(0)
  total         Decimal  @default(0)
  
  // Quotation relation
  quotationId   String
  quotation     Quotation @relation(fields: [quotationId], references: [id], onDelete: Cascade)
  
  @@map("quotation_items")
}

model Invoice {
  id              String   @id @default(cuid())
  
  // Invoice details
  invoiceNumber   String   @unique
  title           String?
  
  // Customer relation
  customerId      String?
  customer        Customer? @relation(fields: [customerId], references: [id])
  
  // Quotation relation
  quotationId     String?
  quotation       Quotation? @relation(fields: [quotationId], references: [id])
  
  // Financial
  subtotal        Decimal  @default(0)
  taxRate         Decimal  @default(0)
  taxAmount       Decimal  @default(0)
  total           Decimal  @default(0)
  paidAmount      Decimal  @default(0)
  
  // Status and dates
  status          InvoiceStatus @default(DRAFT)
  dueDate         DateTime?
  paidDate        DateTime?
  
  // Additional info
  notes           String?
  terms           String?
  
  // Company relation
  companyId       String
  companyRef      Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById     String
  createdBy       User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  items           InvoiceItem[]
  transactions    PaymentTransaction[]
  
  @@map("invoices")
}

model InvoiceItem {
  id            String   @id @default(cuid())
  
  // Item details
  itemId        String?
  item          Item?    @relation(fields: [itemId], references: [id])
  
  // Custom item details (if not from catalog)
  name          String
  description   String?
  
  // Pricing
  quantity      Decimal  @default(1)
  unitPrice     Decimal  @default(0)
  total         Decimal  @default(0)
  
  // Invoice relation
  invoiceId     String
  invoice       Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  
  @@map("invoice_items")
}

model Contract {
  id              String   @id @default(cuid())
  
  // Contract details
  contractNumber  String   @unique
  title           String
  
  // Customer relation
  customerId      String?
  customer        Customer? @relation(fields: [customerId], references: [id])
  
  // Contract details
  value           Decimal?  @default(0)
  status          ContractStatus @default(DRAFT)
  
  // Dates
  startDate       DateTime?
  endDate         DateTime?
  signedDate      DateTime?
  
  // Content
  content         String?
  terms           String?
  notes           String?
  
  // Company relation
  companyId       String
  companyRef      Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById     String
  createdBy       User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@map("contracts")
}

// Activity and Task Management
model Activity {
  id            String   @id @default(cuid())
  
  // Activity details
  type          ActivityType
  title         String
  description   String?
  
  // Relations
  customerId    String?
  customer      Customer? @relation(fields: [customerId], references: [id])
  
  leadId        String?
  lead          Lead?     @relation(fields: [leadId], references: [id])
  
  // Company relation
  companyId     String
  companyRef    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById   String
  createdBy     User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@map("activities")
}

model Task {
  id            String   @id @default(cuid())
  
  // Task details
  title         String
  description   String?
  status        TaskStatus @default(PENDING)
  priority      TaskPriority @default(MEDIUM)
  
  // Dates
  dueDate       DateTime?
  completedDate DateTime?
  
  // Relations
  leadId        String?
  lead          Lead?     @relation(fields: [leadId], references: [id])
  
  // Company relation
  companyId     String
  companyRef    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Assigned to
  assignedToId  String
  assignedTo    User    @relation(fields: [assignedToId], references: [id])
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@map("tasks")
}

model Note {
  id            String   @id @default(cuid())
  
  // Note details
  content       String
  
  // Relations
  leadId        String?
  lead          Lead?     @relation(fields: [leadId], references: [id])
  
  // Company relation
  companyId     String
  companyRef    Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  
  // Created by
  createdById   String
  createdBy     User    @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@map("notes")
}

// System models
model AuditLog {
  id            String   @id @default(cuid())
  
  // Action details
  action        String
  entityType    String
  entityId      String?
  
  // User details
  userId        String?
  userEmail     String?
  userRole      String?
  
  // Request details
  ipAddress     String?
  userAgent     String?
  
  // Changes
  oldValues     Json?
  newValues     Json?
  
  // Metadata
  metadata      Json?
  
  // Timestamps
  createdAt     DateTime @default(now())
  
  @@map("audit_logs")
}

model SystemSetting {
  id            String   @id @default(cuid())
  key           String   @unique
  value         Json
  description   String?
  category      String?
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@map("system_settings")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  USER
  VIEWER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum CompanyStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TRIAL
}

enum SubscriptionStatus {
  TRIAL
  ACTIVE
  CANCELLED
  EXPIRED
  SUSPENDED
  PAST_DUE
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum PaymentType {
  SUBSCRIPTION
  INVOICE
  ONE_TIME
}

enum CustomerStatus {
  ACTIVE
  INACTIVE
  PROSPECT
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum ItemStatus {
  ACTIVE
  INACTIVE
  DISCONTINUED
}

enum QuotationStatus {
  DRAFT
  SENT
  VIEWED
  ACCEPTED
  REJECTED
  EXPIRED
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  OVERDUE
  CANCELLED
}

enum ContractStatus {
  DRAFT
  SENT
  SIGNED
  ACTIVE
  EXPIRED
  CANCELLED
}

enum ActivityType {
  CALL
  EMAIL
  MEETING
  NOTE
  TASK
  FOLLOW_UP
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
