<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Leads CRUD Operations</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <!-- Mock API functions -->
    <script>
        // Mock window.getApiUrl function
        window.getApiUrl = (endpoint) => `/api${endpoint}`;
        
        // Mock localStorage for auth token
        if (!localStorage.getItem('authToken')) {
            localStorage.setItem('authToken', 'mock-token-123');
        }
    </script>

    <!-- Load components in correct order -->
    <script type="text/babel" src="components/leads/ActivityItem.js"></script>
    <script type="text/babel" src="components/leads/TaskItem.js"></script>
    <script type="text/babel" src="components/leads/NoteItem.js"></script>
    <script type="text/babel" src="components/leads/TagInput.js"></script>
    <script type="text/babel" src="components/leads/LeadActivity.js"></script>
    <script type="text/babel" src="components/leads/LeadTasks.js"></script>
    <script type="text/babel" src="components/leads/LeadNotes.js"></script>

    <script type="text/babel">
        const { useState } = React;

        function TestLeadsCRUD() {
            const [activeTab, setActiveTab] = useState('activities');
            const mockLeadId = 'test-lead-123';

            return (
                <div className="min-h-screen bg-gray-100 py-8">
                    <div className="max-w-6xl mx-auto px-4">
                        <div className="bg-white rounded-lg shadow-lg p-6">
                            <h1 className="text-2xl font-bold text-gray-900 mb-6">
                                <i className="fas fa-test-tube mr-2"></i>
                                Test Leads CRUD Operations
                            </h1>

                            <div className="mb-6">
                                <div className="border-b border-gray-200">
                                    <nav className="-mb-px flex space-x-8">
                                        <button
                                            onClick={() => setActiveTab('activities')}
                                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                                activeTab === 'activities'
                                                    ? 'border-blue-500 text-blue-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                            }`}
                                        >
                                            <i className="fas fa-clock mr-2"></i>
                                            Activities
                                        </button>
                                        <button
                                            onClick={() => setActiveTab('tasks')}
                                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                                activeTab === 'tasks'
                                                    ? 'border-blue-500 text-blue-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                            }`}
                                        >
                                            <i className="fas fa-tasks mr-2"></i>
                                            Tasks
                                        </button>
                                        <button
                                            onClick={() => setActiveTab('notes')}
                                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                                activeTab === 'notes'
                                                    ? 'border-blue-500 text-blue-600'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                            }`}
                                        >
                                            <i className="fas fa-sticky-note mr-2"></i>
                                            Notes
                                        </button>
                                    </nav>
                                </div>
                            </div>

                            <div className="mt-6">
                                {activeTab === 'activities' && (
                                    <div>
                                        <h2 className="text-lg font-medium text-gray-900 mb-4">Activities Test</h2>
                                        <LeadActivity leadId={mockLeadId} />
                                    </div>
                                )}

                                {activeTab === 'tasks' && (
                                    <div>
                                        <h2 className="text-lg font-medium text-gray-900 mb-4">Tasks Test</h2>
                                        <LeadTasks leadId={mockLeadId} />
                                    </div>
                                )}

                                {activeTab === 'notes' && (
                                    <div>
                                        <h2 className="text-lg font-medium text-gray-900 mb-4">Notes Test</h2>
                                        <LeadNotes leadId={mockLeadId} />
                                    </div>
                                )}
                            </div>

                            <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
                                <div className="flex items-start">
                                    <i className="fas fa-info-circle text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 className="text-sm font-medium text-yellow-900">Test Instructions</h4>
                                        <ul className="text-sm text-yellow-700 mt-1 list-disc list-inside">
                                            <li>This page tests the CRUD operations for Activities, Tasks, and Notes</li>
                                            <li>All API calls will fail (expected) but components should render without errors</li>
                                            <li>Test the edit and delete buttons on each component</li>
                                            <li>Check that forms work and validation is in place</li>
                                            <li>Verify that error handling works properly</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<TestLeadsCRUD />);
    </script>
</body>
</html>