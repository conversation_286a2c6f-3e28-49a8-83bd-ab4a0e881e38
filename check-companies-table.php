<?php
require_once 'api/db-config.php';

echo "🔍 Checking Companies Table...\n\n";

try {
    $result = $conn->query('DESCRIBE companies');
    if ($result) {
        echo "Companies table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo $row['Field'] . ' - ' . $row['Type'] . ' - ' . $row['Null'] . ' - ' . ($row['Default'] ?? 'NULL') . "\n";
        }
    }
    
    echo "\nChecking existing companies:\n";
    $result = $conn->query('SELECT object_id, name, owner_id FROM companies LIMIT 5');
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "ID: " . $row['object_id'] . " | Name: " . $row['name'] . " | Owner: " . ($row['owner_id'] ?? 'NULL') . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
