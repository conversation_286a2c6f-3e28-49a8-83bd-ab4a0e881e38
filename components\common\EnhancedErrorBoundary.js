/**
 * Enhanced Error Boundary Component
 * Provides comprehensive error handling with user-friendly messages and recovery options
 */

class EnhancedErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true,
            errorId: 'error_' + Date.now()
        };
    }

    componentDidCatch(error, errorInfo) {
        // Log error details
        console.error('Error Boundary caught an error:', error, errorInfo);
        
        this.setState({
            error: error,
            errorInfo: errorInfo
        });

        // Send error to logging service
        this.logErrorToService(error, errorInfo);
    }

    logErrorToService = async (error, errorInfo) => {
        try {
            await fetch('/api/api.php/error-log', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    error: {
                        message: error.message,
                        stack: error.stack,
                        name: error.name
                    },
                    errorInfo: errorInfo,
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    errorId: this.state.errorId
                })
            });
        } catch (logError) {
            console.error('Failed to log error to service:', logError);
        }
    };

    handleRetry = () => {
        this.setState({
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        });
    };

    handleReload = () => {
        window.location.reload();
    };

    handleGoHome = () => {
        window.location.href = '/';
    };

    render() {
        if (this.state.hasError) {
            const { error, errorInfo, errorId } = this.state;
            const { fallback: CustomFallback } = this.props;

            // Use custom fallback if provided
            if (CustomFallback) {
                return (
                    <CustomFallback
                        error={error}
                        errorInfo={errorInfo}
                        errorId={errorId}
                        onRetry={this.handleRetry}
                        onReload={this.handleReload}
                        onGoHome={this.handleGoHome}
                    />
                );
            }

            // Default error UI
            return (
                <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
                    <div className="sm:mx-auto sm:w-full sm:max-w-md">
                        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                            <div className="text-center">
                                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                                    <i className="fas fa-exclamation-triangle text-red-600"></i>
                                </div>
                                
                                <h2 className="text-lg font-medium text-gray-900 mb-2">
                                    Something went wrong
                                </h2>
                                
                                <p className="text-sm text-gray-600 mb-6">
                                    We're sorry, but something unexpected happened. Our team has been notified.
                                </p>

                                {/* Error details for development */}
                                {process.env.NODE_ENV === 'development' && error && (
                                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-left">
                                        <h3 className="text-sm font-medium text-red-800 mb-2">
                                            Error Details (Development Mode)
                                        </h3>
                                        <p className="text-xs text-red-700 font-mono mb-2">
                                            {error.message}
                                        </p>
                                        {errorInfo?.componentStack && (
                                            <details className="text-xs text-red-600">
                                                <summary className="cursor-pointer">Component Stack</summary>
                                                <pre className="mt-2 whitespace-pre-wrap">
                                                    {errorInfo.componentStack}
                                                </pre>
                                            </details>
                                        )}
                                    </div>
                                )}

                                <div className="space-y-3">
                                    <button
                                        onClick={this.handleRetry}
                                        className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        <i className="fas fa-redo mr-2"></i>
                                        Try Again
                                    </button>
                                    
                                    <button
                                        onClick={this.handleReload}
                                        className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        <i className="fas fa-refresh mr-2"></i>
                                        Reload Page
                                    </button>
                                    
                                    <button
                                        onClick={this.handleGoHome}
                                        className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        <i className="fas fa-home mr-2"></i>
                                        Go to Home
                                    </button>
                                </div>

                                {errorId && (
                                    <p className="mt-4 text-xs text-gray-500">
                                        Error ID: {errorId}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

// Enhanced Error Message Component
window.EnhancedErrorMessage = function EnhancedErrorMessage({ 
    error, 
    onRetry, 
    onDismiss,
    type = 'error',
    title,
    showDetails = false 
}) {
    const [showFullError, setShowFullError] = React.useState(false);
    
    const getErrorMessage = (error) => {
        if (typeof error === 'string') return error;
        if (error?.message) return error.message;
        if (error?.error) return error.error;
        return 'An unexpected error occurred';
    };
    
    const getErrorType = (type) => {
        const types = {
            error: {
                bgColor: 'bg-red-50',
                borderColor: 'border-red-200',
                textColor: 'text-red-700',
                iconColor: 'text-red-500',
                icon: 'fas fa-exclamation-circle'
            },
            warning: {
                bgColor: 'bg-yellow-50',
                borderColor: 'border-yellow-200',
                textColor: 'text-yellow-700',
                iconColor: 'text-yellow-500',
                icon: 'fas fa-exclamation-triangle'
            },
            info: {
                bgColor: 'bg-blue-50',
                borderColor: 'border-blue-200',
                textColor: 'text-blue-700',
                iconColor: 'text-blue-500',
                icon: 'fas fa-info-circle'
            }
        };
        return types[type] || types.error;
    };
    
    const errorType = getErrorType(type);
    const errorMessage = getErrorMessage(error);
    
    return (
        <div className={`${errorType.bgColor} border ${errorType.borderColor} rounded-md p-4 my-4`}>
            <div className="flex">
                <div className="flex-shrink-0">
                    <i className={`${errorType.icon} ${errorType.iconColor}`}></i>
                </div>
                <div className="ml-3 flex-1">
                    {title && (
                        <h3 className={`text-sm font-medium ${errorType.textColor} mb-1`}>
                            {title}
                        </h3>
                    )}
                    <p className={`text-sm ${errorType.textColor}`}>
                        {errorMessage}
                    </p>
                    
                    {showDetails && error?.stack && (
                        <div className="mt-2">
                            <button
                                onClick={() => setShowFullError(!showFullError)}
                                className={`text-xs ${errorType.textColor} underline hover:no-underline`}
                            >
                                {showFullError ? 'Hide' : 'Show'} technical details
                            </button>
                            {showFullError && (
                                <pre className={`mt-2 text-xs ${errorType.textColor} bg-white p-2 rounded border overflow-auto max-h-32`}>
                                    {error.stack}
                                </pre>
                            )}
                        </div>
                    )}
                </div>
                <div className="ml-auto pl-3">
                    <div className="flex space-x-2">
                        {onRetry && (
                            <button
                                onClick={onRetry}
                                className={`text-sm ${errorType.textColor} hover:underline`}
                            >
                                Retry
                            </button>
                        )}
                        {onDismiss && (
                            <button
                                onClick={onDismiss}
                                className={`${errorType.textColor} hover:${errorType.textColor.replace('700', '900')}`}
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

// Loading State Component
window.EnhancedLoadingSpinner = function EnhancedLoadingSpinner({ 
    size = 'md', 
    text = 'Loading...', 
    overlay = false,
    color = 'blue' 
}) {
    const sizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-8 w-8',
        lg: 'h-12 w-12',
        xl: 'h-16 w-16'
    };
    
    const colorClasses = {
        blue: 'border-blue-600',
        green: 'border-green-600',
        red: 'border-red-600',
        gray: 'border-gray-600'
    };
    
    const spinner = (
        <div className="flex flex-col items-center justify-center">
            <div className={`animate-spin rounded-full ${sizeClasses[size]} border-b-2 ${colorClasses[color]}`}></div>
            {text && <p className="mt-2 text-gray-600 text-sm">{text}</p>}
        </div>
    );
    
    if (overlay) {
        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white p-6 rounded-lg shadow-lg">
                    {spinner}
                </div>
            </div>
        );
    }
    
    return spinner;
};

// Make components globally available
window.EnhancedErrorBoundary = EnhancedErrorBoundary;