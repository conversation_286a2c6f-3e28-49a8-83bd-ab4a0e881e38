function LeadForm({ lead, onSubmit, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            name: lead?.objectData?.name || '',
            email: lead?.objectData?.email || '',
            phone: lead?.objectData?.phone || '',
            company: lead?.objectData?.company || '',
            source: lead?.objectData?.source || 'website',
            status: lead?.objectData?.status || 'new',
            assignedTo: lead?.objectData?.assignedTo || '',
            value: lead?.objectData?.value || 0,
            notes: lead?.objectData?.notes || '',
            priority: lead?.objectData?.priority || 'medium',
            tags: lead?.objectData?.tags || [],
            followUpDate: lead?.objectData?.followUpDate || ''
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [autoSaving, setAutoSaving] = React.useState(false);
        const [lastSaved, setLastSaved] = React.useState(null);
        const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);

        // Auto-save functionality
        React.useEffect(() => {
            if (!lead || !hasUnsavedChanges) return;

            const timeoutId = setTimeout(async () => {
                await autoSave();
            }, 2000); // Auto-save after 2 seconds of inactivity

            return () => clearTimeout(timeoutId);
        }, [formData, hasUnsavedChanges]);

        const autoSave = async () => {
            if (!lead || !hasUnsavedChanges) return;

            try {
                setAutoSaving(true);
                const token = localStorage.getItem('authToken');

                const endpoint = `lead/${lead.objectId}`;
                const apiUrl = window.getApiUrl(endpoint);
                
                const response = await fetch(apiUrl, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'lead',
                        name: formData.name,
                        email: formData.email || null,
                        phone: formData.phone || null,
                        company: formData.company || null,
                        source: formData.source || 'website',
                        status: formData.status || 'new',
                        assignedTo: formData.assignedTo || null,
                        value: parseFloat(formData.value) || 0,
                        notes: formData.notes || '',
                        priority: formData.priority || 'medium',
                        tags: Array.isArray(formData.tags) ? formData.tags : [],
                        followUpDate: formData.followUpDate || null,
                        updatedAt: new Date().toISOString(),
                        companyId: window.APP_CONFIG.COMPANY_ID || null,
                        modifiedBy: window.APP_CONFIG.USER_ID || null
                    })
                });

                const responseText = await response.text();
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    console.error('Auto-save: Failed to parse response as JSON:', responseText);
                    return;
                }

                if (response.ok && (result.success || result.objectId)) {
                    console.log('Auto-save successful:', result);
                    setLastSaved(new Date());
                    setHasUnsavedChanges(false);
                } else {
                    console.error('Auto-save failed:', result.error || result.message || 'Unknown error');
                }
            } catch (error) {
                console.error('Auto-save failed:', error);
            } finally {
                setAutoSaving(false);
            }
        };

        const validateForm = () => {
            console.log('Validating form data:', formData);
            const newErrors = {};

            // Required fields
            if (!formData.name?.trim()) {
                newErrors.name = 'Name is required';
            }

            // Email validation
            if (formData.email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(formData.email)) {
                    newErrors.email = 'Invalid email format';
                }
            }

            // Phone validation
            if (formData.phone) {
                // Allow various phone formats
                const phoneRegex = /^[+]?[\d\s-()]{8,}$/;
                if (!phoneRegex.test(formData.phone)) {
                    newErrors.phone = 'Invalid phone format';
                }
            }

            // Value validation
            const numericValue = parseFloat(formData.value);
            if (isNaN(numericValue) || numericValue < 0) {
                newErrors.value = 'Value must be a positive number';
            }

            // Follow-up date validation
            if (formData.followUpDate) {
                const followUpDate = new Date(formData.followUpDate);
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Compare dates only, not time
                
                if (followUpDate < today) {
                    newErrors.followUpDate = 'Follow-up date cannot be in the past';
                }
            }

            console.log('Validation errors:', newErrors);
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            console.log('Form submission started');
            
            if (!validateForm()) {
                console.log('Form validation failed:', errors);
                return;
            }

            try {
                setLoading(true);
                const leadData = {
                    type: 'lead',
                    name: formData.name,
                    email: formData.email || null,
                    phone: formData.phone || null,
                    company: formData.company || null,
                    source: formData.source || 'website',
                    status: formData.status || 'new',
                    assignedTo: formData.assignedTo || null,
                    value: parseFloat(formData.value) || 0,
                    notes: formData.notes || '',
                    priority: formData.priority || 'medium',
                    tags: Array.isArray(formData.tags) ? formData.tags : [],
                    followUpDate: formData.followUpDate || null,
                    updatedAt: new Date().toISOString(),
                    companyId: window.APP_CONFIG.COMPANY_ID || null,
                    createdBy: window.APP_CONFIG.USER_ID || null
                };

                const token = localStorage.getItem('authToken');
                if (!token) {
                    throw new Error('Authentication token not found. Please log in again.');
                }
                console.log('Using auth token:', token.substring(0, 10) + '...');

                let response;
                // Construct API endpoint
                const endpoint = lead && lead.objectId ? `lead/${lead.objectId}` : 'lead';
                const apiUrl = window.getApiUrl(endpoint);
                console.log('Making API request to:', apiUrl);

                if (lead && lead.objectId) {
                    // Update existing lead
                    response = await fetch(apiUrl, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(leadData)
                    });
                } else {
                    // Create new lead
                    response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(leadData)
                    });
                }

                console.log('API request sent:', {
                    url: response.url,
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                });
                
                const responseText = await response.text();
                console.log('Raw API Response:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('Parsed API Response:', result);
                } catch (e) {
                    console.error('Failed to parse response as JSON:', e);
                    throw new Error(`Invalid API response format: ${responseText}`);
                }
                
                if (response.ok) {
                    console.log('Lead saved successfully:', result);
                    // Pass the created/updated lead data back to parent
                    if (typeof onSubmit === 'function') {
                        onSubmit(result.data || result.objectData || result);
                    }
                    // Clear form data for new leads
                    if (!lead) {
                        setFormData({
                            name: '',
                            email: '',
                            phone: '',
                            company: '',
                            source: 'website',
                            status: 'new',
                            assignedTo: '',
                            value: 0,
                            notes: '',
                            priority: 'medium',
                            tags: [],
                            followUpDate: ''
                        });
                    }
                } else {
                    const errorMessage = result.error || result.message || `Failed to save lead (${response.status})`;
                    console.error('Lead API Error:', errorMessage);
                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('Error saving lead:', error);
                setErrors({ submit: error.message || 'Failed to save lead' });
            } finally {
                setLoading(false);
            }
        };

        const handleInputChange = (e) => {
            const { name, value, type } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'number' ? parseFloat(value) || 0 : value
            }));

            // Clear field error
            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }

            // Mark as having unsaved changes
            setHasUnsavedChanges(true);
        };

        const handleTagsChange = (tags) => {
            setFormData(prev => ({
                ...prev,
                tags
            }));
        };

        return (
            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Auto-save indicator */}
                {lead && (
                    <div className="flex items-center justify-between text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded">
                        <div className="flex items-center space-x-2">
                            {autoSaving && (
                                <>
                                    <i className="fas fa-spinner fa-spin text-blue-500"></i>
                                    <span>Auto-saving...</span>
                                </>
                            )}
                            {!autoSaving && lastSaved && (
                                <>
                                    <i className="fas fa-check text-green-500"></i>
                                    <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
                                </>
                            )}
                            {!autoSaving && hasUnsavedChanges && (
                                <>
                                    <i className="fas fa-circle text-orange-500" style={{fontSize: '6px'}}></i>
                                    <span>Unsaved changes</span>
                                </>
                            )}
                        </div>
                        <div className="text-xs">
                            Auto-save enabled
                        </div>
                    </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                        label="Name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        error={errors.name}
                        required
                    />

                    <Input
                        label="Email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        error={errors.email}
                    />

                    <Input
                        label="Phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        error={errors.phone}
                    />

                    <Input
                        label="Company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                    />

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Source
                        </label>
                        <select
                            name="source"
                            value={formData.source}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="website">Website</option>
                            <option value="referral">Referral</option>
                            <option value="social">Social Media</option>
                            <option value="email">Email Campaign</option>
                            <option value="event">Event</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Status
                        </label>
                        <select
                            name="status"
                            value={formData.status}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="new">New</option>
                            <option value="contacted">Contacted</option>
                            <option value="qualified">Qualified</option>
                            <option value="proposal">Proposal</option>
                            <option value="negotiation">Negotiation</option>
                            <option value="won">Won</option>
                            <option value="lost">Lost</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Priority
                        </label>
                        <select
                            name="priority"
                            value={formData.priority}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>

                    <Input
                        label="Value"
                        name="value"
                        type="number"
                        value={formData.value}
                        onChange={handleInputChange}
                        error={errors.value}
                    />

                    <Input
                        label="Follow-up Date"
                        name="followUpDate"
                        type="date"
                        value={formData.followUpDate}
                        onChange={handleInputChange}
                    />

                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                            Tags
                        </label>
                        <div className="mt-1">
                            <TagInput
                                tags={formData.tags}
                                onChange={handleTagsChange}
                            />
                        </div>
                    </div>

                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                            Notes
                        </label>
                        <textarea
                            name="notes"
                            value={formData.notes || ''}
                            onChange={handleInputChange}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>
                </div>

                {errors.submit && (
                    <div className="text-red-600 text-sm">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {lead && lead.objectId ? 'Update Lead' : 'Create Lead'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('LeadForm component error:', error);
        reportError(error);
        return null;
    }
}
