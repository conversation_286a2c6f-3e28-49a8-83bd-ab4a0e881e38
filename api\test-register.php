<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        exit;
    }
    
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input. Raw: ' . $rawInput);
    }
    
    // Just return the input for testing
    echo json_encode([
        'success' => true,
        'message' => 'Test successful',
        'received_data' => $input,
        'required_fields_check' => [
            'name' => isset($input['name']) ? 'present' : 'missing',
            'email' => isset($input['email']) ? 'present' : 'missing',
            'password' => isset($input['password']) ? 'present' : 'missing',
            'company_name' => isset($input['company_name']) ? 'present' : 'missing'
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
