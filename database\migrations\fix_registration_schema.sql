-- Fix database schema for registration process

-- Add missing columns to companies table
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS size VARCHAR(50),
ADD COLUMN IF NOT EXISTS industry VARCHAR(100),
ADD COLUMN IF NOT EXISTS subscription_plan VARCHAR(50) DEFAULT 'basic';

-- Add missing columns to users table  
ALTER TABLE users
ADD COLUMN IF NOT EXISTS verification_token VARCHAR(100),
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Update subscriptions table to match registration expectations
ALTER TABLE subscriptions 
ADD COLUMN IF NOT EXISTS plan_id VARCHAR(50);

-- Create simple pricing plans
CREATE TABLE IF NOT EXISTS pricing_plans (
    id VARCHAR(50) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2) NOT NULL,
    features TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert simplified pricing plans
INSERT INTO pricing_plans (id, name, description, price_monthly, price_yearly, features, is_active) VALUES
('basic', 'Business Plan', 'Complete business management solution with all essential features', 500.00, 5000.00, 
'["Customer Management", "Invoice Generation", "Quotation Management", "Contract Management", "Lead Tracking", "Business Analytics", "Email Notifications", "Data Export", "Multi-user Access", "24/7 Support"]', 
TRUE)
ON DUPLICATE KEY UPDATE 
name = VALUES(name),
description = VALUES(description),
price_monthly = VALUES(price_monthly),
price_yearly = VALUES(price_yearly),
features = VALUES(features);
