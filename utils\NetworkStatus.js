/**
 * Network Status Utility
 * Monitors network connectivity and provides offline/online status
 */

window.NetworkStatus = (function() {
    // Private variables
    let isOnline = navigator.onLine;
    let listeners = [];
    
    // Initialize event listeners
    function init() {
        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        
        // Initial check
        checkConnection();
    }
    
    // Handle online event
    function handleOnline() {
        if (!isOnline) {
            isOnline = true;
            notifyListeners();
            
            // Show notification if NotificationManager is available
            if (window.NotificationManager) {
                window.NotificationManager.success('Your internet connection has been restored');
            }
        }
    }
    
    // Handle offline event
    function handleOffline() {
        if (isOnline) {
            isOnline = false;
            notifyListeners();
            
            // Show notification if NotificationManager is available
            if (window.NotificationManager) {
                window.NotificationManager.error(
                    'You are currently offline. Some features may be unavailable.',
                    { duration: 0 } // Persistent notification
                );
            }
        }
    }
    
    // Check connection by making a small fetch request
    async function checkConnection() {
        try {
            // Try to fetch a small resource to verify connection
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);
            
            await fetch('/favicon.ico', { 
                method: 'HEAD',
                cache: 'no-store',
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            // If we get here, we're online
            if (!isOnline) {
                isOnline = true;
                notifyListeners();
            }
        } catch (error) {
            // If fetch fails, we might be offline
            if (isOnline) {
                isOnline = false;
                notifyListeners();
            }
        }
    }
    
    // Notify all listeners of status change
    function notifyListeners() {
        listeners.forEach(listener => {
            try {
                listener(isOnline);
            } catch (error) {
                console.error('Error in network status listener:', error);
            }
        });
    }
    
    /**
     * Add a listener for network status changes
     * @param {Function} listener - Callback function that receives isOnline boolean
     * @returns {Function} - Function to remove the listener
     */
    function addListener(listener) {
        if (typeof listener !== 'function') {
            console.error('Network status listener must be a function');
            return () => {};
        }
        
        listeners.push(listener);
        
        // Immediately notify with current status
        listener(isOnline);
        
        // Return function to remove listener
        return () => {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        };
    }
    
    // Initialize
    init();
    
    // Public API
    return {
        isOnline: () => isOnline,
        checkConnection,
        addListener
    };
})();