function ItemForm({ item, onSubmit, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            name: item && item.name ? item.name : '',
            description: item && item.description ? item.description : '',
            category: item && item.category ? item.category : '',
            subcategory: item && item.subcategory ? item.subcategory : '',
            sku: item && item.sku ? item.sku : '',
            price: item && item.price ? item.price : 0,
            costPrice: item && item.costPrice ? item.costPrice : 0,
            tax: item && item.tax ? item.tax : 18, // Default GST rate
            stockQuantity: item && item.stockQuantity ? item.stockQuantity : 0,
            unit: item && item.unit ? item.unit : 'piece',
            isActive: item && item.isActive !== false, // Default to true
            isRecurring: item && item.isRecurring ? item.isRecurring : false,
            recurringPeriod: item && item.recurringPeriod ? item.recurringPeriod : 'monthly',
            itemType: item && item.itemType ? item.itemType : 'product' // product, service, subscription
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [categories, setCategories] = React.useState([]);
        const [subcategories, setSubcategories] = React.useState([]);

        React.useEffect(() => {
            fetchCategories();
        }, []);

        React.useEffect(() => {
            if (formData.category) {
                fetchSubcategories(formData.category);
            } else {
                setSubcategories([]);
            }
        }, [formData.category]);

        const fetchCategories = async () => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/item_category`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setCategories(data.items || []);
                } else {
                    throw new Error('Failed to fetch categories');
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
                setCategories([]);
            }
        };

        const fetchSubcategories = async (categoryId) => {
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/item_subcategory:${categoryId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setSubcategories(data.items || []);
                } else {
                    throw new Error('Failed to fetch subcategories');
                }
            } catch (error) {
                console.error('Error fetching subcategories:', error);
                setSubcategories([]);
            }
        };

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : 
                       (type === 'number' ? parseFloat(value) || 0 : value)
            }));

            // Clear error for this field if it exists
            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.name) {
                newErrors.name = 'Item name is required';
            }
            if (!formData.category) {
                newErrors.category = 'Category is required';
            }
            if (formData.price < 0) {
                newErrors.price = 'Price cannot be negative';
            }
            if (formData.costPrice < 0) {
                newErrors.costPrice = 'Cost price cannot be negative';
            }
            
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                const itemData = {
                    ...formData,
                    updatedAt: new Date().toISOString()
                };

                const token = localStorage.getItem('authToken');
                let response;

                if (item && item.id) {
                    // Update existing item
                    response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/item/${item.id}`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(itemData)
                    });
                } else {
                    // Create new item
                    response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/item`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(itemData)
                    });
                }

                if (response.ok) {
                    const result = await response.json();
                    // Check for both success property (some APIs) and objectId property (CRUD API)
                    if (result.success || result.objectId) {
                        onSubmit();
                    } else {
                        throw new Error(result.message || result.error || 'Failed to save item');
                    }
                } else {
                    const errorText = await response.text();
                    console.error('Item API Error:', response.status, errorText);

                    // Try to parse error response
                    let errorMessage = `Failed to save item (${response.status})`;
                    try {
                        const errorData = JSON.parse(errorText);
                        errorMessage = errorData.error || errorData.message || errorMessage;
                    } catch (e) {
                        errorMessage += `: ${errorText}`;
                    }

                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('Error saving item:', error);
                setErrors({ submit: 'Failed to save item' });
            } finally {
                setLoading(false);
            }
        };

        return (
            <form data-name="item-form" onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Item Name <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                        {errors.name && (
                            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            SKU / Item Code
                        </label>
                        <input
                            type="text"
                            name="sku"
                            value={formData.sku}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Item Type
                        </label>
                        <select
                            name="itemType"
                            value={formData.itemType}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="product">Product</option>
                            <option value="service">Service</option>
                            <option value="subscription">Subscription</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Category <span className="text-red-500">*</span>
                        </label>
                        <select
                            name="category"
                            value={formData.category}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="">Select Category</option>
                            {categories.map(category => (
                                <option key={category.objectId} value={category.objectId}>
                                    {category.objectData.name}
                                </option>
                            ))}
                        </select>
                        {errors.category && (
                            <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Subcategory
                        </label>
                        <select
                            name="subcategory"
                            value={formData.subcategory}
                            onChange={handleInputChange}
                            disabled={!formData.category}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="">Select Subcategory</option>
                            {subcategories.map(subcategory => (
                                <option key={subcategory.objectId} value={subcategory.objectId}>
                                    {subcategory.objectData.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Selling Price
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 sm:text-sm">₹</span>
                            </div>
                            <input
                                type="number"
                                name="price"
                                value={formData.price}
                                onChange={handleInputChange}
                                min="0"
                                step="0.01"
                                className="block w-full pl-7 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                        {errors.price && (
                            <p className="mt-1 text-sm text-red-600">{errors.price}</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Cost Price
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 sm:text-sm">₹</span>
                            </div>
                            <input
                                type="number"
                                name="costPrice"
                                value={formData.costPrice}
                                onChange={handleInputChange}
                                min="0"
                                step="0.01"
                                className="block w-full pl-7 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                        {errors.costPrice && (
                            <p className="mt-1 text-sm text-red-600">{errors.costPrice}</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Tax Rate (%)
                        </label>
                        <input
                            type="number"
                            name="tax"
                            value={formData.tax}
                            onChange={handleInputChange}
                            min="0"
                            max="100"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>

                    {formData.itemType !== 'service' && (
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Stock Quantity
                            </label>
                            <input
                                type="number"
                                name="stockQuantity"
                                value={formData.stockQuantity}
                                onChange={handleInputChange}
                                min="0"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                    )}

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Unit
                        </label>
                        <select
                            name="unit"
                            value={formData.unit}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="piece">Piece</option>
                            <option value="kg">Kilogram</option>
                            <option value="g">Gram</option>
                            <option value="liter">Liter</option>
                            <option value="ml">Milliliter</option>
                            <option value="meter">Meter</option>
                            <option value="cm">Centimeter</option>
                            <option value="hour">Hour</option>
                            <option value="day">Day</option>
                            <option value="month">Month</option>
                            <option value="project">Project</option>
                            <option value="session">Session</option>
                        </select>
                    </div>

                    <div className="col-span-full">
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                name="isRecurring"
                                id="isRecurring"
                                checked={formData.isRecurring}
                                onChange={handleInputChange}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-900">
                                Recurring Item (Subscription/Billing)
                            </label>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                            Enable for recurring services like monthly subscriptions
                        </p>
                    </div>

                    {formData.isRecurring && (
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Billing Period
                            </label>
                            <select
                                name="recurringPeriod"
                                value={formData.recurringPeriod}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="quarterly">Quarterly</option>
                                <option value="biannually">Bi-annually</option>
                                <option value="annually">Annually</option>
                            </select>
                        </div>
                    )}

                    <div className="col-span-full">
                        <label className="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <textarea
                            name="description"
                            value={formData.description}
                            onChange={handleInputChange}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>

                    <div className="col-span-full">
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                name="isActive"
                                id="isActive"
                                checked={formData.isActive}
                                onChange={handleInputChange}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                                Active Item
                            </label>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                            Inactive items won't appear in quotations and invoices
                        </p>
                    </div>
                </div>

                {errors.submit && (
                    <div className="text-red-600 text-sm mt-2">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {item && item.id ? 'Update Item' : 'Create Item'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('ItemForm component error:', error);
        reportError(error);
        return null;
    }
}
