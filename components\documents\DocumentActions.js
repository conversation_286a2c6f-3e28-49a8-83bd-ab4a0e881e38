function DocumentActions({ onPrint, onDownloadPDF, onEdit, onDelete, onClose, documentType }) {
    try {
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const documentTypeName = documentType ? documentType.charAt(0).toUpperCase() + documentType.slice(1) : 'Document';

        return (
            <div data-name="document-actions" className="bg-white rounded-lg shadow-sm p-4 flex flex-wrap justify-end gap-3">
                <Button
                    variant="secondary"
                    icon="fas fa-print"
                    onClick={onPrint}
                >
                    Print
                </Button>
                <Button
                    variant="secondary"
                    icon="fas fa-download"
                    onClick={onDownloadPDF}
                >
                    Download PDF
                </Button>
                <Button
                    variant="secondary"
                    icon="fas fa-edit"
                    onClick={onEdit}
                >
                    Edit
                </Button>
                <Button
                    variant="danger"
                    icon="fas fa-trash"
                    onClick={() => setShowDeleteConfirm(true)}
                >
                    Delete
                </Button>
                <Button
                    variant="secondary"
                    icon="fas fa-times"
                    onClick={onClose}
                >
                    Close
                </Button>

                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to delete this {documentTypeName.toLowerCase()}? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={() => {
                                        setShowDeleteConfirm(false);
                                        onDelete();
                                    }}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('DocumentActions component error:', error);
        reportError(error);
        return null;
    }
}
