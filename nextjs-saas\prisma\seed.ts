import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create business types
  const businessTypes = [
    {
      id: 'retail',
      name: 'Retail Business',
      description: 'Perfect for retail stores, e-commerce, and product-based businesses',
      icon: 'fas fa-store',
      color: 'blue',
      defaultModules: ['inventory', 'sales', 'customers', 'reports'],
      defaultCategories: ['electronics', 'clothing', 'accessories', 'home'],
      defaultFeatures: ['inventory_management', 'pos_integration', 'customer_loyalty'],
      defaultTemplates: ['retail_invoice', 'sales_receipt', 'return_policy']
    },
    {
      id: 'healthcare',
      name: 'Healthcare Services',
      description: 'Tailored for clinics, hospitals, and healthcare providers',
      icon: 'fas fa-heartbeat',
      color: 'red',
      defaultModules: ['patients', 'appointments', 'prescriptions', 'billing'],
      defaultCategories: ['consultations', 'procedures', 'medications', 'lab_tests'],
      defaultFeatures: ['patient_records', 'appointment_scheduling', 'prescription_management'],
      defaultTemplates: ['prescription', 'medical_report', 'appointment_card']
    },
    {
      id: 'consulting',
      name: 'Consulting Services',
      description: 'Ideal for consultants, agencies, and professional services',
      icon: 'fas fa-briefcase',
      color: 'indigo',
      defaultModules: ['projects', 'clients', 'time_tracking', 'billing'],
      defaultCategories: ['strategy', 'marketing', 'finance', 'hr', 'technology'],
      defaultFeatures: ['project_management', 'time_billing', 'proposal_generation'],
      defaultTemplates: ['proposal', 'invoice', 'project_report']
    },
    {
      id: 'manufacturing',
      name: 'Manufacturing',
      description: 'Built for manufacturers and production companies',
      icon: 'fas fa-industry',
      color: 'orange',
      defaultModules: ['production', 'inventory', 'quality', 'suppliers'],
      defaultCategories: ['raw_materials', 'finished_goods', 'machinery', 'tools'],
      defaultFeatures: ['production_planning', 'quality_control', 'supplier_management'],
      defaultTemplates: ['production_order', 'quality_report', 'supplier_invoice']
    },
    {
      id: 'education',
      name: 'Education Services',
      description: 'Designed for schools, training centers, and educational institutions',
      icon: 'fas fa-graduation-cap',
      color: 'green',
      defaultModules: ['students', 'courses', 'instructors', 'assessments'],
      defaultCategories: ['academic', 'vocational', 'certification', 'online'],
      defaultFeatures: ['student_management', 'course_scheduling', 'grade_tracking'],
      defaultTemplates: ['certificate', 'transcript', 'course_outline']
    },
    {
      id: 'jewellery',
      name: 'Jewellery Business',
      description: 'Specialized for jewellery stores, designers, and manufacturers',
      icon: 'fas fa-gem',
      color: 'purple',
      defaultModules: ['inventory', 'sales', 'customers', 'repairs'],
      defaultCategories: ['gold', 'silver', 'platinum', 'diamonds', 'gemstones'],
      defaultFeatures: ['precious_metals_tracking', 'stone_certification', 'custom_design'],
      defaultTemplates: ['jewellery_invoice', 'repair_receipt', 'certification']
    }
  ]

  for (const businessType of businessTypes) {
    await prisma.businessType.upsert({
      where: { id: businessType.id },
      update: businessType,
      create: businessType
    })
  }

  // Create pricing plans
  const pricingPlans = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small businesses just getting started',
      shortDescription: 'Essential features for small teams',
      priceMonthly: 29,
      priceYearly: 290,
      trialDays: 14,
      features: [
        'Up to 5 users',
        '1,000 customers',
        'Basic CRM',
        'Invoice generation',
        'Email support'
      ],
      limits: {
        users: 5,
        customers: 1000,
        invoices: 100,
        storage: '1GB'
      },
      isVisible: true,
      sortOrder: 1
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Advanced features for growing businesses',
      shortDescription: 'Advanced features and integrations',
      priceMonthly: 79,
      priceYearly: 790,
      trialDays: 14,
      features: [
        'Up to 25 users',
        '10,000 customers',
        'Advanced CRM',
        'Custom templates',
        'API access',
        'Priority support'
      ],
      limits: {
        users: 25,
        customers: 10000,
        invoices: 1000,
        storage: '10GB'
      },
      isVisible: true,
      isPopular: true,
      sortOrder: 2
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Complete solution for large organizations',
      shortDescription: 'Unlimited access with premium support',
      priceMonthly: 199,
      priceYearly: 1990,
      trialDays: 30,
      features: [
        'Unlimited users',
        'Unlimited customers',
        'White-label solution',
        'Custom integrations',
        'Dedicated support',
        'SLA guarantee'
      ],
      limits: {
        users: -1,
        customers: -1,
        invoices: -1,
        storage: 'unlimited'
      },
      isVisible: true,
      sortOrder: 3
    }
  ]

  for (const plan of pricingPlans) {
    await prisma.pricingPlan.upsert({
      where: { id: plan.id },
      update: plan,
      create: plan
    })
  }

  // Create super admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Super Administrator',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      status: 'ACTIVE',
      emailVerified: new Date()
    }
  })

  // Create demo company
  const demoCompany = await prisma.company.upsert({
    where: { id: 'demo-company' },
    update: {},
    create: {
      id: 'demo-company',
      name: 'Demo Company',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Business Street, City, State 12345',
      website: 'https://demo.businesssaas.com',
      industry: 'Technology',
      size: 'SMALL',
      businessType: 'consulting',
      ownerId: superAdmin.id,
      status: 'ACTIVE'
    }
  })

  // Create demo user
  const demoUserPassword = await bcrypt.hash('demo123', 12)
  
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Demo User',
      email: '<EMAIL>',
      password: demoUserPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      emailVerified: new Date()
    }
  })

  // Create demo subscription
  const starterPlan = await prisma.pricingPlan.findUnique({
    where: { id: 'starter' }
  })

  if (starterPlan) {
    await prisma.subscription.upsert({
      where: { id: 'demo-subscription' },
      update: {},
      create: {
        id: 'demo-subscription',
        planId: starterPlan.id,
        status: 'TRIAL',
        billingCycle: 'MONTHLY',
        trialStartDate: new Date(),
        trialEndDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        features: starterPlan.features,
        limits: starterPlan.limits
      }
    })

    // Update company with subscription
    await prisma.company.update({
      where: { id: demoCompany.id },
      data: { subscriptionId: 'demo-subscription' }
    })
  }

  // Create sample customers
  const sampleCustomers = [
    {
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '******-0101',
      company: 'Acme Corporation',
      industry: 'Manufacturing',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    },
    {
      name: 'Tech Solutions Inc',
      email: '<EMAIL>',
      phone: '******-0102',
      company: 'Tech Solutions Inc',
      industry: 'Technology',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    }
  ]

  for (const customer of sampleCustomers) {
    await prisma.customer.create({ data: customer })
  }

  // Create sample items
  const sampleItems = [
    {
      name: 'Consulting Hours',
      description: 'Professional consulting services',
      price: 150,
      cost: 75,
      category: 'Services',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    },
    {
      name: 'Project Management',
      description: 'Complete project management service',
      price: 2500,
      cost: 1200,
      category: 'Services',
      status: 'ACTIVE',
      companyId: demoCompany.id,
      createdById: demoUser.id
    }
  ]

  for (const item of sampleItems) {
    await prisma.item.create({ data: item })
  }

  console.log('✅ Database seeded successfully!')
  console.log('')
  console.log('🔑 Login credentials:')
  console.log('Super Admin: <EMAIL> / admin123')
  console.log('Demo User: <EMAIL> / demo123')
  console.log('')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
