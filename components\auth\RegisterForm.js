// Make RegisterForm component globally available
window.RegisterForm = function RegisterForm({ onRegister, onSwitchToLogin }) {
    try {
        const [currentStep, setCurrentStep] = React.useState(1);
        const [formData, setFormData] = React.useState({
            // Personal Information
            name: '',
            email: '',
            password: '',
            confirmPassword: '',
            // Company Information
            companyName: '',
            companySize: '',
            industry: '',
            // Subscription
            selectedPlan: 'basic',
            acceptTerms: false
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [passwordStrength, setPasswordStrength] = React.useState(null);

        const companySizes = [
            { value: '1-10', label: '1-10 employees' },
            { value: '11-50', label: '11-50 employees' },
            { value: '51-200', label: '51-200 employees' },
            { value: '201-1000', label: '201-1000 employees' },
            { value: '1000+', label: '1000+ employees' }
        ];

        const industries = [
            'Technology', 'Healthcare', 'Finance', 'Education', 'Retail',
            'Manufacturing', 'Real Estate', 'Consulting', 'Marketing', 'Other'
        ];

        const plans = [
            {
                id: 'basic',
                name: 'Basic',
                price: 999,
                features: ['Up to 100 leads', '3 team members', 'Basic reporting', 'Email support']
            },
            {
                id: 'professional',
                name: 'Professional',
                price: 2499,
                features: ['Up to 1,000 leads', '10 team members', 'Advanced reporting', 'Priority support'],
                popular: true
            },
            {
                id: 'enterprise',
                name: 'Enterprise',
                price: 4999,
                features: ['Unlimited leads', 'Unlimited team members', 'Custom reporting', '24/7 support']
            }
        ];

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));

            // Check password strength in real-time
            if (name === 'password' && window.SecurityUtils) {
                const strength = window.SecurityUtils.validatePasswordStrength(value);
                setPasswordStrength(strength);
            }
        };

        const nextStep = () => {
            if (validateCurrentStep()) {
                setCurrentStep(prev => prev + 1);
            }
        };

        const prevStep = () => {
            setCurrentStep(prev => prev - 1);
        };

        const validateCurrentStep = () => {
            const newErrors = {};

            if (currentStep === 1) {
                // Personal Information validation
                if (!formData.name) newErrors.name = 'Name is required';
                if (!formData.email) {
                    newErrors.email = 'Email is required';
                } else if (!isEmailValid(formData.email)) {
                    newErrors.email = 'Invalid email format';
                }
                if (!formData.password) {
                    newErrors.password = 'Password is required';
                } else if (passwordStrength && !passwordStrength.isValid) {
                    newErrors.password = 'Password must meet security requirements';
                }
                if (formData.password !== formData.confirmPassword) {
                    newErrors.confirmPassword = 'Passwords do not match';
                }
            } else if (currentStep === 2) {
                // Company Information validation
                if (!formData.companyName) newErrors.companyName = 'Company name is required';
                if (!formData.companySize) newErrors.companySize = 'Company size is required';
                if (!formData.industry) newErrors.industry = 'Industry is required';
            } else if (currentStep === 3) {
                // Terms validation
                if (!formData.acceptTerms) newErrors.acceptTerms = 'You must accept the terms and conditions';
            }

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.name) {
                newErrors.name = 'Name is required';
            }
            if (!formData.email) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(formData.email)) {
                newErrors.email = 'Invalid email format';
            }
            if (!formData.password) {
                newErrors.password = 'Password is required';
            } else if (!isPasswordStrong(formData.password)) {
                newErrors.password = 'Password must be at least 8 characters with letters, numbers, and special characters';
            }
            if (formData.password !== formData.confirmPassword) {
                newErrors.confirmPassword = 'Passwords do not match';
            }
            if (!formData.companyName) {
                newErrors.companyName = 'Company name is required';
            }
            if (!formData.acceptTerms) {
                newErrors.acceptTerms = 'You must accept the terms and conditions';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                
                // Create user account
                const userResponse = await trickleCreateObject('auth', {
                    type: 'register',
                    name: formData.name,
                    email: formData.email,
                    password: formData.password
                });

                if (userResponse.objectData.token) {
                    // Store token
                    localStorage.setItem('authToken', userResponse.objectData.token);

                    // Create company
                    const companyResponse = await trickleCreateObject('company', {
                        name: formData.companyName,
                        ownerId: userResponse.objectData.user.id,
                        status: 'active',
                        createdAt: new Date().toISOString()
                    });

                    // Create default subscription (free trial)
                    await trickleCreateObject('subscription', {
                        userId: userResponse.objectData.user.id,
                        companyId: companyResponse.objectId,
                        planId: 'trial',
                        planName: 'Trial',
                        status: 'active',
                        startDate: new Date().toISOString(),
                        endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days
                        features: [
                            'Up to 100 leads',
                            '3 team members',
                            'Basic reporting',
                            'Email support',
                            '5GB storage'
                        ],
                        maxLeads: 100,
                        maxUsers: 3
                    });

                    onRegister({
                        user: userResponse.objectData.user,
                        company: companyResponse.objectData
                    });
                }
            } catch (error) {
                console.error('Registration error:', error);
                setErrors({ submit: 'Registration failed. Please try again.' });
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="register-form" className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div>
                        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Start your free trial
                        </h2>
                        <p className="mt-2 text-center text-sm text-gray-600">
                            Already have an account?{' '}
                            {onSwitchToLogin ? (
                                <button
                                    onClick={onSwitchToLogin}
                                    className="font-medium text-blue-600 hover:text-blue-500"
                                >
                                    Sign in
                                </button>
                            ) : (
                                <a href="/login" className="font-medium text-blue-600 hover:text-blue-500">
                                    Sign in
                                </a>
                            )}
                        </p>
                    </div>

                    <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                        <div className="rounded-md shadow-sm -space-y-px">
                            <div>
                                <label htmlFor="name" className="sr-only">Full Name</label>
                                <input
                                    id="name"
                                    name="name"
                                    type="text"
                                    required
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.name ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Full Name"
                                />
                                {errors.name && (
                                    <p className="mt-1 text-xs text-red-600">{errors.name}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="email" className="sr-only">Email address</label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    required
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.email ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Email address"
                                />
                                {errors.email && (
                                    <p className="mt-1 text-xs text-red-600">{errors.email}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="company-name" className="sr-only">Company Name</label>
                                <input
                                    id="company-name"
                                    name="companyName"
                                    type="text"
                                    required
                                    value={formData.companyName}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.companyName ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Company Name"
                                />
                                {errors.companyName && (
                                    <p className="mt-1 text-xs text-red-600">{errors.companyName}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="password" className="sr-only">Password</label>
                                <input
                                    id="password"
                                    name="password"
                                    type="password"
                                    required
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.password ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Password"
                                />
                                {errors.password && (
                                    <p className="mt-1 text-xs text-red-600">{errors.password}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="confirm-password" className="sr-only">Confirm Password</label>
                                <input
                                    id="confirm-password"
                                    name="confirmPassword"
                                    type="password"
                                    required
                                    value={formData.confirmPassword}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Confirm Password"
                                />
                                {errors.confirmPassword && (
                                    <p className="mt-1 text-xs text-red-600">{errors.confirmPassword}</p>
                                )}
                            </div>
                        </div>

                        <div className="flex items-center">
                            <input
                                id="accept-terms"
                                name="acceptTerms"
                                type="checkbox"
                                checked={formData.acceptTerms}
                                onChange={handleInputChange}
                                className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                                    errors.acceptTerms ? 'border-red-300' : ''
                                }`}
                            />
                            <label htmlFor="accept-terms" className="ml-2 block text-sm text-gray-900">
                                I accept the{' '}
                                <a href="/terms" className="font-medium text-blue-600 hover:text-blue-500">
                                    Terms and Conditions
                                </a>
                            </label>
                        </div>
                        {errors.acceptTerms && (
                            <p className="mt-1 text-xs text-red-600">{errors.acceptTerms}</p>
                        )}

                        {errors.submit && (
                            <div className="text-red-600 text-sm text-center">
                                {errors.submit}
                            </div>
                        )}

                        <div>
                            <Button
                                type="submit"
                                loading={loading}
                                disabled={loading}
                                className="w-full"
                            >
                                Start Free Trial
                            </Button>
                        </div>

                        <p className="mt-2 text-xs text-gray-500 text-center">
                            By signing up, you agree to our{' '}
                            <a href="/terms" className="text-blue-600 hover:text-blue-500">Terms of Service</a>
                            {' '}and{' '}
                            <a href="/privacy" className="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                        </p>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('RegisterForm component error:', error);
        reportError(error);
        return null;
    }
}
