// Business Types Manager Component for Super Admin
function BusinessTypesManager({ authContext, setNotification }) {
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [editingType, setEditingType] = React.useState(null);
    const [showModal, setShowModal] = React.useState(false);

    React.useEffect(() => {
        fetchBusinessTypes();
    }, []);

    const fetchBusinessTypes = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/business-types'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setBusinessTypes(data.data || []);
                }
            }
        } catch (error) {
            console.error('Error fetching business types:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load business types'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleCreateType = () => {
        setEditingType({
            id: '',
            name: '',
            description: '',
            icon: 'fas fa-building',
            color: 'blue',
            default_modules: '',
            default_categories: '',
            default_features: '',
            default_templates: '',
            is_active: true
        });
        setShowModal(true);
    };

    const handleEditType = (type) => {
        setEditingType({ ...type });
        setShowModal(true);
    };

    const handleSaveType = async () => {
        try {
            const url = editingType.id 
                ? window.getApiUrl(`/super-admin/business-types/${editingType.id}`)
                : window.getApiUrl('/super-admin/business-types');
            
            const method = editingType.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(editingType)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Business type ${editingType.id ? 'updated' : 'created'} successfully`
                    });
                    setShowModal(false);
                    setEditingType(null);
                    fetchBusinessTypes();
                } else {
                    throw new Error(data.message || 'Failed to save business type');
                }
            } else {
                throw new Error('Failed to save business type');
            }
        } catch (error) {
            console.error('Error saving business type:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to save business type'
            });
        }
    };

    const handleDeleteType = async (typeId) => {
        if (!confirm('Are you sure you want to delete this business type?')) return;

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/business-types/${typeId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: 'Business type deleted successfully'
                    });
                    fetchBusinessTypes();
                } else {
                    throw new Error(data.message || 'Failed to delete business type');
                }
            } else {
                throw new Error('Failed to delete business type');
            }
        } catch (error) {
            console.error('Error deleting business type:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to delete business type'
            });
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">Business Types Management</h3>
                    <p className="text-sm text-gray-600">Configure business types and their default settings</p>
                </div>
                <button
                    onClick={handleCreateType}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Add Business Type
                </button>
            </div>

            {/* Business Types Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {businessTypes.map((type) => (
                    <div key={type.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                        <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center">
                                <div className={`w-10 h-10 rounded-lg bg-${type.color}-100 flex items-center justify-center mr-3`}>
                                    <i className={`${type.icon} text-${type.color}-600`}></i>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900">{type.name}</h4>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        type.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}>
                                        {type.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => handleEditType(type)}
                                    className="text-blue-600 hover:text-blue-800"
                                    title="Edit"
                                >
                                    <i className="fas fa-edit"></i>
                                </button>
                                <button
                                    onClick={() => handleDeleteType(type.id)}
                                    className="text-red-600 hover:text-red-800"
                                    title="Delete"
                                >
                                    <i className="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>

                        <p className="text-gray-600 text-sm mb-4">{type.description}</p>

                        <div className="space-y-2 text-sm">
                            <div>
                                <span className="font-medium text-gray-700">Default Modules:</span>
                                <p className="text-gray-600">{type.default_modules || 'None'}</p>
                            </div>
                            <div>
                                <span className="font-medium text-gray-700">Default Categories:</span>
                                <p className="text-gray-600">{type.default_categories || 'None'}</p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {businessTypes.length === 0 && (
                <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                        <i className="fas fa-building text-4xl"></i>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Types</h3>
                    <p className="text-gray-600 mb-4">
                        Create business types to help users configure their applications.
                    </p>
                    <button
                        onClick={handleCreateType}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Create First Business Type
                    </button>
                </div>
            )}

            {/* Business Type Modal */}
            {showModal && editingType && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-bold text-gray-900">
                                {editingType.id ? 'Edit Business Type' : 'Create Business Type'}
                            </h3>
                            <button
                                onClick={() => setShowModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>

                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                    <input
                                        type="text"
                                        value={editingType.name}
                                        onChange={(e) => setEditingType({...editingType, name: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="e.g., Retail Business"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Icon</label>
                                    <input
                                        type="text"
                                        value={editingType.icon}
                                        onChange={(e) => setEditingType({...editingType, icon: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="fas fa-store"
                                    />
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea
                                    value={editingType.description}
                                    onChange={(e) => setEditingType({...editingType, description: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows="3"
                                    placeholder="Brief description of this business type"
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Default Modules</label>
                                    <textarea
                                        value={editingType.default_modules}
                                        onChange={(e) => setEditingType({...editingType, default_modules: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        rows="2"
                                        placeholder="inventory,sales,customers"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Default Categories</label>
                                    <textarea
                                        value={editingType.default_categories}
                                        onChange={(e) => setEditingType({...editingType, default_categories: e.target.value})}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        rows="2"
                                        placeholder="products,services,materials"
                                    />
                                </div>
                            </div>

                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    checked={editingType.is_active}
                                    onChange={(e) => setEditingType({...editingType, is_active: e.target.checked})}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                                    Active (visible to users during registration)
                                </label>
                            </div>
                        </div>

                        <div className="flex justify-end space-x-4 mt-6">
                            <button
                                onClick={() => setShowModal(false)}
                                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleSaveType}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                                {editingType.id ? 'Update' : 'Create'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

// Make component globally available
window.BusinessTypesManager = BusinessTypesManager;
