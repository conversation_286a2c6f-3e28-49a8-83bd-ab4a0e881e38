<?php
/**
 * Check Customer Table Schema
 */

require_once 'api/db-config.php';

echo "🔍 Checking Customer Table Schema...\n\n";

try {
    // Check if customers table exists
    $result = $conn->query("SHOW TABLES LIKE 'customers'");
    if ($result->num_rows == 0) {
        echo "❌ Customers table does not exist!\n";
        exit;
    }
    
    echo "✅ Customers table exists\n\n";
    
    // Get table structure
    echo "📋 Current table structure:\n";
    echo "============================\n";
    $result = $conn->query("DESCRIBE customers");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo sprintf("%-20s %-20s %-10s %-10s %-20s\n", 
                $row['Field'], 
                $row['Type'], 
                $row['Null'], 
                $row['Key'], 
                $row['Default'] ?? 'NULL'
            );
        }
    }
    
    echo "\n📊 Sample data (first 3 records):\n";
    echo "==================================\n";
    $result = $conn->query("SELECT * FROM customers LIMIT 3");
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo "ID: " . $row['object_id'] . "\n";
            echo "Name: " . $row['name'] . "\n";
            echo "Email: " . ($row['email'] ?? 'NULL') . "\n";
            echo "Company ID: " . ($row['company_id'] ?? 'NULL') . "\n";
            echo "---\n";
        }
    } else {
        echo "No customer records found\n";
    }
    
    // Check for missing columns that might be expected
    $expectedColumns = ['object_id', 'company_id', 'name', 'email', 'phone', 'company', 'address', 'type', 'notes', 'status'];
    
    echo "\n🔍 Checking for expected columns:\n";
    echo "=================================\n";
    
    $existingColumns = [];
    $result = $conn->query("DESCRIBE customers");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $existingColumns[] = $row['Field'];
        }
    }
    
    foreach ($expectedColumns as $column) {
        if (in_array($column, $existingColumns)) {
            echo "✅ $column - EXISTS\n";
        } else {
            echo "❌ $column - MISSING\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
