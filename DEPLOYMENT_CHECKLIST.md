# 🚀 Bizma SaaS - Production Deployment Checklist

## 📋 **Pre-Deployment Checklist**

### ✅ **1. Server Requirements**
- [ ] **PHP 8.0+** installed and configured
- [ ] **MySQL 8.0+** or **MariaDB 10.5+** installed
- [ ] **Apache/Nginx** web server configured
- [ ] **SSL Certificate** installed and configured
- [ ] **Composer** installed for dependency management
- [ ] **Node.js** (optional, for build tools)

### ✅ **2. Database Setup**
- [ ] Create production database: `bizma_production`
- [ ] Create database user with appropriate permissions
- [ ] Import database schema: `mysql -u username -p bizma_production < database/schema.sql`
- [ ] Run migrations: `php database/migrations/run_all.php`
- [ ] Create super admin user: `php reset-superadmin.php`

### ✅ **3. File Permissions**
```bash
# Set proper file permissions
chmod 755 /path/to/bizma
chmod 644 /path/to/bizma/*.php
chmod 644 /path/to/bizma/*.html
chmod 755 /path/to/bizma/uploads
chmod 755 /path/to/bizma/logs
chmod 600 /path/to/bizma/.env
```

### ✅ **4. Environment Configuration**
- [ ] Copy `.env.production` to `.env`
- [ ] Update database credentials in `.env`
- [ ] Set secure JWT secret key
- [ ] Configure email SMTP settings
- [ ] Set payment gateway credentials
- [ ] Update APP_URL to production domain

### ✅ **5. Security Configuration**
- [ ] Generate secure JWT secret: `openssl rand -base64 32`
- [ ] Generate encryption key: `openssl rand -base64 32`
- [ ] Configure security headers in web server
- [ ] Set up firewall rules
- [ ] Configure fail2ban for brute force protection

---

## 🔧 **Web Server Configuration**

### **Apache Configuration (.htaccess)**
```apache
RewriteEngine On

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers
Header always set X-Frame-Options "DENY"
Header always set X-XSS-Protection "1; mode=block"
Header always set X-Content-Type-Options "nosniff"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Strict-Transport-Security "max-age=********; includeSubDomains"

# Hide PHP version
Header unset X-Powered-By
ServerTokens Prod

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# API routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/api.php [QSA,L]

# SPA routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule ^(.*)$ app.html [QSA,L]
```

### **Nginx Configuration**
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    root /path/to/bizma;
    index index.html app.html;

    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;

    # API routing
    location /api/ {
        try_files $uri $uri/ /api/api.php?$query_string;
    }

    # SPA routing
    location / {
        try_files $uri $uri/ /app.html;
    }

    # Deny access to sensitive files
    location ~ /\.env {
        deny all;
    }

    location ~ /\.log {
        deny all;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}
```

---

## 📧 **Email Configuration**

### **Gmail SMTP Setup**
1. Enable 2-factor authentication on Gmail
2. Generate app-specific password
3. Update `.env` with credentials:
```env
MAIL_HOST="smtp.gmail.com"
MAIL_PORT="587"
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your-app-password"
MAIL_ENCRYPTION="tls"
```

### **Test Email Configuration**
```bash
php test-email.php
```

---

## 💳 **Payment Gateway Setup**

### **Razorpay Configuration**
1. Create Razorpay account
2. Get API keys from dashboard
3. Update `.env`:
```env
RAZORPAY_KEY_ID="rzp_live_xxxxxxxxxx"
RAZORPAY_KEY_SECRET="your_secret_key"
```

### **Stripe Configuration**
1. Create Stripe account
2. Get API keys from dashboard
3. Update `.env`:
```env
STRIPE_PUBLISHABLE_KEY="pk_live_xxxxxxxxxx"
STRIPE_SECRET_KEY="sk_live_xxxxxxxxxx"
```

---

## 🔍 **Monitoring & Logging**

### **Log Files Setup**
```bash
# Create log directories
mkdir -p /var/log/bizma
chown www-data:www-data /var/log/bizma
chmod 755 /var/log/bizma

# Setup log rotation
cat > /etc/logrotate.d/bizma << EOF
/var/log/bizma/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

### **Health Check Endpoint**
Test: `curl https://yourdomain.com/api/health`

---

## 🔄 **Backup Strategy**

### **Database Backup Script**
```bash
#!/bin/bash
# /usr/local/bin/backup-bizma.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/bizma"
DB_NAME="bizma_production"
DB_USER="bizma_user"
DB_PASS="your_password"

mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /path/to/bizma/uploads

# Keep only last 30 days
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### **Cron Job Setup**
```bash
# Add to crontab
0 2 * * * /usr/local/bin/backup-bizma.sh
```

---

## 🚀 **Deployment Steps**

### **1. Upload Files**
```bash
# Using rsync
rsync -avz --exclude='.git' --exclude='node_modules' ./ user@server:/path/to/bizma/

# Or using git
git clone https://github.com/yourusername/bizma.git /path/to/bizma
```

### **2. Install Dependencies**
```bash
cd /path/to/bizma
composer install --no-dev --optimize-autoloader
```

### **3. Set Permissions**
```bash
chmod 755 /path/to/bizma
chmod 644 /path/to/bizma/*.php
chmod 755 /path/to/bizma/uploads
chmod 755 /path/to/bizma/logs
chmod 600 /path/to/bizma/.env
```

### **4. Configure Environment**
```bash
cp .env.production .env
# Edit .env with production values
nano .env
```

### **5. Setup Database**
```bash
mysql -u root -p
CREATE DATABASE bizma_production;
CREATE USER 'bizma_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON bizma_production.* TO 'bizma_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Import schema
mysql -u bizma_user -p bizma_production < database/schema.sql
```

### **6. Create Super Admin**
```bash
php reset-superadmin.php
```

### **7. Test Installation**
```bash
# Test database connection
php test-db-connection.php

# Test email configuration
php test-email.php

# Test API endpoints
curl https://yourdomain.com/api/health
```

---

## ✅ **Post-Deployment Verification**

### **Functional Tests**
- [ ] Landing page loads correctly
- [ ] Registration process works
- [ ] Login process works
- [ ] Dashboard loads for authenticated users
- [ ] Super admin dashboard accessible
- [ ] Email notifications working
- [ ] File uploads working
- [ ] Payment gateway integration working

### **Security Tests**
- [ ] HTTPS redirect working
- [ ] Security headers present
- [ ] Sensitive files not accessible
- [ ] SQL injection protection working
- [ ] XSS protection working
- [ ] CSRF protection working

### **Performance Tests**
- [ ] Page load times < 3 seconds
- [ ] API response times < 1 second
- [ ] Database queries optimized
- [ ] Static assets cached
- [ ] Gzip compression enabled

---

## 🔧 **Maintenance Tasks**

### **Regular Maintenance**
- [ ] **Daily**: Check error logs
- [ ] **Weekly**: Review security logs
- [ ] **Monthly**: Update dependencies
- [ ] **Quarterly**: Security audit
- [ ] **Annually**: SSL certificate renewal

### **Monitoring Setup**
- [ ] Setup uptime monitoring (UptimeRobot, Pingdom)
- [ ] Configure error alerting
- [ ] Setup performance monitoring
- [ ] Configure backup monitoring

---

## 📞 **Support & Documentation**

### **Documentation Links**
- [User Manual](docs/user-manual.md)
- [API Documentation](docs/api.md)
- [Admin Guide](docs/admin-guide.md)
- [Troubleshooting](docs/troubleshooting.md)

### **Support Contacts**
- **Technical Support**: <EMAIL>
- **Emergency Contact**: +1-xxx-xxx-xxxx
- **Documentation**: docs.yourdomain.com

---

## 🎉 **Deployment Complete!**

Your Bizma SaaS application is now live and ready for production use!

**Next Steps:**
1. Monitor application performance
2. Collect user feedback
3. Plan feature updates
4. Scale infrastructure as needed

**🚀 Congratulations on your successful deployment!**
