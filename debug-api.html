<!DOCTYPE html>
<html>
<head>
    <title>Debug API Response</title>
</head>
<body>
    <h1>Debug API Response</h1>
    <button onclick="testAPI()">Test Registration API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Testing registration API...');
                
                const testData = {
                    name: 'Test User',
                    email: '<EMAIL>',
                    password: 'TestPassword123!',
                    company_name: 'Test Company',
                    company_size: '1-10 employees',
                    industry: 'Technology'
                };
                
                console.log('Sending data:', testData);
                
                const response = await fetch('/biz/api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('Raw response text:', responseText);
                
                resultDiv.innerHTML = `
                    <h3>Response Details</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Content-Type:</strong> ${response.headers.get('content-type')}</p>
                    <h4>Raw Response:</h4>
                    <pre style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap;">${responseText}</pre>
                `;
                
                // Try to parse as JSON
                try {
                    const jsonData = JSON.parse(responseText);
                    resultDiv.innerHTML += `
                        <h4>Parsed JSON:</h4>
                        <pre style="background: #e8f5e8; padding: 10px; border: 1px solid #4CAF50;">${JSON.stringify(jsonData, null, 2)}</pre>
                    `;
                } catch (parseError) {
                    resultDiv.innerHTML += `
                        <h4>JSON Parse Error:</h4>
                        <pre style="background: #ffe8e8; padding: 10px; border: 1px solid #f44336;">${parseError.message}</pre>
                    `;
                }
                
            } catch (error) {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `
                    <h3>Fetch Error</h3>
                    <pre style="background: #ffe8e8; padding: 10px; border: 1px solid #f44336;">${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
