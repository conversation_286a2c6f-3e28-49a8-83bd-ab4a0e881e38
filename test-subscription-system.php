<?php
echo "=== Testing SaaS Subscription System ===\n\n";

// Test API endpoints
$endpoints = [
    '/super-admin/plans' => 'Plans Management',
    '/super-admin/subscriptions' => 'Subscriptions Management',
    '/super-admin/companies' => 'Companies Management'
];

foreach ($endpoints as $endpoint => $description) {
    echo "Testing $description ($endpoint):\n";
    
    $url = "http://localhost/biz/api/api.php$endpoint";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "  HTTP Code: $httpCode\n";
    
    if ($httpCode == 401) {
        echo "  Status: ✓ Authentication required (expected)\n";
    } elseif ($httpCode == 400) {
        echo "  Status: ✗ Bad Request\n";
        echo "  Response: " . substr($response, 0, 200) . "\n";
    } elseif ($httpCode == 500) {
        echo "  Status: ✗ Server Error\n";
        echo "  Response: " . substr($response, 0, 200) . "\n";
    } else {
        echo "  Status: ? Unexpected code\n";
        echo "  Response: " . substr($response, 0, 200) . "\n";
    }
    echo "\n";
}

// Test database structure
echo "=== Database Structure Check ===\n";
require_once 'api/db-config.php';

$tables = ['pricing_plans', 'subscriptions', 'companies', 'users'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✓ $table table exists\n";
        
        // Count records
        $count = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($count) {
            $countRow = $count->fetch_assoc();
            echo "  Records: " . $countRow['count'] . "\n";
        }
    } else {
        echo "✗ $table table missing\n";
    }
}

// Test pricing plans data
echo "\n=== Pricing Plans ===\n";
$plansResult = $conn->query("SELECT id, name, price_monthly, price_yearly, is_active FROM pricing_plans ORDER BY price_monthly ASC");
if ($plansResult && $plansResult->num_rows > 0) {
    while ($plan = $plansResult->fetch_assoc()) {
        $status = $plan['is_active'] ? 'Active' : 'Inactive';
        echo "- {$plan['name']}: \${$plan['price_monthly']}/month, \${$plan['price_yearly']}/year ({$status})\n";
    }
} else {
    echo "No pricing plans found\n";
}

$conn->close();
echo "\nTest completed!\n";
?>