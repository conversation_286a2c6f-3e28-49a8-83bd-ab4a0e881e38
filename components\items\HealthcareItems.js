function HealthcareItems() {
    try {
        const healthcareItems = [
            // Consultations
            {
                name: "General Consultation",
                description: "General medical consultation with experienced doctor including basic examination.",
                price: 500,
                category: "Consultations",
                subcategory: "General Medicine",
                isRecurring: false,
                itemType: "service",
                unit: "consultation"
            },
            {
                name: "Specialist Consultation - Cardiology",
                description: "Consultation with cardiologist for heart-related conditions and preventive care.",
                price: 1200,
                category: "Consultations",
                subcategory: "Cardiology",
                isRecurring: false,
                itemType: "service",
                unit: "consultation"
            },
            {
                name: "Specialist Consultation - Dermatology",
                description: "Consultation with dermatologist for skin, hair, and nail conditions.",
                price: 800,
                category: "Consultations",
                subcategory: "Dermatology",
                isRecurring: false,
                itemType: "service",
                unit: "consultation"
            },
            {
                name: "Pediatric Consultation",
                description: "Specialized consultation for children and adolescents up to 18 years.",
                price: 600,
                category: "Consultations",
                subcategory: "Pediatrics",
                isRecurring: false,
                itemType: "service",
                unit: "consultation"
            },
            {
                name: "Gynecology Consultation",
                description: "Consultation for women's health, pregnancy care, and reproductive health.",
                price: 700,
                category: "Consultations",
                subcategory: "Gynecology",
                isRecurring: false,
                itemType: "service",
                unit: "consultation"
            },
            
            // Diagnostic Tests
            {
                name: "Complete Blood Count (CBC)",
                description: "Comprehensive blood test to check overall health and detect various disorders.",
                price: 300,
                category: "Diagnostic Tests",
                subcategory: "Blood Tests",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            {
                name: "Lipid Profile",
                description: "Blood test to measure cholesterol and triglyceride levels.",
                price: 400,
                category: "Diagnostic Tests",
                subcategory: "Blood Tests",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            {
                name: "Thyroid Function Test",
                description: "Blood test to check thyroid hormone levels (T3, T4, TSH).",
                price: 500,
                category: "Diagnostic Tests",
                subcategory: "Blood Tests",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            {
                name: "X-Ray - Chest",
                description: "Chest X-ray to examine lungs, heart, and chest bones.",
                price: 400,
                category: "Diagnostic Tests",
                subcategory: "Radiology",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            {
                name: "Ultrasound - Abdomen",
                description: "Abdominal ultrasound to examine internal organs.",
                price: 800,
                category: "Diagnostic Tests",
                subcategory: "Radiology",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            {
                name: "ECG (Electrocardiogram)",
                description: "Test to check heart rhythm and electrical activity.",
                price: 200,
                category: "Diagnostic Tests",
                subcategory: "Cardiology",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            {
                name: "MRI Scan - Brain",
                description: "Magnetic resonance imaging of the brain for detailed examination.",
                price: 5000,
                category: "Diagnostic Tests",
                subcategory: "Radiology",
                isRecurring: false,
                itemType: "service",
                unit: "test"
            },
            
            // Treatments & Procedures
            {
                name: "Vaccination - COVID-19",
                description: "COVID-19 vaccination with approved vaccines.",
                price: 250,
                category: "Treatments",
                subcategory: "Vaccinations",
                isRecurring: false,
                itemType: "service",
                unit: "dose"
            },
            {
                name: "Vaccination - Flu Shot",
                description: "Annual influenza vaccination for seasonal protection.",
                price: 300,
                category: "Treatments",
                subcategory: "Vaccinations",
                isRecurring: false,
                itemType: "service",
                unit: "dose"
            },
            {
                name: "Physiotherapy Session",
                description: "Individual physiotherapy session for rehabilitation and pain management.",
                price: 600,
                category: "Treatments",
                subcategory: "Physiotherapy",
                isRecurring: false,
                itemType: "service",
                unit: "session"
            },
            {
                name: "Dental Cleaning",
                description: "Professional dental cleaning and oral hygiene maintenance.",
                price: 800,
                category: "Treatments",
                subcategory: "Dental",
                isRecurring: false,
                itemType: "service",
                unit: "session"
            },
            {
                name: "Dental Filling",
                description: "Tooth filling for cavities using composite or amalgam material.",
                price: 1500,
                category: "Treatments",
                subcategory: "Dental",
                isRecurring: false,
                itemType: "service",
                unit: "tooth"
            },
            
            // Health Packages
            {
                name: "Basic Health Checkup",
                description: "Comprehensive health package including CBC, blood sugar, blood pressure check.",
                price: 1500,
                category: "Health Packages",
                subcategory: "Preventive Care",
                isRecurring: false,
                itemType: "service",
                unit: "package"
            },
            {
                name: "Executive Health Checkup",
                description: "Comprehensive health package with advanced tests and specialist consultations.",
                price: 5000,
                category: "Health Packages",
                subcategory: "Preventive Care",
                isRecurring: false,
                itemType: "service",
                unit: "package"
            },
            {
                name: "Women's Health Package",
                description: "Specialized health package for women including gynecological examination.",
                price: 3000,
                category: "Health Packages",
                subcategory: "Women's Health",
                isRecurring: false,
                itemType: "service",
                unit: "package"
            },
            {
                name: "Senior Citizen Health Package",
                description: "Comprehensive health package designed for people above 60 years.",
                price: 4000,
                category: "Health Packages",
                subcategory: "Geriatric Care",
                isRecurring: false,
                itemType: "service",
                unit: "package"
            },
            
            // Emergency Services
            {
                name: "Emergency Consultation",
                description: "24/7 emergency medical consultation for urgent health issues.",
                price: 1000,
                category: "Emergency Services",
                subcategory: "Emergency Care",
                isRecurring: false,
                itemType: "service",
                unit: "consultation"
            },
            {
                name: "Ambulance Service",
                description: "Emergency ambulance service with basic life support equipment.",
                price: 1500,
                category: "Emergency Services",
                subcategory: "Transportation",
                isRecurring: false,
                itemType: "service",
                unit: "trip"
            },
            
            // Home Care Services
            {
                name: "Home Nursing Care",
                description: "Professional nursing care at home for post-operative or chronic care.",
                price: 800,
                category: "Home Care",
                subcategory: "Nursing",
                isRecurring: false,
                itemType: "service",
                unit: "hour"
            },
            {
                name: "Home Sample Collection",
                description: "Blood and other sample collection from home for laboratory tests.",
                price: 150,
                category: "Home Care",
                subcategory: "Sample Collection",
                isRecurring: false,
                itemType: "service",
                unit: "visit"
            }
        ];

        const [loading, setLoading] = React.useState(false);
        const [addedItems, setAddedItems] = React.useState([]);
        const [notification, setNotification] = React.useState(null);

        const setupCategoriesAndItems = async () => {
            try {
                setLoading(true);
                
                // Get unique categories and subcategories
                const categories = [...new Set(healthcareItems.map(item => item.category))];
                const categoryMap = {};
                
                for (const categoryName of categories) {
                    // Check if category exists
                    const categoriesResponse = await trickleListObjects('item_category', 100, true);
                    let category = categoriesResponse.items.find(
                        cat => cat.objectData.name === categoryName
                    );
                    
                    if (!category) {
                        const categoryData = {
                            name: categoryName,
                            description: `${categoryName} services for healthcare`,
                            createdAt: new Date().toISOString()
                        };
                        
                        const newCategory = await trickleCreateObject('item_category', categoryData);
                        category = {
                            objectId: newCategory.objectId,
                            objectData: categoryData
                        };
                    }
                    
                    categoryMap[categoryName] = category.objectId;
                    
                    // Create subcategories for this category
                    const subcategories = [...new Set(
                        healthcareItems
                            .filter(item => item.category === categoryName)
                            .map(item => item.subcategory)
                    )];
                    
                    for (const subcategoryName of subcategories) {
                        const subcategoriesResponse = await trickleListObjects(`item_subcategory:${category.objectId}`, 100, true);
                        let existingSubcategory = subcategoriesResponse.items.find(
                            subcat => subcat.objectData.name === subcategoryName
                        );
                        
                        if (!existingSubcategory) {
                            const subcategoryData = {
                                name: subcategoryName,
                                description: `${subcategoryName} services`,
                                createdAt: new Date().toISOString()
                            };
                            
                            await trickleCreateObject(
                                `item_subcategory:${category.objectId}`, 
                                subcategoryData
                            );
                        }
                    }
                }
                
                // Create items
                for (const item of healthcareItems) {
                    const itemsResponse = await trickleListObjects('item', 100, true);
                    const existingItem = itemsResponse.items.find(
                        existingItem => existingItem.objectData.name === item.name
                    );
                    
                    if (!existingItem) {
                        const itemData = {
                            ...item,
                            category: categoryMap[item.category],
                            isActive: true,
                            createdAt: new Date().toISOString()
                        };
                        
                        await trickleCreateObject('item', itemData);
                        setAddedItems(prev => [...prev, item.name]);
                    }
                }
                
                setNotification({
                    type: 'success',
                    message: `Successfully added healthcare services`
                });
                
            } catch (error) {
                console.error('Error setting up healthcare items:', error);
                setNotification({
                    type: 'error',
                    message: 'Error adding healthcare services'
                });
            } finally {
                setLoading(false);
            }
        };

        return (
            <div data-name="healthcare-items" className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">Healthcare Services</h2>
                    <p className="mb-6">
                        Add comprehensive healthcare services including consultations, diagnostic tests, treatments, and health packages.
                    </p>
                    
                    <div className="mb-6">
                        <h3 className="font-medium mb-2">Services that will be added:</h3>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                            <li>Medical Consultations (General & Specialist)</li>
                            <li>Diagnostic Tests (Blood Tests, Radiology, Cardiology)</li>
                            <li>Treatments & Procedures (Vaccinations, Physiotherapy, Dental)</li>
                            <li>Health Packages (Preventive Care, Women's Health)</li>
                            <li>Emergency Services</li>
                            <li>Home Care Services</li>
                        </ul>
                    </div>
                    
                    <div className="flex justify-center">
                        <Button
                            onClick={setupCategoriesAndItems}
                            loading={loading}
                            disabled={loading}
                            icon="fas fa-heartbeat"
                        >
                            Add Healthcare Services
                        </Button>
                    </div>
                    
                    {addedItems.length > 0 && (
                        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                            <h4 className="font-medium text-green-700 mb-2">Successfully added:</h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm text-green-600">
                                {addedItems.map((item, index) => (
                                    <li key={index}>{item}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
                
                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('HealthcareItems component error:', error);
        reportError(error);
        return null;
    }
}
