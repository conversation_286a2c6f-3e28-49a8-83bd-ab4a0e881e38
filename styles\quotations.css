.quotation-form {
    max-width: 800px;
    margin: 0 auto;
}

.quotation-items {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.quotation-item {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr auto;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.quotation-item:last-child {
    border-bottom: none;
}

.quotation-totals {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e5e7eb;
}

.quotation-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.quotation-status.draft {
    background-color: #f3f4f6;
    color: #4b5563;
}

.quotation-status.sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.quotation-status.accepted {
    background-color: #d1fae5;
    color: #065f46;
}

.quotation-status.rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

.quotation-preview {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quotation-preview-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
    .quotation-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}
