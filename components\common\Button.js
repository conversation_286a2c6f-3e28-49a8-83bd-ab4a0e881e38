// Make Button component globally available
window.Button = function Button({ 
    children, 
    type = 'button', 
    variant = 'primary', 
    size = 'md', 
    disabled = false, 
    loading = false,
    icon = null,
    onClick 
}) {
    try {
        const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none transition-colors';
        
        const variantClasses = {
            primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2',
            danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
            success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2'
        };

        const sizeClasses = {
            sm: 'px-3 py-1.5 text-sm',
            md: 'px-4 py-2 text-base',
            lg: 'px-6 py-3 text-lg'
        };

        const disabledClasses = disabled || loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

        return (
            <button
                data-name={`button-${variant}`}
                type={type}
                disabled={disabled || loading}
                className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses}`}
                onClick={onClick}
            >
                {loading ? (
                    <div data-name="button-loading" className="mr-2">
                        <i className="fas fa-spinner fa-spin"></i>
                    </div>
                ) : icon ? (
                    <i className={`${icon} mr-2`}></i>
                ) : null}
                {children}
            </button>
        );
    } catch (error) {
        console.error('Button component error:', error);
        reportError(error);
        return null;
    }
}
