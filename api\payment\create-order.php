<?php
require_once '../config/database.php';
require_once '../utils/SecurityUtils.php';
require_once '../utils/AuthUtils.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication
    $authResult = AuthUtils::verifyToken();
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    $user = $authResult['user'];
    $conn = Database::getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        handleCreateOrder($conn, $user);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Create Order API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleCreateOrder($conn, $user) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $planId = SecurityUtils::sanitizeInput($input['plan_id'] ?? '');
        $billingCycle = SecurityUtils::sanitizeInput($input['billing_cycle'] ?? 'monthly');
        $amount = floatval($input['amount'] ?? 0);
        
        if (empty($planId) || $amount <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid plan or amount']);
            return;
        }
        
        // Get plan details
        $planStmt = $conn->prepare("SELECT * FROM plans WHERE id = ?");
        $planStmt->bind_param("s", $planId);
        $planStmt->execute();
        $planResult = $planStmt->get_result();
        
        if (!$plan = $planResult->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Plan not found']);
            return;
        }
        
        // Get user's company
        $companyStmt = $conn->prepare("SELECT * FROM companies WHERE owner_id = ? OR id = ?");
        $companyStmt->bind_param("ss", $user['object_id'], $user['company_id']);
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if (!$company = $companyResult->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Company not found']);
            return;
        }
        
        // Razorpay configuration (you should store these in environment variables)
        $razorpayKeyId = 'rzp_test_your_key_id'; // Replace with actual key
        $razorpayKeySecret = 'your_key_secret'; // Replace with actual secret
        
        // Create order in database
        $orderId = 'order_' . time() . '_' . rand(1000, 9999);
        $orderAmount = $amount * 100; // Convert to paise
        
        $orderStmt = $conn->prepare("
            INSERT INTO payment_orders (order_id, user_id, company_id, plan_id, amount, currency, 
                                       billing_cycle, status, created_at)
            VALUES (?, ?, ?, ?, ?, 'INR', ?, 'created', NOW())
        ");
        $orderStmt->bind_param("ssssds", $orderId, $user['object_id'], $company['object_id'], 
                              $planId, $amount, $billingCycle);
        
        if (!$orderStmt->execute()) {
            throw new Exception('Failed to create order in database');
        }
        
        // Create Razorpay order
        $razorpayOrder = createRazorpayOrder($razorpayKeyId, $razorpayKeySecret, $orderAmount, $orderId);
        
        if (!$razorpayOrder) {
            throw new Exception('Failed to create Razorpay order');
        }
        
        // Update order with Razorpay order ID
        $updateStmt = $conn->prepare("UPDATE payment_orders SET razorpay_order_id = ? WHERE order_id = ?");
        $updateStmt->bind_param("ss", $razorpayOrder['id'], $orderId);
        $updateStmt->execute();
        
        $responseData = [
            'order_id' => $orderId,
            'razorpay_order_id' => $razorpayOrder['id'],
            'razorpay_key' => $razorpayKeyId,
            'amount' => $orderAmount,
            'currency' => 'INR',
            'company_id' => $company['object_id'],
            'customer_name' => $user['name'],
            'customer_email' => $user['email'],
            'customer_phone' => $user['phone'] ?? '',
            'plan_name' => $plan['name'],
            'billing_cycle' => $billingCycle
        ];
        
        echo json_encode(['success' => true, 'data' => $responseData]);
        
    } catch (Exception $e) {
        error_log("Error creating order: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to create order: ' . $e->getMessage()]);
    }
}

function createRazorpayOrder($keyId, $keySecret, $amount, $receipt) {
    try {
        $url = 'https://api.razorpay.com/v1/orders';
        
        $data = [
            'amount' => $amount,
            'currency' => 'INR',
            'receipt' => $receipt,
            'payment_capture' => 1
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($keyId . ':' . $keySecret)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            return json_decode($response, true);
        } else {
            error_log("Razorpay API Error: " . $response);
            return false;
        }
    } catch (Exception $e) {
        error_log("Razorpay Order Creation Error: " . $e->getMessage());
        return false;
    }
}

// Create payment_orders table if it doesn't exist
function createPaymentOrdersTable($conn) {
    $sql = "
    CREATE TABLE IF NOT EXISTS payment_orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_id VARCHAR(100) UNIQUE NOT NULL,
        razorpay_order_id VARCHAR(100),
        user_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        plan_id VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'INR',
        billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
        status ENUM('created', 'paid', 'failed', 'cancelled') DEFAULT 'created',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_order_id (order_id),
        INDEX idx_user_id (user_id),
        INDEX idx_company_id (company_id)
    )";
    
    $conn->query($sql);
}

// Initialize table
try {
    $conn = Database::getConnection();
    createPaymentOrdersTable($conn);
} catch (Exception $e) {
    error_log("Error creating payment_orders table: " . $e->getMessage());
}
?>
