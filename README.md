# Business Management SaaS - Production Ready

This is the production-ready version of the Business Management SaaS application. This folder contains all the necessary files for deployment to a production environment.

## Deployment Instructions

### Prerequisites

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite enabled
- SSL certificate for HTTPS (recommended for production)

### Installation Steps

1. **Upload Files**: Upload all files in this directory to your web server's document root or a subdirectory.

2. **Database Setup**:
   - Create a MySQL database for the application
   - Import the database schema from `api/setup-database.php` or run it directly

3. **Configuration**:
   - Verify the `.env` file contains the correct database credentials and other settings
   - Ensure the `config.js` file has the correct API base URL and application settings

4. **File Permissions**:
   - Set write permissions for the `uploads`, `logs`, and `cache` directories
   - Recommended: `chmod 755` for directories and `chmod 644` for files
   - For the writable directories: `chmod 775` for `uploads`, `logs`, and `cache`

5. **Web Server Configuration**:
   - Ensure Apache's mod_rewrite is enabled
   - The `.htaccess` file is already configured for URL rewriting and security

6. **SSL Setup**:
   - Install an SSL certificate on your domain
   - Uncomment the HTTPS redirection rules in `.htaccess` once SSL is configured

7. **First Login**:
   - Access the application through your web browser
   - Log in with the default admin credentials (if set up during database initialization)
   - Change the default password immediately

## Directory Structure

- `/api` - Backend API files
- `/components` - React components
- `/pages` - Main application pages
- `/utils` - Utility functions
- `/styles` - CSS files
- `/uploads` - File upload directory (must be writable)
- `/logs` - Application logs (must be writable)
- `/cache` - Application cache (must be writable)
- `/config` - Configuration files

## Security Considerations

- The `.env` file contains sensitive information and should have restricted access
- The `.htaccess` file includes security headers and protections
- Regular backups of the database and uploaded files are recommended
- Keep PHP and all dependencies updated to the latest secure versions

## Troubleshooting

- Check the logs in the `/logs` directory for error information
- Verify file permissions if you encounter upload or write errors
- Ensure the database connection details in `.env` are correct
- If you see a blank page, enable PHP error reporting temporarily for debugging

## Support

For support inquiries, please contact the development team or refer to the documentation.