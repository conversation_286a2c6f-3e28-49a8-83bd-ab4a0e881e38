function TaskItem({ task, onUpdate, onDelete }) {
    const [isEditing, setIsEditing] = React.useState(false);
    const [editData, setEditData] = React.useState({
        title: task.title || task.objectData?.title || '',
        description: task.description || task.objectData?.description || '',
        dueDate: task.dueDate || task.objectData?.dueDate || '',
        priority: task.priority || task.objectData?.priority || 'medium',
        status: task.status || task.objectData?.status || 'pending'
    });

    try {
        const handleSave = () => {
            if (onUpdate && editData.title.trim()) {
                onUpdate(task.id || task.objectId, editData);
            }
            setIsEditing(false);
        };

        const handleCancel = () => {
            setEditData({
                title: task.title || task.objectData?.title || '',
                description: task.description || task.objectData?.description || '',
                dueDate: task.dueDate || task.objectData?.dueDate || '',
                priority: task.priority || task.objectData?.priority || 'medium',
                status: task.status || task.objectData?.status || 'pending'
            });
            setIsEditing(false);
        };

        const handleDelete = () => {
            if (onDelete) {
                onDelete(task.id || task.objectId);
            }
        };

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setEditData(prev => ({
                ...prev,
                [name]: value
            }));
        };

        const taskData = task.objectData || task;

        return (
            <div className={`p-4 rounded-lg border ${
                taskData.status === 'completed' 
                    ? 'bg-green-50 border-green-200' 
                    : taskData.status === 'pending'
                    ? 'bg-yellow-50 border-yellow-200'
                    : 'bg-gray-50 border-gray-200'
            }`}>
                {isEditing ? (
                    <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Title</label>
                                <input
                                    type="text"
                                    name="title"
                                    value={editData.title}
                                    onChange={handleInputChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Due Date</label>
                                <input
                                    type="date"
                                    name="dueDate"
                                    value={editData.dueDate}
                                    onChange={handleInputChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                        
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Description</label>
                            <textarea
                                name="description"
                                value={editData.description}
                                onChange={handleInputChange}
                                rows={2}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Priority</label>
                                <select
                                    name="priority"
                                    value={editData.priority}
                                    onChange={handleInputChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="low">Low</option>
                                    <option value="medium">Medium</option>
                                    <option value="high">High</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Status</label>
                                <select
                                    name="status"
                                    value={editData.status}
                                    onChange={handleInputChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            <button
                                onClick={handleSave}
                                className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                            >
                                Save
                            </button>
                            <button
                                onClick={handleCancel}
                                className="px-4 py-2 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <h4 className="font-medium">
                                {taskData.title}
                            </h4>
                            {taskData.description && (
                                <p className="text-sm text-gray-600 mt-1">
                                    {taskData.description}
                                </p>
                            )}
                            <div className="mt-2 flex items-center space-x-4 text-sm">
                                {taskData.dueDate && (
                                    <span className="text-gray-500">
                                        <i className="fas fa-calendar-alt mr-1"></i>
                                        Due: {new Date(taskData.dueDate).toLocaleDateString()}
                                    </span>
                                )}
                                <span className={`
                                    px-2 py-1 rounded-full text-xs font-medium
                                    ${taskData.priority === 'high' 
                                        ? 'bg-red-100 text-red-800'
                                        : taskData.priority === 'medium'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-green-100 text-green-800'
                                    }
                                `}>
                                    {taskData.priority.charAt(0).toUpperCase() + taskData.priority.slice(1)}
                                </span>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <select
                                value={taskData.status}
                                onChange={(e) => onUpdate && onUpdate(task.id || task.objectId, {
                                    ...taskData,
                                    status: e.target.value
                                })}
                                className="text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                            <button
                                onClick={() => setIsEditing(true)}
                                className="text-gray-400 hover:text-blue-600 text-sm"
                                title="Edit Task"
                            >
                                <i className="fas fa-edit"></i>
                            </button>
                            <button
                                onClick={handleDelete}
                                className="text-gray-400 hover:text-red-600 text-sm"
                                title="Delete Task"
                            >
                                <i className="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('TaskItem component error:', error);
        return (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">Error loading task item</p>
            </div>
        );
    }
}
