<?php
/**
 * Fix Company Foreign Key Constraint
 * Create the missing company record for super admin
 */

require_once 'api/db-config.php';

echo "🔧 Fixing company foreign key constraint...\n\n";

try {
    // Check what company_id the super admin user has
    $sql = "SELECT company_id FROM users WHERE role = 'super_admin' LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $companyId = $user['company_id'];
        
        echo "📋 Super admin company_id: $companyId\n";
        
        // Check if this company exists
        $checkSql = "SELECT * FROM companies WHERE object_id = ?";
        $stmt = $conn->prepare($checkSql);
        $stmt->bind_param("s", $companyId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "✅ Company already exists\n";
            $company = $result->fetch_assoc();
            print_r($company);
        } else {
            echo "❌ Company does not exist. Creating it...\n";
            
            // Create the company
            $insertSql = "INSERT INTO companies (object_id, name, email, phone, address, business_type, status, created_at, updated_at) 
                         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            $insertStmt = $conn->prepare($insertSql);
            
            $companyName = 'Super Admin Company';
            $companyEmail = '<EMAIL>';
            $companyPhone = '+1234567890';
            $companyAddress = 'Admin Office';
            $businessType = 'technology';
            $status = 'active';
            
            $insertStmt->bind_param("sssssss", $companyId, $companyName, $companyEmail, $companyPhone, $companyAddress, $businessType, $status);
            
            if ($insertStmt->execute()) {
                echo "✅ Company created successfully\n";
                echo "Company ID: $companyId\n";
                echo "Company Name: $companyName\n";
            } else {
                echo "❌ Failed to create company: " . $conn->error . "\n";
            }
        }
        
    } else {
        echo "❌ No super admin user found\n";
    }
    
    // Also check if we need to temporarily disable foreign key checks for testing
    echo "\n🔧 Checking foreign key constraints...\n";
    $fkSql = "SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME 
              FROM information_schema.KEY_COLUMN_USAGE 
              WHERE TABLE_SCHEMA = 'business_saas' AND REFERENCED_TABLE_NAME IS NOT NULL";
    $result = $conn->query($fkSql);
    
    if ($result) {
        echo "Foreign key constraints:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['TABLE_NAME']}.{$row['COLUMN_NAME']} -> {$row['REFERENCED_TABLE_NAME']}.{$row['REFERENCED_COLUMN_NAME']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
