<?php
/**
 * Step-by-step Enhanced SaaS Migration Runner
 * Runs the enhanced database schema migration step by step
 */

require_once __DIR__ . '/../api/db-config.php';

echo "🚀 Starting Enhanced SaaS Migration (Step by Step)...\n\n";

try {
    // Step 1: Create business_types table
    echo "📋 Creating business_types table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS business_types (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        icon VARCHAR(100),
        default_modules JSON,
        default_categories JSON,
        default_features JSON,
        default_templates JSON,
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ business_types table created successfully\n";
    } else {
        echo "❌ Error creating business_types table: " . $conn->error . "\n";
    }
    
    // Step 2: Create enhanced pricing_plans table
    echo "💰 Creating pricing_plans table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS pricing_plans (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        short_description VARCHAR(255),
        price_monthly DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        price_yearly DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        trial_days INT DEFAULT 14,
        features JSON,
        limits_data JSON,
        business_types JSON,
        is_trial_available BOOLEAN DEFAULT TRUE,
        is_visible BOOLEAN DEFAULT TRUE,
        is_popular BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ pricing_plans table created successfully\n";
    } else {
        echo "❌ Error creating pricing_plans table: " . $conn->error . "\n";
    }
    
    // Step 3: Backup and recreate subscriptions table
    echo "🔄 Recreating subscriptions table...\n";
    
    // First backup existing data
    $conn->query("CREATE TABLE IF NOT EXISTS subscriptions_backup_" . date('Y_m_d_H_i_s') . " AS SELECT * FROM subscriptions");
    
    // Drop existing table
    $conn->query("DROP TABLE IF EXISTS subscriptions");
    
    $sql = "CREATE TABLE subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        object_id VARCHAR(50) UNIQUE NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        plan_id VARCHAR(50) NOT NULL,
        plan_name VARCHAR(100) NOT NULL,
        status ENUM('trial', 'active', 'cancelled', 'expired', 'suspended') DEFAULT 'trial',
        billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
        price DECIMAL(10,2) DEFAULT 0.00,
        currency VARCHAR(10) DEFAULT 'INR',
        
        trial_start_date DATETIME,
        trial_end_date DATETIME,
        trial_extended_days INT DEFAULT 0,
        
        start_date DATETIME,
        end_date DATETIME,
        next_billing_date DATETIME,
        
        payment_method VARCHAR(50),
        payment_gateway VARCHAR(50),
        gateway_subscription_id VARCHAR(255),
        last_payment_date DATETIME,
        last_payment_amount DECIMAL(10,2),
        
        features JSON,
        limits_data JSON,
        usage_data JSON,
        
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_object_id (object_id),
        INDEX idx_company_id (company_id),
        INDEX idx_user_id (user_id),
        INDEX idx_plan_id (plan_id),
        INDEX idx_status (status),
        INDEX idx_trial_end_date (trial_end_date),
        INDEX idx_next_billing_date (next_billing_date)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ subscriptions table recreated successfully\n";
    } else {
        echo "❌ Error recreating subscriptions table: " . $conn->error . "\n";
    }
    
    // Step 4: Create payment_transactions table
    echo "💳 Creating payment_transactions table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS payment_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        object_id VARCHAR(50) UNIQUE NOT NULL,
        subscription_id VARCHAR(50) NOT NULL,
        company_id VARCHAR(50) NOT NULL,
        user_id VARCHAR(50) NOT NULL,
        
        transaction_type ENUM('subscription', 'upgrade', 'renewal', 'refund') DEFAULT 'subscription',
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'INR',
        status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
        
        payment_gateway VARCHAR(50),
        gateway_transaction_id VARCHAR(255),
        gateway_order_id VARCHAR(255),
        gateway_payment_id VARCHAR(255),
        gateway_signature VARCHAR(255),
        gateway_response JSON,
        
        billing_period_start DATETIME,
        billing_period_end DATETIME,
        
        description TEXT,
        notes TEXT,
        processed_at DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_object_id (object_id),
        INDEX idx_subscription_id (subscription_id),
        INDEX idx_company_id (company_id),
        INDEX idx_status (status),
        INDEX idx_gateway_transaction_id (gateway_transaction_id)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "✅ payment_transactions table created successfully\n";
    } else {
        echo "❌ Error creating payment_transactions table: " . $conn->error . "\n";
    }
    
    echo "\n🎉 Core tables created successfully!\n";
    echo "Next: Run the seed data insertion...\n";
    
} catch (Exception $e) {
    echo "💥 Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Step 1 migration completed.\n";
?>
