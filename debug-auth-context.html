<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Auth Context - Bizma</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="config.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // Load AuthContext
        const AuthContext = React.createContext();

        function AuthProvider({ children }) {
            const [user, setUser] = React.useState(null);
            const [loading, setLoading] = React.useState(true);
            const [error, setError] = React.useState(null);
            const [debugInfo, setDebugInfo] = React.useState([]);

            const addDebugInfo = (info) => {
                setDebugInfo(prev => [...prev, {
                    timestamp: new Date().toISOString(),
                    ...info
                }]);
            };

            React.useEffect(() => {
                checkAuth();
            }, []);

            const checkAuth = async () => {
                try {
                    addDebugInfo({ step: 'start', message: 'Starting auth check' });
                    
                    const token = localStorage.getItem('authToken');
                    if (!token) {
                        addDebugInfo({ step: 'no_token', message: 'No auth token found' });
                        setLoading(false);
                        return;
                    }

                    addDebugInfo({ step: 'token_found', message: `Token found: ${token.substring(0, 20)}...` });

                    // Verify token with server
                    const authUrl = window.getApiUrl('/auth/verify');
                    addDebugInfo({ step: 'calling_api', message: `Calling: ${authUrl}` });
                    
                    const response = await fetch(authUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    addDebugInfo({ 
                        step: 'api_response', 
                        message: `Response status: ${response.status}`,
                        status: response.status,
                        statusText: response.statusText
                    });

                    if (response.ok) {
                        const data = await response.json();
                        addDebugInfo({ 
                            step: 'api_success', 
                            message: 'API call successful',
                            data: data
                        });

                        if (data.success && data.user) {
                            setUser(data.user);
                            addDebugInfo({ 
                                step: 'user_set', 
                                message: 'User data set in context',
                                user: data.user,
                                role: data.user.role,
                                isSuperAdmin: data.user.role === 'super_admin'
                            });
                        } else {
                            addDebugInfo({ step: 'invalid_response', message: 'Invalid response format', data: data });
                            throw new Error('Invalid user data received');
                        }
                    } else {
                        const errorText = await response.text();
                        addDebugInfo({ 
                            step: 'api_error', 
                            message: `API error: ${response.status}`,
                            errorText: errorText
                        });
                        
                        if (response.status === 401 || response.status === 403) {
                            localStorage.removeItem('authToken');
                            throw new Error('Authentication token is invalid or expired');
                        } else {
                            throw new Error(`Server error: ${response.status}`);
                        }
                    }
                } catch (error) {
                    addDebugInfo({ 
                        step: 'error', 
                        message: `Auth check error: ${error.message}`,
                        error: error.message
                    });
                    localStorage.removeItem('authToken');
                    setError(error.message || 'Authentication failed');
                } finally {
                    setLoading(false);
                    addDebugInfo({ step: 'complete', message: 'Auth check complete' });
                }
            };

            const isAuthenticated = !!user;
            const userRole = (user && user.role) || 'user';
            const isAdmin = userRole === 'admin' || userRole === 'super_admin';
            const isSuperAdmin = userRole === 'super_admin';

            return React.createElement(AuthContext.Provider, {
                value: {
                    user,
                    loading,
                    error,
                    isAuthenticated,
                    userRole,
                    isAdmin,
                    isSuperAdmin,
                    debugInfo
                }
            }, children);
        }

        function DebugApp() {
            const auth = React.useContext(AuthContext);

            return React.createElement('div', { className: 'container' }, [
                React.createElement('h1', { key: 'title' }, '🔐 Auth Context Debug'),
                
                React.createElement('div', { key: 'status', className: 'status info' }, [
                    React.createElement('h3', { key: 'h3' }, 'Current Auth State'),
                    React.createElement('p', { key: 'loading' }, `Loading: ${auth.loading}`),
                    React.createElement('p', { key: 'authenticated' }, `Authenticated: ${auth.isAuthenticated}`),
                    React.createElement('p', { key: 'role' }, `User Role: ${auth.userRole}`),
                    React.createElement('p', { key: 'super' }, `Is Super Admin: ${auth.isSuperAdmin}`),
                    auth.error && React.createElement('p', { key: 'error', style: { color: 'red' } }, `Error: ${auth.error}`)
                ]),

                auth.user && React.createElement('div', { key: 'user', className: 'status success' }, [
                    React.createElement('h3', { key: 'h3' }, 'User Data'),
                    React.createElement('pre', { key: 'pre' }, JSON.stringify(auth.user, null, 2))
                ]),

                React.createElement('div', { key: 'debug', className: 'status info' }, [
                    React.createElement('h3', { key: 'h3' }, 'Debug Log'),
                    React.createElement('pre', { key: 'pre' }, JSON.stringify(auth.debugInfo, null, 2))
                ])
            ]);
        }

        function App() {
            return React.createElement(AuthProvider, {}, 
                React.createElement(DebugApp)
            );
        }

        ReactDOM.render(React.createElement(App), document.getElementById('root'));
    </script>
</body>
</html>
