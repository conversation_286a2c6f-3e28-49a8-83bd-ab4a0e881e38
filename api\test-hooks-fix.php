<?php
/**
 * Test endpoint to verify hooks fix
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

echo json_encode([
    'success' => true,
    'message' => 'Hooks fix test endpoint',
    'timestamp' => date('Y-m-d H:i:s'),
    'info' => 'This endpoint can be used to test that the React hooks fix is working properly'
]);
?>
