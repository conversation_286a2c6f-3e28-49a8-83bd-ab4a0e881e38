<?php
/**
 * Debug API endpoint to test all components
 */

// Disable error display to prevent HTML in JSON responses
ini_set('display_errors', 0);
ini_set('log_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$debug = [];

try {
    // Test 1: Check if config files exist
    $debug['config_files'] = [
        'config.php' => file_exists(__DIR__ . '/../config/config.php'),
        'db-config.php' => file_exists(__DIR__ . '/db-config.php'),
        'auth-handler.php' => file_exists(__DIR__ . '/handlers/auth-handler.php')
    ];
    
    // Test 2: Try to include config
    try {
        require_once __DIR__ . '/../config/config.php';
        $debug['config_loaded'] = true;
    } catch (Exception $e) {
        $debug['config_error'] = $e->getMessage();
    }
    
    // Test 3: Try to connect to database
    try {
        require_once __DIR__ . '/db-config.php';
        $debug['database_connected'] = true;
        $debug['database_info'] = [
            'host' => $conn->host_info ?? 'unknown',
            'charset' => $conn->character_set_name() ?? 'unknown'
        ];
    } catch (Exception $e) {
        $debug['database_error'] = $e->getMessage();
    }
    
    // Test 4: Check if tables exist
    if (isset($conn)) {
        $tables = ['users', 'companies', 'customers', 'quotations', 'invoices', 'contracts'];
        $debug['tables'] = [];
        
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            $debug['tables'][$table] = $result && $result->num_rows > 0;
        }
        
        // Test 5: Check users table structure
        if ($debug['tables']['users']) {
            $result = $conn->query("DESCRIBE users");
            $debug['users_columns'] = [];
            while ($row = $result->fetch_assoc()) {
                $debug['users_columns'][] = $row['Field'];
            }
            
            // Check if there are any users
            $result = $conn->query("SELECT COUNT(*) as count FROM users");
            $row = $result->fetch_assoc();
            $debug['users_count'] = $row['count'];
        }
    }
    
    // Test 6: Test auth handler inclusion
    try {
        require_once __DIR__ . '/handlers/auth-handler.php';
        $debug['auth_handler_loaded'] = true;
        $debug['auth_functions'] = [
            'handleAuth' => function_exists('handleAuth'),
            'handleLogin' => function_exists('handleLogin'),
            'generateAuthToken' => function_exists('generateAuthToken')
        ];
    } catch (Exception $e) {
        $debug['auth_handler_error'] = $e->getMessage();
    }
    
    // Test 7: Test a simple login attempt if POST data is provided
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($conn)) {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if ($data && isset($data['email']) && isset($data['password'])) {
            $debug['login_test'] = [
                'input_received' => true,
                'email' => $data['email'],
                'password_length' => strlen($data['password'])
            ];
            
            // Try to find user
            $stmt = $conn->prepare("SELECT object_id, name, email, password_hash, role, status FROM users WHERE email = ?");
            if ($stmt) {
                $stmt->bind_param("s", $data['email']);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $user = $result->fetch_assoc();
                    $debug['login_test']['user_found'] = true;
                    $debug['login_test']['user_status'] = $user['status'];
                    $debug['login_test']['user_role'] = $user['role'];
                    
                    // Test password
                    if ($user['password_hash']) {
                        $debug['login_test']['password_valid'] = password_verify($data['password'], $user['password_hash']);
                    } else {
                        $debug['login_test']['password_hash_missing'] = true;
                    }
                } else {
                    $debug['login_test']['user_found'] = false;
                }
            } else {
                $debug['login_test']['query_error'] = $conn->error;
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'debug' => $debug,
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'request_uri' => $_SERVER['REQUEST_URI']
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => $debug,
        'trace' => $e->getTraceAsString()
    ], JSON_PRETTY_PRINT);
}
?>
