<?php
/**
 * Comprehensive Error Handler for API endpoints
 * Provides consistent error handling, logging, and response formatting
 */

class ErrorHandler {
    
    private static $logFile = null;
    
    /**
     * Initialize error handler
     */
    public static function init() {
        self::$logFile = __DIR__ . '/../logs/api_errors.log';
        
        // Create logs directory if it doesn't exist
        $logDir = dirname(self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Set custom error and exception handlers
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
    }
    
    /**
     * Handle PHP errors
     */
    public static function handleError($severity, $message, $file, $line) {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = self::getErrorType($severity);
        $logMessage = sprintf(
            "[%s] %s: %s in %s on line %d",
            date('Y-m-d H:i:s'),
            $errorType,
            $message,
            $file,
            $line
        );
        
        self::logError($logMessage);
        
        // For fatal errors, send JSON response
        if ($severity === E_ERROR || $severity === E_CORE_ERROR || $severity === E_COMPILE_ERROR) {
            self::sendErrorResponse(500, 'Internal Server Error', $message);
        }
        
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public static function handleException($exception) {
        $logMessage = sprintf(
            "[%s] Uncaught Exception: %s in %s on line %d\nStack trace:\n%s",
            date('Y-m-d H:i:s'),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            $exception->getTraceAsString()
        );
        
        self::logError($logMessage);
        self::sendErrorResponse(500, 'Internal Server Error', $exception->getMessage());
    }
    
    /**
     * Handle API errors with consistent response format
     */
    public static function handleApiError($code, $message, $details = null, $logLevel = 'ERROR') {
        $logMessage = sprintf(
            "[%s] API %s (%d): %s",
            date('Y-m-d H:i:s'),
            $logLevel,
            $code,
            $message
        );
        
        if ($details) {
            $logMessage .= " | Details: " . (is_array($details) ? json_encode($details) : $details);
        }
        
        self::logError($logMessage);
        self::sendErrorResponse($code, $message, $details);
    }
    
    /**
     * Handle database errors
     */
    public static function handleDatabaseError($conn, $operation = 'Database operation') {
        $error = $conn->error;
        $errno = $conn->errno;
        
        $logMessage = sprintf(
            "[%s] Database Error (%d): %s during %s",
            date('Y-m-d H:i:s'),
            $errno,
            $error,
            $operation
        );
        
        self::logError($logMessage);
        self::sendErrorResponse(500, 'Database error occurred', [
            'operation' => $operation,
            'error_code' => $errno
        ]);
    }
    
    /**
     * Handle authentication errors
     */
    public static function handleAuthError($message = 'Authentication required') {
        self::handleApiError(401, $message, null, 'AUTH');
    }
    
    /**
     * Handle authorization errors
     */
    public static function handleAuthorizationError($message = 'Access denied') {
        self::handleApiError(403, $message, null, 'AUTH');
    }
    
    /**
     * Handle validation errors
     */
    public static function handleValidationError($errors) {
        self::handleApiError(400, 'Validation failed', $errors, 'VALIDATION');
    }
    
    /**
     * Handle not found errors
     */
    public static function handleNotFoundError($resource = 'Resource') {
        self::handleApiError(404, $resource . ' not found', null, 'NOT_FOUND');
    }
    
    /**
     * Send JSON error response
     */
    private static function sendErrorResponse($code, $message, $details = null) {
        if (!headers_sent()) {
            http_response_code($code);
            header('Content-Type: application/json');
        }
        
        $response = [
            'success' => false,
            'error' => $message,
            'code' => $code,
            'timestamp' => date('c')
        ];
        
        if ($details !== null) {
            $response['details'] = $details;
        }
        
        echo json_encode($response);
        exit;
    }
    
    /**
     * Log error message
     */
    private static function logError($message) {
        if (self::$logFile) {
            file_put_contents(self::$logFile, $message . PHP_EOL, FILE_APPEND | LOCK_EX);
        }
        
        // Also log to PHP error log
        error_log($message);
    }
    
    /**
     * Get human-readable error type
     */
    private static function getErrorType($severity) {
        switch ($severity) {
            case E_ERROR:
            case E_CORE_ERROR:
            case E_COMPILE_ERROR:
                return 'FATAL ERROR';
            case E_WARNING:
            case E_CORE_WARNING:
            case E_COMPILE_WARNING:
                return 'WARNING';
            case E_NOTICE:
                return 'NOTICE';
            case E_STRICT:
                return 'STRICT';
            case E_DEPRECATED:
                return 'DEPRECATED';
            default:
                return 'ERROR';
        }
    }
    
    /**
     * Validate required parameters
     */
    public static function validateRequired($data, $required) {
        $missing = [];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            self::handleValidationError([
                'missing_fields' => $missing,
                'message' => 'Required fields are missing: ' . implode(', ', $missing)
            ]);
        }
    }
    
    /**
     * Validate request method
     */
    public static function validateMethod($allowedMethods) {
        $method = $_SERVER['REQUEST_METHOD'];
        if (!in_array($method, $allowedMethods)) {
            self::handleApiError(405, 'Method not allowed', [
                'method' => $method,
                'allowed' => $allowedMethods
            ]);
        }
    }
    
    /**
     * Safe JSON decode with error handling
     */
    public static function safeJsonDecode($json, $assoc = true) {
        $data = json_decode($json, $assoc);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            self::handleValidationError([
                'json_error' => json_last_error_msg(),
                'message' => 'Invalid JSON data'
            ]);
        }
        
        return $data;
    }
}
?>
