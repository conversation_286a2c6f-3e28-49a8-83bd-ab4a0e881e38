function Sidebar({ currentPage, setCurrentPage, user, onMobileClose, collapsed = false, onToggle }) {
    try {
        // Use controlled collapsed state from parent or internal state for mobile
        const [internalCollapsed, setInternalCollapsed] = React.useState(false);
        const isCollapsed = onMobileClose ? internalCollapsed : collapsed;









        // Use actual user data or fallback
        const currentUser = user || {
            name: 'User',
            role: 'Admin'
        };

        // Enhanced menu items with badges and grouping
        const menuItems = [
            { 
                icon: 'fas fa-chart-line', 
                text: 'Dashboard', 
                path: '/',
                group: 'main'
            },
            { 
                icon: 'fas fa-users', 
                text: 'Customers', 
                path: '/customers',
                group: 'main'
            },
            { 
                icon: 'fas fa-user-plus', 
                text: 'Leads', 
                path: '/leads',
                group: 'main'
            },
            { 
                icon: 'fas fa-file-invoice-dollar', 
                text: 'Quotations', 
                path: '/quotations',
                group: 'documents'
            },
            { 
                icon: 'fas fa-file-invoice', 
                text: 'Invoices', 
                path: '/invoices',
                group: 'documents'
            },
            { 
                icon: 'fas fa-file-contract', 
                text: 'Contracts', 
                path: '/contracts',
                group: 'documents'
            },
            { 
                icon: 'fas fa-boxes', 
                text: 'Items', 
                path: '/items',
                group: 'inventory'
            },
            { 
                icon: 'fas fa-chart-bar', 
                text: 'Reports', 
                path: '/reports',
                group: 'analytics'
            },
            { 
                icon: 'fas fa-crown', 
                text: 'Subscription', 
                path: '/subscriptions',
                group: 'account',
                badge: window.APP_CONFIG && window.APP_CONFIG.FEATURES.SUBSCRIPTIONS ? 'PRO' : null,
                badgeColor: 'bg-purple-500'
            },
            { 
                icon: 'fas fa-cog', 
                text: 'Settings', 
                path: '/settings',
                group: 'account'
            }
        ];

        // Group menu items
        const menuGroups = {
            main: { label: 'Main', items: [] },
            documents: { label: 'Documents', items: [] },
            inventory: { label: 'Inventory', items: [] },
            analytics: { label: 'Analytics', items: [] },
            account: { label: 'Account', items: [] }
        };
        
        // Populate groups
        menuItems.forEach(item => {
            if (menuGroups[item.group]) {
                menuGroups[item.group].items.push(item);
            } else {
                // Fallback for items without a group
                menuGroups.main.items.push(item);
            }
        });

        const toggleSidebar = React.useCallback(() => {
            console.log('toggleSidebar called');
            if (onMobileClose) {
                // Mobile sidebar - use internal state
                const newState = !internalCollapsed;
                console.log('Mobile: changing from', internalCollapsed, 'to', newState);
                setInternalCollapsed(newState);
            } else {
                // Desktop sidebar - dispatch the same event that Ctrl+B uses
                console.log('Dispatching sidebar-toggle-request event');
                window.dispatchEvent(new CustomEvent('sidebar-toggle-request'));
                
                // Also call the onToggle prop directly to ensure it works
                if (onToggle) {
                    onToggle();
                }
            }
        }, [onMobileClose, internalCollapsed, onToggle]);

        // Listen for sidebar toggle requests from header and keyboard shortcuts
        React.useEffect(() => {
            // Fixed: Prevent infinite recursion by not calling toggleSidebar directly
            const handleToggleRequest = () => {
                // For mobile, toggle internal state
                if (onMobileClose) {
                    setInternalCollapsed(prev => !prev);
                } else if (onToggle) {
                    // For desktop, call onToggle directly
                    onToggle();
                }
            };

            const handleKeyboardShortcut = (event) => {
                // Ctrl+B to toggle sidebar
                if (event.ctrlKey && event.key === 'b') {
                    event.preventDefault();
                    handleToggleRequest();
                }
            };

            window.addEventListener('sidebar-toggle-request', handleToggleRequest);
            document.addEventListener('keydown', handleKeyboardShortcut);

            return () => {
                window.removeEventListener('sidebar-toggle-request', handleToggleRequest);
                document.removeEventListener('keydown', handleKeyboardShortcut);
            };
        }, [toggleSidebar]);

        const navigate = (path) => {
            // Enhanced navigation using the improved routing system
            const page = path === '/' ? 'dashboard' : path.substring(1);
            
            // Dispatch custom navigation event with structured data
            window.dispatchEvent(new CustomEvent('app-navigate', { 
                detail: { 
                    page: page,
                    id: null,
                    action: null,
                    params: {}
                } 
            }));
        };

        // Listen for custom navigation events from header and other components
        React.useEffect(() => {
            const handleLegacyNavigation = (event) => {
                if (event.detail) {
                    // Support for old navigation event format
                    const page = typeof event.detail === 'string' ? event.detail : event.detail.page;
                    
                    // Dispatch using the new format for consistency
                    window.dispatchEvent(new CustomEvent('app-navigate', { 
                        detail: { 
                            page: page,
                            id: null,
                            action: null,
                            params: {}
                        } 
                    }));
                }
            };

            // Keep supporting the old event for backward compatibility
            window.addEventListener('navigate', handleLegacyNavigation);
            
            return () => window.removeEventListener('navigate', handleLegacyNavigation);
        }, []);

        return (
            <div
                data-name="sidebar"
                className={`
                    ${onMobileClose
                        ? 'fixed left-0 top-0 h-full bg-gray-800 text-white shadow-lg z-50 md:hidden'
                        : 'fixed left-0 top-0 h-full bg-gray-800 text-white transition-all duration-300 shadow-lg z-30 hidden md:block'
                    }
                `}
                style={{
                    width: onMobileClose ? '256px' : (isCollapsed ? '80px' : '256px')
                }}
            >
                {/* Close button for mobile */}
                {onMobileClose && (
                    <button 
                        className="absolute top-4 right-4 text-white md:hidden"
                        onClick={onMobileClose}
                    >
                        <i className="fas fa-times"></i>
                    </button>
                )}
                
                <div data-name="sidebar-header" className="p-4 border-b border-gray-700">
                    <div data-name="logo-container" className={`flex items-center ${isCollapsed && !onMobileClose ? 'justify-center' : 'justify-between'}`}>
                        {(!isCollapsed || onMobileClose) ? (
                            <div className="flex items-center">
                                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-2">
                                    <span className="text-white font-bold">B</span>
                                </div>
                                <h1 className="text-xl font-bold">Bizma</h1>
                            </div>
                        ) : (
                            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold">B</span>
                            </div>
                        )}
                        {!onMobileClose && !isCollapsed && (
                            <button
                                data-name="collapse-button"
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log('COLLAPSE BUTTON CLICKED!');
                                    toggleSidebar();
                                }}
                                className="p-2 rounded hover:bg-gray-700 md:block transition-colors"
                                aria-label="Collapse sidebar"
                                title="Collapse sidebar"
                            >
                                <i className="fas fa-chevron-left text-gray-300"></i>
                            </button>
                        )}
                    </div>
                </div>

                <div data-name="user-profile" className="p-4 border-b border-gray-700">
                    {isCollapsed && !onMobileClose ? (
                        <div className="flex items-center justify-center">
                            <div
                                data-name="user-avatar"
                                className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center cursor-pointer hover:bg-blue-600 transition-colors"
                                title={`${currentUser.name || 'User'} - ${(currentUser.company && currentUser.company.name) || currentUser.role || 'Admin'}`}
                            >
                                <span className="text-white font-medium">
                                    {currentUser.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                                </span>
                            </div>
                        </div>
                    ) : (
                        <div data-name="user-info" className="flex items-center space-x-3">
                            <div data-name="user-avatar" className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                                <span className="text-white font-medium">
                                    {currentUser.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                                </span>
                            </div>
                            <div>
                                <p className="font-medium">{currentUser.name || 'User'}</p>
                                <p className="text-sm text-gray-400">
                                    {(currentUser.company && currentUser.company.name) || currentUser.role || 'Admin'}
                                </p>
                            </div>
                        </div>
                    )}
                </div>

                <nav data-name="sidebar-nav" className="p-4 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 180px)' }}>
                    {/* Render menu items by groups */}
                    {Object.keys(menuGroups).map((groupKey) => {
                        const group = menuGroups[groupKey];
                        // Skip empty groups
                        if (group.items.length === 0) return null;
                        
                        return (
                            <div key={groupKey} className="mb-6">
                                {/* Group label - only show when not collapsed */}
                                {(!isCollapsed || onMobileClose) && (
                                    <h3 className="text-xs uppercase text-gray-500 font-semibold mb-2 px-2">
                                        {group.label}
                                    </h3>
                                )}
                                
                                <ul className="space-y-1">
                                    {group.items.map((item, index) => {
                                        const page = item.path === '/' ? 'dashboard' : item.path.substring(1);
                                        const isActive = currentPage === page;

                                        return (
                                            <li key={index}>
                                                <button
                                                    onClick={() => {
                                                        navigate(item.path);
                                                        // Close mobile menu if applicable
                                                        if (onMobileClose) onMobileClose();
                                                    }}
                                                    data-name={`nav-item-${item.text.toLowerCase()}`}
                                                    className={`w-full flex items-center justify-between p-2 rounded text-left transition-colors relative ${
                                                        isActive
                                                            ? 'bg-blue-600 text-white'
                                                            : 'hover:bg-gray-700 text-gray-300'
                                                    }`}
                                                    aria-label={item.text}
                                                    title={isCollapsed && !onMobileClose ? item.text : ''}
                                                >
                                                    <div className="flex items-center">
                                                        <i className={`${item.icon} ${(!isCollapsed || onMobileClose) ? 'w-6' : 'w-full text-center text-lg'}`}></i>
                                                        {(!isCollapsed || onMobileClose) && <span className="ml-3">{item.text}</span>}
                                                    </div>

                                                    {/* Badge - only show when not collapsed or on mobile */}
                                                    {(!isCollapsed || onMobileClose) && item.badge && (
                                                        <span className={`text-xs px-1.5 py-0.5 rounded ${item.badgeColor || 'bg-blue-500'}`}>
                                                            {item.badge}
                                                        </span>
                                                    )}
                                                </button>
                                            </li>
                                        );
                                    })}
                                </ul>
                            </div>
                        );
                    })}
                </nav>
                
                {/* Version info at bottom */}
                {(!isCollapsed || onMobileClose) && (
                    <div className="absolute bottom-4 left-0 right-0 text-center text-xs text-gray-500">
                        <p>v{(window.APP_CONFIG && window.APP_CONFIG.VERSION) || '1.0.0'}</p>
                    </div>
                )}

                {/* Expand button for collapsed sidebar */}
                {isCollapsed && !onMobileClose && (
                    <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                        <button
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('EXPAND BUTTON CLICKED!');
                                toggleSidebar();
                                // Also call onToggle directly to ensure it works
                                if (onToggle) {
                                    onToggle();
                                }
                            }}
                            className="p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors"
                            aria-label="Expand sidebar"
                            title="Expand sidebar"
                        >
                            <i className="fas fa-chevron-right text-gray-300 text-sm"></i>
                        </button>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('Sidebar component error:', error);
        reportError(error);
        return null;
    }
}
