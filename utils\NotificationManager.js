/**
 * Notification Manager
 * A utility for managing application notifications and toasts
 */

window.NotificationManager = (function() {
    // Private variables
    let notifications = [];
    let listeners = [];
    let notificationId = 0;
    
    // Default notification duration in milliseconds
    const DEFAULT_DURATION = 5000;
    
    // Notification types
    const TYPES = {
        SUCCESS: 'success',
        ERROR: 'error',
        WARNING: 'warning',
        INFO: 'info'
    };
    
    /**
     * Add a notification
     * @param {Object} notification - Notification object
     * @param {string} notification.type - Notification type (success, error, warning, info)
     * @param {string} notification.message - Notification message
     * @param {number} [notification.duration] - Duration in milliseconds (0 for persistent)
     * @param {Function} [notification.onAction] - Callback for action button
     * @param {string} [notification.actionText] - Text for action button
     * @returns {number} - Notification ID
     */
    function addNotification(notification) {
        // Validate notification
        if (!notification || !notification.message) {
            console.error('Invalid notification:', notification);
            return -1;
        }
        
        // Set default type if not provided
        if (!notification.type || !Object.values(TYPES).includes(notification.type)) {
            notification.type = TYPES.INFO;
        }
        
        // Create notification object with ID and timestamp
        const newNotification = {
            ...notification,
            id: ++notificationId,
            timestamp: Date.now(),
            duration: notification.duration !== undefined ? notification.duration : DEFAULT_DURATION
        };
        
        // Add to notifications array
        notifications.push(newNotification);
        
        // Notify listeners
        notifyListeners();
        
        // Set timeout to remove notification if duration > 0
        if (newNotification.duration > 0) {
            setTimeout(() => {
                removeNotification(newNotification.id);
            }, newNotification.duration);
        }
        
        return newNotification.id;
    }
    
    /**
     * Remove a notification by ID
     * @param {number} id - Notification ID
     */
    function removeNotification(id) {
        const index = notifications.findIndex(n => n.id === id);
        if (index !== -1) {
            notifications.splice(index, 1);
            notifyListeners();
        }
    }
    
    /**
     * Clear all notifications
     */
    function clearNotifications() {
        notifications = [];
        notifyListeners();
    }
    
    /**
     * Get all active notifications
     * @returns {Array} - Array of notification objects
     */
    function getNotifications() {
        return [...notifications];
    }
    
    /**
     * Add a listener for notification changes
     * @param {Function} listener - Callback function that receives notifications array
     * @returns {Function} - Function to remove the listener
     */
    function addListener(listener) {
        if (typeof listener !== 'function') {
            console.error('Listener must be a function');
            return () => {};
        }
        
        listeners.push(listener);
        
        // Immediately notify with current notifications
        listener([...notifications]);
        
        // Return function to remove listener
        return () => {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        };
    }
    
    /**
     * Notify all listeners of notification changes
     */
    function notifyListeners() {
        const notificationsCopy = [...notifications];
        listeners.forEach(listener => {
            try {
                listener(notificationsCopy);
            } catch (error) {
                console.error('Error in notification listener:', error);
            }
        });
    }
    
    /**
     * Shorthand method to add a success notification
     * @param {string} message - Notification message
     * @param {Object} [options] - Additional options
     * @returns {number} - Notification ID
     */
    function success(message, options = {}) {
        return addNotification({
            type: TYPES.SUCCESS,
            message,
            ...options
        });
    }
    
    /**
     * Shorthand method to add an error notification
     * @param {string} message - Notification message
     * @param {Object} [options] - Additional options
     * @returns {number} - Notification ID
     */
    function error(message, options = {}) {
        return addNotification({
            type: TYPES.ERROR,
            message,
            ...options
        });
    }
    
    /**
     * Shorthand method to add a warning notification
     * @param {string} message - Notification message
     * @param {Object} [options] - Additional options
     * @returns {number} - Notification ID
     */
    function warning(message, options = {}) {
        return addNotification({
            type: TYPES.WARNING,
            message,
            ...options
        });
    }
    
    /**
     * Shorthand method to add an info notification
     * @param {string} message - Notification message
     * @param {Object} [options] - Additional options
     * @returns {number} - Notification ID
     */
    function info(message, options = {}) {
        return addNotification({
            type: TYPES.INFO,
            message,
            ...options
        });
    }
    
    // Public API
    return {
        addNotification,
        removeNotification,
        clearNotifications,
        getNotifications,
        addListener,
        success,
        error,
        warning,
        info,
        TYPES
    };
})();