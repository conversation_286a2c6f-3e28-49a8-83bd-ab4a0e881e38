<?php
/**
 * Database Setup Script for Password Reset Functionality
 * Run this script once to add the necessary columns to the users table
 */

require_once __DIR__ . '/../api/db-config.php';

try {
    echo "Setting up password reset functionality...\n";
    
    // Check if columns already exist
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'reset_token'");
    if ($result->num_rows > 0) {
        echo "Password reset columns already exist. Skipping migration.\n";
        exit(0);
    }
    
    // Add reset_token column
    echo "Adding reset_token column...\n";
    $conn->query("ALTER TABLE users ADD COLUMN reset_token VARCHAR(64) NULL DEFAULT NULL");
    
    // Add reset_expires column
    echo "Adding reset_expires column...\n";
    $conn->query("ALTER TABLE users ADD COLUMN reset_expires DATETIME NULL DEFAULT NULL");
    
    // Add indexes for performance
    echo "Adding indexes...\n";
    $conn->query("CREATE INDEX idx_users_reset_token ON users(reset_token)");
    $conn->query("CREATE INDEX idx_users_reset_expires ON users(reset_expires)");
    
    // Clean up any existing expired tokens
    echo "Cleaning up expired tokens...\n";
    $conn->query("UPDATE users SET reset_token = NULL, reset_expires = NULL WHERE reset_expires < NOW()");
    
    echo "Password reset functionality setup completed successfully!\n";
    echo "\nYou can now use the forgot password feature.\n";
    echo "In development mode, password reset emails will be logged to the error log.\n";
    
} catch (Exception $e) {
    echo "Error setting up password reset functionality: " . $e->getMessage() . "\n";
    exit(1);
}

$conn->close();
?>
