-- Add password reset columns to users table
-- Run this migration to enable forgot password functionality

-- Add reset_token column
ALTER TABLE users ADD COLUMN reset_token VARCHAR(64) NULL DEFAULT NULL;

-- Add reset_expires column  
ALTER TABLE users ADD COLUMN reset_expires DATETIME NULL DEFAULT NULL;

-- Add index for faster token lookups
CREATE INDEX idx_users_reset_token ON users(reset_token);

-- Add index for reset token expiry cleanup
CREATE INDEX idx_users_reset_expires ON users(reset_expires);

-- Optional: Clean up any existing expired tokens (if any)
UPDATE users SET reset_token = NULL, reset_expires = NULL WHERE reset_expires < NOW();
