<?php
require_once '../db-config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication and super admin role
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    if ($currentUser['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        exit;
    }
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetPlans();
            break;
        case 'POST':
            handleCreatePlan();
            break;
        case 'PUT':
            handleUpdatePlan();
            break;
        case 'DELETE':
            handleDeletePlan();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Plans API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetPlans() {
    global $conn;
    
    try {
        $sql = "
            SELECT p.*, 
                   COUNT(s.id) as subscriber_count,
                   SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_subscribers
            FROM pricing_plans p
            LEFT JOIN subscriptions s ON p.id = s.plan_id
            GROUP BY p.id
            ORDER BY p.sort_order ASC, p.created_at DESC
        ";
        
        $result = $conn->query($sql);
        $plans = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // Parse JSON fields
                $row['features'] = $row['features'] ? json_decode($row['features'], true) : [];
                $row['limits_data'] = $row['limits_data'] ? json_decode($row['limits_data'], true) : [];
                $row['business_types'] = $row['business_types'] ? json_decode($row['business_types'], true) : [];
                
                $plans[] = $row;
            }
        }
        
        echo json_encode(['success' => true, 'data' => $plans]);
    } catch (Exception $e) {
        error_log("Error fetching plans: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to fetch plans']);
    }
}

function handleCreatePlan($conn) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        $required = ['id', 'name', 'price_monthly', 'price_yearly'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                echo json_encode(['success' => false, 'message' => "Field '$field' is required"]);
                return;
            }
        }
        
        // Sanitize inputs
        $id = SecurityUtils::sanitizeInput($input['id']);
        $name = SecurityUtils::sanitizeInput($input['name']);
        $description = SecurityUtils::sanitizeInput($input['description'] ?? '');
        $short_description = SecurityUtils::sanitizeInput($input['short_description'] ?? '');
        $price_monthly = floatval($input['price_monthly']);
        $price_yearly = floatval($input['price_yearly']);
        $trial_days = intval($input['trial_days'] ?? 14);
        $max_users = isset($input['limits_data']['max_users']) ? intval($input['limits_data']['max_users']) : null;
        $max_leads = isset($input['limits_data']['max_leads']) ? intval($input['limits_data']['max_leads']) : null;
        $max_storage = isset($input['limits_data']['max_storage']) ? intval($input['limits_data']['max_storage']) : null;
        $features = json_encode($input['features'] ?? []);
        $limits_data = json_encode($input['limits_data'] ?? []);
        $business_types = json_encode($input['business_types'] ?? []);
        $is_trial_available = isset($input['is_trial_available']) ? (bool)$input['is_trial_available'] : true;
        $is_visible = isset($input['is_visible']) ? (bool)$input['is_visible'] : true;
        $is_popular = isset($input['is_popular']) ? (bool)$input['is_popular'] : false;
        $sort_order = intval($input['sort_order'] ?? 0);
        
        // Calculate yearly savings
        $yearly_savings = ($price_monthly * 12) - $price_yearly;
        
        $stmt = $conn->prepare("
            INSERT INTO plans (id, name, description, short_description, price_monthly, price_yearly, 
                              trial_days, max_users, max_leads, max_storage, features, limits_data, 
                              business_types, yearly_savings, is_trial_available, is_visible, 
                              is_popular, sort_order, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->bind_param("ssssddiiissssdiiiii", 
            $id, $name, $description, $short_description, $price_monthly, $price_yearly,
            $trial_days, $max_users, $max_leads, $max_storage, $features, $limits_data,
            $business_types, $yearly_savings, $is_trial_available, $is_visible, 
            $is_popular, $sort_order
        );
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Plan created successfully', 'id' => $id]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to create plan: ' . $stmt->error]);
        }
    } catch (Exception $e) {
        error_log("Error creating plan: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to create plan']);
    }
}

function handleUpdatePlan($conn) {
    try {
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $planId = end($pathParts);
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Sanitize inputs
        $name = SecurityUtils::sanitizeInput($input['name']);
        $description = SecurityUtils::sanitizeInput($input['description'] ?? '');
        $short_description = SecurityUtils::sanitizeInput($input['short_description'] ?? '');
        $price_monthly = floatval($input['price_monthly']);
        $price_yearly = floatval($input['price_yearly']);
        $trial_days = intval($input['trial_days'] ?? 14);
        $max_users = isset($input['limits_data']['max_users']) ? intval($input['limits_data']['max_users']) : null;
        $max_leads = isset($input['limits_data']['max_leads']) ? intval($input['limits_data']['max_leads']) : null;
        $max_storage = isset($input['limits_data']['max_storage']) ? intval($input['limits_data']['max_storage']) : null;
        $features = json_encode($input['features'] ?? []);
        $limits_data = json_encode($input['limits_data'] ?? []);
        $business_types = json_encode($input['business_types'] ?? []);
        $is_trial_available = isset($input['is_trial_available']) ? (bool)$input['is_trial_available'] : true;
        $is_visible = isset($input['is_visible']) ? (bool)$input['is_visible'] : true;
        $is_popular = isset($input['is_popular']) ? (bool)$input['is_popular'] : false;
        $sort_order = intval($input['sort_order'] ?? 0);
        
        // Calculate yearly savings
        $yearly_savings = ($price_monthly * 12) - $price_yearly;
        
        $stmt = $conn->prepare("
            UPDATE plans 
            SET name = ?, description = ?, short_description = ?, price_monthly = ?, price_yearly = ?,
                trial_days = ?, max_users = ?, max_leads = ?, max_storage = ?, features = ?,
                limits_data = ?, business_types = ?, yearly_savings = ?, is_trial_available = ?,
                is_visible = ?, is_popular = ?, sort_order = ?, updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->bind_param("sssddiiiisssdiiiiis", 
            $name, $description, $short_description, $price_monthly, $price_yearly,
            $trial_days, $max_users, $max_leads, $max_storage, $features,
            $limits_data, $business_types, $yearly_savings, $is_trial_available,
            $is_visible, $is_popular, $sort_order, $planId
        );
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Plan updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update plan']);
        }
    } catch (Exception $e) {
        error_log("Error updating plan: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to update plan']);
    }
}

function handleDeletePlan($conn) {
    try {
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $planId = end($pathParts);
        
        // Check if plan has active subscriptions
        $checkStmt = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM subscriptions 
            WHERE plan_id = ? AND status IN ('active', 'trial')
        ");
        $checkStmt->bind_param("s", $planId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        $row = $result->fetch_assoc();
        
        if ($row['count'] > 0) {
            echo json_encode([
                'success' => false, 
                'message' => 'Cannot delete plan with active subscriptions. Please migrate users first.'
            ]);
            return;
        }
        
        $stmt = $conn->prepare("DELETE FROM plans WHERE id = ?");
        $stmt->bind_param("s", $planId);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Plan deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete plan']);
        }
    } catch (Exception $e) {
        error_log("Error deleting plan: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to delete plan']);
    }
}
?>
