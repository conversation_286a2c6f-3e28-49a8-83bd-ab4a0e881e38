<?php
// Clean auth handler that ensures proper JSON output
error_reporting(0); // Suppress PHP warnings/notices
ini_set('display_errors', 0);

// Start output buffering to catch any unwanted output
ob_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/middleware/security.php';
require_once __DIR__ . '/db-config.php';

// Clear any output that might have been generated
ob_clean();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'login':
            handleLoginFixed($input);
            break;
            
        case 'logout':
            handleLogoutFixed($input);
            break;
            
        case 'verify':
            handleVerifyTokenFixed($input);
            break;
            
        case 'refresh':
            handleRefreshTokenFixed($input);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Auth handler error: " . $e->getMessage());
}

function handleLoginFixed($input) {
    // Validate required fields
    if (empty($input['email']) || empty($input['password'])) {
        throw new Exception('Email and password are required');
    }
    
    $email = SecurityMiddleware::sanitizeInput($input['email']);
    $password = $input['password'];
    $remember_me = $input['remember_me'] ?? false;
    
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed');
    }
    
    // Get user with company information
    $stmt = $conn->prepare("
        SELECT u.*, c.name as company_name
        FROM users u
        LEFT JOIN companies c ON u.company_id = c.id
        WHERE u.email = ? AND u.status = 'active'
    ");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('Invalid email or password');
    }

    $user = $result->fetch_assoc();

    // Verify password
    if (!password_verify($password, $user['password_hash'])) {
        throw new Exception('Invalid email or password');
    }
    
    // Generate JWT token
    $token_payload = [
        'user_id' => $user['id'],
        'email' => $user['email'],
        'company_id' => $user['company_id'],
        'role' => $user['role'],
        'exp' => time() + ($remember_me ? 30 * 24 * 3600 : 24 * 3600) // 30 days or 1 day
    ];
    
    $token = SecurityMiddleware::generateJWT($token_payload);
    
    // Update user's auth token and last login
    $stmt = $conn->prepare("
        UPDATE users 
        SET auth_token = ?, token_expires = ?, last_login = NOW(), login_count = login_count + 1 
        WHERE id = ?
    ");
    $expires = date('Y-m-d H:i:s', time() + ($remember_me ? 30 * 24 * 3600 : 24 * 3600));
    $stmt->bind_param("ssi", $token, $expires, $user['id']);
    $stmt->execute();
    
    // Prepare user data for response (exclude sensitive fields)
    unset($user['password_hash']);
    unset($user['auth_token']);
    unset($user['email_verification_token']);
    unset($user['password_reset_token']);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'token' => $token,
        'user' => $user
    ]);
    
    $conn->close();
}

function handleLogoutFixed($input) {
    // Get token from input or Authorization header
    $token = $input['token'] ?? null;
    
    if (!$token) {
        // Try to get from Authorization header
        $headers = getallheaders();
        $auth_header = $headers['Authorization'] ?? '';
        if (strpos($auth_header, 'Bearer ') === 0) {
            $token = substr($auth_header, 7);
        }
    }
    
    if (!$token) {
        throw new Exception('No authentication token provided');
    }
    
    // Connect to database
    $db = Config::getDatabase();
    $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed');
    }
    
    // Invalidate token
    $stmt = $conn->prepare("UPDATE users SET auth_token = NULL, token_expires = NULL WHERE auth_token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Logout successful'
    ]);
    
    $conn->close();
}

function handleVerifyTokenFixed($input) {
    // Get token from input or Authorization header
    $token = $input['token'] ?? null;
    
    if (!$token) {
        // Try to get from Authorization header
        $headers = getallheaders();
        $auth_header = $headers['Authorization'] ?? '';
        if (strpos($auth_header, 'Bearer ') === 0) {
            $token = substr($auth_header, 7);
        }
    }
    
    if (!$token) {
        throw new Exception('No authentication token provided');
    }
    
    // Verify token
    $user = SecurityMiddleware::verifyJWT($token);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Token is valid',
        'user' => $user
    ]);
}

function handleRefreshTokenFixed($input) {
    // Get token from input or Authorization header
    $token = $input['token'] ?? null;
    
    if (!$token) {
        // Try to get from Authorization header
        $headers = getallheaders();
        $auth_header = $headers['Authorization'] ?? '';
        if (strpos($auth_header, 'Bearer ') === 0) {
            $token = substr($auth_header, 7);
        }
    }
    
    if (!$token) {
        throw new Exception('No authentication token provided');
    }
    
    // Verify and refresh token
    $user = SecurityMiddleware::refreshJWT($token);
    $new_token = $user['token'];
    unset($user['token']);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Token refreshed',
        'token' => $new_token,
        'user' => $user
    ]);
}
?>