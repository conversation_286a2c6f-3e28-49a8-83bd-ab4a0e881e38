<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Bizma</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Debug Information</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- JavaScript Console -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">JavaScript Console</h2>
                <div id="console-output" class="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto"></div>
                <button onclick="clearConsole()" class="mt-2 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    Clear Console
                </button>
            </div>
            
            <!-- Component Status -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Component Status</h2>
                <div id="component-status" class="space-y-2"></div>
                <button onclick="checkComponents()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Check Components
                </button>
            </div>
            
            <!-- Network Status -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Network Status</h2>
                <div id="network-status" class="space-y-2"></div>
                <button onclick="checkNetwork()" class="mt-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Check Network
                </button>
            </div>
            
            <!-- Page Information -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Page Information</h2>
                <div id="page-info" class="space-y-2"></div>
                <button onclick="getPageInfo()" class="mt-4 bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Get Page Info
                </button>
            </div>
        </div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'warn' ? 'text-yellow-400' : 'text-green-400';
            consoleOutput.innerHTML += `<div class="${color}">[${timestamp}] ${type.toUpperCase()}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('warn', args.join(' '));
        };
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        function checkComponents() {
            const statusDiv = document.getElementById('component-status');
            statusDiv.innerHTML = '';
            
            const components = [
                'LoadingSpinner',
                'ErrorMessage',
                'NotificationContainer',
                'TrialBanner',
                'ExtendTrialModal',
                'WebsiteHeader',
                'WebsiteFooter',
                'LandingPage',
                'SimplePricingPlans'
            ];
            
            components.forEach(component => {
                const exists = typeof window[component] !== 'undefined';
                const status = exists ? 'Available' : 'Missing';
                const color = exists ? 'text-green-600' : 'text-red-600';
                statusDiv.innerHTML += `<div class="${color}">${component}: ${status}</div>`;
            });
        }
        
        async function checkNetwork() {
            const statusDiv = document.getElementById('network-status');
            statusDiv.innerHTML = 'Checking...';
            
            const endpoints = [
                '/biz/api/pricing-plans.php',
                '/biz/api/business-types.php',
                '/biz/api/login.php',
                '/biz/api/register.php'
            ];
            
            let results = '';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint, { method: 'OPTIONS' });
                    const status = response.ok ? 'OK' : 'Error';
                    const color = response.ok ? 'text-green-600' : 'text-red-600';
                    results += `<div class="${color}">${endpoint}: ${status} (${response.status})</div>`;
                } catch (error) {
                    results += `<div class="text-red-600">${endpoint}: Error - ${error.message}</div>`;
                }
            }
            
            statusDiv.innerHTML = results;
        }
        
        function getPageInfo() {
            const infoDiv = document.getElementById('page-info');
            
            const info = {
                'URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Screen Size': `${screen.width}x${screen.height}`,
                'Window Size': `${window.innerWidth}x${window.innerHeight}`,
                'Local Storage': localStorage.length + ' items',
                'Session Storage': sessionStorage.length + ' items',
                'Cookies': document.cookie ? 'Present' : 'None'
            };
            
            let html = '';
            for (const [key, value] of Object.entries(info)) {
                html += `<div><strong>${key}:</strong> ${value}</div>`;
            }
            
            infoDiv.innerHTML = html;
        }
        
        // Auto-run checks on page load
        window.addEventListener('load', function() {
            console.log('Debug page loaded');
            checkComponents();
            getPageInfo();
        });
        
        // Capture any errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.message, 'at', e.filename + ':' + e.lineno);
        });
        
        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    </script>
</body>
</html>
