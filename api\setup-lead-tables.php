<?php
/**
 * Setup script for lead-related tables (activities, tasks, notes)
 */

require_once 'db-config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

echo "<h2>Setting up Lead-related Tables</h2>\n";

// Create activities table
$activitiesTable = "
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(255) UNIQUE NOT NULL,
    lead_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'general',
    description TEXT NOT NULL,
    activity_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    company_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_lead_id (lead_id),
    INDEX idx_company_id (company_id),
    INDEX idx_type (type),
    INDEX idx_activity_date (activity_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create tasks table
$tasksTable = "
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(255) UNIQUE NOT NULL,
    lead_id VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date DATE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
    company_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_lead_id (lead_id),
    INDEX idx_company_id (company_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create notes table
$notesTable = "
CREATE TABLE IF NOT EXISTS notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    object_id VARCHAR(255) UNIQUE NOT NULL,
    lead_id VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    title VARCHAR(255),
    type VARCHAR(50) DEFAULT 'note',
    status VARCHAR(50) DEFAULT 'active',
    company_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_lead_id (lead_id),
    INDEX idx_company_id (company_id),
    INDEX idx_type (type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Execute table creation
$tables = [
    'activities' => $activitiesTable,
    'tasks' => $tasksTable,
    'notes' => $notesTable
];

foreach ($tables as $tableName => $sql) {
    echo "<h3>Creating $tableName table...</h3>\n";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>✅ Table '$tableName' created successfully</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Error creating table '$tableName': " . $conn->error . "</p>\n";
    }
}

// Check if tables exist and show structure
echo "<h3>Verifying table structures...</h3>\n";

foreach (array_keys($tables) as $tableName) {
    $result = $conn->query("DESCRIBE $tableName");
    if ($result) {
        echo "<h4>$tableName table structure:</h4>\n";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: red;'>❌ Could not describe table '$tableName': " . $conn->error . "</p>\n";
    }
}

// Add foreign key constraints if they don't exist
echo "<h3>Adding foreign key constraints...</h3>\n";

$constraints = [
    "ALTER TABLE activities ADD CONSTRAINT fk_activities_lead FOREIGN KEY (lead_id) REFERENCES leads(object_id) ON DELETE CASCADE",
    "ALTER TABLE tasks ADD CONSTRAINT fk_tasks_lead FOREIGN KEY (lead_id) REFERENCES leads(object_id) ON DELETE CASCADE", 
    "ALTER TABLE notes ADD CONSTRAINT fk_notes_lead FOREIGN KEY (lead_id) REFERENCES leads(object_id) ON DELETE CASCADE"
];

foreach ($constraints as $constraint) {
    // Extract table name for error reporting
    preg_match('/ALTER TABLE (\w+)/', $constraint, $matches);
    $tableName = $matches[1] ?? 'unknown';
    
    if ($conn->query($constraint) === TRUE) {
        echo "<p style='color: green;'>✅ Foreign key constraint added for $tableName</p>\n";
    } else {
        // Check if constraint already exists
        if (strpos($conn->error, 'Duplicate key name') !== false || strpos($conn->error, 'already exists') !== false) {
            echo "<p style='color: blue;'>ℹ️ Foreign key constraint for $tableName already exists</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ Could not add foreign key constraint for $tableName: " . $conn->error . "</p>\n";
        }
    }
}

echo "<h3>Setup Complete!</h3>\n";
echo "<p>All lead-related tables have been set up. You can now use the CRUD operations for activities, tasks, and notes.</p>\n";

$conn->close();
?>