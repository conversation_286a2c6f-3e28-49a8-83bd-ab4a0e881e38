<?php
require_once '../config/database.php';
require_once '../utils/SecurityUtils.php';
require_once '../utils/AuthUtils.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication and super admin role
    $authResult = AuthUtils::verifyToken();
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    $user = $authResult['user'];
    if ($user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Access denied']);
        exit;
    }

    $conn = Database::getConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Handle different endpoints
    if (end($pathParts) === 'stats') {
        handleGetStats($conn);
    } elseif (count($pathParts) >= 4 && $pathParts[count($pathParts)-2] === 'companies') {
        $companyId = end($pathParts);
        if ($method === 'GET') {
            handleGetCompanyDetails($conn, $companyId);
        } elseif ($method === 'PUT') {
            handleUpdateCompanyStatus($conn, $companyId);
        }
    } else {
        if ($method === 'GET') {
            handleGetCompanies($conn);
        } else {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        }
    }

} catch (Exception $e) {
    error_log("Companies API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetCompanies($conn) {
    try {
        // Get filter parameters
        $status = $_GET['status'] ?? 'all';
        $plan = $_GET['plan'] ?? 'all';
        $search = $_GET['search'] ?? '';
        
        $whereConditions = [];
        $params = [];
        $types = '';
        
        if ($status !== 'all') {
            $whereConditions[] = "s.status = ?";
            $params[] = $status;
            $types .= 's';
        }
        
        if ($plan !== 'all') {
            $whereConditions[] = "s.plan_id = ?";
            $params[] = $plan;
            $types .= 's';
        }
        
        if (!empty($search)) {
            $whereConditions[] = "(c.name LIKE ? OR u.email LIKE ?)";
            $searchParam = "%$search%";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $types .= 'ss';
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        $sql = "
            SELECT c.id, c.object_id, c.name, c.industry, c.size, c.created_at,
                   u.name as owner_name, u.email as owner_email, u.phone as owner_phone,
                   s.status as subscription_status, s.plan_id, s.billing_cycle, s.amount,
                   p.name as plan_name, p.price_monthly as plan_price,
                   bt.name as business_type_name,
                   (SELECT COUNT(*) FROM users WHERE company_id = c.id) as user_count
            FROM companies c
            LEFT JOIN users u ON c.owner_id = u.object_id
            LEFT JOIN subscriptions s ON c.object_id = s.company_id AND s.status IN ('active', 'trial', 'expired')
            LEFT JOIN plans p ON s.plan_id = p.id
            LEFT JOIN business_types bt ON c.business_type_id = bt.id
            $whereClause
            ORDER BY c.created_at DESC
        ";
        
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $companies = [];
        while ($row = $result->fetch_assoc()) {
            $companies[] = $row;
        }
        
        echo json_encode(['success' => true, 'data' => $companies]);
    } catch (Exception $e) {
        error_log("Error fetching companies: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to fetch companies']);
    }
}

function handleGetStats($conn) {
    try {
        $stats = [
            'total' => 0,
            'active' => 0,
            'trial' => 0,
            'expired' => 0
        ];
        
        // Get total companies
        $totalStmt = $conn->prepare("SELECT COUNT(*) as count FROM companies");
        $totalStmt->execute();
        $totalResult = $totalStmt->get_result();
        $stats['total'] = $totalResult->fetch_assoc()['count'];
        
        // Get subscription status counts
        $statusStmt = $conn->prepare("
            SELECT s.status, COUNT(*) as count
            FROM subscriptions s
            WHERE s.status IN ('active', 'trial', 'expired')
            GROUP BY s.status
        ");
        $statusStmt->execute();
        $statusResult = $statusStmt->get_result();
        
        while ($row = $statusResult->fetch_assoc()) {
            $stats[$row['status']] = $row['count'];
        }
        
        echo json_encode(['success' => true, 'data' => $stats]);
    } catch (Exception $e) {
        error_log("Error fetching stats: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to fetch statistics']);
    }
}

function handleGetCompanyDetails($conn, $companyId) {
    try {
        // Get company details
        $companyStmt = $conn->prepare("
            SELECT c.*, u.name as owner_name, u.email as owner_email, u.phone as owner_phone,
                   bt.name as business_type_name
            FROM companies c
            LEFT JOIN users u ON c.owner_id = u.object_id
            LEFT JOIN business_types bt ON c.business_type_id = bt.id
            WHERE c.id = ? OR c.object_id = ?
        ");
        $companyStmt->bind_param("ss", $companyId, $companyId);
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if ($company = $companyResult->fetch_assoc()) {
            // Get subscription details
            $subscriptionStmt = $conn->prepare("
                SELECT s.*, p.name as plan_name, p.price_monthly, p.price_yearly
                FROM subscriptions s
                LEFT JOIN plans p ON s.plan_id = p.id
                WHERE s.company_id = ?
                ORDER BY s.created_at DESC
                LIMIT 1
            ");
            $subscriptionStmt->bind_param("s", $company['object_id']);
            $subscriptionStmt->execute();
            $subscriptionResult = $subscriptionStmt->get_result();
            $company['subscription'] = $subscriptionResult->fetch_assoc();
            
            // Get usage statistics
            $usageStats = [];
            
            // Count users
            $userStmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE company_id = ?");
            $userStmt->bind_param("s", $company['id']);
            $userStmt->execute();
            $userResult = $userStmt->get_result();
            $usageStats['users'] = $userResult->fetch_assoc()['count'];
            
            // Count leads
            $leadStmt = $conn->prepare("SELECT COUNT(*) as count FROM leads WHERE company_id = ?");
            $leadStmt->bind_param("s", $company['object_id']);
            $leadStmt->execute();
            $leadResult = $leadStmt->get_result();
            $usageStats['leads'] = $leadResult->fetch_assoc()['count'];
            
            // Count customers
            $customerStmt = $conn->prepare("SELECT COUNT(*) as count FROM customers WHERE company_id = ?");
            $customerStmt->bind_param("s", $company['object_id']);
            $customerStmt->execute();
            $customerResult = $customerStmt->get_result();
            $usageStats['customers'] = $customerResult->fetch_assoc()['count'];
            
            $company['usage'] = $usageStats;
            
            echo json_encode(['success' => true, 'data' => $company]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Company not found']);
        }
    } catch (Exception $e) {
        error_log("Error fetching company details: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to fetch company details']);
    }
}

function handleUpdateCompanyStatus($conn, $companyId) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $newStatus = SecurityUtils::sanitizeInput($input['status'] ?? '');
        
        if (empty($newStatus)) {
            echo json_encode(['success' => false, 'message' => 'Status is required']);
            return;
        }
        
        // Get company object_id
        $companyStmt = $conn->prepare("SELECT object_id FROM companies WHERE id = ? OR object_id = ?");
        $companyStmt->bind_param("ss", $companyId, $companyId);
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if ($company = $companyResult->fetch_assoc()) {
            // Update subscription status
            $updateStmt = $conn->prepare("
                UPDATE subscriptions 
                SET status = ?, updated_at = NOW() 
                WHERE company_id = ?
            ");
            $updateStmt->bind_param("ss", $newStatus, $company['object_id']);
            
            if ($updateStmt->execute()) {
                echo json_encode(['success' => true, 'message' => 'Company status updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update status']);
            }
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Company not found']);
        }
    } catch (Exception $e) {
        error_log("Error updating company status: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to update company status']);
    }
}
?>
